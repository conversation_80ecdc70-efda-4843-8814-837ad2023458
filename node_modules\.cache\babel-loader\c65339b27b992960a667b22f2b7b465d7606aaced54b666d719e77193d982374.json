{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0634\\u0631\\u0648\\u0639\\\\src\\\\components\\\\AIAssistant\\\\AIAssistant.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { XMarkIcon, PaperAirplaneIcon, MinusIcon, StarIcon } from '@heroicons/react/24/outline';\nimport { SparklesIcon as SparklesIconSolid } from '@heroicons/react/24/solid';\n\n// Types\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AIAssistant = ({\n  context = 'student'\n}) => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const [isMinimized, setIsMinimized] = useState(false);\n  const getWelcomeMessage = () => {\n    switch (context) {\n      case 'login':\n        return 'مرحباً! أنا المساعد الذكي لمنصة ALaa Abd Hamied. هل تحتاج مساعدة في تسجيل الدخول أو لديك أسئلة حول المنصة؟';\n      case 'admin':\n        return 'مرحباً أيها المدير! أنا هنا لمساعدتك في إدارة المنصة والإجابة على أي استفسارات تقنية.';\n      default:\n        return 'مرحباً عزيزي الطالب! أنا المساعد الذكي لمنصة ALaa Abd Hamied. كيف يمكنني مساعدتك في رحلتك التعليمية اليوم؟';\n    }\n  };\n  const [messages, setMessages] = useState([{\n    id: '1',\n    type: 'assistant',\n    content: getWelcomeMessage(),\n    timestamp: new Date()\n  }]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const messagesEndRef = useRef(null);\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  const quickReplies = ['كيف أشاهد الكورسات؟', 'كيف أؤدي الاختبارات؟', 'كيف أحصل على الشهادة؟', 'مشكلة في تشغيل الفيديو', 'تغيير كلمة المرور', 'التواصل مع الدعم'];\n  const getAIResponse = userMessage => {\n    const message = userMessage.toLowerCase();\n    if (message.includes('كورس') || message.includes('مشاهدة')) {\n      return 'لمشاهدة الكورسات، اذهب إلى قسم \"كورساتي\" من القائمة الرئيسية. ستجد جميع الكورسات المتاحة لك مع إمكانية متابعة التقدم.';\n    }\n    if (message.includes('اختبار') || message.includes('امتحان')) {\n      return 'يمكنك الوصول للاختبارات من داخل كل كورس. بعد مشاهدة الفيديوهات المطلوبة، ستظهر لك الاختبارات المتاحة. تأكد من قراءة التعليمات قبل البدء.';\n    }\n    if (message.includes('شهادة')) {\n      return 'للحصول على الشهادة، يجب إكمال جميع فيديوهات الكورس واجتياز الاختبارات بنجاح. ستظهر الشهادة تلقائياً في قسم \"شهاداتي\".';\n    }\n    if (message.includes('فيديو') || message.includes('تشغيل')) {\n      return 'إذا واجهت مشكلة في تشغيل الفيديو، تأكد من اتصالك بالإنترنت وحاول تحديث الصفحة. إذا استمرت المشكلة، جرب متصفح آخر.';\n    }\n    if (message.includes('مرور') || message.includes('كلمة')) {\n      return 'لا يمكن تغيير كلمة المرور للطلاب حيث يتم الدخول بكود الوصول. إذا نسيت الكود، تواصل مع المدير للحصول على كود جديد.';\n    }\n    if (message.includes('دعم') || message.includes('مساعدة') || message.includes('مشكلة')) {\n      return 'يمكنك التواصل مع فريق الدعم من خلال النقر على \"اتصل بنا\" في أسفل الصفحة، أو إرسال رسالة مباشرة للمدير.';\n    }\n    if (message.includes('مرحبا') || message.includes('السلام') || message.includes('أهلا')) {\n      return 'أهلاً وسهلاً بك! أنا هنا لمساعدتك في أي استفسار حول المنصة. ما الذي تحتاج مساعدة فيه؟';\n    }\n    if (message.includes('شكرا') || message.includes('شكراً')) {\n      return 'العفو! أتمنى أن أكون قد ساعدتك. لا تتردد في سؤالي عن أي شيء آخر.';\n    }\n    return 'أعتذر، لم أفهم سؤالك بوضوح. يمكنك تجربة إحدى الأسئلة الشائعة أدناه، أو إعادة صياغة سؤالك بطريقة أخرى.';\n  };\n  const handleSendMessage = async () => {\n    if (!inputMessage.trim()) return;\n    const userMessage = {\n      id: Date.now().toString(),\n      type: 'user',\n      content: inputMessage,\n      timestamp: new Date()\n    };\n    setMessages(prev => [...prev, userMessage]);\n    setInputMessage('');\n    setIsTyping(true);\n\n    // Simulate AI thinking time\n    setTimeout(() => {\n      const aiResponse = {\n        id: (Date.now() + 1).toString(),\n        type: 'assistant',\n        content: getAIResponse(inputMessage),\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, aiResponse]);\n      setIsTyping(false);\n    }, 1000 + Math.random() * 1000);\n  };\n  const handleQuickReply = reply => {\n    setInputMessage(reply);\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: !isOpen && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          scale: 0,\n          opacity: 0\n        },\n        animate: {\n          scale: 1,\n          opacity: 1\n        },\n        exit: {\n          scale: 0,\n          opacity: 0\n        },\n        className: \"fixed bottom-6 left-6 z-50\",\n        children: [/*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.1,\n            rotate: 5\n          },\n          whileTap: {\n            scale: 0.9\n          },\n          onClick: () => setIsOpen(true),\n          className: \"relative w-16 h-16 bg-gradient-to-br from-yellow-400 via-yellow-500 to-amber-600 text-white rounded-full shadow-2xl hover:shadow-yellow-500/25 transition-all duration-300 flex items-center justify-center group overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-gradient-to-br from-yellow-300 to-amber-500 rounded-full opacity-0 group-hover:opacity-20 transition-opacity duration-300\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 rounded-full\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              animate: {\n                rotate: 360\n              },\n              transition: {\n                duration: 8,\n                repeat: Infinity,\n                ease: \"linear\"\n              },\n              className: \"absolute inset-2 border-2 border-yellow-300/30 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              animate: {\n                rotate: -360\n              },\n              transition: {\n                duration: 12,\n                repeat: Infinity,\n                ease: \"linear\"\n              },\n              className: \"absolute inset-1 border border-yellow-200/20 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative z-10 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(SparklesIconSolid, {\n              className: \"w-7 h-7 text-white drop-shadow-lg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            animate: {\n              scale: [1, 1.2, 1],\n              opacity: [0.7, 1, 0.7]\n            },\n            transition: {\n              duration: 2,\n              repeat: Infinity,\n              ease: \"easeInOut\"\n            },\n            className: \"absolute -top-1 -right-1 w-3 h-3 bg-yellow-300 rounded-full shadow-lg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            animate: {\n              scale: [1, 1.3, 1],\n              opacity: [0.5, 1, 0.5]\n            },\n            transition: {\n              duration: 2.5,\n              repeat: Infinity,\n              ease: \"easeInOut\",\n              delay: 0.5\n            },\n            className: \"absolute -bottom-1 -left-1 w-2 h-2 bg-amber-300 rounded-full shadow-lg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            animate: {\n              scale: [1, 1.1, 1],\n              opacity: [0.6, 1, 0.6]\n            },\n            transition: {\n              duration: 3,\n              repeat: Infinity,\n              ease: \"easeInOut\",\n              delay: 1\n            },\n            className: \"absolute top-1 -left-2 w-1.5 h-1.5 bg-yellow-200 rounded-full shadow-lg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full animate-pulse border-2 border-white shadow-lg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: -10\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            delay: 1\n          },\n          className: \"absolute right-20 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white px-3 py-2 rounded-lg text-sm whitespace-nowrap shadow-lg\",\n          children: [\"\\u0627\\u0644\\u0645\\u0633\\u0627\\u0639\\u062F \\u0627\\u0644\\u0630\\u0643\\u064A\", /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute left-0 top-1/2 transform -translate-y-1/2 translate-x-1 w-2 h-2 bg-gray-900 rotate-45\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: isOpen && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 100,\n          scale: 0.9\n        },\n        animate: {\n          opacity: 1,\n          y: 0,\n          scale: 1,\n          height: isMinimized ? 60 : 500\n        },\n        exit: {\n          opacity: 0,\n          y: 100,\n          scale: 0.9\n        },\n        className: \"fixed bottom-6 left-6 w-80 bg-white rounded-xl shadow-2xl border border-gray-200 z-50 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-yellow-500 via-amber-500 to-yellow-600 text-white p-4 flex items-center justify-between relative overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 opacity-10\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute top-2 left-4 w-2 h-2 bg-white rounded-full animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute top-6 right-8 w-1 h-1 bg-white rounded-full animate-pulse\",\n              style: {\n                animationDelay: '0.5s'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute bottom-3 left-12 w-1.5 h-1.5 bg-white rounded-full animate-pulse\",\n              style: {\n                animationDelay: '1s'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center relative z-10\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-10 h-10 bg-gradient-to-br from-white/30 to-white/10 rounded-full flex items-center justify-center ml-3 backdrop-blur-sm border border-white/20\",\n              children: /*#__PURE__*/_jsxDEV(SparklesIconSolid, {\n                className: \"w-5 h-5 text-white drop-shadow-lg\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-bold text-sm flex items-center\",\n                children: [\"\\u0627\\u0644\\u0645\\u0633\\u0627\\u0639\\u062F \\u0627\\u0644\\u0630\\u0643\\u064A\", /*#__PURE__*/_jsxDEV(StarIcon, {\n                  className: \"w-3 h-3 mr-1 text-yellow-200\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs opacity-90 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-2 h-2 bg-green-400 rounded-full mr-1 animate-pulse\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 21\n                }, this), \"\\u0645\\u062A\\u0627\\u062D \\u0627\\u0644\\u0622\\u0646 \\u0644\\u0644\\u0645\\u0633\\u0627\\u0639\\u062F\\u0629\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 space-x-reverse\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsMinimized(!isMinimized),\n              className: \"p-1 hover:bg-white hover:bg-opacity-20 rounded\",\n              children: /*#__PURE__*/_jsxDEV(MinusIcon, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsOpen(false),\n              className: \"p-1 hover:bg-white hover:bg-opacity-20 rounded\",\n              children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 13\n        }, this), !isMinimized && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-80 overflow-y-auto p-4 space-y-4\",\n            children: [messages.map(message => /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 10\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              className: `flex ${message.type === 'user' ? 'justify-start' : 'justify-end'}`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `\n                        max-w-xs px-4 py-3 rounded-lg text-sm shadow-sm\n                        ${message.type === 'user' ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-br-sm' : 'bg-gradient-to-r from-yellow-50 to-amber-50 text-gray-800 rounded-bl-sm border border-yellow-200/50'}\n                      `,\n                children: message.content\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 23\n              }, this)\n            }, message.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 21\n            }, this)), isTyping && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0\n              },\n              animate: {\n                opacity: 1\n              },\n              className: \"flex justify-end\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-yellow-50 to-amber-50 px-4 py-3 rounded-lg rounded-bl-sm border border-yellow-200/50 shadow-sm\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-1 items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(SparklesIconSolid, {\n                    className: \"w-3 h-3 text-yellow-500 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 327,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-yellow-500 rounded-full animate-bounce\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 328,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-amber-500 rounded-full animate-bounce\",\n                    style: {\n                      animationDelay: '0.1s'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-yellow-600 rounded-full animate-bounce\",\n                    style: {\n                      animationDelay: '0.2s'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-xs text-gray-600 mr-2\",\n                    children: \"\\u064A\\u0643\\u062A\\u0628...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 331,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              ref: messagesEndRef\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 17\n          }, this), messages.length <= 2 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-4 pb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500 mb-2\",\n              children: \"\\u0623\\u0633\\u0626\\u0644\\u0629 \\u0634\\u0627\\u0626\\u0639\\u0629:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-wrap gap-1\",\n              children: quickReplies.slice(0, 3).map(reply => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleQuickReply(reply),\n                className: \"text-xs bg-gradient-to-r from-yellow-100 to-amber-100 hover:from-yellow-200 hover:to-amber-200 text-gray-700 px-3 py-1.5 rounded-full transition-all duration-200 border border-yellow-200/50 hover:border-yellow-300 shadow-sm hover:shadow-md\",\n                children: reply\n              }, reply, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 25\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 border-t border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 space-x-reverse\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: inputMessage,\n                onChange: e => setInputMessage(e.target.value),\n                onKeyPress: handleKeyPress,\n                placeholder: \"\\u0627\\u0643\\u062A\\u0628 \\u0631\\u0633\\u0627\\u0644\\u062A\\u0643...\",\n                className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm\",\n                disabled: isTyping\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                onClick: handleSendMessage,\n                disabled: !inputMessage.trim() || isTyping,\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                className: \"p-2.5 bg-gradient-to-r from-yellow-500 to-amber-600 text-white rounded-lg hover:from-yellow-600 hover:to-amber-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl disabled:hover:scale-100\",\n                children: /*#__PURE__*/_jsxDEV(PaperAirplaneIcon, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(AIAssistant, \"czjJEpDcIy5UgZYwWPaaoufKVMM=\");\n_c = AIAssistant;\nexport default AIAssistant;\nvar _c;\n$RefreshReg$(_c, \"AIAssistant\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "motion", "AnimatePresence", "XMarkIcon", "PaperAirplaneIcon", "MinusIcon", "StarIcon", "SparklesIcon", "SparklesIconSolid", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AIAssistant", "context", "_s", "isOpen", "setIsOpen", "isMinimized", "setIsMinimized", "getWelcomeMessage", "messages", "setMessages", "id", "type", "content", "timestamp", "Date", "inputMessage", "setInputMessage", "isTyping", "setIsTyping", "messagesEndRef", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "quickReplies", "getAIResponse", "userMessage", "message", "toLowerCase", "includes", "handleSendMessage", "trim", "now", "toString", "prev", "setTimeout", "aiResponse", "Math", "random", "handleQuickReply", "reply", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "children", "div", "initial", "scale", "opacity", "animate", "exit", "className", "button", "whileHover", "rotate", "whileTap", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "transition", "duration", "repeat", "Infinity", "ease", "delay", "x", "y", "height", "style", "animationDelay", "map", "ref", "length", "slice", "value", "onChange", "target", "onKeyPress", "placeholder", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/AIAssistant/AIAssistant.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  ChatBubbleLeftRightIcon,\n  XMarkIcon,\n  PaperAirplaneIcon,\n  SparklesIcon,\n  MinusIcon,\n  StarIcon\n} from '@heroicons/react/24/outline';\nimport { SparklesIcon as SparklesIconSolid } from '@heroicons/react/24/solid';\n\n// Types\nimport { ChatMessage } from '../../types';\n\ninterface AIAssistantProps {\n  context?: 'login' | 'student' | 'admin';\n}\n\nconst AIAssistant: React.FC<AIAssistantProps> = ({ context = 'student' }) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [isMinimized, setIsMinimized] = useState(false);\n\n  const getWelcomeMessage = () => {\n    switch (context) {\n      case 'login':\n        return 'مرحباً! أنا المساعد الذكي لمنصة ALaa Abd Hamied. هل تحتاج مساعدة في تسجيل الدخول أو لديك أسئلة حول المنصة؟';\n      case 'admin':\n        return 'مرحباً أيها المدير! أنا هنا لمساعدتك في إدارة المنصة والإجابة على أي استفسارات تقنية.';\n      default:\n        return 'مرحباً عزيزي الطالب! أنا المساعد الذكي لمنصة ALaa Abd Hamied. كيف يمكنني مساعدتك في رحلتك التعليمية اليوم؟';\n    }\n  };\n\n  const [messages, setMessages] = useState<ChatMessage[]>([\n    {\n      id: '1',\n      type: 'assistant',\n      content: getWelcomeMessage(),\n      timestamp: new Date()\n    }\n  ]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const quickReplies = [\n    'كيف أشاهد الكورسات؟',\n    'كيف أؤدي الاختبارات؟',\n    'كيف أحصل على الشهادة؟',\n    'مشكلة في تشغيل الفيديو',\n    'تغيير كلمة المرور',\n    'التواصل مع الدعم'\n  ];\n\n  const getAIResponse = (userMessage: string): string => {\n    const message = userMessage.toLowerCase();\n    \n    if (message.includes('كورس') || message.includes('مشاهدة')) {\n      return 'لمشاهدة الكورسات، اذهب إلى قسم \"كورساتي\" من القائمة الرئيسية. ستجد جميع الكورسات المتاحة لك مع إمكانية متابعة التقدم.';\n    }\n    \n    if (message.includes('اختبار') || message.includes('امتحان')) {\n      return 'يمكنك الوصول للاختبارات من داخل كل كورس. بعد مشاهدة الفيديوهات المطلوبة، ستظهر لك الاختبارات المتاحة. تأكد من قراءة التعليمات قبل البدء.';\n    }\n    \n    if (message.includes('شهادة')) {\n      return 'للحصول على الشهادة، يجب إكمال جميع فيديوهات الكورس واجتياز الاختبارات بنجاح. ستظهر الشهادة تلقائياً في قسم \"شهاداتي\".';\n    }\n    \n    if (message.includes('فيديو') || message.includes('تشغيل')) {\n      return 'إذا واجهت مشكلة في تشغيل الفيديو، تأكد من اتصالك بالإنترنت وحاول تحديث الصفحة. إذا استمرت المشكلة، جرب متصفح آخر.';\n    }\n    \n    if (message.includes('مرور') || message.includes('كلمة')) {\n      return 'لا يمكن تغيير كلمة المرور للطلاب حيث يتم الدخول بكود الوصول. إذا نسيت الكود، تواصل مع المدير للحصول على كود جديد.';\n    }\n    \n    if (message.includes('دعم') || message.includes('مساعدة') || message.includes('مشكلة')) {\n      return 'يمكنك التواصل مع فريق الدعم من خلال النقر على \"اتصل بنا\" في أسفل الصفحة، أو إرسال رسالة مباشرة للمدير.';\n    }\n    \n    if (message.includes('مرحبا') || message.includes('السلام') || message.includes('أهلا')) {\n      return 'أهلاً وسهلاً بك! أنا هنا لمساعدتك في أي استفسار حول المنصة. ما الذي تحتاج مساعدة فيه؟';\n    }\n    \n    if (message.includes('شكرا') || message.includes('شكراً')) {\n      return 'العفو! أتمنى أن أكون قد ساعدتك. لا تتردد في سؤالي عن أي شيء آخر.';\n    }\n    \n    return 'أعتذر، لم أفهم سؤالك بوضوح. يمكنك تجربة إحدى الأسئلة الشائعة أدناه، أو إعادة صياغة سؤالك بطريقة أخرى.';\n  };\n\n  const handleSendMessage = async () => {\n    if (!inputMessage.trim()) return;\n\n    const userMessage: ChatMessage = {\n      id: Date.now().toString(),\n      type: 'user',\n      content: inputMessage,\n      timestamp: new Date()\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setInputMessage('');\n    setIsTyping(true);\n\n    // Simulate AI thinking time\n    setTimeout(() => {\n      const aiResponse: ChatMessage = {\n        id: (Date.now() + 1).toString(),\n        type: 'assistant',\n        content: getAIResponse(inputMessage),\n        timestamp: new Date()\n      };\n\n      setMessages(prev => [...prev, aiResponse]);\n      setIsTyping(false);\n    }, 1000 + Math.random() * 1000);\n  };\n\n  const handleQuickReply = (reply: string) => {\n    setInputMessage(reply);\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  return (\n    <>\n      {/* Chat Button */}\n      <AnimatePresence>\n        {!isOpen && (\n          <motion.div\n            initial={{ scale: 0, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            exit={{ scale: 0, opacity: 0 }}\n            className=\"fixed bottom-6 left-6 z-50\"\n          >\n            <motion.button\n              whileHover={{ scale: 1.1, rotate: 5 }}\n              whileTap={{ scale: 0.9 }}\n              onClick={() => setIsOpen(true)}\n              className=\"relative w-16 h-16 bg-gradient-to-br from-yellow-400 via-yellow-500 to-amber-600 text-white rounded-full shadow-2xl hover:shadow-yellow-500/25 transition-all duration-300 flex items-center justify-center group overflow-hidden\"\n            >\n              {/* Background glow effect */}\n              <div className=\"absolute inset-0 bg-gradient-to-br from-yellow-300 to-amber-500 rounded-full opacity-0 group-hover:opacity-20 transition-opacity duration-300\" />\n\n              {/* Sparkle animation background */}\n              <div className=\"absolute inset-0 rounded-full\">\n                <motion.div\n                  animate={{ rotate: 360 }}\n                  transition={{ duration: 8, repeat: Infinity, ease: \"linear\" }}\n                  className=\"absolute inset-2 border-2 border-yellow-300/30 rounded-full\"\n                />\n                <motion.div\n                  animate={{ rotate: -360 }}\n                  transition={{ duration: 12, repeat: Infinity, ease: \"linear\" }}\n                  className=\"absolute inset-1 border border-yellow-200/20 rounded-full\"\n                />\n              </div>\n\n              {/* Main icon */}\n              <div className=\"relative z-10 flex items-center justify-center\">\n                <SparklesIconSolid className=\"w-7 h-7 text-white drop-shadow-lg\" />\n              </div>\n\n              {/* Floating sparkles */}\n              <motion.div\n                animate={{\n                  scale: [1, 1.2, 1],\n                  opacity: [0.7, 1, 0.7]\n                }}\n                transition={{\n                  duration: 2,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                }}\n                className=\"absolute -top-1 -right-1 w-3 h-3 bg-yellow-300 rounded-full shadow-lg\"\n              />\n\n              <motion.div\n                animate={{\n                  scale: [1, 1.3, 1],\n                  opacity: [0.5, 1, 0.5]\n                }}\n                transition={{\n                  duration: 2.5,\n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                  delay: 0.5\n                }}\n                className=\"absolute -bottom-1 -left-1 w-2 h-2 bg-amber-300 rounded-full shadow-lg\"\n              />\n\n              <motion.div\n                animate={{\n                  scale: [1, 1.1, 1],\n                  opacity: [0.6, 1, 0.6]\n                }}\n                transition={{\n                  duration: 3,\n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                  delay: 1\n                }}\n                className=\"absolute top-1 -left-2 w-1.5 h-1.5 bg-yellow-200 rounded-full shadow-lg\"\n              />\n\n              {/* Status indicator */}\n              <div className=\"absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full animate-pulse border-2 border-white shadow-lg\" />\n            </motion.button>\n\n            {/* Tooltip */}\n            <motion.div\n              initial={{ opacity: 0, x: -10 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: 1 }}\n              className=\"absolute right-20 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white px-3 py-2 rounded-lg text-sm whitespace-nowrap shadow-lg\"\n            >\n              المساعد الذكي\n              <div className=\"absolute left-0 top-1/2 transform -translate-y-1/2 translate-x-1 w-2 h-2 bg-gray-900 rotate-45\" />\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Chat Window */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ opacity: 0, y: 100, scale: 0.9 }}\n            animate={{ \n              opacity: 1, \n              y: 0, \n              scale: 1,\n              height: isMinimized ? 60 : 500\n            }}\n            exit={{ opacity: 0, y: 100, scale: 0.9 }}\n            className=\"fixed bottom-6 left-6 w-80 bg-white rounded-xl shadow-2xl border border-gray-200 z-50 overflow-hidden\"\n          >\n            {/* Header */}\n            <div className=\"bg-gradient-to-r from-yellow-500 via-amber-500 to-yellow-600 text-white p-4 flex items-center justify-between relative overflow-hidden\">\n              {/* Background pattern */}\n              <div className=\"absolute inset-0 opacity-10\">\n                <div className=\"absolute top-2 left-4 w-2 h-2 bg-white rounded-full animate-pulse\" />\n                <div className=\"absolute top-6 right-8 w-1 h-1 bg-white rounded-full animate-pulse\" style={{ animationDelay: '0.5s' }} />\n                <div className=\"absolute bottom-3 left-12 w-1.5 h-1.5 bg-white rounded-full animate-pulse\" style={{ animationDelay: '1s' }} />\n              </div>\n\n              <div className=\"flex items-center relative z-10\">\n                <div className=\"w-10 h-10 bg-gradient-to-br from-white/30 to-white/10 rounded-full flex items-center justify-center ml-3 backdrop-blur-sm border border-white/20\">\n                  <SparklesIconSolid className=\"w-5 h-5 text-white drop-shadow-lg\" />\n                </div>\n                <div>\n                  <h3 className=\"font-bold text-sm flex items-center\">\n                    المساعد الذكي\n                    <StarIcon className=\"w-3 h-3 mr-1 text-yellow-200\" />\n                  </h3>\n                  <p className=\"text-xs opacity-90 flex items-center\">\n                    <div className=\"w-2 h-2 bg-green-400 rounded-full mr-1 animate-pulse\" />\n                    متاح الآن للمساعدة\n                  </p>\n                </div>\n              </div>\n              <div className=\"flex items-center space-x-2 space-x-reverse\">\n                <button\n                  onClick={() => setIsMinimized(!isMinimized)}\n                  className=\"p-1 hover:bg-white hover:bg-opacity-20 rounded\"\n                >\n                  <MinusIcon className=\"w-4 h-4\" />\n                </button>\n                <button\n                  onClick={() => setIsOpen(false)}\n                  className=\"p-1 hover:bg-white hover:bg-opacity-20 rounded\"\n                >\n                  <XMarkIcon className=\"w-4 h-4\" />\n                </button>\n              </div>\n            </div>\n\n            {/* Chat Content */}\n            {!isMinimized && (\n              <>\n                {/* Messages */}\n                <div className=\"h-80 overflow-y-auto p-4 space-y-4\">\n                  {messages.map((message) => (\n                    <motion.div\n                      key={message.id}\n                      initial={{ opacity: 0, y: 10 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      className={`flex ${message.type === 'user' ? 'justify-start' : 'justify-end'}`}\n                    >\n                      <div className={`\n                        max-w-xs px-4 py-3 rounded-lg text-sm shadow-sm\n                        ${message.type === 'user'\n                          ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-br-sm'\n                          : 'bg-gradient-to-r from-yellow-50 to-amber-50 text-gray-800 rounded-bl-sm border border-yellow-200/50'\n                        }\n                      `}>\n                        {message.content}\n                      </div>\n                    </motion.div>\n                  ))}\n\n                  {/* Typing Indicator */}\n                  {isTyping && (\n                    <motion.div\n                      initial={{ opacity: 0 }}\n                      animate={{ opacity: 1 }}\n                      className=\"flex justify-end\"\n                    >\n                      <div className=\"bg-gradient-to-r from-yellow-50 to-amber-50 px-4 py-3 rounded-lg rounded-bl-sm border border-yellow-200/50 shadow-sm\">\n                        <div className=\"flex space-x-1 items-center\">\n                          <SparklesIconSolid className=\"w-3 h-3 text-yellow-500 mr-2\" />\n                          <div className=\"w-2 h-2 bg-yellow-500 rounded-full animate-bounce\" />\n                          <div className=\"w-2 h-2 bg-amber-500 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }} />\n                          <div className=\"w-2 h-2 bg-yellow-600 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }} />\n                          <span className=\"text-xs text-gray-600 mr-2\">يكتب...</span>\n                        </div>\n                      </div>\n                    </motion.div>\n                  )}\n\n                  <div ref={messagesEndRef} />\n                </div>\n\n                {/* Quick Replies */}\n                {messages.length <= 2 && (\n                  <div className=\"px-4 pb-2\">\n                    <p className=\"text-xs text-gray-500 mb-2\">أسئلة شائعة:</p>\n                    <div className=\"flex flex-wrap gap-1\">\n                      {quickReplies.slice(0, 3).map((reply) => (\n                        <button\n                          key={reply}\n                          onClick={() => handleQuickReply(reply)}\n                          className=\"text-xs bg-gradient-to-r from-yellow-100 to-amber-100 hover:from-yellow-200 hover:to-amber-200 text-gray-700 px-3 py-1.5 rounded-full transition-all duration-200 border border-yellow-200/50 hover:border-yellow-300 shadow-sm hover:shadow-md\"\n                        >\n                          {reply}\n                        </button>\n                      ))}\n                    </div>\n                  </div>\n                )}\n\n                {/* Input */}\n                <div className=\"p-4 border-t border-gray-200\">\n                  <div className=\"flex items-center space-x-2 space-x-reverse\">\n                    <input\n                      type=\"text\"\n                      value={inputMessage}\n                      onChange={(e) => setInputMessage(e.target.value)}\n                      onKeyPress={handleKeyPress}\n                      placeholder=\"اكتب رسالتك...\"\n                      className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm\"\n                      disabled={isTyping}\n                    />\n                    <motion.button\n                      onClick={handleSendMessage}\n                      disabled={!inputMessage.trim() || isTyping}\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      className=\"p-2.5 bg-gradient-to-r from-yellow-500 to-amber-600 text-white rounded-lg hover:from-yellow-600 hover:to-amber-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl disabled:hover:scale-100\"\n                    >\n                      <PaperAirplaneIcon className=\"w-4 h-4\" />\n                    </motion.button>\n                  </div>\n                </div>\n              </>\n            )}\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </>\n  );\n};\n\nexport default AIAssistant;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAEEC,SAAS,EACTC,iBAAiB,EAEjBC,SAAS,EACTC,QAAQ,QACH,6BAA6B;AACpC,SAASC,YAAY,IAAIC,iBAAiB,QAAQ,2BAA2B;;AAE7E;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAOA,MAAMC,WAAuC,GAAGA,CAAC;EAAEC,OAAO,GAAG;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC3E,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMsB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQN,OAAO;MACb,KAAK,OAAO;QACV,OAAO,4GAA4G;MACrH,KAAK,OAAO;QACV,OAAO,uFAAuF;MAChG;QACE,OAAO,4GAA4G;IACvH;EACF,CAAC;EAED,MAAM,CAACO,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAgB,CACtD;IACEyB,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,WAAW;IACjBC,OAAO,EAAEL,iBAAiB,CAAC,CAAC;IAC5BM,SAAS,EAAE,IAAIC,IAAI,CAAC;EACtB,CAAC,CACF,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACgC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAMkC,cAAc,GAAGjC,MAAM,CAAiB,IAAI,CAAC;EAEnD,MAAMkC,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAF,cAAc,CAACG,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAEDrC,SAAS,CAAC,MAAM;IACdiC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACZ,QAAQ,CAAC,CAAC;EAEd,MAAMiB,YAAY,GAAG,CACnB,qBAAqB,EACrB,sBAAsB,EACtB,uBAAuB,EACvB,wBAAwB,EACxB,mBAAmB,EACnB,kBAAkB,CACnB;EAED,MAAMC,aAAa,GAAIC,WAAmB,IAAa;IACrD,MAAMC,OAAO,GAAGD,WAAW,CAACE,WAAW,CAAC,CAAC;IAEzC,IAAID,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAC1D,OAAO,uHAAuH;IAChI;IAEA,IAAIF,OAAO,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAC5D,OAAO,0IAA0I;IACnJ;IAEA,IAAIF,OAAO,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC7B,OAAO,uHAAuH;IAChI;IAEA,IAAIF,OAAO,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC1D,OAAO,mHAAmH;IAC5H;IAEA,IAAIF,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MACxD,OAAO,mHAAmH;IAC5H;IAEA,IAAIF,OAAO,CAACE,QAAQ,CAAC,KAAK,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;MACtF,OAAO,wGAAwG;IACjH;IAEA,IAAIF,OAAO,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MACvF,OAAO,uFAAuF;IAChG;IAEA,IAAIF,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;MACzD,OAAO,kEAAkE;IAC3E;IAEA,OAAO,uGAAuG;EAChH,CAAC;EAED,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAChB,YAAY,CAACiB,IAAI,CAAC,CAAC,EAAE;IAE1B,MAAML,WAAwB,GAAG;MAC/BjB,EAAE,EAAEI,IAAI,CAACmB,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MACzBvB,IAAI,EAAE,MAAM;MACZC,OAAO,EAAEG,YAAY;MACrBF,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC;IAEDL,WAAW,CAAC0B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAER,WAAW,CAAC,CAAC;IAC3CX,eAAe,CAAC,EAAE,CAAC;IACnBE,WAAW,CAAC,IAAI,CAAC;;IAEjB;IACAkB,UAAU,CAAC,MAAM;MACf,MAAMC,UAAuB,GAAG;QAC9B3B,EAAE,EAAE,CAACI,IAAI,CAACmB,GAAG,CAAC,CAAC,GAAG,CAAC,EAAEC,QAAQ,CAAC,CAAC;QAC/BvB,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAEc,aAAa,CAACX,YAAY,CAAC;QACpCF,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC;MAEDL,WAAW,CAAC0B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEE,UAAU,CAAC,CAAC;MAC1CnB,WAAW,CAAC,KAAK,CAAC;IACpB,CAAC,EAAE,IAAI,GAAGoB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC;EACjC,CAAC;EAED,MAAMC,gBAAgB,GAAIC,KAAa,IAAK;IAC1CzB,eAAe,CAACyB,KAAK,CAAC;EACxB,CAAC;EAED,MAAMC,cAAc,GAAIC,CAAsB,IAAK;IACjD,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBf,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAED,oBACElC,OAAA,CAAAE,SAAA;IAAAgD,QAAA,gBAEElD,OAAA,CAACR,eAAe;MAAA0D,QAAA,EACb,CAAC5C,MAAM,iBACNN,OAAA,CAACT,MAAM,CAAC4D,GAAG;QACTC,OAAO,EAAE;UAAEC,KAAK,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAE,CAAE;QAClCC,OAAO,EAAE;UAAEF,KAAK,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAE,CAAE;QAClCE,IAAI,EAAE;UAAEH,KAAK,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAE,CAAE;QAC/BG,SAAS,EAAC,4BAA4B;QAAAP,QAAA,gBAEtClD,OAAA,CAACT,MAAM,CAACmE,MAAM;UACZC,UAAU,EAAE;YAAEN,KAAK,EAAE,GAAG;YAAEO,MAAM,EAAE;UAAE,CAAE;UACtCC,QAAQ,EAAE;YAAER,KAAK,EAAE;UAAI,CAAE;UACzBS,OAAO,EAAEA,CAAA,KAAMvD,SAAS,CAAC,IAAI,CAAE;UAC/BkD,SAAS,EAAC,mOAAmO;UAAAP,QAAA,gBAG7OlD,OAAA;YAAKyD,SAAS,EAAC;UAA+I;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGjKlE,OAAA;YAAKyD,SAAS,EAAC,+BAA+B;YAAAP,QAAA,gBAC5ClD,OAAA,CAACT,MAAM,CAAC4D,GAAG;cACTI,OAAO,EAAE;gBAAEK,MAAM,EAAE;cAAI,CAAE;cACzBO,UAAU,EAAE;gBAAEC,QAAQ,EAAE,CAAC;gBAAEC,MAAM,EAAEC,QAAQ;gBAAEC,IAAI,EAAE;cAAS,CAAE;cAC9Dd,SAAS,EAAC;YAA6D;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC,eACFlE,OAAA,CAACT,MAAM,CAAC4D,GAAG;cACTI,OAAO,EAAE;gBAAEK,MAAM,EAAE,CAAC;cAAI,CAAE;cAC1BO,UAAU,EAAE;gBAAEC,QAAQ,EAAE,EAAE;gBAAEC,MAAM,EAAEC,QAAQ;gBAAEC,IAAI,EAAE;cAAS,CAAE;cAC/Dd,SAAS,EAAC;YAA2D;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNlE,OAAA;YAAKyD,SAAS,EAAC,gDAAgD;YAAAP,QAAA,eAC7DlD,OAAA,CAACF,iBAAiB;cAAC2D,SAAS,EAAC;YAAmC;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC,eAGNlE,OAAA,CAACT,MAAM,CAAC4D,GAAG;YACTI,OAAO,EAAE;cACPF,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;cAClBC,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;YACvB,CAAE;YACFa,UAAU,EAAE;cACVC,QAAQ,EAAE,CAAC;cACXC,MAAM,EAAEC,QAAQ;cAChBC,IAAI,EAAE;YACR,CAAE;YACFd,SAAS,EAAC;UAAuE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF,CAAC,eAEFlE,OAAA,CAACT,MAAM,CAAC4D,GAAG;YACTI,OAAO,EAAE;cACPF,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;cAClBC,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;YACvB,CAAE;YACFa,UAAU,EAAE;cACVC,QAAQ,EAAE,GAAG;cACbC,MAAM,EAAEC,QAAQ;cAChBC,IAAI,EAAE,WAAW;cACjBC,KAAK,EAAE;YACT,CAAE;YACFf,SAAS,EAAC;UAAwE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF,CAAC,eAEFlE,OAAA,CAACT,MAAM,CAAC4D,GAAG;YACTI,OAAO,EAAE;cACPF,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;cAClBC,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;YACvB,CAAE;YACFa,UAAU,EAAE;cACVC,QAAQ,EAAE,CAAC;cACXC,MAAM,EAAEC,QAAQ;cAChBC,IAAI,EAAE,WAAW;cACjBC,KAAK,EAAE;YACT,CAAE;YACFf,SAAS,EAAC;UAAyE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC,eAGFlE,OAAA;YAAKyD,SAAS,EAAC;UAA0G;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/G,CAAC,eAGhBlE,OAAA,CAACT,MAAM,CAAC4D,GAAG;UACTC,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAEmB,CAAC,EAAE,CAAC;UAAG,CAAE;UAChClB,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEmB,CAAC,EAAE;UAAE,CAAE;UAC9BN,UAAU,EAAE;YAAEK,KAAK,EAAE;UAAE,CAAE;UACzBf,SAAS,EAAC,sIAAsI;UAAAP,QAAA,GACjJ,2EAEC,eAAAlD,OAAA;YAAKyD,SAAS,EAAC;UAAgG;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACb;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC,eAGlBlE,OAAA,CAACR,eAAe;MAAA0D,QAAA,EACb5C,MAAM,iBACLN,OAAA,CAACT,MAAM,CAAC4D,GAAG;QACTC,OAAO,EAAE;UAAEE,OAAO,EAAE,CAAC;UAAEoB,CAAC,EAAE,GAAG;UAAErB,KAAK,EAAE;QAAI,CAAE;QAC5CE,OAAO,EAAE;UACPD,OAAO,EAAE,CAAC;UACVoB,CAAC,EAAE,CAAC;UACJrB,KAAK,EAAE,CAAC;UACRsB,MAAM,EAAEnE,WAAW,GAAG,EAAE,GAAG;QAC7B,CAAE;QACFgD,IAAI,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEoB,CAAC,EAAE,GAAG;UAAErB,KAAK,EAAE;QAAI,CAAE;QACzCI,SAAS,EAAC,uGAAuG;QAAAP,QAAA,gBAGjHlD,OAAA;UAAKyD,SAAS,EAAC,wIAAwI;UAAAP,QAAA,gBAErJlD,OAAA;YAAKyD,SAAS,EAAC,6BAA6B;YAAAP,QAAA,gBAC1ClD,OAAA;cAAKyD,SAAS,EAAC;YAAmE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrFlE,OAAA;cAAKyD,SAAS,EAAC,oEAAoE;cAACmB,KAAK,EAAE;gBAAEC,cAAc,EAAE;cAAO;YAAE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzHlE,OAAA;cAAKyD,SAAS,EAAC,2EAA2E;cAACmB,KAAK,EAAE;gBAAEC,cAAc,EAAE;cAAK;YAAE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3H,CAAC,eAENlE,OAAA;YAAKyD,SAAS,EAAC,iCAAiC;YAAAP,QAAA,gBAC9ClD,OAAA;cAAKyD,SAAS,EAAC,kJAAkJ;cAAAP,QAAA,eAC/JlD,OAAA,CAACF,iBAAiB;gBAAC2D,SAAS,EAAC;cAAmC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,eACNlE,OAAA;cAAAkD,QAAA,gBACElD,OAAA;gBAAIyD,SAAS,EAAC,qCAAqC;gBAAAP,QAAA,GAAC,2EAElD,eAAAlD,OAAA,CAACJ,QAAQ;kBAAC6D,SAAS,EAAC;gBAA8B;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC,eACLlE,OAAA;gBAAGyD,SAAS,EAAC,sCAAsC;gBAAAP,QAAA,gBACjDlD,OAAA;kBAAKyD,SAAS,EAAC;gBAAsD;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,sGAE1E;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNlE,OAAA;YAAKyD,SAAS,EAAC,6CAA6C;YAAAP,QAAA,gBAC1DlD,OAAA;cACE8D,OAAO,EAAEA,CAAA,KAAMrD,cAAc,CAAC,CAACD,WAAW,CAAE;cAC5CiD,SAAS,EAAC,gDAAgD;cAAAP,QAAA,eAE1DlD,OAAA,CAACL,SAAS;gBAAC8D,SAAS,EAAC;cAAS;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACTlE,OAAA;cACE8D,OAAO,EAAEA,CAAA,KAAMvD,SAAS,CAAC,KAAK,CAAE;cAChCkD,SAAS,EAAC,gDAAgD;cAAAP,QAAA,eAE1DlD,OAAA,CAACP,SAAS;gBAACgE,SAAS,EAAC;cAAS;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL,CAAC1D,WAAW,iBACXR,OAAA,CAAAE,SAAA;UAAAgD,QAAA,gBAEElD,OAAA;YAAKyD,SAAS,EAAC,oCAAoC;YAAAP,QAAA,GAChDvC,QAAQ,CAACmE,GAAG,CAAE/C,OAAO,iBACpB/B,OAAA,CAACT,MAAM,CAAC4D,GAAG;cAETC,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEoB,CAAC,EAAE;cAAG,CAAE;cAC/BnB,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEoB,CAAC,EAAE;cAAE,CAAE;cAC9BjB,SAAS,EAAE,QAAQ1B,OAAO,CAACjB,IAAI,KAAK,MAAM,GAAG,eAAe,GAAG,aAAa,EAAG;cAAAoC,QAAA,eAE/ElD,OAAA;gBAAKyD,SAAS,EAAE;AACtC;AACA,0BAA0B1B,OAAO,CAACjB,IAAI,KAAK,MAAM,GACrB,qEAAqE,GACrE,qGAAqG;AACjI,uBACwB;gBAAAoC,QAAA,EACCnB,OAAO,CAAChB;cAAO;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC,GAbDnC,OAAO,CAAClB,EAAE;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAcL,CACb,CAAC,EAGD9C,QAAQ,iBACPpB,OAAA,CAACT,MAAM,CAAC4D,GAAG;cACTC,OAAO,EAAE;gBAAEE,OAAO,EAAE;cAAE,CAAE;cACxBC,OAAO,EAAE;gBAAED,OAAO,EAAE;cAAE,CAAE;cACxBG,SAAS,EAAC,kBAAkB;cAAAP,QAAA,eAE5BlD,OAAA;gBAAKyD,SAAS,EAAC,sHAAsH;gBAAAP,QAAA,eACnIlD,OAAA;kBAAKyD,SAAS,EAAC,6BAA6B;kBAAAP,QAAA,gBAC1ClD,OAAA,CAACF,iBAAiB;oBAAC2D,SAAS,EAAC;kBAA8B;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9DlE,OAAA;oBAAKyD,SAAS,EAAC;kBAAmD;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACrElE,OAAA;oBAAKyD,SAAS,EAAC,kDAAkD;oBAACmB,KAAK,EAAE;sBAAEC,cAAc,EAAE;oBAAO;kBAAE;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvGlE,OAAA;oBAAKyD,SAAS,EAAC,mDAAmD;oBAACmB,KAAK,EAAE;sBAAEC,cAAc,EAAE;oBAAO;kBAAE;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACxGlE,OAAA;oBAAMyD,SAAS,EAAC,4BAA4B;oBAAAP,QAAA,EAAC;kBAAO;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,eAEDlE,OAAA;cAAK+E,GAAG,EAAEzD;YAAe;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,EAGLvD,QAAQ,CAACqE,MAAM,IAAI,CAAC,iBACnBhF,OAAA;YAAKyD,SAAS,EAAC,WAAW;YAAAP,QAAA,gBACxBlD,OAAA;cAAGyD,SAAS,EAAC,4BAA4B;cAAAP,QAAA,EAAC;YAAY;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC1DlE,OAAA;cAAKyD,SAAS,EAAC,sBAAsB;cAAAP,QAAA,EAClCtB,YAAY,CAACqD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACH,GAAG,CAAElC,KAAK,iBAClC5C,OAAA;gBAEE8D,OAAO,EAAEA,CAAA,KAAMnB,gBAAgB,CAACC,KAAK,CAAE;gBACvCa,SAAS,EAAC,iPAAiP;gBAAAP,QAAA,EAE1PN;cAAK,GAJDA,KAAK;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKJ,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDlE,OAAA;YAAKyD,SAAS,EAAC,8BAA8B;YAAAP,QAAA,eAC3ClD,OAAA;cAAKyD,SAAS,EAAC,6CAA6C;cAAAP,QAAA,gBAC1DlD,OAAA;gBACEc,IAAI,EAAC,MAAM;gBACXoE,KAAK,EAAEhE,YAAa;gBACpBiE,QAAQ,EAAGrC,CAAC,IAAK3B,eAAe,CAAC2B,CAAC,CAACsC,MAAM,CAACF,KAAK,CAAE;gBACjDG,UAAU,EAAExC,cAAe;gBAC3ByC,WAAW,EAAC,kEAAgB;gBAC5B7B,SAAS,EAAC,yHAAyH;gBACnI8B,QAAQ,EAAEnE;cAAS;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACFlE,OAAA,CAACT,MAAM,CAACmE,MAAM;gBACZI,OAAO,EAAE5B,iBAAkB;gBAC3BqD,QAAQ,EAAE,CAACrE,YAAY,CAACiB,IAAI,CAAC,CAAC,IAAIf,QAAS;gBAC3CuC,UAAU,EAAE;kBAAEN,KAAK,EAAE;gBAAK,CAAE;gBAC5BQ,QAAQ,EAAE;kBAAER,KAAK,EAAE;gBAAK,CAAE;gBAC1BI,SAAS,EAAC,mPAAmP;gBAAAP,QAAA,eAE7PlD,OAAA,CAACN,iBAAiB;kBAAC+D,SAAS,EAAC;gBAAS;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,eACN,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS;IACb;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC;EAAA,eAClB,CAAC;AAEP,CAAC;AAAC7D,EAAA,CAhXIF,WAAuC;AAAAqF,EAAA,GAAvCrF,WAAuC;AAkX7C,eAAeA,WAAW;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}