{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const LoadingSpinner=_ref=>{let{size='md',color='primary',text='جاري التحميل...'}=_ref;const sizeClasses={sm:'w-4 h-4',md:'w-8 h-8',lg:'w-12 h-12'};const colorClasses={primary:'border-primary-600',white:'border-white',gray:'border-gray-600'};return/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col items-center justify-center min-h-screen\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:`\n            ${sizeClasses[size]} \n            border-4 \n            ${colorClasses[color]} \n            border-t-transparent \n            rounded-full \n            animate-spin\n          `}),/*#__PURE__*/_jsx(\"div\",{className:`\n            absolute \n            inset-0 \n            ${sizeClasses[size]} \n            border-4 \n            border-transparent \n            border-t-${color==='primary'?'primary-300':color==='white'?'gray-300':'gray-400'} \n            rounded-full \n            animate-spin \n            animation-delay-150\n          `})]}),text&&/*#__PURE__*/_jsx(\"p\",{className:`\n          mt-4 \n          text-sm \n          font-medium \n          ${color==='white'?'text-white':'text-gray-600'}\n          animate-pulse\n        `,children:text})]});};export default LoadingSpinner;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "LoadingSpinner", "_ref", "size", "color", "text", "sizeClasses", "sm", "md", "lg", "colorClasses", "primary", "white", "gray", "className", "children"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/common/LoadingSpinner.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  color?: 'primary' | 'white' | 'gray';\n  text?: string;\n}\n\nconst LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ \n  size = 'md', \n  color = 'primary',\n  text = 'جاري التحميل...'\n}) => {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12'\n  };\n\n  const colorClasses = {\n    primary: 'border-primary-600',\n    white: 'border-white',\n    gray: 'border-gray-600'\n  };\n\n  return (\n    <div className=\"flex flex-col items-center justify-center min-h-screen\">\n      <div className=\"relative\">\n        <div \n          className={`\n            ${sizeClasses[size]} \n            border-4 \n            ${colorClasses[color]} \n            border-t-transparent \n            rounded-full \n            animate-spin\n          `}\n        />\n        <div \n          className={`\n            absolute \n            inset-0 \n            ${sizeClasses[size]} \n            border-4 \n            border-transparent \n            border-t-${color === 'primary' ? 'primary-300' : color === 'white' ? 'gray-300' : 'gray-400'} \n            rounded-full \n            animate-spin \n            animation-delay-150\n          `}\n        />\n      </div>\n      \n      {text && (\n        <p className={`\n          mt-4 \n          text-sm \n          font-medium \n          ${color === 'white' ? 'text-white' : 'text-gray-600'}\n          animate-pulse\n        `}>\n          {text}\n        </p>\n      )}\n    </div>\n  );\n};\n\nexport default LoadingSpinner;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAQ1B,KAAM,CAAAC,cAA6C,CAAGC,IAAA,EAIhD,IAJiD,CACrDC,IAAI,CAAG,IAAI,CACXC,KAAK,CAAG,SAAS,CACjBC,IAAI,CAAG,iBACT,CAAC,CAAAH,IAAA,CACC,KAAM,CAAAI,WAAW,CAAG,CAClBC,EAAE,CAAE,SAAS,CACbC,EAAE,CAAE,SAAS,CACbC,EAAE,CAAE,WACN,CAAC,CAED,KAAM,CAAAC,YAAY,CAAG,CACnBC,OAAO,CAAE,oBAAoB,CAC7BC,KAAK,CAAE,cAAc,CACrBC,IAAI,CAAE,iBACR,CAAC,CAED,mBACEb,KAAA,QAAKc,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrEf,KAAA,QAAKc,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBjB,IAAA,QACEgB,SAAS,CAAE;AACrB,cAAcR,WAAW,CAACH,IAAI,CAAC;AAC/B;AACA,cAAcO,YAAY,CAACN,KAAK,CAAC;AACjC;AACA;AACA;AACA,WAAY,CACH,CAAC,cACFN,IAAA,QACEgB,SAAS,CAAE;AACrB;AACA;AACA,cAAcR,WAAW,CAACH,IAAI,CAAC;AAC/B;AACA;AACA,uBAAuBC,KAAK,GAAK,SAAS,CAAG,aAAa,CAAGA,KAAK,GAAK,OAAO,CAAG,UAAU,CAAG,UAAU;AACxG;AACA;AACA;AACA,WAAY,CACH,CAAC,EACC,CAAC,CAELC,IAAI,eACHP,IAAA,MAAGgB,SAAS,CAAE;AACtB;AACA;AACA;AACA,YAAYV,KAAK,GAAK,OAAO,CAAG,YAAY,CAAG,eAAe;AAC9D;AACA,SAAU,CAAAW,QAAA,CACCV,IAAI,CACJ,CACJ,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}