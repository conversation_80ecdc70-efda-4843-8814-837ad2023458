{"ast": null, "code": "export const mockCourses=[{id:'1',title:'أساسيات البرمجة',description:'تعلم أساسيات البرمجة من الصفر حتى الاحتراف',instructor:'أ. علاء عبد الحميد',duration:'40 ساعة',level:'مبتدئ',price:299,originalPrice:399,image:'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=250&fit=crop',category:'البرمجة',rating:4.8,studentsCount:1250,lessonsCount:25,isPublished:true,createdAt:new Date('2024-01-01'),updatedAt:new Date('2024-01-15')},{id:'2',title:'تطوير المواقع الحديثة',description:'تعلم تطوير المواقع باستخدام أحدث التقنيات',instructor:'أ. علاء عبد الحميد',duration:'60 ساعة',level:'متوسط',price:499,originalPrice:699,image:'https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=400&h=250&fit=crop',category:'تطوير الويب',rating:4.9,studentsCount:890,lessonsCount:35,isPublished:true,createdAt:new Date('2024-01-10'),updatedAt:new Date('2024-01-20')},{id:'3',title:'الذكاء الاصطناعي للمبتدئين',description:'مقدمة شاملة في عالم الذكاء الاصطناعي',instructor:'أ. علاء عبد الحميد',duration:'50 ساعة',level:'متوسط',price:599,originalPrice:799,image:'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=400&h=250&fit=crop',category:'الذكاء الاصطناعي',rating:4.7,studentsCount:650,lessonsCount:30,isPublished:true,createdAt:new Date('2024-01-15'),updatedAt:new Date('2024-01-25')}];export const mockVideos=[{id:'1',title:'مقدمة في البرمجة',description:'تعرف على أساسيات البرمجة',url:'https://www.youtube.com/watch?v=dQw4w9WgXcQ',duration:'15:30',courseId:'1',order:1,isWatched:false},{id:'2',title:'المتغيرات والثوابت',description:'تعلم كيفية استخدام المتغيرات',url:'https://www.youtube.com/watch?v=dQw4w9WgXcQ',duration:'20:45',courseId:'1',order:2,isWatched:false}];export const mockQuizzes=[{id:'1',title:'اختبار أساسيات البرمجة',description:'اختبر معرفتك في أساسيات البرمجة',courseId:'1',questions:[{id:'1',question:'ما هو المتغير في البرمجة؟',options:['مكان لتخزين البيانات','نوع من الدوال','أمر للطباعة','لا شيء مما سبق'],correctAnswer:0,explanation:'المتغير هو مكان في الذاكرة لتخزين البيانات'}],timeLimit:30,passingScore:70,createdAt:new Date('2024-01-01')}];export const mockCertificates=[{id:'cert-001',studentId:'student-001',courseId:'1',courseName:'أساسيات البرمجة',studentName:'أحمد محمد',issueDate:new Date('2024-02-01'),certificateUrl:'https://example.com/certificate/cert-001.pdf'}];", "map": {"version": 3, "names": ["mockCourses", "id", "title", "description", "instructor", "duration", "level", "price", "originalPrice", "image", "category", "rating", "studentsCount", "lessonsCount", "isPublished", "createdAt", "Date", "updatedAt", "mockVideos", "url", "courseId", "order", "isWatched", "mockQuizzes", "questions", "question", "options", "<PERSON><PERSON><PERSON><PERSON>", "explanation", "timeLimit", "passingScore", "mockCertificates", "studentId", "courseName", "studentName", "issueDate", "certificateUrl"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/data/mockCourses.ts"], "sourcesContent": ["import { Course, Video, Quiz, Certificate } from '../types';\n\nexport const mockCourses: Course[] = [\n  {\n    id: '1',\n    title: 'أساسيات البرمجة',\n    description: 'تعلم أساسيات البرمجة من الصفر حتى الاحتراف',\n    instructor: 'أ. علاء عبد الحميد',\n    duration: '40 ساعة',\n    level: 'مبتدئ',\n    price: 299,\n    originalPrice: 399,\n    image: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=250&fit=crop',\n    category: 'البرمجة',\n    rating: 4.8,\n    studentsCount: 1250,\n    lessonsCount: 25,\n    isPublished: true,\n    createdAt: new Date('2024-01-01'),\n    updatedAt: new Date('2024-01-15')\n  },\n  {\n    id: '2',\n    title: 'تطوير المواقع الحديثة',\n    description: 'تعلم تطوير المواقع باستخدام أحدث التقنيات',\n    instructor: 'أ. علاء عبد الحميد',\n    duration: '60 ساعة',\n    level: 'متوسط',\n    price: 499,\n    originalPrice: 699,\n    image: 'https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=400&h=250&fit=crop',\n    category: 'تطوير الويب',\n    rating: 4.9,\n    studentsCount: 890,\n    lessonsCount: 35,\n    isPublished: true,\n    createdAt: new Date('2024-01-10'),\n    updatedAt: new Date('2024-01-20')\n  },\n  {\n    id: '3',\n    title: 'الذكاء الاصطناعي للمبتدئين',\n    description: 'مقدمة شاملة في عالم الذكاء الاصطناعي',\n    instructor: 'أ. علاء عبد الحميد',\n    duration: '50 ساعة',\n    level: 'متوسط',\n    price: 599,\n    originalPrice: 799,\n    image: 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=400&h=250&fit=crop',\n    category: 'الذكاء الاصطناعي',\n    rating: 4.7,\n    studentsCount: 650,\n    lessonsCount: 30,\n    isPublished: true,\n    createdAt: new Date('2024-01-15'),\n    updatedAt: new Date('2024-01-25')\n  }\n];\n\nexport const mockVideos: Video[] = [\n  {\n    id: '1',\n    title: 'مقدمة في البرمجة',\n    description: 'تعرف على أساسيات البرمجة',\n    url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',\n    duration: '15:30',\n    courseId: '1',\n    order: 1,\n    isWatched: false\n  },\n  {\n    id: '2',\n    title: 'المتغيرات والثوابت',\n    description: 'تعلم كيفية استخدام المتغيرات',\n    url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',\n    duration: '20:45',\n    courseId: '1',\n    order: 2,\n    isWatched: false\n  }\n];\n\nexport const mockQuizzes: Quiz[] = [\n  {\n    id: '1',\n    title: 'اختبار أساسيات البرمجة',\n    description: 'اختبر معرفتك في أساسيات البرمجة',\n    courseId: '1',\n    questions: [\n      {\n        id: '1',\n        question: 'ما هو المتغير في البرمجة؟',\n        options: [\n          'مكان لتخزين البيانات',\n          'نوع من الدوال',\n          'أمر للطباعة',\n          'لا شيء مما سبق'\n        ],\n        correctAnswer: 0,\n        explanation: 'المتغير هو مكان في الذاكرة لتخزين البيانات'\n      }\n    ],\n    timeLimit: 30,\n    passingScore: 70,\n    createdAt: new Date('2024-01-01')\n  }\n];\n\nexport const mockCertificates: Certificate[] = [\n  {\n    id: 'cert-001',\n    studentId: 'student-001',\n    courseId: '1',\n    courseName: 'أساسيات البرمجة',\n    studentName: 'أحمد محمد',\n    issueDate: new Date('2024-02-01'),\n    certificateUrl: 'https://example.com/certificate/cert-001.pdf'\n  }\n];\n"], "mappings": "AAEA,MAAO,MAAM,CAAAA,WAAqB,CAAG,CACnC,CACEC,EAAE,CAAE,GAAG,CACPC,KAAK,CAAE,iBAAiB,CACxBC,WAAW,CAAE,4CAA4C,CACzDC,UAAU,CAAE,oBAAoB,CAChCC,QAAQ,CAAE,SAAS,CACnBC,KAAK,CAAE,OAAO,CACdC,KAAK,CAAE,GAAG,CACVC,aAAa,CAAE,GAAG,CAClBC,KAAK,CAAE,mFAAmF,CAC1FC,QAAQ,CAAE,SAAS,CACnBC,MAAM,CAAE,GAAG,CACXC,aAAa,CAAE,IAAI,CACnBC,YAAY,CAAE,EAAE,CAChBC,WAAW,CAAE,IAAI,CACjBC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAAC,CACjCC,SAAS,CAAE,GAAI,CAAAD,IAAI,CAAC,YAAY,CAClC,CAAC,CACD,CACEf,EAAE,CAAE,GAAG,CACPC,KAAK,CAAE,uBAAuB,CAC9BC,WAAW,CAAE,2CAA2C,CACxDC,UAAU,CAAE,oBAAoB,CAChCC,QAAQ,CAAE,SAAS,CACnBC,KAAK,CAAE,OAAO,CACdC,KAAK,CAAE,GAAG,CACVC,aAAa,CAAE,GAAG,CAClBC,KAAK,CAAE,mFAAmF,CAC1FC,QAAQ,CAAE,aAAa,CACvBC,MAAM,CAAE,GAAG,CACXC,aAAa,CAAE,GAAG,CAClBC,YAAY,CAAE,EAAE,CAChBC,WAAW,CAAE,IAAI,CACjBC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAAC,CACjCC,SAAS,CAAE,GAAI,CAAAD,IAAI,CAAC,YAAY,CAClC,CAAC,CACD,CACEf,EAAE,CAAE,GAAG,CACPC,KAAK,CAAE,4BAA4B,CACnCC,WAAW,CAAE,sCAAsC,CACnDC,UAAU,CAAE,oBAAoB,CAChCC,QAAQ,CAAE,SAAS,CACnBC,KAAK,CAAE,OAAO,CACdC,KAAK,CAAE,GAAG,CACVC,aAAa,CAAE,GAAG,CAClBC,KAAK,CAAE,mFAAmF,CAC1FC,QAAQ,CAAE,kBAAkB,CAC5BC,MAAM,CAAE,GAAG,CACXC,aAAa,CAAE,GAAG,CAClBC,YAAY,CAAE,EAAE,CAChBC,WAAW,CAAE,IAAI,CACjBC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAAC,CACjCC,SAAS,CAAE,GAAI,CAAAD,IAAI,CAAC,YAAY,CAClC,CAAC,CACF,CAED,MAAO,MAAM,CAAAE,UAAmB,CAAG,CACjC,CACEjB,EAAE,CAAE,GAAG,CACPC,KAAK,CAAE,kBAAkB,CACzBC,WAAW,CAAE,0BAA0B,CACvCgB,GAAG,CAAE,6CAA6C,CAClDd,QAAQ,CAAE,OAAO,CACjBe,QAAQ,CAAE,GAAG,CACbC,KAAK,CAAE,CAAC,CACRC,SAAS,CAAE,KACb,CAAC,CACD,CACErB,EAAE,CAAE,GAAG,CACPC,KAAK,CAAE,oBAAoB,CAC3BC,WAAW,CAAE,8BAA8B,CAC3CgB,GAAG,CAAE,6CAA6C,CAClDd,QAAQ,CAAE,OAAO,CACjBe,QAAQ,CAAE,GAAG,CACbC,KAAK,CAAE,CAAC,CACRC,SAAS,CAAE,KACb,CAAC,CACF,CAED,MAAO,MAAM,CAAAC,WAAmB,CAAG,CACjC,CACEtB,EAAE,CAAE,GAAG,CACPC,KAAK,CAAE,wBAAwB,CAC/BC,WAAW,CAAE,iCAAiC,CAC9CiB,QAAQ,CAAE,GAAG,CACbI,SAAS,CAAE,CACT,CACEvB,EAAE,CAAE,GAAG,CACPwB,QAAQ,CAAE,2BAA2B,CACrCC,OAAO,CAAE,CACP,sBAAsB,CACtB,eAAe,CACf,aAAa,CACb,gBAAgB,CACjB,CACDC,aAAa,CAAE,CAAC,CAChBC,WAAW,CAAE,4CACf,CAAC,CACF,CACDC,SAAS,CAAE,EAAE,CACbC,YAAY,CAAE,EAAE,CAChBf,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAClC,CAAC,CACF,CAED,MAAO,MAAM,CAAAe,gBAA+B,CAAG,CAC7C,CACE9B,EAAE,CAAE,UAAU,CACd+B,SAAS,CAAE,aAAa,CACxBZ,QAAQ,CAAE,GAAG,CACba,UAAU,CAAE,iBAAiB,CAC7BC,WAAW,CAAE,WAAW,CACxBC,SAAS,CAAE,GAAI,CAAAnB,IAAI,CAAC,YAAY,CAAC,CACjCoB,cAAc,CAAE,8CAClB,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}