{"ast": null, "code": "import { frame, cancelFrame, frameData } from '../../../frameloop/frame.mjs';\nconst frameloopDriver = update => {\n  const passTimestamp = _ref => {\n    let {\n      timestamp\n    } = _ref;\n    return update(timestamp);\n  };\n  return {\n    start: () => frame.update(passTimestamp, true),\n    stop: () => cancelFrame(passTimestamp),\n    /**\n     * If we're processing this frame we can use the\n     * framelocked timestamp to keep things in sync.\n     */\n    now: () => frameData.isProcessing ? frameData.timestamp : performance.now()\n  };\n};\nexport { frameloopDriver };", "map": {"version": 3, "names": ["frame", "cancelFrame", "frameData", "frameloopDriver", "update", "passTimestamp", "_ref", "timestamp", "start", "stop", "now", "isProcessing", "performance"], "sources": ["C:/Users/<USER>/Desktop/مشروع/node_modules/framer-motion/dist/es/animation/animators/js/driver-frameloop.mjs"], "sourcesContent": ["import { frame, cancelFrame, frameData } from '../../../frameloop/frame.mjs';\n\nconst frameloopDriver = (update) => {\n    const passTimestamp = ({ timestamp }) => update(timestamp);\n    return {\n        start: () => frame.update(passTimestamp, true),\n        stop: () => cancelFrame(passTimestamp),\n        /**\n         * If we're processing this frame we can use the\n         * framelocked timestamp to keep things in sync.\n         */\n        now: () => frameData.isProcessing ? frameData.timestamp : performance.now(),\n    };\n};\n\nexport { frameloopDriver };\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,WAAW,EAAEC,SAAS,QAAQ,8BAA8B;AAE5E,MAAMC,eAAe,GAAIC,MAAM,IAAK;EAChC,MAAMC,aAAa,GAAGC,IAAA;IAAA,IAAC;MAAEC;IAAU,CAAC,GAAAD,IAAA;IAAA,OAAKF,MAAM,CAACG,SAAS,CAAC;EAAA;EAC1D,OAAO;IACHC,KAAK,EAAEA,CAAA,KAAMR,KAAK,CAACI,MAAM,CAACC,aAAa,EAAE,IAAI,CAAC;IAC9CI,IAAI,EAAEA,CAAA,KAAMR,WAAW,CAACI,aAAa,CAAC;IACtC;AACR;AACA;AACA;IACQK,GAAG,EAAEA,CAAA,KAAMR,SAAS,CAACS,YAAY,GAAGT,SAAS,CAACK,SAAS,GAAGK,WAAW,CAACF,GAAG,CAAC;EAC9E,CAAC;AACL,CAAC;AAED,SAASP,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}