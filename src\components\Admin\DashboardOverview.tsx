import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  UsersIcon,
  AcademicCapIcon,
  FolderIcon,
  ClipboardDocumentListIcon,
  ChartBarIcon,
  TrendingUpIcon,
  EyeIcon,
  PlayIcon
} from '@heroicons/react/24/outline';

// Components
import StatsCard from './StatsCard';
import RecentActivity from './RecentActivity';
import QuickActions from './QuickActions';

const DashboardOverview: React.FC = () => {
  const [stats, setStats] = useState({
    totalStudents: 0,
    totalCourses: 0,
    totalCategories: 0,
    totalQuizzes: 0,
    activeStudents: 0,
    completedCourses: 0,
    totalViews: 0,
    totalWatchTime: 0
  });

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate loading stats
    const loadStats = async () => {
      // In real app, fetch from Firebase/Supabase
      setTimeout(() => {
        setStats({
          totalStudents: 156,
          totalCourses: 24,
          totalCategories: 8,
          totalQuizzes: 45,
          activeStudents: 89,
          completedCourses: 234,
          totalViews: 1250,
          totalWatchTime: 4580 // in minutes
        });
        setLoading(false);
      }, 1000);
    };

    loadStats();
  }, []);

  const statsCards = [
    {
      title: 'إجمالي الطلاب',
      value: stats.totalStudents,
      change: '+12%',
      changeType: 'increase' as const,
      icon: UsersIcon,
      color: 'blue'
    },
    {
      title: 'الكورسات المتاحة',
      value: stats.totalCourses,
      change: '+3',
      changeType: 'increase' as const,
      icon: AcademicCapIcon,
      color: 'green'
    },
    {
      title: 'الأقسام',
      value: stats.totalCategories,
      change: '+1',
      changeType: 'increase' as const,
      icon: FolderIcon,
      color: 'purple'
    },
    {
      title: 'الاختبارات',
      value: stats.totalQuizzes,
      change: '+8',
      changeType: 'increase' as const,
      icon: ClipboardDocumentListIcon,
      color: 'orange'
    }
  ];

  const performanceCards = [
    {
      title: 'الطلاب النشطون',
      value: stats.activeStudents,
      subtitle: 'في آخر 30 يوم',
      icon: TrendingUpIcon,
      color: 'blue'
    },
    {
      title: 'الكورسات المكتملة',
      value: stats.completedCourses,
      subtitle: 'إجمالي الإنجازات',
      icon: ChartBarIcon,
      color: 'green'
    },
    {
      title: 'إجمالي المشاهدات',
      value: stats.totalViews,
      subtitle: 'مشاهدة فيديو',
      icon: EyeIcon,
      color: 'purple'
    },
    {
      title: 'وقت المشاهدة',
      value: `${Math.floor(stats.totalWatchTime / 60)}س ${stats.totalWatchTime % 60}د`,
      subtitle: 'إجمالي الوقت',
      icon: PlayIcon,
      color: 'orange'
    }
  ];

  if (loading) {
    return (
      <div className="space-y-6">
        {/* Loading Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white rounded-xl p-6 shadow-sm animate-pulse">
              <div className="flex items-center justify-between">
                <div>
                  <div className="h-4 bg-gray-200 rounded w-20 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-16"></div>
                </div>
                <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-l from-primary-600 to-primary-700 rounded-xl p-6 text-white"
      >
        <h1 className="text-2xl font-bold mb-2">مرحباً بك في لوحة التحكم</h1>
        <p className="text-primary-100">
          إدارة شاملة لمنصة ALaa Abd Hamied للكورسات الإلكترونية
        </p>
      </motion.div>

      {/* Main Stats */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
      >
        {statsCards.map((stat, index) => (
          <StatsCard
            key={stat.title}
            {...stat}
            delay={index * 0.1}
          />
        ))}
      </motion.div>

      {/* Performance Metrics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <h2 className="text-xl font-bold text-gray-900 mb-4">مؤشرات الأداء</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {performanceCards.map((card, index) => (
            <motion.div
              key={card.title}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.3 + index * 0.1 }}
              className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{card.title}</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">
                    {typeof card.value === 'number' ? card.value.toLocaleString() : card.value}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">{card.subtitle}</p>
                </div>
                <div className={`
                  w-12 h-12 rounded-lg flex items-center justify-center
                  ${card.color === 'blue' ? 'bg-blue-100' : ''}
                  ${card.color === 'green' ? 'bg-green-100' : ''}
                  ${card.color === 'purple' ? 'bg-purple-100' : ''}
                  ${card.color === 'orange' ? 'bg-orange-100' : ''}
                `}>
                  <card.icon className={`
                    w-6 h-6
                    ${card.color === 'blue' ? 'text-blue-600' : ''}
                    ${card.color === 'green' ? 'text-green-600' : ''}
                    ${card.color === 'purple' ? 'text-purple-600' : ''}
                    ${card.color === 'orange' ? 'text-orange-600' : ''}
                  `} />
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Quick Actions & Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4 }}
        >
          <QuickActions />
        </motion.div>
        
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.5 }}
        >
          <RecentActivity />
        </motion.div>
      </div>
    </div>
  );
};

export default DashboardOverview;
