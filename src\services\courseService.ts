import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy,
  serverTimestamp 
} from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { db, storage } from '../config/firebase';
import { Course, Category, Video, PDF, Quiz } from '../types';

class CourseService {
  // Categories
  async getCategories(): Promise<Category[]> {
    try {
      const categoriesRef = collection(db, 'categories');
      const q = query(categoriesRef, where('isActive', '==', true), orderBy('createdAt', 'desc'));
      const snapshot = await getDocs(q);
      
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date()
      })) as Category[];
    } catch (error) {
      throw new Error('فشل في جلب الأقسام');
    }
  }

  async createCategory(categoryData: Omit<Category, 'id' | 'createdAt'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, 'categories'), {
        ...categoryData,
        createdAt: serverTimestamp()
      });
      return docRef.id;
    } catch (error) {
      throw new Error('فشل في إنشاء القسم');
    }
  }

  async updateCategory(id: string, categoryData: Partial<Category>): Promise<void> {
    try {
      const categoryRef = doc(db, 'categories', id);
      await updateDoc(categoryRef, {
        ...categoryData,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      throw new Error('فشل في تحديث القسم');
    }
  }

  async deleteCategory(id: string): Promise<void> {
    try {
      await deleteDoc(doc(db, 'categories', id));
    } catch (error) {
      throw new Error('فشل في حذف القسم');
    }
  }

  // Courses
  async getCourses(categoryId?: string): Promise<Course[]> {
    try {
      const coursesRef = collection(db, 'courses');
      let q = query(coursesRef, where('isActive', '==', true), orderBy('createdAt', 'desc'));
      
      if (categoryId) {
        q = query(coursesRef, where('categoryId', '==', categoryId), where('isActive', '==', true));
      }
      
      const snapshot = await getDocs(q);
      
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate() || new Date()
      })) as Course[];
    } catch (error) {
      throw new Error('فشل في جلب الكورسات');
    }
  }

  async getCourse(id: string): Promise<Course | null> {
    try {
      const courseDoc = await getDoc(doc(db, 'courses', id));
      if (!courseDoc.exists()) return null;
      
      return {
        id: courseDoc.id,
        ...courseDoc.data(),
        createdAt: courseDoc.data().createdAt?.toDate() || new Date(),
        updatedAt: courseDoc.data().updatedAt?.toDate() || new Date()
      } as Course;
    } catch (error) {
      throw new Error('فشل في جلب الكورس');
    }
  }

  async createCourse(courseData: Omit<Course, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, 'courses'), {
        ...courseData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
      return docRef.id;
    } catch (error) {
      throw new Error('فشل في إنشاء الكورس');
    }
  }

  async updateCourse(id: string, courseData: Partial<Course>): Promise<void> {
    try {
      const courseRef = doc(db, 'courses', id);
      await updateDoc(courseRef, {
        ...courseData,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      throw new Error('فشل في تحديث الكورس');
    }
  }

  async deleteCourse(id: string): Promise<void> {
    try {
      await deleteDoc(doc(db, 'courses', id));
    } catch (error) {
      throw new Error('فشل في حذف الكورس');
    }
  }

  // Videos
  async uploadVideo(file: File, courseId: string, onProgress?: (progress: number) => void): Promise<string> {
    try {
      const videoRef = ref(storage, `videos/${courseId}/${Date.now()}_${file.name}`);
      const uploadTask = uploadBytes(videoRef, file);
      
      const snapshot = await uploadTask;
      const downloadURL = await getDownloadURL(snapshot.ref);
      
      return downloadURL;
    } catch (error) {
      throw new Error('فشل في رفع الفيديو');
    }
  }

  async addVideoToCourse(courseId: string, videoData: Omit<Video, 'id' | 'createdAt'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, 'videos'), {
        ...videoData,
        courseId,
        createdAt: serverTimestamp()
      });
      return docRef.id;
    } catch (error) {
      throw new Error('فشل في إضافة الفيديو');
    }
  }

  async getCourseVideos(courseId: string): Promise<Video[]> {
    try {
      const videosRef = collection(db, 'videos');
      const q = query(
        videosRef, 
        where('courseId', '==', courseId), 
        where('isActive', '==', true),
        orderBy('orderIndex', 'asc')
      );
      const snapshot = await getDocs(q);
      
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date()
      })) as Video[];
    } catch (error) {
      throw new Error('فشل في جلب فيديوهات الكورس');
    }
  }

  // PDFs
  async uploadPDF(file: File, courseId: string): Promise<string> {
    try {
      const pdfRef = ref(storage, `pdfs/${courseId}/${Date.now()}_${file.name}`);
      const snapshot = await uploadBytes(pdfRef, file);
      const downloadURL = await getDownloadURL(snapshot.ref);
      
      return downloadURL;
    } catch (error) {
      throw new Error('فشل في رفع ملف PDF');
    }
  }

  async addPDFToCourse(courseId: string, pdfData: Omit<PDF, 'id' | 'createdAt'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, 'pdfs'), {
        ...pdfData,
        courseId,
        createdAt: serverTimestamp()
      });
      return docRef.id;
    } catch (error) {
      throw new Error('فشل في إضافة ملف PDF');
    }
  }

  async getCoursePDFs(courseId: string): Promise<PDF[]> {
    try {
      const pdfsRef = collection(db, 'pdfs');
      const q = query(
        pdfsRef, 
        where('courseId', '==', courseId), 
        where('isActive', '==', true),
        orderBy('orderIndex', 'asc')
      );
      const snapshot = await getDocs(q);
      
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date()
      })) as PDF[];
    } catch (error) {
      throw new Error('فشل في جلب ملفات PDF للكورس');
    }
  }

  // Quizzes
  async createQuiz(quizData: Omit<Quiz, 'id' | 'createdAt'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, 'quizzes'), {
        ...quizData,
        createdAt: serverTimestamp()
      });
      return docRef.id;
    } catch (error) {
      throw new Error('فشل في إنشاء الاختبار');
    }
  }

  async getCourseQuizzes(courseId: string): Promise<Quiz[]> {
    try {
      const quizzesRef = collection(db, 'quizzes');
      const q = query(
        quizzesRef, 
        where('courseId', '==', courseId), 
        where('isActive', '==', true),
        orderBy('createdAt', 'desc')
      );
      const snapshot = await getDocs(q);
      
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date()
      })) as Quiz[];
    } catch (error) {
      throw new Error('فشل في جلب اختبارات الكورس');
    }
  }

  async getQuiz(id: string): Promise<Quiz | null> {
    try {
      const quizDoc = await getDoc(doc(db, 'quizzes', id));
      if (!quizDoc.exists()) return null;
      
      return {
        id: quizDoc.id,
        ...quizDoc.data(),
        createdAt: quizDoc.data().createdAt?.toDate() || new Date()
      } as Quiz;
    } catch (error) {
      throw new Error('فشل في جلب الاختبار');
    }
  }
}

export const courseService = new CourseService();
