{"ast": null, "code": "import React from'react';import{motion}from'framer-motion';import{UserPlusIcon,AcademicCapIcon,ClipboardDocumentCheckIcon,DocumentTextIcon,EyeIcon}from'@heroicons/react/24/outline';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const RecentActivity=()=>{const activities=[{id:'1',type:'student_joined',title:'طالب جديد',description:'انضم أحمد محمد إلى المنصة',time:'منذ 5 دقائق',user:'أحمد محمد'},{id:'2',type:'course_completed',title:'كورس مكتمل',description:'أكملت سارة أحمد كورس \"أساسيات البرمجة\"',time:'منذ 15 دقيقة',user:'سارة أحمد'},{id:'3',type:'quiz_taken',title:'اختبار جديد',description:'أدى محمد علي اختبار JavaScript',time:'منذ 30 دقيقة',user:'محمد علي'},{id:'4',type:'certificate_issued',title:'شهادة جديدة',description:'تم إصدار شهادة لفاطمة حسن',time:'منذ ساعة',user:'فاطمة حسن'},{id:'5',type:'video_watched',title:'مشاهدة فيديو',description:'شاهد خالد أحمد فيديو \"المتغيرات في JavaScript\"',time:'منذ ساعتين',user:'خالد أحمد'}];const getActivityIcon=type=>{switch(type){case'student_joined':return UserPlusIcon;case'course_completed':return AcademicCapIcon;case'quiz_taken':return ClipboardDocumentCheckIcon;case'certificate_issued':return DocumentTextIcon;case'video_watched':return EyeIcon;default:return UserPlusIcon;}};const getActivityColor=type=>{switch(type){case'student_joined':return'bg-blue-100 text-blue-600';case'course_completed':return'bg-green-100 text-green-600';case'quiz_taken':return'bg-orange-100 text-orange-600';case'certificate_issued':return'bg-purple-100 text-purple-600';case'video_watched':return'bg-indigo-100 text-indigo-600';default:return'bg-gray-100 text-gray-600';}};return/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-xl p-6 shadow-sm\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-4\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-bold text-gray-900\",children:\"\\u0627\\u0644\\u0646\\u0634\\u0627\\u0637 \\u0627\\u0644\\u0623\\u062E\\u064A\\u0631\"}),/*#__PURE__*/_jsx(\"button\",{className:\"text-sm text-primary-600 hover:text-primary-700 font-medium\",children:\"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u0643\\u0644\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-4\",children:activities.map((activity,index)=>{const Icon=getActivityIcon(activity.type);const colorClass=getActivityColor(activity.type);return/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:index*0.1},className:\"flex items-start space-x-3 space-x-reverse p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200\",children:[/*#__PURE__*/_jsx(\"div\",{className:`w-10 h-10 rounded-full flex items-center justify-center ${colorClass}`,children:/*#__PURE__*/_jsx(Icon,{className:\"w-5 h-5\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 min-w-0\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-medium text-gray-900\",children:activity.title}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500\",children:activity.time})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600 mt-1\",children:activity.description}),activity.user&&/*#__PURE__*/_jsx(\"span\",{className:\"inline-block mt-2 px-2 py-1 bg-gray-100 text-xs text-gray-700 rounded-full\",children:activity.user})]})]},activity.id);})}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-6 pt-4 border-t border-gray-200\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"button\",{className:\"text-sm text-primary-600 hover:text-primary-700 font-medium\",children:\"\\u062A\\u062D\\u062F\\u064A\\u062B \\u0627\\u0644\\u0646\\u0634\\u0627\\u0637\"})})})]});};export default RecentActivity;", "map": {"version": 3, "names": ["React", "motion", "UserPlusIcon", "AcademicCapIcon", "ClipboardDocumentCheckIcon", "DocumentTextIcon", "EyeIcon", "jsx", "_jsx", "jsxs", "_jsxs", "RecentActivity", "activities", "id", "type", "title", "description", "time", "user", "getActivityIcon", "getActivityColor", "className", "children", "map", "activity", "index", "Icon", "colorClass", "div", "initial", "opacity", "x", "animate", "transition", "delay"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Admin/RecentActivity.tsx"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  UserPlusIcon,\n  AcademicCapIcon,\n  ClipboardDocumentCheckIcon,\n  DocumentTextIcon,\n  EyeIcon\n} from '@heroicons/react/24/outline';\n\ninterface Activity {\n  id: string;\n  type: 'student_joined' | 'course_completed' | 'quiz_taken' | 'certificate_issued' | 'video_watched';\n  title: string;\n  description: string;\n  time: string;\n  user?: string;\n}\n\nconst RecentActivity: React.FC = () => {\n  const activities: Activity[] = [\n    {\n      id: '1',\n      type: 'student_joined',\n      title: 'طالب جديد',\n      description: 'انضم أحمد محمد إلى المنصة',\n      time: 'منذ 5 دقائق',\n      user: 'أحمد محمد'\n    },\n    {\n      id: '2',\n      type: 'course_completed',\n      title: 'كورس مكتمل',\n      description: 'أكملت سارة أحمد كورس \"أساسيات البرمجة\"',\n      time: 'منذ 15 دقيقة',\n      user: 'سارة أحمد'\n    },\n    {\n      id: '3',\n      type: 'quiz_taken',\n      title: 'اختبار جديد',\n      description: 'أدى محمد علي اختبار JavaScript',\n      time: 'منذ 30 دقيقة',\n      user: 'محمد علي'\n    },\n    {\n      id: '4',\n      type: 'certificate_issued',\n      title: 'شهادة جديدة',\n      description: 'تم إصدار شهادة لفاطمة حسن',\n      time: 'منذ ساعة',\n      user: 'فاطمة حسن'\n    },\n    {\n      id: '5',\n      type: 'video_watched',\n      title: 'مشاهدة فيديو',\n      description: 'شاهد خالد أحمد فيديو \"المتغيرات في JavaScript\"',\n      time: 'منذ ساعتين',\n      user: 'خالد أحمد'\n    }\n  ];\n\n  const getActivityIcon = (type: Activity['type']) => {\n    switch (type) {\n      case 'student_joined':\n        return UserPlusIcon;\n      case 'course_completed':\n        return AcademicCapIcon;\n      case 'quiz_taken':\n        return ClipboardDocumentCheckIcon;\n      case 'certificate_issued':\n        return DocumentTextIcon;\n      case 'video_watched':\n        return EyeIcon;\n      default:\n        return UserPlusIcon;\n    }\n  };\n\n  const getActivityColor = (type: Activity['type']) => {\n    switch (type) {\n      case 'student_joined':\n        return 'bg-blue-100 text-blue-600';\n      case 'course_completed':\n        return 'bg-green-100 text-green-600';\n      case 'quiz_taken':\n        return 'bg-orange-100 text-orange-600';\n      case 'certificate_issued':\n        return 'bg-purple-100 text-purple-600';\n      case 'video_watched':\n        return 'bg-indigo-100 text-indigo-600';\n      default:\n        return 'bg-gray-100 text-gray-600';\n    }\n  };\n\n  return (\n    <div className=\"bg-white rounded-xl p-6 shadow-sm\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-lg font-bold text-gray-900\">النشاط الأخير</h3>\n        <button className=\"text-sm text-primary-600 hover:text-primary-700 font-medium\">\n          عرض الكل\n        </button>\n      </div>\n      \n      <div className=\"space-y-4\">\n        {activities.map((activity, index) => {\n          const Icon = getActivityIcon(activity.type);\n          const colorClass = getActivityColor(activity.type);\n          \n          return (\n            <motion.div\n              key={activity.id}\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: index * 0.1 }}\n              className=\"flex items-start space-x-3 space-x-reverse p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200\"\n            >\n              <div className={`w-10 h-10 rounded-full flex items-center justify-center ${colorClass}`}>\n                <Icon className=\"w-5 h-5\" />\n              </div>\n              \n              <div className=\"flex-1 min-w-0\">\n                <div className=\"flex items-center justify-between\">\n                  <h4 className=\"text-sm font-medium text-gray-900\">\n                    {activity.title}\n                  </h4>\n                  <span className=\"text-xs text-gray-500\">\n                    {activity.time}\n                  </span>\n                </div>\n                <p className=\"text-sm text-gray-600 mt-1\">\n                  {activity.description}\n                </p>\n                {activity.user && (\n                  <span className=\"inline-block mt-2 px-2 py-1 bg-gray-100 text-xs text-gray-700 rounded-full\">\n                    {activity.user}\n                  </span>\n                )}\n              </div>\n            </motion.div>\n          );\n        })}\n      </div>\n      \n      <div className=\"mt-6 pt-4 border-t border-gray-200\">\n        <div className=\"flex items-center justify-center\">\n          <button className=\"text-sm text-primary-600 hover:text-primary-700 font-medium\">\n            تحديث النشاط\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RecentActivity;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,KAAQ,eAAe,CACtC,OACEC,YAAY,CACZC,eAAe,CACfC,0BAA0B,CAC1BC,gBAAgB,CAChBC,OAAO,KACF,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAWrC,KAAM,CAAAC,cAAwB,CAAGA,CAAA,GAAM,CACrC,KAAM,CAAAC,UAAsB,CAAG,CAC7B,CACEC,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,gBAAgB,CACtBC,KAAK,CAAE,WAAW,CAClBC,WAAW,CAAE,2BAA2B,CACxCC,IAAI,CAAE,aAAa,CACnBC,IAAI,CAAE,WACR,CAAC,CACD,CACEL,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,kBAAkB,CACxBC,KAAK,CAAE,YAAY,CACnBC,WAAW,CAAE,wCAAwC,CACrDC,IAAI,CAAE,cAAc,CACpBC,IAAI,CAAE,WACR,CAAC,CACD,CACEL,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,YAAY,CAClBC,KAAK,CAAE,aAAa,CACpBC,WAAW,CAAE,gCAAgC,CAC7CC,IAAI,CAAE,cAAc,CACpBC,IAAI,CAAE,UACR,CAAC,CACD,CACEL,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,oBAAoB,CAC1BC,KAAK,CAAE,aAAa,CACpBC,WAAW,CAAE,2BAA2B,CACxCC,IAAI,CAAE,UAAU,CAChBC,IAAI,CAAE,WACR,CAAC,CACD,CACEL,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,eAAe,CACrBC,KAAK,CAAE,cAAc,CACrBC,WAAW,CAAE,gDAAgD,CAC7DC,IAAI,CAAE,YAAY,CAClBC,IAAI,CAAE,WACR,CAAC,CACF,CAED,KAAM,CAAAC,eAAe,CAAIL,IAAsB,EAAK,CAClD,OAAQA,IAAI,EACV,IAAK,gBAAgB,CACnB,MAAO,CAAAZ,YAAY,CACrB,IAAK,kBAAkB,CACrB,MAAO,CAAAC,eAAe,CACxB,IAAK,YAAY,CACf,MAAO,CAAAC,0BAA0B,CACnC,IAAK,oBAAoB,CACvB,MAAO,CAAAC,gBAAgB,CACzB,IAAK,eAAe,CAClB,MAAO,CAAAC,OAAO,CAChB,QACE,MAAO,CAAAJ,YAAY,CACvB,CACF,CAAC,CAED,KAAM,CAAAkB,gBAAgB,CAAIN,IAAsB,EAAK,CACnD,OAAQA,IAAI,EACV,IAAK,gBAAgB,CACnB,MAAO,2BAA2B,CACpC,IAAK,kBAAkB,CACrB,MAAO,6BAA6B,CACtC,IAAK,YAAY,CACf,MAAO,+BAA+B,CACxC,IAAK,oBAAoB,CACvB,MAAO,+BAA+B,CACxC,IAAK,eAAe,CAClB,MAAO,+BAA+B,CACxC,QACE,MAAO,2BAA2B,CACtC,CACF,CAAC,CAED,mBACEJ,KAAA,QAAKW,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDZ,KAAA,QAAKW,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDd,IAAA,OAAIa,SAAS,CAAC,iCAAiC,CAAAC,QAAA,CAAC,2EAAa,CAAI,CAAC,cAClEd,IAAA,WAAQa,SAAS,CAAC,6DAA6D,CAAAC,QAAA,CAAC,6CAEhF,CAAQ,CAAC,EACN,CAAC,cAENd,IAAA,QAAKa,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBV,UAAU,CAACW,GAAG,CAAC,CAACC,QAAQ,CAAEC,KAAK,GAAK,CACnC,KAAM,CAAAC,IAAI,CAAGP,eAAe,CAACK,QAAQ,CAACV,IAAI,CAAC,CAC3C,KAAM,CAAAa,UAAU,CAAGP,gBAAgB,CAACI,QAAQ,CAACV,IAAI,CAAC,CAElD,mBACEJ,KAAA,CAACT,MAAM,CAAC2B,GAAG,EAETC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,KAAK,CAAET,KAAK,CAAG,GAAI,CAAE,CACnCJ,SAAS,CAAC,2GAA2G,CAAAC,QAAA,eAErHd,IAAA,QAAKa,SAAS,CAAE,2DAA2DM,UAAU,EAAG,CAAAL,QAAA,cACtFd,IAAA,CAACkB,IAAI,EAACL,SAAS,CAAC,SAAS,CAAE,CAAC,CACzB,CAAC,cAENX,KAAA,QAAKW,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BZ,KAAA,QAAKW,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDd,IAAA,OAAIa,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC9CE,QAAQ,CAACT,KAAK,CACb,CAAC,cACLP,IAAA,SAAMa,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CACpCE,QAAQ,CAACP,IAAI,CACV,CAAC,EACJ,CAAC,cACNT,IAAA,MAAGa,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CACtCE,QAAQ,CAACR,WAAW,CACpB,CAAC,CACHQ,QAAQ,CAACN,IAAI,eACZV,IAAA,SAAMa,SAAS,CAAC,4EAA4E,CAAAC,QAAA,CACzFE,QAAQ,CAACN,IAAI,CACV,CACP,EACE,CAAC,GA3BDM,QAAQ,CAACX,EA4BJ,CAAC,CAEjB,CAAC,CAAC,CACC,CAAC,cAENL,IAAA,QAAKa,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjDd,IAAA,QAAKa,SAAS,CAAC,kCAAkC,CAAAC,QAAA,cAC/Cd,IAAA,WAAQa,SAAS,CAAC,6DAA6D,CAAAC,QAAA,CAAC,qEAEhF,CAAQ,CAAC,CACN,CAAC,CACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAX,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}