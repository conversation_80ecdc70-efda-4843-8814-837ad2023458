{"ast": null, "code": "import { px } from '../../../value/types/numbers/units.mjs';\nfunction calcOrigin(origin, offset, size) {\n  return typeof origin === \"string\" ? origin : px.transform(offset + size * origin);\n}\n/**\n * The SVG transform origin defaults are different to CSS and is less intuitive,\n * so we use the measured dimensions of the SVG to reconcile these.\n */\nfunction calcSVGTransformOrigin(dimensions, originX, originY) {\n  const pxOriginX = calcOrigin(originX, dimensions.x, dimensions.width);\n  const pxOriginY = calcOrigin(originY, dimensions.y, dimensions.height);\n  return `${pxOriginX} ${pxOriginY}`;\n}\nexport { calcSVGTransformOrigin };", "map": {"version": 3, "names": ["px", "calcOrigin", "origin", "offset", "size", "transform", "calcSVGTransformOrigin", "dimensions", "originX", "originY", "pxOriginX", "x", "width", "pxOriginY", "y", "height"], "sources": ["C:/Users/<USER>/Desktop/مشروع/node_modules/framer-motion/dist/es/render/svg/utils/transform-origin.mjs"], "sourcesContent": ["import { px } from '../../../value/types/numbers/units.mjs';\n\nfunction calcOrigin(origin, offset, size) {\n    return typeof origin === \"string\"\n        ? origin\n        : px.transform(offset + size * origin);\n}\n/**\n * The SVG transform origin defaults are different to CSS and is less intuitive,\n * so we use the measured dimensions of the SVG to reconcile these.\n */\nfunction calcSVGTransformOrigin(dimensions, originX, originY) {\n    const pxOriginX = calcOrigin(originX, dimensions.x, dimensions.width);\n    const pxOriginY = calcOrigin(originY, dimensions.y, dimensions.height);\n    return `${pxOriginX} ${pxOriginY}`;\n}\n\nexport { calcSVGTransformOrigin };\n"], "mappings": "AAAA,SAASA,EAAE,QAAQ,wCAAwC;AAE3D,SAASC,UAAUA,CAACC,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAE;EACtC,OAAO,OAAOF,MAAM,KAAK,QAAQ,GAC3BA,MAAM,GACNF,EAAE,CAACK,SAAS,CAACF,MAAM,GAAGC,IAAI,GAAGF,MAAM,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACA,SAASI,sBAAsBA,CAACC,UAAU,EAAEC,OAAO,EAAEC,OAAO,EAAE;EAC1D,MAAMC,SAAS,GAAGT,UAAU,CAACO,OAAO,EAAED,UAAU,CAACI,CAAC,EAAEJ,UAAU,CAACK,KAAK,CAAC;EACrE,MAAMC,SAAS,GAAGZ,UAAU,CAACQ,OAAO,EAAEF,UAAU,CAACO,CAAC,EAAEP,UAAU,CAACQ,MAAM,CAAC;EACtE,OAAO,GAAGL,SAAS,IAAIG,SAAS,EAAE;AACtC;AAEA,SAASP,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}