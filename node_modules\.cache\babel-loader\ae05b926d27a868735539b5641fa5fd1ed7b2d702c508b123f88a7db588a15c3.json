{"ast": null, "code": "import React from'react';import{motion}from'framer-motion';import{ArrowUpIcon,ArrowDownIcon}from'@heroicons/react/24/outline';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const StatsCard=_ref=>{let{title,value,change,changeType='increase',icon:Icon,color,delay=0}=_ref;const colorClasses={blue:{bg:'bg-blue-100',text:'text-blue-600',border:'border-blue-200'},green:{bg:'bg-green-100',text:'text-green-600',border:'border-green-200'},purple:{bg:'bg-purple-100',text:'text-purple-600',border:'border-purple-200'},orange:{bg:'bg-orange-100',text:'text-orange-600',border:'border-orange-200'},red:{bg:'bg-red-100',text:'text-red-600',border:'border-red-200'}};return/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay,duration:0.3},whileHover:{y:-2},className:\"bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-200 border border-gray-100\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-600 mb-1\",children:title}),/*#__PURE__*/_jsx(\"p\",{className:\"text-3xl font-bold text-gray-900\",children:typeof value==='number'?value.toLocaleString():value}),change&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mt-2\",children:[changeType==='increase'?/*#__PURE__*/_jsx(ArrowUpIcon,{className:\"w-4 h-4 text-green-500 ml-1\"}):/*#__PURE__*/_jsx(ArrowDownIcon,{className:\"w-4 h-4 text-red-500 ml-1\"}),/*#__PURE__*/_jsx(\"span\",{className:`text-sm font-medium ${changeType==='increase'?'text-green-600':'text-red-600'}`,children:change}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-500 mr-1\",children:\"\\u0645\\u0646 \\u0627\\u0644\\u0634\\u0647\\u0631 \\u0627\\u0644\\u0645\\u0627\\u0636\\u064A\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:`\n          w-14 h-14 rounded-xl flex items-center justify-center\n          ${colorClasses[color].bg}\n          ${colorClasses[color].border}\n          border\n        `,children:/*#__PURE__*/_jsx(Icon,{className:`w-7 h-7 ${colorClasses[color].text}`})})]})});};export default StatsCard;", "map": {"version": 3, "names": ["React", "motion", "ArrowUpIcon", "ArrowDownIcon", "jsx", "_jsx", "jsxs", "_jsxs", "StatsCard", "_ref", "title", "value", "change", "changeType", "icon", "Icon", "color", "delay", "colorClasses", "blue", "bg", "text", "border", "green", "purple", "orange", "red", "div", "initial", "opacity", "y", "animate", "transition", "duration", "whileHover", "className", "children", "toLocaleString"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Admin/StatsCard.tsx"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { ArrowUpIcon, ArrowDownIcon } from '@heroicons/react/24/outline';\n\ninterface StatsCardProps {\n  title: string;\n  value: number | string;\n  change?: string;\n  changeType?: 'increase' | 'decrease';\n  icon: React.ComponentType<{ className?: string }>;\n  color: 'blue' | 'green' | 'purple' | 'orange' | 'red';\n  delay?: number;\n}\n\nconst StatsCard: React.FC<StatsCardProps> = ({\n  title,\n  value,\n  change,\n  changeType = 'increase',\n  icon: Icon,\n  color,\n  delay = 0\n}) => {\n  const colorClasses = {\n    blue: {\n      bg: 'bg-blue-100',\n      text: 'text-blue-600',\n      border: 'border-blue-200'\n    },\n    green: {\n      bg: 'bg-green-100',\n      text: 'text-green-600',\n      border: 'border-green-200'\n    },\n    purple: {\n      bg: 'bg-purple-100',\n      text: 'text-purple-600',\n      border: 'border-purple-200'\n    },\n    orange: {\n      bg: 'bg-orange-100',\n      text: 'text-orange-600',\n      border: 'border-orange-200'\n    },\n    red: {\n      bg: 'bg-red-100',\n      text: 'text-red-600',\n      border: 'border-red-200'\n    }\n  };\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ delay, duration: 0.3 }}\n      whileHover={{ y: -2 }}\n      className=\"bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-200 border border-gray-100\"\n    >\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex-1\">\n          <p className=\"text-sm font-medium text-gray-600 mb-1\">{title}</p>\n          <p className=\"text-3xl font-bold text-gray-900\">\n            {typeof value === 'number' ? value.toLocaleString() : value}\n          </p>\n          \n          {change && (\n            <div className=\"flex items-center mt-2\">\n              {changeType === 'increase' ? (\n                <ArrowUpIcon className=\"w-4 h-4 text-green-500 ml-1\" />\n              ) : (\n                <ArrowDownIcon className=\"w-4 h-4 text-red-500 ml-1\" />\n              )}\n              <span className={`text-sm font-medium ${\n                changeType === 'increase' ? 'text-green-600' : 'text-red-600'\n              }`}>\n                {change}\n              </span>\n              <span className=\"text-sm text-gray-500 mr-1\">من الشهر الماضي</span>\n            </div>\n          )}\n        </div>\n        \n        <div className={`\n          w-14 h-14 rounded-xl flex items-center justify-center\n          ${colorClasses[color].bg}\n          ${colorClasses[color].border}\n          border\n        `}>\n          <Icon className={`w-7 h-7 ${colorClasses[color].text}`} />\n        </div>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default StatsCard;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,KAAQ,eAAe,CACtC,OAASC,WAAW,CAAEC,aAAa,KAAQ,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAYzE,KAAM,CAAAC,SAAmC,CAAGC,IAAA,EAQtC,IARuC,CAC3CC,KAAK,CACLC,KAAK,CACLC,MAAM,CACNC,UAAU,CAAG,UAAU,CACvBC,IAAI,CAAEC,IAAI,CACVC,KAAK,CACLC,KAAK,CAAG,CACV,CAAC,CAAAR,IAAA,CACC,KAAM,CAAAS,YAAY,CAAG,CACnBC,IAAI,CAAE,CACJC,EAAE,CAAE,aAAa,CACjBC,IAAI,CAAE,eAAe,CACrBC,MAAM,CAAE,iBACV,CAAC,CACDC,KAAK,CAAE,CACLH,EAAE,CAAE,cAAc,CAClBC,IAAI,CAAE,gBAAgB,CACtBC,MAAM,CAAE,kBACV,CAAC,CACDE,MAAM,CAAE,CACNJ,EAAE,CAAE,eAAe,CACnBC,IAAI,CAAE,iBAAiB,CACvBC,MAAM,CAAE,mBACV,CAAC,CACDG,MAAM,CAAE,CACNL,EAAE,CAAE,eAAe,CACnBC,IAAI,CAAE,iBAAiB,CACvBC,MAAM,CAAE,mBACV,CAAC,CACDI,GAAG,CAAE,CACHN,EAAE,CAAE,YAAY,CAChBC,IAAI,CAAE,cAAc,CACpBC,MAAM,CAAE,gBACV,CACF,CAAC,CAED,mBACEjB,IAAA,CAACJ,MAAM,CAAC0B,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEf,KAAK,CAAEgB,QAAQ,CAAE,GAAI,CAAE,CACrCC,UAAU,CAAE,CAAEJ,CAAC,CAAE,CAAC,CAAE,CAAE,CACtBK,SAAS,CAAC,sGAAsG,CAAAC,QAAA,cAEhH7B,KAAA,QAAK4B,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD7B,KAAA,QAAK4B,SAAS,CAAC,QAAQ,CAAAC,QAAA,eACrB/B,IAAA,MAAG8B,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAE1B,KAAK,CAAI,CAAC,cACjEL,IAAA,MAAG8B,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAC5C,MAAO,CAAAzB,KAAK,GAAK,QAAQ,CAAGA,KAAK,CAAC0B,cAAc,CAAC,CAAC,CAAG1B,KAAK,CAC1D,CAAC,CAEHC,MAAM,eACLL,KAAA,QAAK4B,SAAS,CAAC,wBAAwB,CAAAC,QAAA,EACpCvB,UAAU,GAAK,UAAU,cACxBR,IAAA,CAACH,WAAW,EAACiC,SAAS,CAAC,6BAA6B,CAAE,CAAC,cAEvD9B,IAAA,CAACF,aAAa,EAACgC,SAAS,CAAC,2BAA2B,CAAE,CACvD,cACD9B,IAAA,SAAM8B,SAAS,CAAE,uBACftB,UAAU,GAAK,UAAU,CAAG,gBAAgB,CAAG,cAAc,EAC5D,CAAAuB,QAAA,CACAxB,MAAM,CACH,CAAC,cACPP,IAAA,SAAM8B,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,kFAAe,CAAM,CAAC,EAChE,CACN,EACE,CAAC,cAEN/B,IAAA,QAAK8B,SAAS,CAAE;AACxB;AACA,YAAYjB,YAAY,CAACF,KAAK,CAAC,CAACI,EAAE;AAClC,YAAYF,YAAY,CAACF,KAAK,CAAC,CAACM,MAAM;AACtC;AACA,SAAU,CAAAc,QAAA,cACA/B,IAAA,CAACU,IAAI,EAACoB,SAAS,CAAE,WAAWjB,YAAY,CAACF,KAAK,CAAC,CAACK,IAAI,EAAG,CAAE,CAAC,CACvD,CAAC,EACH,CAAC,CACI,CAAC,CAEjB,CAAC,CAED,cAAe,CAAAb,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}