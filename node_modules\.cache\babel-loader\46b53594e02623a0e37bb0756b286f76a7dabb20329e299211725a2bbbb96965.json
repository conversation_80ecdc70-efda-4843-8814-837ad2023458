{"ast": null, "code": "import { warning, invariant } from '../utils/errors.mjs';\nimport { createBox } from '../projection/geometry/models.mjs';\nimport { isRefObject } from '../utils/is-ref-object.mjs';\nimport { initPrefersReducedMotion } from '../utils/reduced-motion/index.mjs';\nimport { hasReducedMotionListener, prefersReducedMotion } from '../utils/reduced-motion/state.mjs';\nimport { SubscriptionManager } from '../utils/subscription-manager.mjs';\nimport { motionValue } from '../value/index.mjs';\nimport { isWillChangeMotionValue } from '../value/use-will-change/is.mjs';\nimport { isMotionValue } from '../value/utils/is-motion-value.mjs';\nimport { transformProps } from './html/utils/transform.mjs';\nimport { isControllingVariants, isVariantNode } from './utils/is-controlling-variants.mjs';\nimport { isVariantLabel } from './utils/is-variant-label.mjs';\nimport { updateMotionValuesFromProps } from './utils/motion-values.mjs';\nimport { resolveVariantFromProps } from './utils/resolve-variants.mjs';\nimport { warnOnce } from '../utils/warn-once.mjs';\nimport { featureDefinitions } from '../motion/features/definitions.mjs';\nimport { variantProps } from './utils/variant-props.mjs';\nimport { visualElementStore } from './store.mjs';\nimport { frame, cancelFrame } from '../frameloop/frame.mjs';\nconst featureNames = Object.keys(featureDefinitions);\nconst numFeatures = featureNames.length;\nconst propEventHandlers = [\"AnimationStart\", \"AnimationComplete\", \"Update\", \"BeforeLayoutMeasure\", \"LayoutMeasure\", \"LayoutAnimationStart\", \"LayoutAnimationComplete\"];\nconst numVariantProps = variantProps.length;\n/**\n * A VisualElement is an imperative abstraction around UI elements such as\n * HTMLElement, SVGElement, Three.Object3D etc.\n */\nclass VisualElement {\n  constructor({\n    parent,\n    props,\n    presenceContext,\n    reducedMotionConfig,\n    visualState\n  }, options = {}) {\n    /**\n     * A reference to the current underlying Instance, e.g. a HTMLElement\n     * or Three.Mesh etc.\n     */\n    this.current = null;\n    /**\n     * A set containing references to this VisualElement's children.\n     */\n    this.children = new Set();\n    /**\n     * Determine what role this visual element should take in the variant tree.\n     */\n    this.isVariantNode = false;\n    this.isControllingVariants = false;\n    /**\n     * Decides whether this VisualElement should animate in reduced motion\n     * mode.\n     *\n     * TODO: This is currently set on every individual VisualElement but feels\n     * like it could be set globally.\n     */\n    this.shouldReduceMotion = null;\n    /**\n     * A map of all motion values attached to this visual element. Motion\n     * values are source of truth for any given animated value. A motion\n     * value might be provided externally by the component via props.\n     */\n    this.values = new Map();\n    /**\n     * Cleanup functions for active features (hover/tap/exit etc)\n     */\n    this.features = {};\n    /**\n     * A map of every subscription that binds the provided or generated\n     * motion values onChange listeners to this visual element.\n     */\n    this.valueSubscriptions = new Map();\n    /**\n     * A reference to the previously-provided motion values as returned\n     * from scrapeMotionValuesFromProps. We use the keys in here to determine\n     * if any motion values need to be removed after props are updated.\n     */\n    this.prevMotionValues = {};\n    /**\n     * An object containing a SubscriptionManager for each active event.\n     */\n    this.events = {};\n    /**\n     * An object containing an unsubscribe function for each prop event subscription.\n     * For example, every \"Update\" event can have multiple subscribers via\n     * VisualElement.on(), but only one of those can be defined via the onUpdate prop.\n     */\n    this.propEventSubscriptions = {};\n    this.notifyUpdate = () => this.notify(\"Update\", this.latestValues);\n    this.render = () => {\n      if (!this.current) return;\n      this.triggerBuild();\n      this.renderInstance(this.current, this.renderState, this.props.style, this.projection);\n    };\n    this.scheduleRender = () => frame.render(this.render, false, true);\n    const {\n      latestValues,\n      renderState\n    } = visualState;\n    this.latestValues = latestValues;\n    this.baseTarget = {\n      ...latestValues\n    };\n    this.initialValues = props.initial ? {\n      ...latestValues\n    } : {};\n    this.renderState = renderState;\n    this.parent = parent;\n    this.props = props;\n    this.presenceContext = presenceContext;\n    this.depth = parent ? parent.depth + 1 : 0;\n    this.reducedMotionConfig = reducedMotionConfig;\n    this.options = options;\n    this.isControllingVariants = isControllingVariants(props);\n    this.isVariantNode = isVariantNode(props);\n    if (this.isVariantNode) {\n      this.variantChildren = new Set();\n    }\n    this.manuallyAnimateOnMount = Boolean(parent && parent.current);\n    /**\n     * Any motion values that are provided to the element when created\n     * aren't yet bound to the element, as this would technically be impure.\n     * However, we iterate through the motion values and set them to the\n     * initial values for this component.\n     *\n     * TODO: This is impure and we should look at changing this to run on mount.\n     * Doing so will break some tests but this isn't neccessarily a breaking change,\n     * more a reflection of the test.\n     */\n    const {\n      willChange,\n      ...initialMotionValues\n    } = this.scrapeMotionValuesFromProps(props, {});\n    for (const key in initialMotionValues) {\n      const value = initialMotionValues[key];\n      if (latestValues[key] !== undefined && isMotionValue(value)) {\n        value.set(latestValues[key], false);\n        if (isWillChangeMotionValue(willChange)) {\n          willChange.add(key);\n        }\n      }\n    }\n  }\n  /**\n   * This method takes React props and returns found MotionValues. For example, HTML\n   * MotionValues will be found within the style prop, whereas for Three.js within attribute arrays.\n   *\n   * This isn't an abstract method as it needs calling in the constructor, but it is\n   * intended to be one.\n   */\n  scrapeMotionValuesFromProps(_props, _prevProps) {\n    return {};\n  }\n  mount(instance) {\n    this.current = instance;\n    visualElementStore.set(instance, this);\n    if (this.projection && !this.projection.instance) {\n      this.projection.mount(instance);\n    }\n    if (this.parent && this.isVariantNode && !this.isControllingVariants) {\n      this.removeFromVariantTree = this.parent.addVariantChild(this);\n    }\n    this.values.forEach((value, key) => this.bindToMotionValue(key, value));\n    if (!hasReducedMotionListener.current) {\n      initPrefersReducedMotion();\n    }\n    this.shouldReduceMotion = this.reducedMotionConfig === \"never\" ? false : this.reducedMotionConfig === \"always\" ? true : prefersReducedMotion.current;\n    if (process.env.NODE_ENV !== \"production\") {\n      warnOnce(this.shouldReduceMotion !== true, \"You have Reduced Motion enabled on your device. Animations may not appear as expected.\");\n    }\n    if (this.parent) this.parent.children.add(this);\n    this.update(this.props, this.presenceContext);\n  }\n  unmount() {\n    visualElementStore.delete(this.current);\n    this.projection && this.projection.unmount();\n    cancelFrame(this.notifyUpdate);\n    cancelFrame(this.render);\n    this.valueSubscriptions.forEach(remove => remove());\n    this.removeFromVariantTree && this.removeFromVariantTree();\n    this.parent && this.parent.children.delete(this);\n    for (const key in this.events) {\n      this.events[key].clear();\n    }\n    for (const key in this.features) {\n      this.features[key].unmount();\n    }\n    this.current = null;\n  }\n  bindToMotionValue(key, value) {\n    const valueIsTransform = transformProps.has(key);\n    const removeOnChange = value.on(\"change\", latestValue => {\n      this.latestValues[key] = latestValue;\n      this.props.onUpdate && frame.update(this.notifyUpdate, false, true);\n      if (valueIsTransform && this.projection) {\n        this.projection.isTransformDirty = true;\n      }\n    });\n    const removeOnRenderRequest = value.on(\"renderRequest\", this.scheduleRender);\n    this.valueSubscriptions.set(key, () => {\n      removeOnChange();\n      removeOnRenderRequest();\n    });\n  }\n  sortNodePosition(other) {\n    /**\n     * If these nodes aren't even of the same type we can't compare their depth.\n     */\n    if (!this.current || !this.sortInstanceNodePosition || this.type !== other.type) {\n      return 0;\n    }\n    return this.sortInstanceNodePosition(this.current, other.current);\n  }\n  loadFeatures({\n    children,\n    ...renderedProps\n  }, isStrict, preloadedFeatures, initialLayoutGroupConfig) {\n    let ProjectionNodeConstructor;\n    let MeasureLayout;\n    /**\n     * If we're in development mode, check to make sure we're not rendering a motion component\n     * as a child of LazyMotion, as this will break the file-size benefits of using it.\n     */\n    if (process.env.NODE_ENV !== \"production\" && preloadedFeatures && isStrict) {\n      const strictMessage = \"You have rendered a `motion` component within a `LazyMotion` component. This will break tree shaking. Import and render a `m` component instead.\";\n      renderedProps.ignoreStrict ? warning(false, strictMessage) : invariant(false, strictMessage);\n    }\n    for (let i = 0; i < numFeatures; i++) {\n      const name = featureNames[i];\n      const {\n        isEnabled,\n        Feature: FeatureConstructor,\n        ProjectionNode,\n        MeasureLayout: MeasureLayoutComponent\n      } = featureDefinitions[name];\n      if (ProjectionNode) ProjectionNodeConstructor = ProjectionNode;\n      if (isEnabled(renderedProps)) {\n        if (!this.features[name] && FeatureConstructor) {\n          this.features[name] = new FeatureConstructor(this);\n        }\n        if (MeasureLayoutComponent) {\n          MeasureLayout = MeasureLayoutComponent;\n        }\n      }\n    }\n    if ((this.type === \"html\" || this.type === \"svg\") && !this.projection && ProjectionNodeConstructor) {\n      this.projection = new ProjectionNodeConstructor(this.latestValues, this.parent && this.parent.projection);\n      const {\n        layoutId,\n        layout,\n        drag,\n        dragConstraints,\n        layoutScroll,\n        layoutRoot\n      } = renderedProps;\n      this.projection.setOptions({\n        layoutId,\n        layout,\n        alwaysMeasureLayout: Boolean(drag) || dragConstraints && isRefObject(dragConstraints),\n        visualElement: this,\n        scheduleRender: () => this.scheduleRender(),\n        /**\n         * TODO: Update options in an effect. This could be tricky as it'll be too late\n         * to update by the time layout animations run.\n         * We also need to fix this safeToRemove by linking it up to the one returned by usePresence,\n         * ensuring it gets called if there's no potential layout animations.\n         *\n         */\n        animationType: typeof layout === \"string\" ? layout : \"both\",\n        initialPromotionConfig: initialLayoutGroupConfig,\n        layoutScroll,\n        layoutRoot\n      });\n    }\n    return MeasureLayout;\n  }\n  updateFeatures() {\n    for (const key in this.features) {\n      const feature = this.features[key];\n      if (feature.isMounted) {\n        feature.update();\n      } else {\n        feature.mount();\n        feature.isMounted = true;\n      }\n    }\n  }\n  triggerBuild() {\n    this.build(this.renderState, this.latestValues, this.options, this.props);\n  }\n  /**\n   * Measure the current viewport box with or without transforms.\n   * Only measures axis-aligned boxes, rotate and skew must be manually\n   * removed with a re-render to work.\n   */\n  measureViewportBox() {\n    return this.current ? this.measureInstanceViewportBox(this.current, this.props) : createBox();\n  }\n  getStaticValue(key) {\n    return this.latestValues[key];\n  }\n  setStaticValue(key, value) {\n    this.latestValues[key] = value;\n  }\n  /**\n   * Make a target animatable by Popmotion. For instance, if we're\n   * trying to animate width from 100px to 100vw we need to measure 100vw\n   * in pixels to determine what we really need to animate to. This is also\n   * pluggable to support Framer's custom value types like Color,\n   * and CSS variables.\n   */\n  makeTargetAnimatable(target, canMutate = true) {\n    return this.makeTargetAnimatableFromInstance(target, this.props, canMutate);\n  }\n  /**\n   * Update the provided props. Ensure any newly-added motion values are\n   * added to our map, old ones removed, and listeners updated.\n   */\n  update(props, presenceContext) {\n    if (props.transformTemplate || this.props.transformTemplate) {\n      this.scheduleRender();\n    }\n    this.prevProps = this.props;\n    this.props = props;\n    this.prevPresenceContext = this.presenceContext;\n    this.presenceContext = presenceContext;\n    /**\n     * Update prop event handlers ie onAnimationStart, onAnimationComplete\n     */\n    for (let i = 0; i < propEventHandlers.length; i++) {\n      const key = propEventHandlers[i];\n      if (this.propEventSubscriptions[key]) {\n        this.propEventSubscriptions[key]();\n        delete this.propEventSubscriptions[key];\n      }\n      const listener = props[\"on\" + key];\n      if (listener) {\n        this.propEventSubscriptions[key] = this.on(key, listener);\n      }\n    }\n    this.prevMotionValues = updateMotionValuesFromProps(this, this.scrapeMotionValuesFromProps(props, this.prevProps), this.prevMotionValues);\n    if (this.handleChildMotionValue) {\n      this.handleChildMotionValue();\n    }\n  }\n  getProps() {\n    return this.props;\n  }\n  /**\n   * Returns the variant definition with a given name.\n   */\n  getVariant(name) {\n    return this.props.variants ? this.props.variants[name] : undefined;\n  }\n  /**\n   * Returns the defined default transition on this component.\n   */\n  getDefaultTransition() {\n    return this.props.transition;\n  }\n  getTransformPagePoint() {\n    return this.props.transformPagePoint;\n  }\n  getClosestVariantNode() {\n    return this.isVariantNode ? this : this.parent ? this.parent.getClosestVariantNode() : undefined;\n  }\n  getVariantContext(startAtParent = false) {\n    if (startAtParent) {\n      return this.parent ? this.parent.getVariantContext() : undefined;\n    }\n    if (!this.isControllingVariants) {\n      const context = this.parent ? this.parent.getVariantContext() || {} : {};\n      if (this.props.initial !== undefined) {\n        context.initial = this.props.initial;\n      }\n      return context;\n    }\n    const context = {};\n    for (let i = 0; i < numVariantProps; i++) {\n      const name = variantProps[i];\n      const prop = this.props[name];\n      if (isVariantLabel(prop) || prop === false) {\n        context[name] = prop;\n      }\n    }\n    return context;\n  }\n  /**\n   * Add a child visual element to our set of children.\n   */\n  addVariantChild(child) {\n    const closestVariantNode = this.getClosestVariantNode();\n    if (closestVariantNode) {\n      closestVariantNode.variantChildren && closestVariantNode.variantChildren.add(child);\n      return () => closestVariantNode.variantChildren.delete(child);\n    }\n  }\n  /**\n   * Add a motion value and bind it to this visual element.\n   */\n  addValue(key, value) {\n    // Remove existing value if it exists\n    if (value !== this.values.get(key)) {\n      this.removeValue(key);\n      this.bindToMotionValue(key, value);\n    }\n    this.values.set(key, value);\n    this.latestValues[key] = value.get();\n  }\n  /**\n   * Remove a motion value and unbind any active subscriptions.\n   */\n  removeValue(key) {\n    this.values.delete(key);\n    const unsubscribe = this.valueSubscriptions.get(key);\n    if (unsubscribe) {\n      unsubscribe();\n      this.valueSubscriptions.delete(key);\n    }\n    delete this.latestValues[key];\n    this.removeValueFromRenderState(key, this.renderState);\n  }\n  /**\n   * Check whether we have a motion value for this key\n   */\n  hasValue(key) {\n    return this.values.has(key);\n  }\n  getValue(key, defaultValue) {\n    if (this.props.values && this.props.values[key]) {\n      return this.props.values[key];\n    }\n    let value = this.values.get(key);\n    if (value === undefined && defaultValue !== undefined) {\n      value = motionValue(defaultValue, {\n        owner: this\n      });\n      this.addValue(key, value);\n    }\n    return value;\n  }\n  /**\n   * If we're trying to animate to a previously unencountered value,\n   * we need to check for it in our state and as a last resort read it\n   * directly from the instance (which might have performance implications).\n   */\n  readValue(key) {\n    var _a;\n    return this.latestValues[key] !== undefined || !this.current ? this.latestValues[key] : (_a = this.getBaseTargetFromProps(this.props, key)) !== null && _a !== void 0 ? _a : this.readValueFromInstance(this.current, key, this.options);\n  }\n  /**\n   * Set the base target to later animate back to. This is currently\n   * only hydrated on creation and when we first read a value.\n   */\n  setBaseTarget(key, value) {\n    this.baseTarget[key] = value;\n  }\n  /**\n   * Find the base target for a value thats been removed from all animation\n   * props.\n   */\n  getBaseTarget(key) {\n    var _a;\n    const {\n      initial\n    } = this.props;\n    const valueFromInitial = typeof initial === \"string\" || typeof initial === \"object\" ? (_a = resolveVariantFromProps(this.props, initial)) === null || _a === void 0 ? void 0 : _a[key] : undefined;\n    /**\n     * If this value still exists in the current initial variant, read that.\n     */\n    if (initial && valueFromInitial !== undefined) {\n      return valueFromInitial;\n    }\n    /**\n     * Alternatively, if this VisualElement config has defined a getBaseTarget\n     * so we can read the value from an alternative source, try that.\n     */\n    const target = this.getBaseTargetFromProps(this.props, key);\n    if (target !== undefined && !isMotionValue(target)) return target;\n    /**\n     * If the value was initially defined on initial, but it doesn't any more,\n     * return undefined. Otherwise return the value as initially read from the DOM.\n     */\n    return this.initialValues[key] !== undefined && valueFromInitial === undefined ? undefined : this.baseTarget[key];\n  }\n  on(eventName, callback) {\n    if (!this.events[eventName]) {\n      this.events[eventName] = new SubscriptionManager();\n    }\n    return this.events[eventName].add(callback);\n  }\n  notify(eventName, ...args) {\n    if (this.events[eventName]) {\n      this.events[eventName].notify(...args);\n    }\n  }\n}\nexport { VisualElement };", "map": {"version": 3, "names": ["warning", "invariant", "createBox", "isRefObject", "initPrefersReducedMotion", "hasReducedMotionListener", "prefersReducedMotion", "SubscriptionManager", "motionValue", "isWillChangeMotionValue", "isMotionValue", "transformProps", "isControllingVariants", "isVariantNode", "isVariantLabel", "updateMotionValuesFromProps", "resolveVariantFromProps", "warnOnce", "featureDefinitions", "variantProps", "visualElementStore", "frame", "cancelFrame", "featureNames", "Object", "keys", "numFeatures", "length", "propEventHandlers", "numVariantProps", "VisualElement", "constructor", "parent", "props", "presenceContext", "reducedMotionConfig", "visualState", "options", "current", "children", "Set", "shouldReduceMotion", "values", "Map", "features", "valueSubscriptions", "prevMotionValues", "events", "propEventSubscriptions", "notifyUpdate", "notify", "latestValues", "render", "triggerBuild", "renderInstance", "renderState", "style", "projection", "scheduleRender", "baseTarget", "initialValues", "initial", "depth", "variant<PERSON><PERSON><PERSON>n", "manuallyAnimateOnMount", "Boolean", "<PERSON><PERSON><PERSON><PERSON>", "initialMotionValues", "scrapeMotionValuesFromProps", "key", "value", "undefined", "set", "add", "_props", "_prevProps", "mount", "instance", "removeFromVariantTree", "addVariant<PERSON>hild", "for<PERSON>ach", "bindToMotionValue", "process", "env", "NODE_ENV", "update", "unmount", "delete", "remove", "clear", "valueIsTransform", "has", "removeOnChange", "on", "latestValue", "onUpdate", "isTransformDirty", "removeOnRenderRequest", "sortNodePosition", "other", "sortInstanceNodePosition", "type", "loadFeatures", "renderedProps", "isStrict", "preloadedFeatures", "initialLayoutGroupConfig", "ProjectionNodeConstructor", "MeasureLayout", "strictMessage", "ignoreStrict", "i", "name", "isEnabled", "Feature", "FeatureConstructor", "ProjectionNode", "MeasureLayoutComponent", "layoutId", "layout", "drag", "dragConstraints", "layoutScroll", "layoutRoot", "setOptions", "alwaysMeasureLayout", "visualElement", "animationType", "initialPromotionConfig", "updateFeatures", "feature", "isMounted", "build", "measureViewportBox", "measureInstanceViewportBox", "getStaticValue", "setStaticValue", "makeTargetAnimatable", "target", "canMutate", "makeTargetAnimatableFromInstance", "transformTemplate", "prevProps", "prevPresenceContext", "listener", "handleChildMotionValue", "getProps", "getVariant", "variants", "getDefaultTransition", "transition", "getTransformPagePoint", "transformPagePoint", "getClosestVariantNode", "getVariantContext", "startAtParent", "context", "prop", "child", "closestVariantNode", "addValue", "get", "removeValue", "unsubscribe", "removeValueFromRenderState", "hasValue", "getValue", "defaultValue", "owner", "readValue", "_a", "getBaseTargetFromProps", "readValueFromInstance", "set<PERSON><PERSON><PERSON><PERSON>get", "getBase<PERSON>arget", "valueFromInitial", "eventName", "callback", "args"], "sources": ["C:/Users/<USER>/Desktop/مشروع/node_modules/framer-motion/dist/es/render/VisualElement.mjs"], "sourcesContent": ["import { warning, invariant } from '../utils/errors.mjs';\nimport { createBox } from '../projection/geometry/models.mjs';\nimport { isRefObject } from '../utils/is-ref-object.mjs';\nimport { initPrefersReducedMotion } from '../utils/reduced-motion/index.mjs';\nimport { hasReducedMotionListener, prefersReducedMotion } from '../utils/reduced-motion/state.mjs';\nimport { SubscriptionManager } from '../utils/subscription-manager.mjs';\nimport { motionValue } from '../value/index.mjs';\nimport { isWillChangeMotionValue } from '../value/use-will-change/is.mjs';\nimport { isMotionValue } from '../value/utils/is-motion-value.mjs';\nimport { transformProps } from './html/utils/transform.mjs';\nimport { isControllingVariants, isVariantNode } from './utils/is-controlling-variants.mjs';\nimport { isVariantLabel } from './utils/is-variant-label.mjs';\nimport { updateMotionValuesFromProps } from './utils/motion-values.mjs';\nimport { resolveVariantFromProps } from './utils/resolve-variants.mjs';\nimport { warnOnce } from '../utils/warn-once.mjs';\nimport { featureDefinitions } from '../motion/features/definitions.mjs';\nimport { variantProps } from './utils/variant-props.mjs';\nimport { visualElementStore } from './store.mjs';\nimport { frame, cancelFrame } from '../frameloop/frame.mjs';\n\nconst featureNames = Object.keys(featureDefinitions);\nconst numFeatures = featureNames.length;\nconst propEventHandlers = [\n    \"AnimationStart\",\n    \"AnimationComplete\",\n    \"Update\",\n    \"BeforeLayoutMeasure\",\n    \"LayoutMeasure\",\n    \"LayoutAnimationStart\",\n    \"LayoutAnimationComplete\",\n];\nconst numVariantProps = variantProps.length;\n/**\n * A VisualElement is an imperative abstraction around UI elements such as\n * HTMLElement, SVGElement, Three.Object3D etc.\n */\nclass VisualElement {\n    constructor({ parent, props, presenceContext, reducedMotionConfig, visualState, }, options = {}) {\n        /**\n         * A reference to the current underlying Instance, e.g. a HTMLElement\n         * or Three.Mesh etc.\n         */\n        this.current = null;\n        /**\n         * A set containing references to this VisualElement's children.\n         */\n        this.children = new Set();\n        /**\n         * Determine what role this visual element should take in the variant tree.\n         */\n        this.isVariantNode = false;\n        this.isControllingVariants = false;\n        /**\n         * Decides whether this VisualElement should animate in reduced motion\n         * mode.\n         *\n         * TODO: This is currently set on every individual VisualElement but feels\n         * like it could be set globally.\n         */\n        this.shouldReduceMotion = null;\n        /**\n         * A map of all motion values attached to this visual element. Motion\n         * values are source of truth for any given animated value. A motion\n         * value might be provided externally by the component via props.\n         */\n        this.values = new Map();\n        /**\n         * Cleanup functions for active features (hover/tap/exit etc)\n         */\n        this.features = {};\n        /**\n         * A map of every subscription that binds the provided or generated\n         * motion values onChange listeners to this visual element.\n         */\n        this.valueSubscriptions = new Map();\n        /**\n         * A reference to the previously-provided motion values as returned\n         * from scrapeMotionValuesFromProps. We use the keys in here to determine\n         * if any motion values need to be removed after props are updated.\n         */\n        this.prevMotionValues = {};\n        /**\n         * An object containing a SubscriptionManager for each active event.\n         */\n        this.events = {};\n        /**\n         * An object containing an unsubscribe function for each prop event subscription.\n         * For example, every \"Update\" event can have multiple subscribers via\n         * VisualElement.on(), but only one of those can be defined via the onUpdate prop.\n         */\n        this.propEventSubscriptions = {};\n        this.notifyUpdate = () => this.notify(\"Update\", this.latestValues);\n        this.render = () => {\n            if (!this.current)\n                return;\n            this.triggerBuild();\n            this.renderInstance(this.current, this.renderState, this.props.style, this.projection);\n        };\n        this.scheduleRender = () => frame.render(this.render, false, true);\n        const { latestValues, renderState } = visualState;\n        this.latestValues = latestValues;\n        this.baseTarget = { ...latestValues };\n        this.initialValues = props.initial ? { ...latestValues } : {};\n        this.renderState = renderState;\n        this.parent = parent;\n        this.props = props;\n        this.presenceContext = presenceContext;\n        this.depth = parent ? parent.depth + 1 : 0;\n        this.reducedMotionConfig = reducedMotionConfig;\n        this.options = options;\n        this.isControllingVariants = isControllingVariants(props);\n        this.isVariantNode = isVariantNode(props);\n        if (this.isVariantNode) {\n            this.variantChildren = new Set();\n        }\n        this.manuallyAnimateOnMount = Boolean(parent && parent.current);\n        /**\n         * Any motion values that are provided to the element when created\n         * aren't yet bound to the element, as this would technically be impure.\n         * However, we iterate through the motion values and set them to the\n         * initial values for this component.\n         *\n         * TODO: This is impure and we should look at changing this to run on mount.\n         * Doing so will break some tests but this isn't neccessarily a breaking change,\n         * more a reflection of the test.\n         */\n        const { willChange, ...initialMotionValues } = this.scrapeMotionValuesFromProps(props, {});\n        for (const key in initialMotionValues) {\n            const value = initialMotionValues[key];\n            if (latestValues[key] !== undefined && isMotionValue(value)) {\n                value.set(latestValues[key], false);\n                if (isWillChangeMotionValue(willChange)) {\n                    willChange.add(key);\n                }\n            }\n        }\n    }\n    /**\n     * This method takes React props and returns found MotionValues. For example, HTML\n     * MotionValues will be found within the style prop, whereas for Three.js within attribute arrays.\n     *\n     * This isn't an abstract method as it needs calling in the constructor, but it is\n     * intended to be one.\n     */\n    scrapeMotionValuesFromProps(_props, _prevProps) {\n        return {};\n    }\n    mount(instance) {\n        this.current = instance;\n        visualElementStore.set(instance, this);\n        if (this.projection && !this.projection.instance) {\n            this.projection.mount(instance);\n        }\n        if (this.parent && this.isVariantNode && !this.isControllingVariants) {\n            this.removeFromVariantTree = this.parent.addVariantChild(this);\n        }\n        this.values.forEach((value, key) => this.bindToMotionValue(key, value));\n        if (!hasReducedMotionListener.current) {\n            initPrefersReducedMotion();\n        }\n        this.shouldReduceMotion =\n            this.reducedMotionConfig === \"never\"\n                ? false\n                : this.reducedMotionConfig === \"always\"\n                    ? true\n                    : prefersReducedMotion.current;\n        if (process.env.NODE_ENV !== \"production\") {\n            warnOnce(this.shouldReduceMotion !== true, \"You have Reduced Motion enabled on your device. Animations may not appear as expected.\");\n        }\n        if (this.parent)\n            this.parent.children.add(this);\n        this.update(this.props, this.presenceContext);\n    }\n    unmount() {\n        visualElementStore.delete(this.current);\n        this.projection && this.projection.unmount();\n        cancelFrame(this.notifyUpdate);\n        cancelFrame(this.render);\n        this.valueSubscriptions.forEach((remove) => remove());\n        this.removeFromVariantTree && this.removeFromVariantTree();\n        this.parent && this.parent.children.delete(this);\n        for (const key in this.events) {\n            this.events[key].clear();\n        }\n        for (const key in this.features) {\n            this.features[key].unmount();\n        }\n        this.current = null;\n    }\n    bindToMotionValue(key, value) {\n        const valueIsTransform = transformProps.has(key);\n        const removeOnChange = value.on(\"change\", (latestValue) => {\n            this.latestValues[key] = latestValue;\n            this.props.onUpdate &&\n                frame.update(this.notifyUpdate, false, true);\n            if (valueIsTransform && this.projection) {\n                this.projection.isTransformDirty = true;\n            }\n        });\n        const removeOnRenderRequest = value.on(\"renderRequest\", this.scheduleRender);\n        this.valueSubscriptions.set(key, () => {\n            removeOnChange();\n            removeOnRenderRequest();\n        });\n    }\n    sortNodePosition(other) {\n        /**\n         * If these nodes aren't even of the same type we can't compare their depth.\n         */\n        if (!this.current ||\n            !this.sortInstanceNodePosition ||\n            this.type !== other.type) {\n            return 0;\n        }\n        return this.sortInstanceNodePosition(this.current, other.current);\n    }\n    loadFeatures({ children, ...renderedProps }, isStrict, preloadedFeatures, initialLayoutGroupConfig) {\n        let ProjectionNodeConstructor;\n        let MeasureLayout;\n        /**\n         * If we're in development mode, check to make sure we're not rendering a motion component\n         * as a child of LazyMotion, as this will break the file-size benefits of using it.\n         */\n        if (process.env.NODE_ENV !== \"production\" &&\n            preloadedFeatures &&\n            isStrict) {\n            const strictMessage = \"You have rendered a `motion` component within a `LazyMotion` component. This will break tree shaking. Import and render a `m` component instead.\";\n            renderedProps.ignoreStrict\n                ? warning(false, strictMessage)\n                : invariant(false, strictMessage);\n        }\n        for (let i = 0; i < numFeatures; i++) {\n            const name = featureNames[i];\n            const { isEnabled, Feature: FeatureConstructor, ProjectionNode, MeasureLayout: MeasureLayoutComponent, } = featureDefinitions[name];\n            if (ProjectionNode)\n                ProjectionNodeConstructor = ProjectionNode;\n            if (isEnabled(renderedProps)) {\n                if (!this.features[name] && FeatureConstructor) {\n                    this.features[name] = new FeatureConstructor(this);\n                }\n                if (MeasureLayoutComponent) {\n                    MeasureLayout = MeasureLayoutComponent;\n                }\n            }\n        }\n        if ((this.type === \"html\" || this.type === \"svg\") &&\n            !this.projection &&\n            ProjectionNodeConstructor) {\n            this.projection = new ProjectionNodeConstructor(this.latestValues, this.parent && this.parent.projection);\n            const { layoutId, layout, drag, dragConstraints, layoutScroll, layoutRoot, } = renderedProps;\n            this.projection.setOptions({\n                layoutId,\n                layout,\n                alwaysMeasureLayout: Boolean(drag) ||\n                    (dragConstraints && isRefObject(dragConstraints)),\n                visualElement: this,\n                scheduleRender: () => this.scheduleRender(),\n                /**\n                 * TODO: Update options in an effect. This could be tricky as it'll be too late\n                 * to update by the time layout animations run.\n                 * We also need to fix this safeToRemove by linking it up to the one returned by usePresence,\n                 * ensuring it gets called if there's no potential layout animations.\n                 *\n                 */\n                animationType: typeof layout === \"string\" ? layout : \"both\",\n                initialPromotionConfig: initialLayoutGroupConfig,\n                layoutScroll,\n                layoutRoot,\n            });\n        }\n        return MeasureLayout;\n    }\n    updateFeatures() {\n        for (const key in this.features) {\n            const feature = this.features[key];\n            if (feature.isMounted) {\n                feature.update();\n            }\n            else {\n                feature.mount();\n                feature.isMounted = true;\n            }\n        }\n    }\n    triggerBuild() {\n        this.build(this.renderState, this.latestValues, this.options, this.props);\n    }\n    /**\n     * Measure the current viewport box with or without transforms.\n     * Only measures axis-aligned boxes, rotate and skew must be manually\n     * removed with a re-render to work.\n     */\n    measureViewportBox() {\n        return this.current\n            ? this.measureInstanceViewportBox(this.current, this.props)\n            : createBox();\n    }\n    getStaticValue(key) {\n        return this.latestValues[key];\n    }\n    setStaticValue(key, value) {\n        this.latestValues[key] = value;\n    }\n    /**\n     * Make a target animatable by Popmotion. For instance, if we're\n     * trying to animate width from 100px to 100vw we need to measure 100vw\n     * in pixels to determine what we really need to animate to. This is also\n     * pluggable to support Framer's custom value types like Color,\n     * and CSS variables.\n     */\n    makeTargetAnimatable(target, canMutate = true) {\n        return this.makeTargetAnimatableFromInstance(target, this.props, canMutate);\n    }\n    /**\n     * Update the provided props. Ensure any newly-added motion values are\n     * added to our map, old ones removed, and listeners updated.\n     */\n    update(props, presenceContext) {\n        if (props.transformTemplate || this.props.transformTemplate) {\n            this.scheduleRender();\n        }\n        this.prevProps = this.props;\n        this.props = props;\n        this.prevPresenceContext = this.presenceContext;\n        this.presenceContext = presenceContext;\n        /**\n         * Update prop event handlers ie onAnimationStart, onAnimationComplete\n         */\n        for (let i = 0; i < propEventHandlers.length; i++) {\n            const key = propEventHandlers[i];\n            if (this.propEventSubscriptions[key]) {\n                this.propEventSubscriptions[key]();\n                delete this.propEventSubscriptions[key];\n            }\n            const listener = props[\"on\" + key];\n            if (listener) {\n                this.propEventSubscriptions[key] = this.on(key, listener);\n            }\n        }\n        this.prevMotionValues = updateMotionValuesFromProps(this, this.scrapeMotionValuesFromProps(props, this.prevProps), this.prevMotionValues);\n        if (this.handleChildMotionValue) {\n            this.handleChildMotionValue();\n        }\n    }\n    getProps() {\n        return this.props;\n    }\n    /**\n     * Returns the variant definition with a given name.\n     */\n    getVariant(name) {\n        return this.props.variants ? this.props.variants[name] : undefined;\n    }\n    /**\n     * Returns the defined default transition on this component.\n     */\n    getDefaultTransition() {\n        return this.props.transition;\n    }\n    getTransformPagePoint() {\n        return this.props.transformPagePoint;\n    }\n    getClosestVariantNode() {\n        return this.isVariantNode\n            ? this\n            : this.parent\n                ? this.parent.getClosestVariantNode()\n                : undefined;\n    }\n    getVariantContext(startAtParent = false) {\n        if (startAtParent) {\n            return this.parent ? this.parent.getVariantContext() : undefined;\n        }\n        if (!this.isControllingVariants) {\n            const context = this.parent\n                ? this.parent.getVariantContext() || {}\n                : {};\n            if (this.props.initial !== undefined) {\n                context.initial = this.props.initial;\n            }\n            return context;\n        }\n        const context = {};\n        for (let i = 0; i < numVariantProps; i++) {\n            const name = variantProps[i];\n            const prop = this.props[name];\n            if (isVariantLabel(prop) || prop === false) {\n                context[name] = prop;\n            }\n        }\n        return context;\n    }\n    /**\n     * Add a child visual element to our set of children.\n     */\n    addVariantChild(child) {\n        const closestVariantNode = this.getClosestVariantNode();\n        if (closestVariantNode) {\n            closestVariantNode.variantChildren &&\n                closestVariantNode.variantChildren.add(child);\n            return () => closestVariantNode.variantChildren.delete(child);\n        }\n    }\n    /**\n     * Add a motion value and bind it to this visual element.\n     */\n    addValue(key, value) {\n        // Remove existing value if it exists\n        if (value !== this.values.get(key)) {\n            this.removeValue(key);\n            this.bindToMotionValue(key, value);\n        }\n        this.values.set(key, value);\n        this.latestValues[key] = value.get();\n    }\n    /**\n     * Remove a motion value and unbind any active subscriptions.\n     */\n    removeValue(key) {\n        this.values.delete(key);\n        const unsubscribe = this.valueSubscriptions.get(key);\n        if (unsubscribe) {\n            unsubscribe();\n            this.valueSubscriptions.delete(key);\n        }\n        delete this.latestValues[key];\n        this.removeValueFromRenderState(key, this.renderState);\n    }\n    /**\n     * Check whether we have a motion value for this key\n     */\n    hasValue(key) {\n        return this.values.has(key);\n    }\n    getValue(key, defaultValue) {\n        if (this.props.values && this.props.values[key]) {\n            return this.props.values[key];\n        }\n        let value = this.values.get(key);\n        if (value === undefined && defaultValue !== undefined) {\n            value = motionValue(defaultValue, { owner: this });\n            this.addValue(key, value);\n        }\n        return value;\n    }\n    /**\n     * If we're trying to animate to a previously unencountered value,\n     * we need to check for it in our state and as a last resort read it\n     * directly from the instance (which might have performance implications).\n     */\n    readValue(key) {\n        var _a;\n        return this.latestValues[key] !== undefined || !this.current\n            ? this.latestValues[key]\n            : (_a = this.getBaseTargetFromProps(this.props, key)) !== null && _a !== void 0 ? _a : this.readValueFromInstance(this.current, key, this.options);\n    }\n    /**\n     * Set the base target to later animate back to. This is currently\n     * only hydrated on creation and when we first read a value.\n     */\n    setBaseTarget(key, value) {\n        this.baseTarget[key] = value;\n    }\n    /**\n     * Find the base target for a value thats been removed from all animation\n     * props.\n     */\n    getBaseTarget(key) {\n        var _a;\n        const { initial } = this.props;\n        const valueFromInitial = typeof initial === \"string\" || typeof initial === \"object\"\n            ? (_a = resolveVariantFromProps(this.props, initial)) === null || _a === void 0 ? void 0 : _a[key]\n            : undefined;\n        /**\n         * If this value still exists in the current initial variant, read that.\n         */\n        if (initial && valueFromInitial !== undefined) {\n            return valueFromInitial;\n        }\n        /**\n         * Alternatively, if this VisualElement config has defined a getBaseTarget\n         * so we can read the value from an alternative source, try that.\n         */\n        const target = this.getBaseTargetFromProps(this.props, key);\n        if (target !== undefined && !isMotionValue(target))\n            return target;\n        /**\n         * If the value was initially defined on initial, but it doesn't any more,\n         * return undefined. Otherwise return the value as initially read from the DOM.\n         */\n        return this.initialValues[key] !== undefined &&\n            valueFromInitial === undefined\n            ? undefined\n            : this.baseTarget[key];\n    }\n    on(eventName, callback) {\n        if (!this.events[eventName]) {\n            this.events[eventName] = new SubscriptionManager();\n        }\n        return this.events[eventName].add(callback);\n    }\n    notify(eventName, ...args) {\n        if (this.events[eventName]) {\n            this.events[eventName].notify(...args);\n        }\n    }\n}\n\nexport { VisualElement };\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,SAAS,QAAQ,qBAAqB;AACxD,SAASC,SAAS,QAAQ,mCAAmC;AAC7D,SAASC,WAAW,QAAQ,4BAA4B;AACxD,SAASC,wBAAwB,QAAQ,mCAAmC;AAC5E,SAASC,wBAAwB,EAAEC,oBAAoB,QAAQ,mCAAmC;AAClG,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,uBAAuB,QAAQ,iCAAiC;AACzE,SAASC,aAAa,QAAQ,oCAAoC;AAClE,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,qBAAqB,EAAEC,aAAa,QAAQ,qCAAqC;AAC1F,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,2BAA2B,QAAQ,2BAA2B;AACvE,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,kBAAkB,QAAQ,oCAAoC;AACvE,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,kBAAkB,QAAQ,aAAa;AAChD,SAASC,KAAK,EAAEC,WAAW,QAAQ,wBAAwB;AAE3D,MAAMC,YAAY,GAAGC,MAAM,CAACC,IAAI,CAACP,kBAAkB,CAAC;AACpD,MAAMQ,WAAW,GAAGH,YAAY,CAACI,MAAM;AACvC,MAAMC,iBAAiB,GAAG,CACtB,gBAAgB,EAChB,mBAAmB,EACnB,QAAQ,EACR,qBAAqB,EACrB,eAAe,EACf,sBAAsB,EACtB,yBAAyB,CAC5B;AACD,MAAMC,eAAe,GAAGV,YAAY,CAACQ,MAAM;AAC3C;AACA;AACA;AACA;AACA,MAAMG,aAAa,CAAC;EAChBC,WAAWA,CAAC;IAAEC,MAAM;IAAEC,KAAK;IAAEC,eAAe;IAAEC,mBAAmB;IAAEC;EAAa,CAAC,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC7F;AACR;AACA;AACA;IACQ,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;IACzB;AACR;AACA;IACQ,IAAI,CAAC3B,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACD,qBAAqB,GAAG,KAAK;IAClC;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC6B,kBAAkB,GAAG,IAAI;IAC9B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC;IACvB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;IAClB;AACR;AACA;AACA;IACQ,IAAI,CAACC,kBAAkB,GAAG,IAAIF,GAAG,CAAC,CAAC;IACnC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACG,gBAAgB,GAAG,CAAC,CAAC;IAC1B;AACR;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;IAChB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,sBAAsB,GAAG,CAAC,CAAC;IAChC,IAAI,CAACC,YAAY,GAAG,MAAM,IAAI,CAACC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAACC,YAAY,CAAC;IAClE,IAAI,CAACC,MAAM,GAAG,MAAM;MAChB,IAAI,CAAC,IAAI,CAACd,OAAO,EACb;MACJ,IAAI,CAACe,YAAY,CAAC,CAAC;MACnB,IAAI,CAACC,cAAc,CAAC,IAAI,CAAChB,OAAO,EAAE,IAAI,CAACiB,WAAW,EAAE,IAAI,CAACtB,KAAK,CAACuB,KAAK,EAAE,IAAI,CAACC,UAAU,CAAC;IAC1F,CAAC;IACD,IAAI,CAACC,cAAc,GAAG,MAAMrC,KAAK,CAAC+B,MAAM,CAAC,IAAI,CAACA,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC;IAClE,MAAM;MAAED,YAAY;MAAEI;IAAY,CAAC,GAAGnB,WAAW;IACjD,IAAI,CAACe,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACQ,UAAU,GAAG;MAAE,GAAGR;IAAa,CAAC;IACrC,IAAI,CAACS,aAAa,GAAG3B,KAAK,CAAC4B,OAAO,GAAG;MAAE,GAAGV;IAAa,CAAC,GAAG,CAAC,CAAC;IAC7D,IAAI,CAACI,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACvB,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAAC4B,KAAK,GAAG9B,MAAM,GAAGA,MAAM,CAAC8B,KAAK,GAAG,CAAC,GAAG,CAAC;IAC1C,IAAI,CAAC3B,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACE,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACzB,qBAAqB,GAAGA,qBAAqB,CAACqB,KAAK,CAAC;IACzD,IAAI,CAACpB,aAAa,GAAGA,aAAa,CAACoB,KAAK,CAAC;IACzC,IAAI,IAAI,CAACpB,aAAa,EAAE;MACpB,IAAI,CAACkD,eAAe,GAAG,IAAIvB,GAAG,CAAC,CAAC;IACpC;IACA,IAAI,CAACwB,sBAAsB,GAAGC,OAAO,CAACjC,MAAM,IAAIA,MAAM,CAACM,OAAO,CAAC;IAC/D;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAM;MAAE4B,UAAU;MAAE,GAAGC;IAAoB,CAAC,GAAG,IAAI,CAACC,2BAA2B,CAACnC,KAAK,EAAE,CAAC,CAAC,CAAC;IAC1F,KAAK,MAAMoC,GAAG,IAAIF,mBAAmB,EAAE;MACnC,MAAMG,KAAK,GAAGH,mBAAmB,CAACE,GAAG,CAAC;MACtC,IAAIlB,YAAY,CAACkB,GAAG,CAAC,KAAKE,SAAS,IAAI7D,aAAa,CAAC4D,KAAK,CAAC,EAAE;QACzDA,KAAK,CAACE,GAAG,CAACrB,YAAY,CAACkB,GAAG,CAAC,EAAE,KAAK,CAAC;QACnC,IAAI5D,uBAAuB,CAACyD,UAAU,CAAC,EAAE;UACrCA,UAAU,CAACO,GAAG,CAACJ,GAAG,CAAC;QACvB;MACJ;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACID,2BAA2BA,CAACM,MAAM,EAAEC,UAAU,EAAE;IAC5C,OAAO,CAAC,CAAC;EACb;EACAC,KAAKA,CAACC,QAAQ,EAAE;IACZ,IAAI,CAACvC,OAAO,GAAGuC,QAAQ;IACvBzD,kBAAkB,CAACoD,GAAG,CAACK,QAAQ,EAAE,IAAI,CAAC;IACtC,IAAI,IAAI,CAACpB,UAAU,IAAI,CAAC,IAAI,CAACA,UAAU,CAACoB,QAAQ,EAAE;MAC9C,IAAI,CAACpB,UAAU,CAACmB,KAAK,CAACC,QAAQ,CAAC;IACnC;IACA,IAAI,IAAI,CAAC7C,MAAM,IAAI,IAAI,CAACnB,aAAa,IAAI,CAAC,IAAI,CAACD,qBAAqB,EAAE;MAClE,IAAI,CAACkE,qBAAqB,GAAG,IAAI,CAAC9C,MAAM,CAAC+C,eAAe,CAAC,IAAI,CAAC;IAClE;IACA,IAAI,CAACrC,MAAM,CAACsC,OAAO,CAAC,CAACV,KAAK,EAAED,GAAG,KAAK,IAAI,CAACY,iBAAiB,CAACZ,GAAG,EAAEC,KAAK,CAAC,CAAC;IACvE,IAAI,CAACjE,wBAAwB,CAACiC,OAAO,EAAE;MACnClC,wBAAwB,CAAC,CAAC;IAC9B;IACA,IAAI,CAACqC,kBAAkB,GACnB,IAAI,CAACN,mBAAmB,KAAK,OAAO,GAC9B,KAAK,GACL,IAAI,CAACA,mBAAmB,KAAK,QAAQ,GACjC,IAAI,GACJ7B,oBAAoB,CAACgC,OAAO;IAC1C,IAAI4C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACvCnE,QAAQ,CAAC,IAAI,CAACwB,kBAAkB,KAAK,IAAI,EAAE,wFAAwF,CAAC;IACxI;IACA,IAAI,IAAI,CAACT,MAAM,EACX,IAAI,CAACA,MAAM,CAACO,QAAQ,CAACkC,GAAG,CAAC,IAAI,CAAC;IAClC,IAAI,CAACY,MAAM,CAAC,IAAI,CAACpD,KAAK,EAAE,IAAI,CAACC,eAAe,CAAC;EACjD;EACAoD,OAAOA,CAAA,EAAG;IACNlE,kBAAkB,CAACmE,MAAM,CAAC,IAAI,CAACjD,OAAO,CAAC;IACvC,IAAI,CAACmB,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC6B,OAAO,CAAC,CAAC;IAC5ChE,WAAW,CAAC,IAAI,CAAC2B,YAAY,CAAC;IAC9B3B,WAAW,CAAC,IAAI,CAAC8B,MAAM,CAAC;IACxB,IAAI,CAACP,kBAAkB,CAACmC,OAAO,CAAEQ,MAAM,IAAKA,MAAM,CAAC,CAAC,CAAC;IACrD,IAAI,CAACV,qBAAqB,IAAI,IAAI,CAACA,qBAAqB,CAAC,CAAC;IAC1D,IAAI,CAAC9C,MAAM,IAAI,IAAI,CAACA,MAAM,CAACO,QAAQ,CAACgD,MAAM,CAAC,IAAI,CAAC;IAChD,KAAK,MAAMlB,GAAG,IAAI,IAAI,CAACtB,MAAM,EAAE;MAC3B,IAAI,CAACA,MAAM,CAACsB,GAAG,CAAC,CAACoB,KAAK,CAAC,CAAC;IAC5B;IACA,KAAK,MAAMpB,GAAG,IAAI,IAAI,CAACzB,QAAQ,EAAE;MAC7B,IAAI,CAACA,QAAQ,CAACyB,GAAG,CAAC,CAACiB,OAAO,CAAC,CAAC;IAChC;IACA,IAAI,CAAChD,OAAO,GAAG,IAAI;EACvB;EACA2C,iBAAiBA,CAACZ,GAAG,EAAEC,KAAK,EAAE;IAC1B,MAAMoB,gBAAgB,GAAG/E,cAAc,CAACgF,GAAG,CAACtB,GAAG,CAAC;IAChD,MAAMuB,cAAc,GAAGtB,KAAK,CAACuB,EAAE,CAAC,QAAQ,EAAGC,WAAW,IAAK;MACvD,IAAI,CAAC3C,YAAY,CAACkB,GAAG,CAAC,GAAGyB,WAAW;MACpC,IAAI,CAAC7D,KAAK,CAAC8D,QAAQ,IACf1E,KAAK,CAACgE,MAAM,CAAC,IAAI,CAACpC,YAAY,EAAE,KAAK,EAAE,IAAI,CAAC;MAChD,IAAIyC,gBAAgB,IAAI,IAAI,CAACjC,UAAU,EAAE;QACrC,IAAI,CAACA,UAAU,CAACuC,gBAAgB,GAAG,IAAI;MAC3C;IACJ,CAAC,CAAC;IACF,MAAMC,qBAAqB,GAAG3B,KAAK,CAACuB,EAAE,CAAC,eAAe,EAAE,IAAI,CAACnC,cAAc,CAAC;IAC5E,IAAI,CAACb,kBAAkB,CAAC2B,GAAG,CAACH,GAAG,EAAE,MAAM;MACnCuB,cAAc,CAAC,CAAC;MAChBK,qBAAqB,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACAC,gBAAgBA,CAACC,KAAK,EAAE;IACpB;AACR;AACA;IACQ,IAAI,CAAC,IAAI,CAAC7D,OAAO,IACb,CAAC,IAAI,CAAC8D,wBAAwB,IAC9B,IAAI,CAACC,IAAI,KAAKF,KAAK,CAACE,IAAI,EAAE;MAC1B,OAAO,CAAC;IACZ;IACA,OAAO,IAAI,CAACD,wBAAwB,CAAC,IAAI,CAAC9D,OAAO,EAAE6D,KAAK,CAAC7D,OAAO,CAAC;EACrE;EACAgE,YAAYA,CAAC;IAAE/D,QAAQ;IAAE,GAAGgE;EAAc,CAAC,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,wBAAwB,EAAE;IAChG,IAAIC,yBAAyB;IAC7B,IAAIC,aAAa;IACjB;AACR;AACA;AACA;IACQ,IAAI1B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACrCqB,iBAAiB,IACjBD,QAAQ,EAAE;MACV,MAAMK,aAAa,GAAG,kJAAkJ;MACxKN,aAAa,CAACO,YAAY,GACpB9G,OAAO,CAAC,KAAK,EAAE6G,aAAa,CAAC,GAC7B5G,SAAS,CAAC,KAAK,EAAE4G,aAAa,CAAC;IACzC;IACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrF,WAAW,EAAEqF,CAAC,EAAE,EAAE;MAClC,MAAMC,IAAI,GAAGzF,YAAY,CAACwF,CAAC,CAAC;MAC5B,MAAM;QAAEE,SAAS;QAAEC,OAAO,EAAEC,kBAAkB;QAAEC,cAAc;QAAER,aAAa,EAAES;MAAwB,CAAC,GAAGnG,kBAAkB,CAAC8F,IAAI,CAAC;MACnI,IAAII,cAAc,EACdT,yBAAyB,GAAGS,cAAc;MAC9C,IAAIH,SAAS,CAACV,aAAa,CAAC,EAAE;QAC1B,IAAI,CAAC,IAAI,CAAC3D,QAAQ,CAACoE,IAAI,CAAC,IAAIG,kBAAkB,EAAE;UAC5C,IAAI,CAACvE,QAAQ,CAACoE,IAAI,CAAC,GAAG,IAAIG,kBAAkB,CAAC,IAAI,CAAC;QACtD;QACA,IAAIE,sBAAsB,EAAE;UACxBT,aAAa,GAAGS,sBAAsB;QAC1C;MACJ;IACJ;IACA,IAAI,CAAC,IAAI,CAAChB,IAAI,KAAK,MAAM,IAAI,IAAI,CAACA,IAAI,KAAK,KAAK,KAC5C,CAAC,IAAI,CAAC5C,UAAU,IAChBkD,yBAAyB,EAAE;MAC3B,IAAI,CAAClD,UAAU,GAAG,IAAIkD,yBAAyB,CAAC,IAAI,CAACxD,YAAY,EAAE,IAAI,CAACnB,MAAM,IAAI,IAAI,CAACA,MAAM,CAACyB,UAAU,CAAC;MACzG,MAAM;QAAE6D,QAAQ;QAAEC,MAAM;QAAEC,IAAI;QAAEC,eAAe;QAAEC,YAAY;QAAEC;MAAY,CAAC,GAAGpB,aAAa;MAC5F,IAAI,CAAC9C,UAAU,CAACmE,UAAU,CAAC;QACvBN,QAAQ;QACRC,MAAM;QACNM,mBAAmB,EAAE5D,OAAO,CAACuD,IAAI,CAAC,IAC7BC,eAAe,IAAItH,WAAW,CAACsH,eAAe,CAAE;QACrDK,aAAa,EAAE,IAAI;QACnBpE,cAAc,EAAEA,CAAA,KAAM,IAAI,CAACA,cAAc,CAAC,CAAC;QAC3C;AAChB;AACA;AACA;AACA;AACA;AACA;QACgBqE,aAAa,EAAE,OAAOR,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAG,MAAM;QAC3DS,sBAAsB,EAAEtB,wBAAwB;QAChDgB,YAAY;QACZC;MACJ,CAAC,CAAC;IACN;IACA,OAAOf,aAAa;EACxB;EACAqB,cAAcA,CAAA,EAAG;IACb,KAAK,MAAM5D,GAAG,IAAI,IAAI,CAACzB,QAAQ,EAAE;MAC7B,MAAMsF,OAAO,GAAG,IAAI,CAACtF,QAAQ,CAACyB,GAAG,CAAC;MAClC,IAAI6D,OAAO,CAACC,SAAS,EAAE;QACnBD,OAAO,CAAC7C,MAAM,CAAC,CAAC;MACpB,CAAC,MACI;QACD6C,OAAO,CAACtD,KAAK,CAAC,CAAC;QACfsD,OAAO,CAACC,SAAS,GAAG,IAAI;MAC5B;IACJ;EACJ;EACA9E,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC+E,KAAK,CAAC,IAAI,CAAC7E,WAAW,EAAE,IAAI,CAACJ,YAAY,EAAE,IAAI,CAACd,OAAO,EAAE,IAAI,CAACJ,KAAK,CAAC;EAC7E;EACA;AACJ;AACA;AACA;AACA;EACIoG,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAC/F,OAAO,GACb,IAAI,CAACgG,0BAA0B,CAAC,IAAI,CAAChG,OAAO,EAAE,IAAI,CAACL,KAAK,CAAC,GACzD/B,SAAS,CAAC,CAAC;EACrB;EACAqI,cAAcA,CAAClE,GAAG,EAAE;IAChB,OAAO,IAAI,CAAClB,YAAY,CAACkB,GAAG,CAAC;EACjC;EACAmE,cAAcA,CAACnE,GAAG,EAAEC,KAAK,EAAE;IACvB,IAAI,CAACnB,YAAY,CAACkB,GAAG,CAAC,GAAGC,KAAK;EAClC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACImE,oBAAoBA,CAACC,MAAM,EAAEC,SAAS,GAAG,IAAI,EAAE;IAC3C,OAAO,IAAI,CAACC,gCAAgC,CAACF,MAAM,EAAE,IAAI,CAACzG,KAAK,EAAE0G,SAAS,CAAC;EAC/E;EACA;AACJ;AACA;AACA;EACItD,MAAMA,CAACpD,KAAK,EAAEC,eAAe,EAAE;IAC3B,IAAID,KAAK,CAAC4G,iBAAiB,IAAI,IAAI,CAAC5G,KAAK,CAAC4G,iBAAiB,EAAE;MACzD,IAAI,CAACnF,cAAc,CAAC,CAAC;IACzB;IACA,IAAI,CAACoF,SAAS,GAAG,IAAI,CAAC7G,KAAK;IAC3B,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC8G,mBAAmB,GAAG,IAAI,CAAC7G,eAAe;IAC/C,IAAI,CAACA,eAAe,GAAGA,eAAe;IACtC;AACR;AACA;IACQ,KAAK,IAAI6E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnF,iBAAiB,CAACD,MAAM,EAAEoF,CAAC,EAAE,EAAE;MAC/C,MAAM1C,GAAG,GAAGzC,iBAAiB,CAACmF,CAAC,CAAC;MAChC,IAAI,IAAI,CAAC/D,sBAAsB,CAACqB,GAAG,CAAC,EAAE;QAClC,IAAI,CAACrB,sBAAsB,CAACqB,GAAG,CAAC,CAAC,CAAC;QAClC,OAAO,IAAI,CAACrB,sBAAsB,CAACqB,GAAG,CAAC;MAC3C;MACA,MAAM2E,QAAQ,GAAG/G,KAAK,CAAC,IAAI,GAAGoC,GAAG,CAAC;MAClC,IAAI2E,QAAQ,EAAE;QACV,IAAI,CAAChG,sBAAsB,CAACqB,GAAG,CAAC,GAAG,IAAI,CAACwB,EAAE,CAACxB,GAAG,EAAE2E,QAAQ,CAAC;MAC7D;IACJ;IACA,IAAI,CAAClG,gBAAgB,GAAG/B,2BAA2B,CAAC,IAAI,EAAE,IAAI,CAACqD,2BAA2B,CAACnC,KAAK,EAAE,IAAI,CAAC6G,SAAS,CAAC,EAAE,IAAI,CAAChG,gBAAgB,CAAC;IACzI,IAAI,IAAI,CAACmG,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAAC,CAAC;IACjC;EACJ;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACjH,KAAK;EACrB;EACA;AACJ;AACA;EACIkH,UAAUA,CAACnC,IAAI,EAAE;IACb,OAAO,IAAI,CAAC/E,KAAK,CAACmH,QAAQ,GAAG,IAAI,CAACnH,KAAK,CAACmH,QAAQ,CAACpC,IAAI,CAAC,GAAGzC,SAAS;EACtE;EACA;AACJ;AACA;EACI8E,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACpH,KAAK,CAACqH,UAAU;EAChC;EACAC,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACtH,KAAK,CAACuH,kBAAkB;EACxC;EACAC,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAAC5I,aAAa,GACnB,IAAI,GACJ,IAAI,CAACmB,MAAM,GACP,IAAI,CAACA,MAAM,CAACyH,qBAAqB,CAAC,CAAC,GACnClF,SAAS;EACvB;EACAmF,iBAAiBA,CAACC,aAAa,GAAG,KAAK,EAAE;IACrC,IAAIA,aAAa,EAAE;MACf,OAAO,IAAI,CAAC3H,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC0H,iBAAiB,CAAC,CAAC,GAAGnF,SAAS;IACpE;IACA,IAAI,CAAC,IAAI,CAAC3D,qBAAqB,EAAE;MAC7B,MAAMgJ,OAAO,GAAG,IAAI,CAAC5H,MAAM,GACrB,IAAI,CAACA,MAAM,CAAC0H,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC,GACrC,CAAC,CAAC;MACR,IAAI,IAAI,CAACzH,KAAK,CAAC4B,OAAO,KAAKU,SAAS,EAAE;QAClCqF,OAAO,CAAC/F,OAAO,GAAG,IAAI,CAAC5B,KAAK,CAAC4B,OAAO;MACxC;MACA,OAAO+F,OAAO;IAClB;IACA,MAAMA,OAAO,GAAG,CAAC,CAAC;IAClB,KAAK,IAAI7C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlF,eAAe,EAAEkF,CAAC,EAAE,EAAE;MACtC,MAAMC,IAAI,GAAG7F,YAAY,CAAC4F,CAAC,CAAC;MAC5B,MAAM8C,IAAI,GAAG,IAAI,CAAC5H,KAAK,CAAC+E,IAAI,CAAC;MAC7B,IAAIlG,cAAc,CAAC+I,IAAI,CAAC,IAAIA,IAAI,KAAK,KAAK,EAAE;QACxCD,OAAO,CAAC5C,IAAI,CAAC,GAAG6C,IAAI;MACxB;IACJ;IACA,OAAOD,OAAO;EAClB;EACA;AACJ;AACA;EACI7E,eAAeA,CAAC+E,KAAK,EAAE;IACnB,MAAMC,kBAAkB,GAAG,IAAI,CAACN,qBAAqB,CAAC,CAAC;IACvD,IAAIM,kBAAkB,EAAE;MACpBA,kBAAkB,CAAChG,eAAe,IAC9BgG,kBAAkB,CAAChG,eAAe,CAACU,GAAG,CAACqF,KAAK,CAAC;MACjD,OAAO,MAAMC,kBAAkB,CAAChG,eAAe,CAACwB,MAAM,CAACuE,KAAK,CAAC;IACjE;EACJ;EACA;AACJ;AACA;EACIE,QAAQA,CAAC3F,GAAG,EAAEC,KAAK,EAAE;IACjB;IACA,IAAIA,KAAK,KAAK,IAAI,CAAC5B,MAAM,CAACuH,GAAG,CAAC5F,GAAG,CAAC,EAAE;MAChC,IAAI,CAAC6F,WAAW,CAAC7F,GAAG,CAAC;MACrB,IAAI,CAACY,iBAAiB,CAACZ,GAAG,EAAEC,KAAK,CAAC;IACtC;IACA,IAAI,CAAC5B,MAAM,CAAC8B,GAAG,CAACH,GAAG,EAAEC,KAAK,CAAC;IAC3B,IAAI,CAACnB,YAAY,CAACkB,GAAG,CAAC,GAAGC,KAAK,CAAC2F,GAAG,CAAC,CAAC;EACxC;EACA;AACJ;AACA;EACIC,WAAWA,CAAC7F,GAAG,EAAE;IACb,IAAI,CAAC3B,MAAM,CAAC6C,MAAM,CAAClB,GAAG,CAAC;IACvB,MAAM8F,WAAW,GAAG,IAAI,CAACtH,kBAAkB,CAACoH,GAAG,CAAC5F,GAAG,CAAC;IACpD,IAAI8F,WAAW,EAAE;MACbA,WAAW,CAAC,CAAC;MACb,IAAI,CAACtH,kBAAkB,CAAC0C,MAAM,CAAClB,GAAG,CAAC;IACvC;IACA,OAAO,IAAI,CAAClB,YAAY,CAACkB,GAAG,CAAC;IAC7B,IAAI,CAAC+F,0BAA0B,CAAC/F,GAAG,EAAE,IAAI,CAACd,WAAW,CAAC;EAC1D;EACA;AACJ;AACA;EACI8G,QAAQA,CAAChG,GAAG,EAAE;IACV,OAAO,IAAI,CAAC3B,MAAM,CAACiD,GAAG,CAACtB,GAAG,CAAC;EAC/B;EACAiG,QAAQA,CAACjG,GAAG,EAAEkG,YAAY,EAAE;IACxB,IAAI,IAAI,CAACtI,KAAK,CAACS,MAAM,IAAI,IAAI,CAACT,KAAK,CAACS,MAAM,CAAC2B,GAAG,CAAC,EAAE;MAC7C,OAAO,IAAI,CAACpC,KAAK,CAACS,MAAM,CAAC2B,GAAG,CAAC;IACjC;IACA,IAAIC,KAAK,GAAG,IAAI,CAAC5B,MAAM,CAACuH,GAAG,CAAC5F,GAAG,CAAC;IAChC,IAAIC,KAAK,KAAKC,SAAS,IAAIgG,YAAY,KAAKhG,SAAS,EAAE;MACnDD,KAAK,GAAG9D,WAAW,CAAC+J,YAAY,EAAE;QAAEC,KAAK,EAAE;MAAK,CAAC,CAAC;MAClD,IAAI,CAACR,QAAQ,CAAC3F,GAAG,EAAEC,KAAK,CAAC;IAC7B;IACA,OAAOA,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;EACImG,SAASA,CAACpG,GAAG,EAAE;IACX,IAAIqG,EAAE;IACN,OAAO,IAAI,CAACvH,YAAY,CAACkB,GAAG,CAAC,KAAKE,SAAS,IAAI,CAAC,IAAI,CAACjC,OAAO,GACtD,IAAI,CAACa,YAAY,CAACkB,GAAG,CAAC,GACtB,CAACqG,EAAE,GAAG,IAAI,CAACC,sBAAsB,CAAC,IAAI,CAAC1I,KAAK,EAAEoC,GAAG,CAAC,MAAM,IAAI,IAAIqG,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI,CAACE,qBAAqB,CAAC,IAAI,CAACtI,OAAO,EAAE+B,GAAG,EAAE,IAAI,CAAChC,OAAO,CAAC;EAC1J;EACA;AACJ;AACA;AACA;EACIwI,aAAaA,CAACxG,GAAG,EAAEC,KAAK,EAAE;IACtB,IAAI,CAACX,UAAU,CAACU,GAAG,CAAC,GAAGC,KAAK;EAChC;EACA;AACJ;AACA;AACA;EACIwG,aAAaA,CAACzG,GAAG,EAAE;IACf,IAAIqG,EAAE;IACN,MAAM;MAAE7G;IAAQ,CAAC,GAAG,IAAI,CAAC5B,KAAK;IAC9B,MAAM8I,gBAAgB,GAAG,OAAOlH,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,KAAK,QAAQ,GAC7E,CAAC6G,EAAE,GAAG1J,uBAAuB,CAAC,IAAI,CAACiB,KAAK,EAAE4B,OAAO,CAAC,MAAM,IAAI,IAAI6G,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACrG,GAAG,CAAC,GAChGE,SAAS;IACf;AACR;AACA;IACQ,IAAIV,OAAO,IAAIkH,gBAAgB,KAAKxG,SAAS,EAAE;MAC3C,OAAOwG,gBAAgB;IAC3B;IACA;AACR;AACA;AACA;IACQ,MAAMrC,MAAM,GAAG,IAAI,CAACiC,sBAAsB,CAAC,IAAI,CAAC1I,KAAK,EAAEoC,GAAG,CAAC;IAC3D,IAAIqE,MAAM,KAAKnE,SAAS,IAAI,CAAC7D,aAAa,CAACgI,MAAM,CAAC,EAC9C,OAAOA,MAAM;IACjB;AACR;AACA;AACA;IACQ,OAAO,IAAI,CAAC9E,aAAa,CAACS,GAAG,CAAC,KAAKE,SAAS,IACxCwG,gBAAgB,KAAKxG,SAAS,GAC5BA,SAAS,GACT,IAAI,CAACZ,UAAU,CAACU,GAAG,CAAC;EAC9B;EACAwB,EAAEA,CAACmF,SAAS,EAAEC,QAAQ,EAAE;IACpB,IAAI,CAAC,IAAI,CAAClI,MAAM,CAACiI,SAAS,CAAC,EAAE;MACzB,IAAI,CAACjI,MAAM,CAACiI,SAAS,CAAC,GAAG,IAAIzK,mBAAmB,CAAC,CAAC;IACtD;IACA,OAAO,IAAI,CAACwC,MAAM,CAACiI,SAAS,CAAC,CAACvG,GAAG,CAACwG,QAAQ,CAAC;EAC/C;EACA/H,MAAMA,CAAC8H,SAAS,EAAE,GAAGE,IAAI,EAAE;IACvB,IAAI,IAAI,CAACnI,MAAM,CAACiI,SAAS,CAAC,EAAE;MACxB,IAAI,CAACjI,MAAM,CAACiI,SAAS,CAAC,CAAC9H,MAAM,CAAC,GAAGgI,IAAI,CAAC;IAC1C;EACJ;AACJ;AAEA,SAASpJ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}