{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0634\\u0631\\u0648\\u0639\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { Toaster } from 'react-hot-toast';\n// Services\nimport { authService } from './services/authService';\n\n// Components\nimport LoadingSpinner from './components/common/LoadingSpinner';\nimport LoginPage from './pages/LoginPage';\nimport AdminDashboard from './pages/admin/AdminDashboard';\nimport StudentDashboard from './pages/student/StudentDashboard';\nimport AIAssistant from './components/AIAssistant/AIAssistant';\n\n// Types\n\n// Styles\nimport './App.css';\n\n// Helper functions to check user types\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst isAdmin = user => {\n  return user.role === 'admin';\n};\nconst isStudent = user => {\n  return user.role === 'student';\n};\nfunction App() {\n  _s();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [firebaseUser, setFirebaseUser] = useState(null);\n  useEffect(() => {\n    const unsubscribe = authService.onAuthStateChange(async firebaseUser => {\n      setFirebaseUser(firebaseUser);\n      if (firebaseUser) {\n        // User is signed in, get user data\n        try {\n          // This would be implemented based on your auth logic\n          // For now, we'll handle it in the login components\n        } catch (error) {\n          console.error('Error getting user data:', error);\n        }\n      } else {\n        setUser(null);\n      }\n      setLoading(false);\n    });\n    return () => unsubscribe();\n  }, []);\n  const handleLogin = userData => {\n    setUser(userData);\n  };\n  const handleLogout = async () => {\n    try {\n      await authService.logout();\n      setUser(null);\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App min-h-screen bg-gray-50\",\n    dir: \"rtl\",\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: [/*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: user ? /*#__PURE__*/_jsxDEV(Navigate, {\n            to: user.role === 'admin' ? '/admin' : '/student',\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(LoginPage, {\n            onLogin: handleLogin\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin/*\",\n          element: user && isAdmin(user) ? /*#__PURE__*/_jsxDEV(AdminDashboard, {\n            user: user,\n            onLogout: handleLogout\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/login\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/student/*\",\n          element: user && isStudent(user) ? /*#__PURE__*/_jsxDEV(StudentDashboard, {\n            user: user,\n            onLogout: handleLogout\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/login\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: user ? user.role === 'admin' ? '/admin' : '/student' : '/login',\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), user && user.role === 'student' && /*#__PURE__*/_jsxDEV(AIAssistant, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 45\n      }, this), /*#__PURE__*/_jsxDEV(Toaster, {\n        position: \"top-center\",\n        toastOptions: {\n          duration: 4000,\n          style: {\n            background: '#363636',\n            color: '#fff',\n            fontFamily: 'Cairo, sans-serif',\n            direction: 'rtl'\n          },\n          success: {\n            style: {\n              background: '#10b981'\n            }\n          },\n          error: {\n            style: {\n              background: '#ef4444'\n            }\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 76,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"i0dpnYEwBVuTrzEGBDFjNyGeOGA=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "Toaster", "authService", "LoadingSpinner", "LoginPage", "AdminDashboard", "StudentDashboard", "AIAssistant", "jsxDEV", "_jsxDEV", "isAdmin", "user", "role", "isStudent", "App", "_s", "setUser", "loading", "setLoading", "firebaseUser", "setFirebaseUser", "unsubscribe", "onAuthStateChange", "error", "console", "handleLogin", "userData", "handleLogout", "logout", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "dir", "children", "path", "element", "to", "replace", "onLogin", "onLogout", "position", "toastOptions", "duration", "style", "background", "color", "fontFamily", "direction", "success", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/App.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { Toaster } from 'react-hot-toast';\nimport { User as FirebaseUser } from 'firebase/auth';\n\n// Services\nimport { authService } from './services/authService';\n\n// Components\nimport LoadingSpinner from './components/common/LoadingSpinner';\nimport LoginPage from './pages/LoginPage';\nimport AdminDashboard from './pages/admin/AdminDashboard';\nimport StudentDashboard from './pages/student/StudentDashboard';\nimport AIAssistant from './components/AIAssistant/AIAssistant';\n\n// Types\nimport { User, Admin, Student } from './types';\n\n// Styles\nimport './App.css';\n\n// Helper functions to check user types\nconst isAdmin = (user: User): boolean => {\n  return user.role === 'admin';\n};\n\nconst isStudent = (user: User): boolean => {\n  return user.role === 'student';\n};\n\nfunction App() {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [firebaseUser, setFirebaseUser] = useState<FirebaseUser | null>(null);\n\n  useEffect(() => {\n    const unsubscribe = authService.onAuthStateChange(async (firebaseUser) => {\n      setFirebaseUser(firebaseUser);\n      \n      if (firebaseUser) {\n        // User is signed in, get user data\n        try {\n          // This would be implemented based on your auth logic\n          // For now, we'll handle it in the login components\n        } catch (error) {\n          console.error('Error getting user data:', error);\n        }\n      } else {\n        setUser(null);\n      }\n      \n      setLoading(false);\n    });\n\n    return () => unsubscribe();\n  }, []);\n\n  const handleLogin = (userData: User) => {\n    setUser(userData);\n  };\n\n  const handleLogout = async () => {\n    try {\n      await authService.logout();\n      setUser(null);\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n\n  if (loading) {\n    return <LoadingSpinner />;\n  }\n\n  return (\n    <div className=\"App min-h-screen bg-gray-50\" dir=\"rtl\">\n      <Router>\n        <Routes>\n          {/* Public Routes */}\n          <Route \n            path=\"/login\" \n            element={\n              user ? (\n                <Navigate to={user.role === 'admin' ? '/admin' : '/student'} replace />\n              ) : (\n                <LoginPage onLogin={handleLogin} />\n              )\n            } \n          />\n          \n          {/* Protected Admin Routes */}\n          <Route\n            path=\"/admin/*\"\n            element={\n              user && isAdmin(user) ? (\n                <AdminDashboard user={user} onLogout={handleLogout} />\n              ) : (\n                <Navigate to=\"/login\" replace />\n              )\n            }\n          />\n          \n          {/* Protected Student Routes */}\n          <Route\n            path=\"/student/*\"\n            element={\n              user && isStudent(user) ? (\n                <StudentDashboard user={user} onLogout={handleLogout} />\n              ) : (\n                <Navigate to=\"/login\" replace />\n              )\n            }\n          />\n          \n          {/* Default Route */}\n          <Route \n            path=\"/\" \n            element={\n              <Navigate to={\n                user \n                  ? (user.role === 'admin' ? '/admin' : '/student')\n                  : '/login'\n              } replace />\n            } \n          />\n        </Routes>\n        \n        {/* AI Assistant - Available for students */}\n        {user && user.role === 'student' && <AIAssistant />}\n        \n        {/* Toast Notifications */}\n        <Toaster\n          position=\"top-center\"\n          toastOptions={{\n            duration: 4000,\n            style: {\n              background: '#363636',\n              color: '#fff',\n              fontFamily: 'Cairo, sans-serif',\n              direction: 'rtl'\n            },\n            success: {\n              style: {\n                background: '#10b981',\n              },\n            },\n            error: {\n              style: {\n                background: '#ef4444',\n              },\n            },\n          }}\n        />\n      </Router>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,OAAO,QAAQ,iBAAiB;AAGzC;AACA,SAASC,WAAW,QAAQ,wBAAwB;;AAEpD;AACA,OAAOC,cAAc,MAAM,oCAAoC;AAC/D,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,WAAW,MAAM,sCAAsC;;AAE9D;;AAGA;AACA,OAAO,WAAW;;AAElB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,OAAO,GAAIC,IAAU,IAAc;EACvC,OAAOA,IAAI,CAACC,IAAI,KAAK,OAAO;AAC9B,CAAC;AAED,MAAMC,SAAS,GAAIF,IAAU,IAAc;EACzC,OAAOA,IAAI,CAACC,IAAI,KAAK,SAAS;AAChC,CAAC;AAED,SAASE,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACJ,IAAI,EAAEK,OAAO,CAAC,GAAGtB,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAsB,IAAI,CAAC;EAE3EC,SAAS,CAAC,MAAM;IACd,MAAM0B,WAAW,GAAGnB,WAAW,CAACoB,iBAAiB,CAAC,MAAOH,YAAY,IAAK;MACxEC,eAAe,CAACD,YAAY,CAAC;MAE7B,IAAIA,YAAY,EAAE;QAChB;QACA,IAAI;UACF;UACA;QAAA,CACD,CAAC,OAAOI,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAClD;MACF,CAAC,MAAM;QACLP,OAAO,CAAC,IAAI,CAAC;MACf;MAEAE,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC;IAEF,OAAO,MAAMG,WAAW,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,WAAW,GAAIC,QAAc,IAAK;IACtCV,OAAO,CAACU,QAAQ,CAAC;EACnB,CAAC;EAED,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMzB,WAAW,CAAC0B,MAAM,CAAC,CAAC;MAC1BZ,OAAO,CAAC,IAAI,CAAC;IACf,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC;EACF,CAAC;EAED,IAAIN,OAAO,EAAE;IACX,oBAAOR,OAAA,CAACN,cAAc;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC3B;EAEA,oBACEvB,OAAA;IAAKwB,SAAS,EAAC,6BAA6B;IAACC,GAAG,EAAC,KAAK;IAAAC,QAAA,eACpD1B,OAAA,CAACZ,MAAM;MAAAsC,QAAA,gBACL1B,OAAA,CAACX,MAAM;QAAAqC,QAAA,gBAEL1B,OAAA,CAACV,KAAK;UACJqC,IAAI,EAAC,QAAQ;UACbC,OAAO,EACL1B,IAAI,gBACFF,OAAA,CAACT,QAAQ;YAACsC,EAAE,EAAE3B,IAAI,CAACC,IAAI,KAAK,OAAO,GAAG,QAAQ,GAAG,UAAW;YAAC2B,OAAO;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEvEvB,OAAA,CAACL,SAAS;YAACoC,OAAO,EAAEf;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAErC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGFvB,OAAA,CAACV,KAAK;UACJqC,IAAI,EAAC,UAAU;UACfC,OAAO,EACL1B,IAAI,IAAID,OAAO,CAACC,IAAI,CAAC,gBACnBF,OAAA,CAACJ,cAAc;YAACM,IAAI,EAAEA,IAAK;YAAC8B,QAAQ,EAAEd;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEtDvB,OAAA,CAACT,QAAQ;YAACsC,EAAE,EAAC,QAAQ;YAACC,OAAO;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAElC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGFvB,OAAA,CAACV,KAAK;UACJqC,IAAI,EAAC,YAAY;UACjBC,OAAO,EACL1B,IAAI,IAAIE,SAAS,CAACF,IAAI,CAAC,gBACrBF,OAAA,CAACH,gBAAgB;YAACK,IAAI,EAAEA,IAAK;YAAC8B,QAAQ,EAAEd;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAExDvB,OAAA,CAACT,QAAQ;YAACsC,EAAE,EAAC,QAAQ;YAACC,OAAO;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAElC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGFvB,OAAA,CAACV,KAAK;UACJqC,IAAI,EAAC,GAAG;UACRC,OAAO,eACL5B,OAAA,CAACT,QAAQ;YAACsC,EAAE,EACV3B,IAAI,GACCA,IAAI,CAACC,IAAI,KAAK,OAAO,GAAG,QAAQ,GAAG,UAAU,GAC9C,QACL;YAAC2B,OAAO;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QACZ;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,EAGRrB,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAK,SAAS,iBAAIH,OAAA,CAACF,WAAW;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGnDvB,OAAA,CAACR,OAAO;QACNyC,QAAQ,EAAC,YAAY;QACrBC,YAAY,EAAE;UACZC,QAAQ,EAAE,IAAI;UACdC,KAAK,EAAE;YACLC,UAAU,EAAE,SAAS;YACrBC,KAAK,EAAE,MAAM;YACbC,UAAU,EAAE,mBAAmB;YAC/BC,SAAS,EAAE;UACb,CAAC;UACDC,OAAO,EAAE;YACPL,KAAK,EAAE;cACLC,UAAU,EAAE;YACd;UACF,CAAC;UACDvB,KAAK,EAAE;YACLsB,KAAK,EAAE;cACLC,UAAU,EAAE;YACd;UACF;QACF;MAAE;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAACjB,EAAA,CA9HQD,GAAG;AAAAqC,EAAA,GAAHrC,GAAG;AAgIZ,eAAeA,GAAG;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}