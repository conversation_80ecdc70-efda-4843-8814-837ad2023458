import React, { useState } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { motion } from 'framer-motion';

// Components
import AdminSidebar from '../../components/Admin/AdminSidebar';
import AdminHeader from '../../components/Admin/AdminHeader';
import DashboardOverview from '../../components/Admin/DashboardOverview';
import CategoriesManagement from '../../components/Admin/CategoriesManagement';
import CoursesManagement from '../../components/Admin/CoursesManagement';
import StudentsManagement from '../../components/Admin/StudentsManagement';
import QuizzesManagement from '../../components/Admin/QuizzesManagement';
import CertificatesManagement from '../../components/Admin/CertificatesManagement';
import AnalyticsPage from '../../components/Admin/AnalyticsPage';
import SettingsPage from '../../components/Admin/SettingsPage';

// Types
import { Admin } from '../../types';

interface AdminDashboardProps {
  user: Admin;
  onLogout: () => void;
}

const AdminDashboard: React.FC<AdminDashboardProps> = ({ user, onLogout }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <div className="flex h-screen bg-gray-50" dir="rtl">
      {/* Sidebar */}
      <AdminSidebar 
        isOpen={sidebarOpen} 
        onClose={() => setSidebarOpen(false)} 
      />
      
      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <AdminHeader 
          user={user}
          onMenuClick={() => setSidebarOpen(true)}
          onLogout={onLogout}
        />
        
        {/* Page Content */}
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Routes>
              <Route path="/" element={<DashboardOverview />} />
              <Route path="/categories" element={<CategoriesManagement />} />
              <Route path="/courses" element={<CoursesManagement />} />
              <Route path="/students" element={<StudentsManagement />} />
              <Route path="/quizzes" element={<QuizzesManagement />} />
              <Route path="/certificates" element={<CertificatesManagement />} />
              <Route path="/analytics" element={<AnalyticsPage />} />
              <Route path="/settings" element={<SettingsPage />} />
              <Route path="*" element={<Navigate to="/admin" replace />} />
            </Routes>
          </motion.div>
        </main>
      </div>
      
      {/* Mobile Sidebar Overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
};

export default AdminDashboard;
