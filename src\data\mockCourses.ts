import { Course, Video, Quiz, Certificate } from '../types';

export const mockCourses: Course[] = [
  {
    id: '1',
    title: 'أساسيات البرمجة',
    description: 'تعلم أساسيات البرمجة من الصفر حتى الاحتراف',
    categoryId: 'programming',
    instructorId: 'admin-001',
    videos: [],
    pdfs: [],
    quizzes: [],
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: '2',
    title: 'تطوير المواقع الحديثة',
    description: 'تعلم تطوير المواقع باستخدام أحدث التقنيات',
    categoryId: 'web',
    instructorId: 'admin-001',
    videos: [],
    pdfs: [],
    quizzes: [],
    isActive: true,
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-20')
  },
  {
    id: '3',
    title: 'الذكاء الاصطناعي للمبتدئين',
    description: 'مقدمة شاملة في عالم الذكاء الاصطناعي',
    categoryId: 'ai',
    instructorId: 'admin-001',
    videos: [],
    pdfs: [],
    quizzes: [],
    isActive: true,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-25')
  }
];

export const mockVideos: Video[] = [
  {
    id: '1',
    title: 'مقدمة في البرمجة',
    description: 'تعرف على أساسيات البرمجة',
    url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
    duration: '15:30',
    courseId: '1',
    order: 1,
    isWatched: false
  },
  {
    id: '2',
    title: 'المتغيرات والثوابت',
    description: 'تعلم كيفية استخدام المتغيرات',
    url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
    duration: '20:45',
    courseId: '1',
    order: 2,
    isWatched: false
  }
];

export const mockQuizzes: Quiz[] = [
  {
    id: '1',
    title: 'اختبار أساسيات البرمجة',
    description: 'اختبر معرفتك في أساسيات البرمجة',
    courseId: '1',
    questions: [
      {
        id: '1',
        question: 'ما هو المتغير في البرمجة؟',
        options: [
          'مكان لتخزين البيانات',
          'نوع من الدوال',
          'أمر للطباعة',
          'لا شيء مما سبق'
        ],
        correctAnswer: 0,
        explanation: 'المتغير هو مكان في الذاكرة لتخزين البيانات'
      }
    ],
    timeLimit: 30,
    passingScore: 70,
    createdAt: new Date('2024-01-01')
  }
];

export const mockCertificates: Certificate[] = [
  {
    id: 'cert-001',
    studentId: 'student-001',
    courseId: '1',
    courseName: 'أساسيات البرمجة',
    studentName: 'أحمد محمد',
    issueDate: new Date('2024-02-01'),
    certificateUrl: 'https://example.com/certificate/cert-001.pdf'
  }
];
