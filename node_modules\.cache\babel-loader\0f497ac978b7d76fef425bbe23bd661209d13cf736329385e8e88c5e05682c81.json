{"ast": null, "code": "const appearStoreId = (id, value) => `${id}: ${value}`;\nexport { appearStoreId };", "map": {"version": 3, "names": ["appearStoreId", "id", "value"], "sources": ["C:/Users/<USER>/Desktop/مشروع/node_modules/framer-motion/dist/es/animation/optimized-appear/store-id.mjs"], "sourcesContent": ["const appearStoreId = (id, value) => `${id}: ${value}`;\n\nexport { appearStoreId };\n"], "mappings": "AAAA,MAAMA,aAAa,GAAGA,CAACC,EAAE,EAAEC,KAAK,KAAK,GAAGD,EAAE,KAAKC,KAAK,EAAE;AAEtD,SAASF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}