import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';
import { getFunctions } from 'firebase/functions';

const firebaseConfig = {
  apiKey: "AIzaSyDXQJ8QJ8QJ8QJ8QJ8QJ8QJ8QJ8QJ8QJ8Q",
  authDomain: "alaa-courses-platform.firebaseapp.com",
  projectId: "alaa-courses-platform",
  storageBucket: "alaa-courses-platform.appspot.com",
  messagingSenderId: "341945258779",
  appId: "1:341945258779:web:a1b2c3d4e5f6g7h8i9j0k1l2"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);
export const functions = getFunctions(app);

export default app;
