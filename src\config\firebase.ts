import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';
import { getFunctions } from 'firebase/functions';

const firebaseConfig = {
  apiKey: "AIzaSyBqJVqKvXxGtJxGtJxGtJxGtJxGtJxGtJx", // سيتم تحديثها لاحقاً
  authDomain: "alaa-courses-platform.firebaseapp.com",
  projectId: "alaa-courses-platform",
  storageBucket: "alaa-courses-platform.appspot.com",
  messagingSenderId: "341945258779",
  appId: "1:341945258779:web:abcdefghijklmnop" // سيتم تحديثها لاحقاً
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);
export const functions = getFunctions(app);

export default app;
