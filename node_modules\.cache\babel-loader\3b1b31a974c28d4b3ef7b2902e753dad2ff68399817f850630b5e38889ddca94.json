{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0634\\u0631\\u0648\\u0639\\\\src\\\\pages\\\\student\\\\StudentDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\n\n// Components\nimport StudentSidebar from '../../components/Student/StudentSidebar';\nimport StudentHeader from '../../components/Student/StudentHeader';\nimport StudentOverview from '../../components/Student/StudentOverview';\nimport MyCourses from '../../components/Student/MyCourses';\nimport CourseViewer from '../../components/Student/CourseViewer';\nimport QuizPage from '../../components/Student/QuizPage';\nimport MyCertificates from '../../components/Student/MyCertificates';\nimport StudentProfile from '../../components/Student/StudentProfile';\n\n// Types\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StudentDashboard = ({\n  user,\n  onLogout\n}) => {\n  _s();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    dir: \"rtl\",\n    children: [/*#__PURE__*/_jsxDEV(StudentSidebar, {\n      isOpen: sidebarOpen,\n      onClose: () => setSidebarOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(StudentHeader, {\n        user: user,\n        onMenuClick: () => setSidebarOpen(true),\n        onLogout: onLogout\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-6\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.3\n          },\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(StudentOverview, {\n                user: user\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/courses\",\n              element: /*#__PURE__*/_jsxDEV(MyCourses, {\n                user: user\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/course/:courseId\",\n              element: /*#__PURE__*/_jsxDEV(CourseViewer, {\n                user: user\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 56\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/quiz/:quizId\",\n              element: /*#__PURE__*/_jsxDEV(QuizPage, {\n                user: user\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 52\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/certificates\",\n              element: /*#__PURE__*/_jsxDEV(MyCertificates, {\n                user: user\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 52\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/profile\",\n              element: /*#__PURE__*/_jsxDEV(StudentProfile, {\n                user: user\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"*\",\n              element: /*#__PURE__*/_jsxDEV(Navigate, {\n                to: \"/student\",\n                replace: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this), sidebarOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\",\n      onClick: () => setSidebarOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n};\n_s(StudentDashboard, \"5rGDkYpGQ8fHM9RkMWnKOwsxadk=\");\n_c = StudentDashboard;\nexport default StudentDashboard;\nvar _c;\n$RefreshReg$(_c, \"StudentDashboard\");", "map": {"version": 3, "names": ["React", "useState", "Routes", "Route", "Navigate", "motion", "StudentSidebar", "StudentHeader", "StudentOverview", "MyCourses", "<PERSON><PERSON>iewer", "QuizPage", "MyCertificates", "StudentProfile", "jsxDEV", "_jsxDEV", "StudentDashboard", "user", "onLogout", "_s", "sidebarOpen", "setSidebarOpen", "className", "dir", "children", "isOpen", "onClose", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onMenuClick", "div", "initial", "opacity", "y", "animate", "transition", "duration", "path", "element", "to", "replace", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/pages/student/StudentDashboard.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\n\n// Components\nimport StudentSidebar from '../../components/Student/StudentSidebar';\nimport StudentHeader from '../../components/Student/StudentHeader';\nimport StudentOverview from '../../components/Student/StudentOverview';\nimport MyCourses from '../../components/Student/MyCourses';\nimport CourseViewer from '../../components/Student/CourseViewer';\nimport QuizPage from '../../components/Student/QuizPage';\nimport MyCertificates from '../../components/Student/MyCertificates';\nimport StudentProfile from '../../components/Student/StudentProfile';\n\n// Types\nimport { Student } from '../../types';\n\ninterface StudentDashboardProps {\n  user: Student;\n  onLogout: () => void;\n}\n\nconst StudentDashboard: React.FC<StudentDashboardProps> = ({ user, onLogout }) => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  return (\n    <div className=\"flex h-screen bg-gray-50\" dir=\"rtl\">\n      {/* Sidebar */}\n      <StudentSidebar \n        isOpen={sidebarOpen} \n        onClose={() => setSidebarOpen(false)} \n      />\n      \n      {/* Main Content */}\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        {/* Header */}\n        <StudentHeader \n          user={user}\n          onMenuClick={() => setSidebarOpen(true)}\n          onLogout={onLogout}\n        />\n        \n        {/* Page Content */}\n        <main className=\"flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-6\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.3 }}\n          >\n            <Routes>\n              <Route path=\"/\" element={<StudentOverview user={user} />} />\n              <Route path=\"/courses\" element={<MyCourses user={user} />} />\n              <Route path=\"/course/:courseId\" element={<CourseViewer user={user} />} />\n              <Route path=\"/quiz/:quizId\" element={<QuizPage user={user} />} />\n              <Route path=\"/certificates\" element={<MyCertificates user={user} />} />\n              <Route path=\"/profile\" element={<StudentProfile user={user} />} />\n              <Route path=\"*\" element={<Navigate to=\"/student\" replace />} />\n            </Routes>\n          </motion.div>\n        </main>\n      </div>\n      \n      {/* Mobile Sidebar Overlay */}\n      {sidebarOpen && (\n        <div \n          className=\"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\"\n          onClick={() => setSidebarOpen(false)}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default StudentDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AAC1D,SAASC,MAAM,QAAQ,eAAe;;AAEtC;AACA,OAAOC,cAAc,MAAM,yCAAyC;AACpE,OAAOC,aAAa,MAAM,wCAAwC;AAClE,OAAOC,eAAe,MAAM,0CAA0C;AACtE,OAAOC,SAAS,MAAM,oCAAoC;AAC1D,OAAOC,YAAY,MAAM,uCAAuC;AAChE,OAAOC,QAAQ,MAAM,mCAAmC;AACxD,OAAOC,cAAc,MAAM,yCAAyC;AACpE,OAAOC,cAAc,MAAM,yCAAyC;;AAEpE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAQA,MAAMC,gBAAiD,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAChF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAErD,oBACEc,OAAA;IAAKO,SAAS,EAAC,0BAA0B;IAACC,GAAG,EAAC,KAAK;IAAAC,QAAA,gBAEjDT,OAAA,CAACT,cAAc;MACbmB,MAAM,EAAEL,WAAY;MACpBM,OAAO,EAAEA,CAAA,KAAML,cAAc,CAAC,KAAK;IAAE;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,eAGFf,OAAA;MAAKO,SAAS,EAAC,sCAAsC;MAAAE,QAAA,gBAEnDT,OAAA,CAACR,aAAa;QACZU,IAAI,EAAEA,IAAK;QACXc,WAAW,EAAEA,CAAA,KAAMV,cAAc,CAAC,IAAI,CAAE;QACxCH,QAAQ,EAAEA;MAAS;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,eAGFf,OAAA;QAAMO,SAAS,EAAC,yDAAyD;QAAAE,QAAA,eACvET,OAAA,CAACV,MAAM,CAAC2B,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAAd,QAAA,eAE9BT,OAAA,CAACb,MAAM;YAAAsB,QAAA,gBACLT,OAAA,CAACZ,KAAK;cAACoC,IAAI,EAAC,GAAG;cAACC,OAAO,eAAEzB,OAAA,CAACP,eAAe;gBAACS,IAAI,EAAEA;cAAK;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5Df,OAAA,CAACZ,KAAK;cAACoC,IAAI,EAAC,UAAU;cAACC,OAAO,eAAEzB,OAAA,CAACN,SAAS;gBAACQ,IAAI,EAAEA;cAAK;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7Df,OAAA,CAACZ,KAAK;cAACoC,IAAI,EAAC,mBAAmB;cAACC,OAAO,eAAEzB,OAAA,CAACL,YAAY;gBAACO,IAAI,EAAEA;cAAK;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzEf,OAAA,CAACZ,KAAK;cAACoC,IAAI,EAAC,eAAe;cAACC,OAAO,eAAEzB,OAAA,CAACJ,QAAQ;gBAACM,IAAI,EAAEA;cAAK;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjEf,OAAA,CAACZ,KAAK;cAACoC,IAAI,EAAC,eAAe;cAACC,OAAO,eAAEzB,OAAA,CAACH,cAAc;gBAACK,IAAI,EAAEA;cAAK;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvEf,OAAA,CAACZ,KAAK;cAACoC,IAAI,EAAC,UAAU;cAACC,OAAO,eAAEzB,OAAA,CAACF,cAAc;gBAACI,IAAI,EAAEA;cAAK;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClEf,OAAA,CAACZ,KAAK;cAACoC,IAAI,EAAC,GAAG;cAACC,OAAO,eAAEzB,OAAA,CAACX,QAAQ;gBAACqC,EAAE,EAAC,UAAU;gBAACC,OAAO;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAGLV,WAAW,iBACVL,OAAA;MACEO,SAAS,EAAC,qDAAqD;MAC/DqB,OAAO,EAAEA,CAAA,KAAMtB,cAAc,CAAC,KAAK;IAAE;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACX,EAAA,CAjDIH,gBAAiD;AAAA4B,EAAA,GAAjD5B,gBAAiD;AAmDvD,eAAeA,gBAAgB;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}