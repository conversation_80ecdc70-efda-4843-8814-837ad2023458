{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0634\\u0631\\u0648\\u0639\\\\src\\\\components\\\\Student\\\\CourseViewer.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { PlayIcon, DocumentIcon, ClipboardDocumentListIcon, CheckCircleIcon, ArrowLeftIcon, ArrowRightIcon } from '@heroicons/react/24/outline';\n\n// Types\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CourseViewer = ({\n  user,\n  courseId,\n  onBack\n}) => {\n  _s();\n  const [activeTab, setActiveTab] = useState('videos');\n  const [selectedVideo, setSelectedVideo] = useState('1');\n\n  // Mock course data\n  const course = {\n    id: courseId,\n    title: 'أساسيات البرمجة',\n    description: 'تعلم أساسيات البرمجة من الصفر',\n    videos: [{\n      id: '1',\n      title: 'مقدمة في البرمجة',\n      duration: '15:30',\n      completed: true\n    }, {\n      id: '2',\n      title: 'المتغيرات والثوابت',\n      duration: '22:45',\n      completed: true\n    }, {\n      id: '3',\n      title: 'الحلقات التكرارية',\n      duration: '18:20',\n      completed: false\n    }, {\n      id: '4',\n      title: 'الدوال',\n      duration: '25:10',\n      completed: false\n    }],\n    pdfs: [{\n      id: '1',\n      title: 'مرجع البرمجة الأساسي',\n      size: '2.5 MB'\n    }, {\n      id: '2',\n      title: 'تمارين عملية',\n      size: '1.8 MB'\n    }],\n    quizzes: [{\n      id: '1',\n      title: 'اختبار المتغيرات',\n      questions: 10,\n      completed: true,\n      score: 85\n    }, {\n      id: '2',\n      title: 'اختبار الحلقات',\n      questions: 8,\n      completed: false,\n      score: null\n    }]\n  };\n  const currentVideo = course.videos.find(v => v.id === selectedVideo);\n  const handleNextVideo = () => {\n    const currentIndex = course.videos.findIndex(v => v.id === selectedVideo);\n    if (currentIndex < course.videos.length - 1) {\n      setSelectedVideo(course.videos[currentIndex + 1].id);\n    }\n  };\n  const handlePrevVideo = () => {\n    const currentIndex = course.videos.findIndex(v => v.id === selectedVideo);\n    if (currentIndex > 0) {\n      setSelectedVideo(course.videos[currentIndex - 1].id);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-4 space-x-reverse\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onBack,\n        className: \"p-2 text-gray-600 hover:text-gray-900 transition-colors\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-6 h-6\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M15 19l-7-7 7-7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: course.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: course.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"lg:col-span-2 space-y-4\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          className: \"bg-white rounded-lg shadow-sm overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"aspect-video bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center text-white\",\n              children: [/*#__PURE__*/_jsxDEV(PlayIcon, {\n                className: \"w-16 h-16 mx-auto mb-4 opacity-50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-semibold mb-2\",\n                children: currentVideo === null || currentVideo === void 0 ? void 0 : currentVideo.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 92,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-300\",\n                children: [\"\\u0645\\u062F\\u0629 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648: \", currentVideo === null || currentVideo === void 0 ? void 0 : currentVideo.duration]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 border-t border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4 space-x-reverse\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handlePrevVideo,\n                  disabled: course.videos.findIndex(v => v.id === selectedVideo) === 0,\n                  className: \"flex items-center space-x-2 space-x-reverse px-3 py-2 text-sm bg-gray-100 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                  children: [/*#__PURE__*/_jsxDEV(ArrowRightIcon, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 106,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u0627\\u0644\\u0633\\u0627\\u0628\\u0642\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 107,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 101,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleNextVideo,\n                  disabled: course.videos.findIndex(v => v.id === selectedVideo) === course.videos.length - 1,\n                  className: \"flex items-center space-x-2 space-x-reverse px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u0627\\u0644\\u062A\\u0627\\u0644\\u064A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 114,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ArrowLeftIcon, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 115,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 109,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                children: \"\\u062A\\u0645 \\u0627\\u0644\\u0645\\u0634\\u0627\\u0647\\u062F\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-sm p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-2 space-x-reverse\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab('videos'),\n              className: `flex-1 py-2 px-3 text-sm rounded-lg transition-colors ${activeTab === 'videos' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n              children: \"\\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\\u0647\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab('pdfs'),\n              className: `flex-1 py-2 px-3 text-sm rounded-lg transition-colors ${activeTab === 'pdfs' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n              children: \"\\u0627\\u0644\\u0645\\u0644\\u0641\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab('quizzes'),\n              className: `flex-1 py-2 px-3 text-sm rounded-lg transition-colors ${activeTab === 'quizzes' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n              children: \"\\u0627\\u0644\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          className: \"bg-white rounded-lg shadow-sm p-4\",\n          children: [activeTab === 'videos' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold text-gray-900 mb-4\",\n              children: \"\\u0642\\u0627\\u0626\\u0645\\u0629 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\\u0647\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 17\n            }, this), course.videos.map((video, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              onClick: () => setSelectedVideo(video.id),\n              className: `p-3 rounded-lg cursor-pointer transition-colors ${selectedVideo === video.id ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50 hover:bg-gray-100'}`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3 space-x-reverse\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0\",\n                  children: video.completed ? /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                    className: \"w-5 h-5 text-green-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 187,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(PlayIcon, {\n                    className: \"w-5 h-5 text-gray-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 189,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-gray-900 truncate\",\n                    children: [index + 1, \". \", video.title]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500\",\n                    children: video.duration\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 196,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 21\n              }, this)\n            }, video.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 15\n          }, this), activeTab === 'pdfs' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold text-gray-900 mb-4\",\n              children: \"\\u0627\\u0644\\u0645\\u0644\\u0641\\u0627\\u062A \\u0648\\u0627\\u0644\\u0645\\u0631\\u0627\\u062C\\u0639\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 17\n            }, this), course.pdfs.map(pdf => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3 space-x-reverse\",\n                children: [/*#__PURE__*/_jsxDEV(DocumentIcon, {\n                  className: \"w-5 h-5 text-red-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-gray-900 truncate\",\n                    children: pdf.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500\",\n                    children: pdf.size\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 21\n              }, this)\n            }, pdf.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 15\n          }, this), activeTab === 'quizzes' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold text-gray-900 mb-4\",\n              children: \"\\u0627\\u0644\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 17\n            }, this), course.quizzes.map(quiz => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3 space-x-reverse\",\n                children: [/*#__PURE__*/_jsxDEV(ClipboardDocumentListIcon, {\n                  className: \"w-5 h-5 text-purple-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-gray-900 truncate\",\n                    children: quiz.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500\",\n                    children: [quiz.questions, \" \\u0633\\u0624\\u0627\\u0644\", quiz.completed && quiz.score && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-green-600 mr-2\",\n                      children: [\"\\u2022 \\u0627\\u0644\\u0646\\u062A\\u064A\\u062C\\u0629: \", quiz.score, \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 243,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 23\n                }, this), quiz.completed && /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                  className: \"w-5 h-5 text-green-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 21\n              }, this)\n            }, quiz.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 15\n          }, this)]\n        }, activeTab, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 63,\n    columnNumber: 5\n  }, this);\n};\n_s(CourseViewer, \"KlygfkVRXJX0ygwx1J+oagG8n28=\");\n_c = CourseViewer;\nexport default CourseViewer;\nvar _c;\n$RefreshReg$(_c, \"CourseViewer\");", "map": {"version": 3, "names": ["React", "useState", "motion", "PlayIcon", "DocumentIcon", "ClipboardDocumentListIcon", "CheckCircleIcon", "ArrowLeftIcon", "ArrowRightIcon", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>iewer", "user", "courseId", "onBack", "_s", "activeTab", "setActiveTab", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedVideo", "course", "id", "title", "description", "videos", "duration", "completed", "pdfs", "size", "quizzes", "questions", "score", "currentVideo", "find", "v", "handleNextVideo", "currentIndex", "findIndex", "length", "handlePrevVideo", "className", "children", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "y", "animate", "disabled", "map", "video", "index", "pdf", "quiz", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Student/CourseViewer.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  PlayIcon,\n  DocumentIcon,\n  ClipboardDocumentListIcon,\n  CheckCircleIcon,\n  ArrowLeftIcon,\n  ArrowRightIcon\n} from '@heroicons/react/24/outline';\n\n// Types\nimport { Student } from '../../types';\n\ninterface CourseViewerProps {\n  user?: Student;\n  courseId?: string;\n  onBack?: () => void;\n}\n\nconst CourseViewer: React.FC<CourseViewerProps> = ({ user, courseId, onBack }) => {\n  const [activeTab, setActiveTab] = useState('videos');\n  const [selectedVideo, setSelectedVideo] = useState('1');\n\n  // Mock course data\n  const course = {\n    id: courseId,\n    title: 'أساسيات البرمجة',\n    description: 'تعلم أساسيات البرمجة من الصفر',\n    videos: [\n      { id: '1', title: 'مقدمة في البرمجة', duration: '15:30', completed: true },\n      { id: '2', title: 'المتغيرات والثوابت', duration: '22:45', completed: true },\n      { id: '3', title: 'الحلقات التكرارية', duration: '18:20', completed: false },\n      { id: '4', title: 'الدوال', duration: '25:10', completed: false }\n    ],\n    pdfs: [\n      { id: '1', title: 'مرجع البرمجة الأساسي', size: '2.5 MB' },\n      { id: '2', title: 'تمارين عملية', size: '1.8 MB' }\n    ],\n    quizzes: [\n      { id: '1', title: 'اختبار المتغيرات', questions: 10, completed: true, score: 85 },\n      { id: '2', title: 'اختبار الحلقات', questions: 8, completed: false, score: null }\n    ]\n  };\n\n  const currentVideo = course.videos.find(v => v.id === selectedVideo);\n\n  const handleNextVideo = () => {\n    const currentIndex = course.videos.findIndex(v => v.id === selectedVideo);\n    if (currentIndex < course.videos.length - 1) {\n      setSelectedVideo(course.videos[currentIndex + 1].id);\n    }\n  };\n\n  const handlePrevVideo = () => {\n    const currentIndex = course.videos.findIndex(v => v.id === selectedVideo);\n    if (currentIndex > 0) {\n      setSelectedVideo(course.videos[currentIndex - 1].id);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center space-x-4 space-x-reverse\">\n        <button\n          onClick={onBack}\n          className=\"p-2 text-gray-600 hover:text-gray-900 transition-colors\"\n        >\n          <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n          </svg>\n        </button>\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">{course.title}</h1>\n          <p className=\"text-gray-600\">{course.description}</p>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Video Player */}\n        <div className=\"lg:col-span-2 space-y-4\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            className=\"bg-white rounded-lg shadow-sm overflow-hidden\"\n          >\n            {/* Video Area */}\n            <div className=\"aspect-video bg-gray-900 flex items-center justify-center\">\n              <div className=\"text-center text-white\">\n                <PlayIcon className=\"w-16 h-16 mx-auto mb-4 opacity-50\" />\n                <h3 className=\"text-xl font-semibold mb-2\">{currentVideo?.title}</h3>\n                <p className=\"text-gray-300\">مدة الفيديو: {currentVideo?.duration}</p>\n              </div>\n            </div>\n\n            {/* Video Controls */}\n            <div className=\"p-4 border-t border-gray-200\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-4 space-x-reverse\">\n                  <button\n                    onClick={handlePrevVideo}\n                    disabled={course.videos.findIndex(v => v.id === selectedVideo) === 0}\n                    className=\"flex items-center space-x-2 space-x-reverse px-3 py-2 text-sm bg-gray-100 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n                  >\n                    <ArrowRightIcon className=\"w-4 h-4\" />\n                    <span>السابق</span>\n                  </button>\n                  <button\n                    onClick={handleNextVideo}\n                    disabled={course.videos.findIndex(v => v.id === selectedVideo) === course.videos.length - 1}\n                    className=\"flex items-center space-x-2 space-x-reverse px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n                  >\n                    <span>التالي</span>\n                    <ArrowLeftIcon className=\"w-4 h-4\" />\n                  </button>\n                </div>\n                <button className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\">\n                  تم المشاهدة\n                </button>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n\n        {/* Course Content Sidebar */}\n        <div className=\"space-y-4\">\n          {/* Tabs */}\n          <div className=\"bg-white rounded-lg shadow-sm p-4\">\n            <div className=\"flex space-x-2 space-x-reverse\">\n              <button\n                onClick={() => setActiveTab('videos')}\n                className={`flex-1 py-2 px-3 text-sm rounded-lg transition-colors ${\n                  activeTab === 'videos'\n                    ? 'bg-blue-600 text-white'\n                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                }`}\n              >\n                الفيديوهات\n              </button>\n              <button\n                onClick={() => setActiveTab('pdfs')}\n                className={`flex-1 py-2 px-3 text-sm rounded-lg transition-colors ${\n                  activeTab === 'pdfs'\n                    ? 'bg-blue-600 text-white'\n                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                }`}\n              >\n                الملفات\n              </button>\n              <button\n                onClick={() => setActiveTab('quizzes')}\n                className={`flex-1 py-2 px-3 text-sm rounded-lg transition-colors ${\n                  activeTab === 'quizzes'\n                    ? 'bg-blue-600 text-white'\n                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                }`}\n              >\n                الاختبارات\n              </button>\n            </div>\n          </div>\n\n          {/* Content */}\n          <motion.div\n            key={activeTab}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            className=\"bg-white rounded-lg shadow-sm p-4\"\n          >\n            {activeTab === 'videos' && (\n              <div className=\"space-y-3\">\n                <h3 className=\"font-semibold text-gray-900 mb-4\">قائمة الفيديوهات</h3>\n                {course.videos.map((video, index) => (\n                  <div\n                    key={video.id}\n                    onClick={() => setSelectedVideo(video.id)}\n                    className={`p-3 rounded-lg cursor-pointer transition-colors ${\n                      selectedVideo === video.id\n                        ? 'bg-blue-50 border border-blue-200'\n                        : 'bg-gray-50 hover:bg-gray-100'\n                    }`}\n                  >\n                    <div className=\"flex items-center space-x-3 space-x-reverse\">\n                      <div className=\"flex-shrink-0\">\n                        {video.completed ? (\n                          <CheckCircleIcon className=\"w-5 h-5 text-green-600\" />\n                        ) : (\n                          <PlayIcon className=\"w-5 h-5 text-gray-400\" />\n                        )}\n                      </div>\n                      <div className=\"flex-1 min-w-0\">\n                        <p className=\"text-sm font-medium text-gray-900 truncate\">\n                          {index + 1}. {video.title}\n                        </p>\n                        <p className=\"text-xs text-gray-500\">{video.duration}</p>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n\n            {activeTab === 'pdfs' && (\n              <div className=\"space-y-3\">\n                <h3 className=\"font-semibold text-gray-900 mb-4\">الملفات والمراجع</h3>\n                {course.pdfs.map((pdf) => (\n                  <div\n                    key={pdf.id}\n                    className=\"p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors\"\n                  >\n                    <div className=\"flex items-center space-x-3 space-x-reverse\">\n                      <DocumentIcon className=\"w-5 h-5 text-red-600\" />\n                      <div className=\"flex-1 min-w-0\">\n                        <p className=\"text-sm font-medium text-gray-900 truncate\">\n                          {pdf.title}\n                        </p>\n                        <p className=\"text-xs text-gray-500\">{pdf.size}</p>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n\n            {activeTab === 'quizzes' && (\n              <div className=\"space-y-3\">\n                <h3 className=\"font-semibold text-gray-900 mb-4\">الاختبارات</h3>\n                {course.quizzes.map((quiz) => (\n                  <div\n                    key={quiz.id}\n                    className=\"p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors\"\n                  >\n                    <div className=\"flex items-center space-x-3 space-x-reverse\">\n                      <ClipboardDocumentListIcon className=\"w-5 h-5 text-purple-600\" />\n                      <div className=\"flex-1 min-w-0\">\n                        <p className=\"text-sm font-medium text-gray-900 truncate\">\n                          {quiz.title}\n                        </p>\n                        <p className=\"text-xs text-gray-500\">\n                          {quiz.questions} سؤال\n                          {quiz.completed && quiz.score && (\n                            <span className=\"text-green-600 mr-2\">• النتيجة: {quiz.score}%</span>\n                          )}\n                        </p>\n                      </div>\n                      {quiz.completed && (\n                        <CheckCircleIcon className=\"w-5 h-5 text-green-600\" />\n                      )}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </motion.div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CourseViewer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,QAAQ,EACRC,YAAY,EACZC,yBAAyB,EACzBC,eAAe,EACfC,aAAa,EACbC,cAAc,QACT,6BAA6B;;AAEpC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AASA,MAAMC,YAAyC,GAAGA,CAAC;EAAEC,IAAI;EAAEC,QAAQ;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAChF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,QAAQ,CAAC;EACpD,MAAM,CAACiB,aAAa,EAAEC,gBAAgB,CAAC,GAAGlB,QAAQ,CAAC,GAAG,CAAC;;EAEvD;EACA,MAAMmB,MAAM,GAAG;IACbC,EAAE,EAAER,QAAQ;IACZS,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,+BAA+B;IAC5CC,MAAM,EAAE,CACN;MAAEH,EAAE,EAAE,GAAG;MAAEC,KAAK,EAAE,kBAAkB;MAAEG,QAAQ,EAAE,OAAO;MAAEC,SAAS,EAAE;IAAK,CAAC,EAC1E;MAAEL,EAAE,EAAE,GAAG;MAAEC,KAAK,EAAE,oBAAoB;MAAEG,QAAQ,EAAE,OAAO;MAAEC,SAAS,EAAE;IAAK,CAAC,EAC5E;MAAEL,EAAE,EAAE,GAAG;MAAEC,KAAK,EAAE,mBAAmB;MAAEG,QAAQ,EAAE,OAAO;MAAEC,SAAS,EAAE;IAAM,CAAC,EAC5E;MAAEL,EAAE,EAAE,GAAG;MAAEC,KAAK,EAAE,QAAQ;MAAEG,QAAQ,EAAE,OAAO;MAAEC,SAAS,EAAE;IAAM,CAAC,CAClE;IACDC,IAAI,EAAE,CACJ;MAAEN,EAAE,EAAE,GAAG;MAAEC,KAAK,EAAE,sBAAsB;MAAEM,IAAI,EAAE;IAAS,CAAC,EAC1D;MAAEP,EAAE,EAAE,GAAG;MAAEC,KAAK,EAAE,cAAc;MAAEM,IAAI,EAAE;IAAS,CAAC,CACnD;IACDC,OAAO,EAAE,CACP;MAAER,EAAE,EAAE,GAAG;MAAEC,KAAK,EAAE,kBAAkB;MAAEQ,SAAS,EAAE,EAAE;MAAEJ,SAAS,EAAE,IAAI;MAAEK,KAAK,EAAE;IAAG,CAAC,EACjF;MAAEV,EAAE,EAAE,GAAG;MAAEC,KAAK,EAAE,gBAAgB;MAAEQ,SAAS,EAAE,CAAC;MAAEJ,SAAS,EAAE,KAAK;MAAEK,KAAK,EAAE;IAAK,CAAC;EAErF,CAAC;EAED,MAAMC,YAAY,GAAGZ,MAAM,CAACI,MAAM,CAACS,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACb,EAAE,KAAKH,aAAa,CAAC;EAEpE,MAAMiB,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,YAAY,GAAGhB,MAAM,CAACI,MAAM,CAACa,SAAS,CAACH,CAAC,IAAIA,CAAC,CAACb,EAAE,KAAKH,aAAa,CAAC;IACzE,IAAIkB,YAAY,GAAGhB,MAAM,CAACI,MAAM,CAACc,MAAM,GAAG,CAAC,EAAE;MAC3CnB,gBAAgB,CAACC,MAAM,CAACI,MAAM,CAACY,YAAY,GAAG,CAAC,CAAC,CAACf,EAAE,CAAC;IACtD;EACF,CAAC;EAED,MAAMkB,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMH,YAAY,GAAGhB,MAAM,CAACI,MAAM,CAACa,SAAS,CAACH,CAAC,IAAIA,CAAC,CAACb,EAAE,KAAKH,aAAa,CAAC;IACzE,IAAIkB,YAAY,GAAG,CAAC,EAAE;MACpBjB,gBAAgB,CAACC,MAAM,CAACI,MAAM,CAACY,YAAY,GAAG,CAAC,CAAC,CAACf,EAAE,CAAC;IACtD;EACF,CAAC;EAED,oBACEX,OAAA;IAAK8B,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB/B,OAAA;MAAK8B,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAC1D/B,OAAA;QACEgC,OAAO,EAAE5B,MAAO;QAChB0B,SAAS,EAAC,yDAAyD;QAAAC,QAAA,eAEnE/B,OAAA;UAAK8B,SAAS,EAAC,SAAS;UAACG,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAJ,QAAA,eAC5E/B,OAAA;YAAMoC,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,WAAW,EAAE,CAAE;YAACC,CAAC,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACT3C,OAAA;QAAA+B,QAAA,gBACE/B,OAAA;UAAI8B,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAErB,MAAM,CAACE;QAAK;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpE3C,OAAA;UAAG8B,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAErB,MAAM,CAACG;QAAW;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN3C,OAAA;MAAK8B,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpD/B,OAAA;QAAK8B,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACtC/B,OAAA,CAACR,MAAM,CAACoD,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BjB,SAAS,EAAC,+CAA+C;UAAAC,QAAA,gBAGzD/B,OAAA;YAAK8B,SAAS,EAAC,2DAA2D;YAAAC,QAAA,eACxE/B,OAAA;cAAK8B,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrC/B,OAAA,CAACP,QAAQ;gBAACqC,SAAS,EAAC;cAAmC;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1D3C,OAAA;gBAAI8B,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAET,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEV;cAAK;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrE3C,OAAA;gBAAG8B,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,iEAAa,EAACT,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEP,QAAQ;cAAA;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN3C,OAAA;YAAK8B,SAAS,EAAC,8BAA8B;YAAAC,QAAA,eAC3C/B,OAAA;cAAK8B,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD/B,OAAA;gBAAK8B,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBAC1D/B,OAAA;kBACEgC,OAAO,EAAEH,eAAgB;kBACzBoB,QAAQ,EAAEvC,MAAM,CAACI,MAAM,CAACa,SAAS,CAACH,CAAC,IAAIA,CAAC,CAACb,EAAE,KAAKH,aAAa,CAAC,KAAK,CAAE;kBACrEsB,SAAS,EAAC,0KAA0K;kBAAAC,QAAA,gBAEpL/B,OAAA,CAACF,cAAc;oBAACgC,SAAS,EAAC;kBAAS;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACtC3C,OAAA;oBAAA+B,QAAA,EAAM;kBAAM;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,eACT3C,OAAA;kBACEgC,OAAO,EAAEP,eAAgB;kBACzBwB,QAAQ,EAAEvC,MAAM,CAACI,MAAM,CAACa,SAAS,CAACH,CAAC,IAAIA,CAAC,CAACb,EAAE,KAAKH,aAAa,CAAC,KAAKE,MAAM,CAACI,MAAM,CAACc,MAAM,GAAG,CAAE;kBAC5FE,SAAS,EAAC,qLAAqL;kBAAAC,QAAA,gBAE/L/B,OAAA;oBAAA+B,QAAA,EAAM;kBAAM;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnB3C,OAAA,CAACH,aAAa;oBAACiC,SAAS,EAAC;kBAAS;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACN3C,OAAA;gBAAQ8B,SAAS,EAAC,mFAAmF;gBAAAC,QAAA,EAAC;cAEtG;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGN3C,OAAA;QAAK8B,SAAS,EAAC,WAAW;QAAAC,QAAA,gBAExB/B,OAAA;UAAK8B,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChD/B,OAAA;YAAK8B,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7C/B,OAAA;cACEgC,OAAO,EAAEA,CAAA,KAAMzB,YAAY,CAAC,QAAQ,CAAE;cACtCuB,SAAS,EAAE,yDACTxB,SAAS,KAAK,QAAQ,GAClB,wBAAwB,GACxB,6CAA6C,EAChD;cAAAyB,QAAA,EACJ;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT3C,OAAA;cACEgC,OAAO,EAAEA,CAAA,KAAMzB,YAAY,CAAC,MAAM,CAAE;cACpCuB,SAAS,EAAE,yDACTxB,SAAS,KAAK,MAAM,GAChB,wBAAwB,GACxB,6CAA6C,EAChD;cAAAyB,QAAA,EACJ;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT3C,OAAA;cACEgC,OAAO,EAAEA,CAAA,KAAMzB,YAAY,CAAC,SAAS,CAAE;cACvCuB,SAAS,EAAE,yDACTxB,SAAS,KAAK,SAAS,GACnB,wBAAwB,GACxB,6CAA6C,EAChD;cAAAyB,QAAA,EACJ;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN3C,OAAA,CAACR,MAAM,CAACoD,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BjB,SAAS,EAAC,mCAAmC;UAAAC,QAAA,GAE5CzB,SAAS,KAAK,QAAQ,iBACrBN,OAAA;YAAK8B,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB/B,OAAA;cAAI8B,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAgB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACrEjC,MAAM,CAACI,MAAM,CAACoC,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBAC9BpD,OAAA;cAEEgC,OAAO,EAAEA,CAAA,KAAMvB,gBAAgB,CAAC0C,KAAK,CAACxC,EAAE,CAAE;cAC1CmB,SAAS,EAAE,mDACTtB,aAAa,KAAK2C,KAAK,CAACxC,EAAE,GACtB,mCAAmC,GACnC,8BAA8B,EACjC;cAAAoB,QAAA,eAEH/B,OAAA;gBAAK8B,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBAC1D/B,OAAA;kBAAK8B,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAC3BoB,KAAK,CAACnC,SAAS,gBACdhB,OAAA,CAACJ,eAAe;oBAACkC,SAAS,EAAC;kBAAwB;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAEtD3C,OAAA,CAACP,QAAQ;oBAACqC,SAAS,EAAC;kBAAuB;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAC9C;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACN3C,OAAA;kBAAK8B,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7B/B,OAAA;oBAAG8B,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,GACtDqB,KAAK,GAAG,CAAC,EAAC,IAAE,EAACD,KAAK,CAACvC,KAAK;kBAAA;oBAAA4B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,eACJ3C,OAAA;oBAAG8B,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAEoB,KAAK,CAACpC;kBAAQ;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GAtBDQ,KAAK,CAACxC,EAAE;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuBV,CACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,EAEArC,SAAS,KAAK,MAAM,iBACnBN,OAAA;YAAK8B,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB/B,OAAA;cAAI8B,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAgB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACrEjC,MAAM,CAACO,IAAI,CAACiC,GAAG,CAAEG,GAAG,iBACnBrD,OAAA;cAEE8B,SAAS,EAAC,8EAA8E;cAAAC,QAAA,eAExF/B,OAAA;gBAAK8B,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBAC1D/B,OAAA,CAACN,YAAY;kBAACoC,SAAS,EAAC;gBAAsB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjD3C,OAAA;kBAAK8B,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7B/B,OAAA;oBAAG8B,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,EACtDsB,GAAG,CAACzC;kBAAK;oBAAA4B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACJ3C,OAAA;oBAAG8B,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAEsB,GAAG,CAACnC;kBAAI;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GAXDU,GAAG,CAAC1C,EAAE;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAYR,CACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,EAEArC,SAAS,KAAK,SAAS,iBACtBN,OAAA;YAAK8B,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB/B,OAAA;cAAI8B,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAU;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAC/DjC,MAAM,CAACS,OAAO,CAAC+B,GAAG,CAAEI,IAAI,iBACvBtD,OAAA;cAEE8B,SAAS,EAAC,8EAA8E;cAAAC,QAAA,eAExF/B,OAAA;gBAAK8B,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBAC1D/B,OAAA,CAACL,yBAAyB;kBAACmC,SAAS,EAAC;gBAAyB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjE3C,OAAA;kBAAK8B,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7B/B,OAAA;oBAAG8B,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,EACtDuB,IAAI,CAAC1C;kBAAK;oBAAA4B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACJ3C,OAAA;oBAAG8B,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GACjCuB,IAAI,CAAClC,SAAS,EAAC,2BAChB,EAACkC,IAAI,CAACtC,SAAS,IAAIsC,IAAI,CAACjC,KAAK,iBAC3BrB,OAAA;sBAAM8B,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,GAAC,qDAAW,EAACuB,IAAI,CAACjC,KAAK,EAAC,GAAC;oBAAA;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CACrE;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,EACLW,IAAI,CAACtC,SAAS,iBACbhB,OAAA,CAACJ,eAAe;kBAACkC,SAAS,EAAC;gBAAwB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CACtD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC,GAnBDW,IAAI,CAAC3C,EAAE;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBT,CACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA,GAxFIrC,SAAS;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAyFJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtC,EAAA,CA/OIJ,YAAyC;AAAAsD,EAAA,GAAzCtD,YAAyC;AAiP/C,eAAeA,YAAY;AAAC,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}