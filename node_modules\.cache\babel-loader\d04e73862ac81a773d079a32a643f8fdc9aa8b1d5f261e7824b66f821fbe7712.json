{"ast": null, "code": "export const mockCourses = [{\n  id: '1',\n  title: 'أساسيات البرمجة',\n  description: 'تعلم أساسيات البرمجة من الصفر حتى الاحتراف',\n  categoryId: 'programming',\n  instructorId: 'admin-001',\n  videos: [],\n  pdfs: [],\n  quizzes: [],\n  isActive: true,\n  createdAt: new Date('2024-01-01'),\n  updatedAt: new Date('2024-01-15')\n}, {\n  id: '2',\n  title: 'تطوير المواقع الحديثة',\n  description: 'تعلم تطوير المواقع باستخدام أحدث التقنيات',\n  categoryId: 'web',\n  instructorId: 'admin-001',\n  videos: [],\n  pdfs: [],\n  quizzes: [],\n  isActive: true,\n  createdAt: new Date('2024-01-10'),\n  updatedAt: new Date('2024-01-20')\n}, {\n  id: '3',\n  title: 'الذكاء الاصطناعي للمبتدئين',\n  description: 'مقدمة شاملة في عالم الذكاء الاصطناعي',\n  categoryId: 'ai',\n  instructorId: 'admin-001',\n  videos: [],\n  pdfs: [],\n  quizzes: [],\n  isActive: true,\n  createdAt: new Date('2024-01-15'),\n  updatedAt: new Date('2024-01-25')\n}];\nexport const mockVideos = [{\n  id: '1',\n  title: 'مقدمة في البرمجة',\n  description: 'تعرف على أساسيات البرمجة',\n  url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',\n  duration: '15:30',\n  courseId: '1',\n  order: 1,\n  isWatched: false\n}, {\n  id: '2',\n  title: 'المتغيرات والثوابت',\n  description: 'تعلم كيفية استخدام المتغيرات',\n  url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',\n  duration: '20:45',\n  courseId: '1',\n  order: 2,\n  isWatched: false\n}];\nexport const mockQuizzes = [{\n  id: '1',\n  title: 'اختبار أساسيات البرمجة',\n  description: 'اختبر معرفتك في أساسيات البرمجة',\n  courseId: '1',\n  questions: [{\n    id: '1',\n    question: 'ما هو المتغير في البرمجة؟',\n    options: ['مكان لتخزين البيانات', 'نوع من الدوال', 'أمر للطباعة', 'لا شيء مما سبق'],\n    correctAnswer: 0,\n    explanation: 'المتغير هو مكان في الذاكرة لتخزين البيانات'\n  }],\n  timeLimit: 30,\n  passingScore: 70,\n  createdAt: new Date('2024-01-01')\n}];\nexport const mockCertificates = [{\n  id: 'cert-001',\n  studentId: 'student-001',\n  courseId: '1',\n  courseName: 'أساسيات البرمجة',\n  studentName: 'أحمد محمد',\n  issueDate: new Date('2024-02-01'),\n  certificateUrl: 'https://example.com/certificate/cert-001.pdf'\n}];", "map": {"version": 3, "names": ["mockCourses", "id", "title", "description", "categoryId", "instructorId", "videos", "pdfs", "quizzes", "isActive", "createdAt", "Date", "updatedAt", "mockVideos", "url", "duration", "courseId", "order", "isWatched", "mockQuizzes", "questions", "question", "options", "<PERSON><PERSON><PERSON><PERSON>", "explanation", "timeLimit", "passingScore", "mockCertificates", "studentId", "courseName", "studentName", "issueDate", "certificateUrl"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/data/mockCourses.ts"], "sourcesContent": ["import { Course, Video, Quiz, Certificate } from '../types';\n\nexport const mockCourses: Course[] = [\n  {\n    id: '1',\n    title: 'أساسيات البرمجة',\n    description: 'تعلم أساسيات البرمجة من الصفر حتى الاحتراف',\n    categoryId: 'programming',\n    instructorId: 'admin-001',\n    videos: [],\n    pdfs: [],\n    quizzes: [],\n    isActive: true,\n    createdAt: new Date('2024-01-01'),\n    updatedAt: new Date('2024-01-15')\n  },\n  {\n    id: '2',\n    title: 'تطوير المواقع الحديثة',\n    description: 'تعلم تطوير المواقع باستخدام أحدث التقنيات',\n    categoryId: 'web',\n    instructorId: 'admin-001',\n    videos: [],\n    pdfs: [],\n    quizzes: [],\n    isActive: true,\n    createdAt: new Date('2024-01-10'),\n    updatedAt: new Date('2024-01-20')\n  },\n  {\n    id: '3',\n    title: 'الذكاء الاصطناعي للمبتدئين',\n    description: 'مقدمة شاملة في عالم الذكاء الاصطناعي',\n    categoryId: 'ai',\n    instructorId: 'admin-001',\n    videos: [],\n    pdfs: [],\n    quizzes: [],\n    isActive: true,\n    createdAt: new Date('2024-01-15'),\n    updatedAt: new Date('2024-01-25')\n  }\n];\n\nexport const mockVideos: Video[] = [\n  {\n    id: '1',\n    title: 'مقدمة في البرمجة',\n    description: 'تعرف على أساسيات البرمجة',\n    url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',\n    duration: '15:30',\n    courseId: '1',\n    order: 1,\n    isWatched: false\n  },\n  {\n    id: '2',\n    title: 'المتغيرات والثوابت',\n    description: 'تعلم كيفية استخدام المتغيرات',\n    url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',\n    duration: '20:45',\n    courseId: '1',\n    order: 2,\n    isWatched: false\n  }\n];\n\nexport const mockQuizzes: Quiz[] = [\n  {\n    id: '1',\n    title: 'اختبار أساسيات البرمجة',\n    description: 'اختبر معرفتك في أساسيات البرمجة',\n    courseId: '1',\n    questions: [\n      {\n        id: '1',\n        question: 'ما هو المتغير في البرمجة؟',\n        options: [\n          'مكان لتخزين البيانات',\n          'نوع من الدوال',\n          'أمر للطباعة',\n          'لا شيء مما سبق'\n        ],\n        correctAnswer: 0,\n        explanation: 'المتغير هو مكان في الذاكرة لتخزين البيانات'\n      }\n    ],\n    timeLimit: 30,\n    passingScore: 70,\n    createdAt: new Date('2024-01-01')\n  }\n];\n\nexport const mockCertificates: Certificate[] = [\n  {\n    id: 'cert-001',\n    studentId: 'student-001',\n    courseId: '1',\n    courseName: 'أساسيات البرمجة',\n    studentName: 'أحمد محمد',\n    issueDate: new Date('2024-02-01'),\n    certificateUrl: 'https://example.com/certificate/cert-001.pdf'\n  }\n];\n"], "mappings": "AAEA,OAAO,MAAMA,WAAqB,GAAG,CACnC;EACEC,EAAE,EAAE,GAAG;EACPC,KAAK,EAAE,iBAAiB;EACxBC,WAAW,EAAE,4CAA4C;EACzDC,UAAU,EAAE,aAAa;EACzBC,YAAY,EAAE,WAAW;EACzBC,MAAM,EAAE,EAAE;EACVC,IAAI,EAAE,EAAE;EACRC,OAAO,EAAE,EAAE;EACXC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;EACjCC,SAAS,EAAE,IAAID,IAAI,CAAC,YAAY;AAClC,CAAC,EACD;EACEV,EAAE,EAAE,GAAG;EACPC,KAAK,EAAE,uBAAuB;EAC9BC,WAAW,EAAE,2CAA2C;EACxDC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE,WAAW;EACzBC,MAAM,EAAE,EAAE;EACVC,IAAI,EAAE,EAAE;EACRC,OAAO,EAAE,EAAE;EACXC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;EACjCC,SAAS,EAAE,IAAID,IAAI,CAAC,YAAY;AAClC,CAAC,EACD;EACEV,EAAE,EAAE,GAAG;EACPC,KAAK,EAAE,4BAA4B;EACnCC,WAAW,EAAE,sCAAsC;EACnDC,UAAU,EAAE,IAAI;EAChBC,YAAY,EAAE,WAAW;EACzBC,MAAM,EAAE,EAAE;EACVC,IAAI,EAAE,EAAE;EACRC,OAAO,EAAE,EAAE;EACXC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;EACjCC,SAAS,EAAE,IAAID,IAAI,CAAC,YAAY;AAClC,CAAC,CACF;AAED,OAAO,MAAME,UAAmB,GAAG,CACjC;EACEZ,EAAE,EAAE,GAAG;EACPC,KAAK,EAAE,kBAAkB;EACzBC,WAAW,EAAE,0BAA0B;EACvCW,GAAG,EAAE,6CAA6C;EAClDC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE,GAAG;EACbC,KAAK,EAAE,CAAC;EACRC,SAAS,EAAE;AACb,CAAC,EACD;EACEjB,EAAE,EAAE,GAAG;EACPC,KAAK,EAAE,oBAAoB;EAC3BC,WAAW,EAAE,8BAA8B;EAC3CW,GAAG,EAAE,6CAA6C;EAClDC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE,GAAG;EACbC,KAAK,EAAE,CAAC;EACRC,SAAS,EAAE;AACb,CAAC,CACF;AAED,OAAO,MAAMC,WAAmB,GAAG,CACjC;EACElB,EAAE,EAAE,GAAG;EACPC,KAAK,EAAE,wBAAwB;EAC/BC,WAAW,EAAE,iCAAiC;EAC9Ca,QAAQ,EAAE,GAAG;EACbI,SAAS,EAAE,CACT;IACEnB,EAAE,EAAE,GAAG;IACPoB,QAAQ,EAAE,2BAA2B;IACrCC,OAAO,EAAE,CACP,sBAAsB,EACtB,eAAe,EACf,aAAa,EACb,gBAAgB,CACjB;IACDC,aAAa,EAAE,CAAC;IAChBC,WAAW,EAAE;EACf,CAAC,CACF;EACDC,SAAS,EAAE,EAAE;EACbC,YAAY,EAAE,EAAE;EAChBhB,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;AAClC,CAAC,CACF;AAED,OAAO,MAAMgB,gBAA+B,GAAG,CAC7C;EACE1B,EAAE,EAAE,UAAU;EACd2B,SAAS,EAAE,aAAa;EACxBZ,QAAQ,EAAE,GAAG;EACba,UAAU,EAAE,iBAAiB;EAC7BC,WAAW,EAAE,WAAW;EACxBC,SAAS,EAAE,IAAIpB,IAAI,CAAC,YAAY,CAAC;EACjCqB,cAAc,EAAE;AAClB,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}