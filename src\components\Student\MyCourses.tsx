import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  AcademicCapIcon,
  PlayIcon,
  DocumentIcon,
  ClipboardDocumentListIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

// Types
import { Course } from '../../types';

interface MyCoursesProps {
  onBack: () => void;
  onSelectCourse: (courseId: string) => void;
}

const MyCourses: React.FC<MyCoursesProps> = ({ onBack, onSelectCourse }) => {
  const [filter, setFilter] = useState('all');

  // Mock data for demonstration
  const mockCourses: Course[] = [
    {
      id: '1',
      title: 'أساسيات البرمجة',
      description: 'تعلم أساسيات البرمجة من الصفر',
      categoryId: 'programming',
      instructorId: 'admin',
      videos: [{ id: '1', courseId: '1', title: 'مقدمة', videoUrl: '', orderIndex: 1, isActive: true, createdAt: new Date() }],
      pdfs: [],
      quizzes: [],
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: '2',
      title: 'تطوير المواقع',
      description: 'تعلم تطوير المواقع الحديثة',
      categoryId: 'web',
      instructorId: 'admin',
      videos: [
        { id: '2', courseId: '2', title: 'HTML', videoUrl: '', orderIndex: 1, isActive: true, createdAt: new Date() },
        { id: '3', courseId: '2', title: 'CSS', videoUrl: '', orderIndex: 2, isActive: true, createdAt: new Date() }
      ],
      pdfs: [{ id: '1', courseId: '2', title: 'مرجع HTML', fileUrl: '', fileSize: 1024, orderIndex: 1, isActive: true, createdAt: new Date() }],
      quizzes: [{ id: '1', courseId: '2', title: 'اختبار HTML', questions: [], passingScore: 70, attempts: 3, isActive: true, createdAt: new Date() }],
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  const [courses] = useState(mockCourses);

  // Mock progress data
  const courseProgress: { [key: string]: { completed: number; totalVideos: number; completedVideos: number } } = {
    '1': { completed: 80, totalVideos: 10, completedVideos: 8 },
    '2': { completed: 45, totalVideos: 15, completedVideos: 7 }
  };

  const filteredCourses = courses.filter(course => {
    if (filter === 'completed') {
      return courseProgress[course.id]?.completed === 100;
    } else if (filter === 'in-progress') {
      return courseProgress[course.id]?.completed > 0 && courseProgress[course.id]?.completed < 100;
    }
    return true;
  });

  const getProgressColor = (progress: number) => {
    if (progress === 100) return 'bg-green-500';
    if (progress >= 50) return 'bg-blue-500';
    return 'bg-yellow-500';
  };

  const getStatusIcon = (progress: number) => {
    if (progress === 100) {
      return <CheckCircleIcon className="w-5 h-5 text-green-600" />;
    }
    return <ClockIcon className="w-5 h-5 text-blue-600" />;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4 space-x-reverse">
        <button
          onClick={onBack}
          className="p-2 text-gray-600 hover:text-gray-900 transition-colors"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">كورساتي</h1>
          <p className="text-gray-600">تابع تقدمك في الكورسات المسجل بها</p>
        </div>
      </div>

      {/* Filter Tabs */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex space-x-4 space-x-reverse">
          <button
            onClick={() => setFilter('all')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              filter === 'all'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            جميع الكورسات
          </button>
          <button
            onClick={() => setFilter('in-progress')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              filter === 'in-progress'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            قيد التقدم
          </button>
          <button
            onClick={() => setFilter('completed')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              filter === 'completed'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            مكتملة
          </button>
        </div>
      </div>

      {/* Courses Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredCourses.map((course, index) => {
          const progress = courseProgress[course.id] || { completed: 0, totalVideos: 0, completedVideos: 0 };
          
          return (
            <motion.div
              key={course.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => onSelectCourse(course.id)}
            >
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <AcademicCapIcon className="w-6 h-6 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{course.title}</h3>
                      <p className="text-sm text-gray-600">{course.description}</p>
                    </div>
                  </div>
                  {getStatusIcon(progress.completed)}
                </div>

                {/* Progress Bar */}
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600">التقدم</span>
                    <span className="text-sm font-medium text-gray-900">{progress.completed}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(progress.completed)}`}
                      style={{ width: `${progress.completed}%` }}
                    ></div>
                  </div>
                </div>

                {/* Course Stats */}
                <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                  <div className="flex items-center space-x-1 space-x-reverse">
                    <PlayIcon className="w-4 h-4" />
                    <span>{progress.completedVideos}/{progress.totalVideos} فيديو</span>
                  </div>
                  <div className="flex items-center space-x-1 space-x-reverse">
                    <DocumentIcon className="w-4 h-4" />
                    <span>{course.pdfs.length} ملف</span>
                  </div>
                  <div className="flex items-center space-x-1 space-x-reverse">
                    <ClipboardDocumentListIcon className="w-4 h-4" />
                    <span>{course.quizzes.length} اختبار</span>
                  </div>
                </div>

                {/* Continue Button */}
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onSelectCourse(course.id);
                  }}
                  className="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  {progress.completed === 100 ? 'مراجعة الكورس' : 'متابعة التعلم'}
                </button>
              </div>
            </motion.div>
          );
        })}
      </div>

      {filteredCourses.length === 0 && (
        <div className="text-center py-12">
          <AcademicCapIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد كورسات</h3>
          <p className="text-gray-600">
            {filter === 'completed' 
              ? 'لم تكمل أي كورسات بعد'
              : filter === 'in-progress'
              ? 'لا توجد كورسات قيد التقدم'
              : 'لم تسجل في أي كورسات بعد'
            }
          </p>
        </div>
      )}
    </div>
  );
};

export default MyCourses;
