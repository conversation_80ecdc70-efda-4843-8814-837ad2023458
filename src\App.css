@tailwind base;
@tailwind components;
@tailwind utilities;

/* RTL Support */
* {
  direction: rtl;
}

body {
  font-family: 'Cairo', 'Taja<PERSON>', sans-serif;
  direction: rtl;
  text-align: right;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.bounce-gentle {
  animation: bounceGentle 2s infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounceGentle {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* Custom Button Styles */
.btn-primary {
  @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-secondary {
  @apply bg-secondary-200 hover:bg-secondary-300 text-secondary-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-outline {
  @apply border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white font-medium py-2 px-4 rounded-lg transition-all duration-200;
}

/* Card Styles */
.card {
  @apply bg-white rounded-xl shadow-sm border border-gray-100 p-6;
}

.card-hover {
  @apply card hover:shadow-md transition-shadow duration-200;
}

/* Form Styles */
.form-input {
  @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

/* Loading Animation */
.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: dots 1.5s steps(4, end) infinite;
}

@keyframes dots {
  0%, 20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%, 100% {
    content: '...';
  }
}

/* Gradient Backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* Video Player Styles */
.video-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.video-container iframe,
.video-container video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 12px;
}

/* Quiz Styles */
.quiz-option {
  @apply p-4 border-2 border-gray-200 rounded-lg cursor-pointer transition-all duration-200 hover:border-primary-300;
}

.quiz-option.selected {
  @apply border-primary-500 bg-primary-50;
}

.quiz-option.correct {
  @apply border-green-500 bg-green-50;
}

.quiz-option.incorrect {
  @apply border-red-500 bg-red-50;
}

/* Certificate Styles */
.certificate-container {
  @apply bg-white border-4 border-primary-600 rounded-lg p-8 text-center shadow-lg;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .mobile-menu {
    @apply fixed inset-0 z-50 bg-white;
  }
  
  .mobile-menu-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 z-40;
  }
}

/* Dark Mode Support (Optional) */
@media (prefers-color-scheme: dark) {
  .dark-mode {
    @apply bg-gray-900 text-white;
  }
  
  .dark-mode .card {
    @apply bg-gray-800 border-gray-700;
  }
  
  .dark-mode .form-input {
    @apply bg-gray-800 border-gray-600 text-white;
  }
}
