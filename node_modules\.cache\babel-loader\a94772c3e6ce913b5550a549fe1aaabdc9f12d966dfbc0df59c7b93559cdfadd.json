{"ast": null, "code": "import React,{useState}from'react';import{motion}from'framer-motion';import{PlayIcon,DocumentIcon,ClipboardDocumentListIcon,CheckCircleIcon,ArrowLeftIcon,ArrowRightIcon}from'@heroicons/react/24/outline';// Types\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CourseViewer=_ref=>{let{user,courseId,onBack}=_ref;const[activeTab,setActiveTab]=useState('videos');const[selectedVideo,setSelectedVideo]=useState('1');// Mock course data\nconst course={id:courseId,title:'أساسيات البرمجة',description:'تعلم أساسيات البرمجة من الصفر',videos:[{id:'1',title:'مقدمة في البرمجة',duration:'15:30',completed:true},{id:'2',title:'المتغيرات والثوابت',duration:'22:45',completed:true},{id:'3',title:'الحلقات التكرارية',duration:'18:20',completed:false},{id:'4',title:'الدوال',duration:'25:10',completed:false}],pdfs:[{id:'1',title:'مرجع البرمجة الأساسي',size:'2.5 MB'},{id:'2',title:'تمارين عملية',size:'1.8 MB'}],quizzes:[{id:'1',title:'اختبار المتغيرات',questions:10,completed:true,score:85},{id:'2',title:'اختبار الحلقات',questions:8,completed:false,score:null}]};const currentVideo=course.videos.find(v=>v.id===selectedVideo);const handleNextVideo=()=>{const currentIndex=course.videos.findIndex(v=>v.id===selectedVideo);if(currentIndex<course.videos.length-1){setSelectedVideo(course.videos[currentIndex+1].id);}};const handlePrevVideo=()=>{const currentIndex=course.videos.findIndex(v=>v.id===selectedVideo);if(currentIndex>0){setSelectedVideo(course.videos[currentIndex-1].id);}};return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4 space-x-reverse\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:onBack,className:\"p-2 text-gray-600 hover:text-gray-900 transition-colors\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-6 h-6\",fill:\"none\",stroke:\"currentColor\",viewBox:\"0 0 24 24\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M15 19l-7-7 7-7\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:course.title}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:course.description})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 lg:grid-cols-3 gap-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"lg:col-span-2 space-y-4\",children:/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:\"bg-white rounded-lg shadow-sm overflow-hidden\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"aspect-video bg-gray-900 flex items-center justify-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center text-white\",children:[/*#__PURE__*/_jsx(PlayIcon,{className:\"w-16 h-16 mx-auto mb-4 opacity-50\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold mb-2\",children:currentVideo===null||currentVideo===void 0?void 0:currentVideo.title}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-gray-300\",children:[\"\\u0645\\u062F\\u0629 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648: \",currentVideo===null||currentVideo===void 0?void 0:currentVideo.duration]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"p-4 border-t border-gray-200\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4 space-x-reverse\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:handlePrevVideo,disabled:course.videos.findIndex(v=>v.id===selectedVideo)===0,className:\"flex items-center space-x-2 space-x-reverse px-3 py-2 text-sm bg-gray-100 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",children:[/*#__PURE__*/_jsx(ArrowRightIcon,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u0627\\u0644\\u0633\\u0627\\u0628\\u0642\"})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleNextVideo,disabled:course.videos.findIndex(v=>v.id===selectedVideo)===course.videos.length-1,className:\"flex items-center space-x-2 space-x-reverse px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u0627\\u0644\\u062A\\u0627\\u0644\\u064A\"}),/*#__PURE__*/_jsx(ArrowLeftIcon,{className:\"w-4 h-4\"})]})]}),/*#__PURE__*/_jsx(\"button\",{className:\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",children:\"\\u062A\\u0645 \\u0627\\u0644\\u0645\\u0634\\u0627\\u0647\\u062F\\u0629\"})]})})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow-sm p-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-2 space-x-reverse\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setActiveTab('videos'),className:`flex-1 py-2 px-3 text-sm rounded-lg transition-colors ${activeTab==='videos'?'bg-blue-600 text-white':'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,children:\"\\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\\u0647\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setActiveTab('pdfs'),className:`flex-1 py-2 px-3 text-sm rounded-lg transition-colors ${activeTab==='pdfs'?'bg-blue-600 text-white':'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,children:\"\\u0627\\u0644\\u0645\\u0644\\u0641\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setActiveTab('quizzes'),className:`flex-1 py-2 px-3 text-sm rounded-lg transition-colors ${activeTab==='quizzes'?'bg-blue-600 text-white':'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,children:\"\\u0627\\u0644\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\\u0627\\u062A\"})]})}),/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:\"bg-white rounded-lg shadow-sm p-4\",children:[activeTab==='videos'&&/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold text-gray-900 mb-4\",children:\"\\u0642\\u0627\\u0626\\u0645\\u0629 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\\u0647\\u0627\\u062A\"}),course.videos.map((video,index)=>/*#__PURE__*/_jsx(\"div\",{onClick:()=>setSelectedVideo(video.id),className:`p-3 rounded-lg cursor-pointer transition-colors ${selectedVideo===video.id?'bg-blue-50 border border-blue-200':'bg-gray-50 hover:bg-gray-100'}`,children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 space-x-reverse\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:video.completed?/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"w-5 h-5 text-green-600\"}):/*#__PURE__*/_jsx(PlayIcon,{className:\"w-5 h-5 text-gray-400\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 min-w-0\",children:[/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm font-medium text-gray-900 truncate\",children:[index+1,\". \",video.title]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500\",children:video.duration})]})]})},video.id))]}),activeTab==='pdfs'&&/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold text-gray-900 mb-4\",children:\"\\u0627\\u0644\\u0645\\u0644\\u0641\\u0627\\u062A \\u0648\\u0627\\u0644\\u0645\\u0631\\u0627\\u062C\\u0639\"}),course.pdfs.map(pdf=>/*#__PURE__*/_jsx(\"div\",{className:\"p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 space-x-reverse\",children:[/*#__PURE__*/_jsx(DocumentIcon,{className:\"w-5 h-5 text-red-600\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 min-w-0\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-900 truncate\",children:pdf.title}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500\",children:pdf.size})]})]})},pdf.id))]}),activeTab==='quizzes'&&/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold text-gray-900 mb-4\",children:\"\\u0627\\u0644\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\\u0627\\u062A\"}),course.quizzes.map(quiz=>/*#__PURE__*/_jsx(\"div\",{className:\"p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 space-x-reverse\",children:[/*#__PURE__*/_jsx(ClipboardDocumentListIcon,{className:\"w-5 h-5 text-purple-600\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 min-w-0\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-900 truncate\",children:quiz.title}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-xs text-gray-500\",children:[quiz.questions,\" \\u0633\\u0624\\u0627\\u0644\",quiz.completed&&quiz.score&&/*#__PURE__*/_jsxs(\"span\",{className:\"text-green-600 mr-2\",children:[\"\\u2022 \\u0627\\u0644\\u0646\\u062A\\u064A\\u062C\\u0629: \",quiz.score,\"%\"]})]})]}),quiz.completed&&/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"w-5 h-5 text-green-600\"})]})},quiz.id))]})]},activeTab)]})]})]});};export default CourseViewer;", "map": {"version": 3, "names": ["React", "useState", "motion", "PlayIcon", "DocumentIcon", "ClipboardDocumentListIcon", "CheckCircleIcon", "ArrowLeftIcon", "ArrowRightIcon", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON>iewer", "_ref", "user", "courseId", "onBack", "activeTab", "setActiveTab", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedVideo", "course", "id", "title", "description", "videos", "duration", "completed", "pdfs", "size", "quizzes", "questions", "score", "currentVideo", "find", "v", "handleNextVideo", "currentIndex", "findIndex", "length", "handlePrevVideo", "className", "children", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "div", "initial", "opacity", "y", "animate", "disabled", "map", "video", "index", "pdf", "quiz"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Student/CourseViewer.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  PlayIcon,\n  DocumentIcon,\n  ClipboardDocumentListIcon,\n  CheckCircleIcon,\n  ArrowLeftIcon,\n  ArrowRightIcon\n} from '@heroicons/react/24/outline';\n\n// Types\nimport { Student } from '../../types';\n\ninterface CourseViewerProps {\n  user?: Student;\n  courseId?: string;\n  onBack?: () => void;\n}\n\nconst CourseViewer: React.FC<CourseViewerProps> = ({ user, courseId, onBack }) => {\n  const [activeTab, setActiveTab] = useState('videos');\n  const [selectedVideo, setSelectedVideo] = useState('1');\n\n  // Mock course data\n  const course = {\n    id: courseId,\n    title: 'أساسيات البرمجة',\n    description: 'تعلم أساسيات البرمجة من الصفر',\n    videos: [\n      { id: '1', title: 'مقدمة في البرمجة', duration: '15:30', completed: true },\n      { id: '2', title: 'المتغيرات والثوابت', duration: '22:45', completed: true },\n      { id: '3', title: 'الحلقات التكرارية', duration: '18:20', completed: false },\n      { id: '4', title: 'الدوال', duration: '25:10', completed: false }\n    ],\n    pdfs: [\n      { id: '1', title: 'مرجع البرمجة الأساسي', size: '2.5 MB' },\n      { id: '2', title: 'تمارين عملية', size: '1.8 MB' }\n    ],\n    quizzes: [\n      { id: '1', title: 'اختبار المتغيرات', questions: 10, completed: true, score: 85 },\n      { id: '2', title: 'اختبار الحلقات', questions: 8, completed: false, score: null }\n    ]\n  };\n\n  const currentVideo = course.videos.find(v => v.id === selectedVideo);\n\n  const handleNextVideo = () => {\n    const currentIndex = course.videos.findIndex(v => v.id === selectedVideo);\n    if (currentIndex < course.videos.length - 1) {\n      setSelectedVideo(course.videos[currentIndex + 1].id);\n    }\n  };\n\n  const handlePrevVideo = () => {\n    const currentIndex = course.videos.findIndex(v => v.id === selectedVideo);\n    if (currentIndex > 0) {\n      setSelectedVideo(course.videos[currentIndex - 1].id);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center space-x-4 space-x-reverse\">\n        <button\n          onClick={onBack}\n          className=\"p-2 text-gray-600 hover:text-gray-900 transition-colors\"\n        >\n          <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n          </svg>\n        </button>\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">{course.title}</h1>\n          <p className=\"text-gray-600\">{course.description}</p>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Video Player */}\n        <div className=\"lg:col-span-2 space-y-4\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            className=\"bg-white rounded-lg shadow-sm overflow-hidden\"\n          >\n            {/* Video Area */}\n            <div className=\"aspect-video bg-gray-900 flex items-center justify-center\">\n              <div className=\"text-center text-white\">\n                <PlayIcon className=\"w-16 h-16 mx-auto mb-4 opacity-50\" />\n                <h3 className=\"text-xl font-semibold mb-2\">{currentVideo?.title}</h3>\n                <p className=\"text-gray-300\">مدة الفيديو: {currentVideo?.duration}</p>\n              </div>\n            </div>\n\n            {/* Video Controls */}\n            <div className=\"p-4 border-t border-gray-200\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-4 space-x-reverse\">\n                  <button\n                    onClick={handlePrevVideo}\n                    disabled={course.videos.findIndex(v => v.id === selectedVideo) === 0}\n                    className=\"flex items-center space-x-2 space-x-reverse px-3 py-2 text-sm bg-gray-100 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n                  >\n                    <ArrowRightIcon className=\"w-4 h-4\" />\n                    <span>السابق</span>\n                  </button>\n                  <button\n                    onClick={handleNextVideo}\n                    disabled={course.videos.findIndex(v => v.id === selectedVideo) === course.videos.length - 1}\n                    className=\"flex items-center space-x-2 space-x-reverse px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n                  >\n                    <span>التالي</span>\n                    <ArrowLeftIcon className=\"w-4 h-4\" />\n                  </button>\n                </div>\n                <button className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\">\n                  تم المشاهدة\n                </button>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n\n        {/* Course Content Sidebar */}\n        <div className=\"space-y-4\">\n          {/* Tabs */}\n          <div className=\"bg-white rounded-lg shadow-sm p-4\">\n            <div className=\"flex space-x-2 space-x-reverse\">\n              <button\n                onClick={() => setActiveTab('videos')}\n                className={`flex-1 py-2 px-3 text-sm rounded-lg transition-colors ${\n                  activeTab === 'videos'\n                    ? 'bg-blue-600 text-white'\n                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                }`}\n              >\n                الفيديوهات\n              </button>\n              <button\n                onClick={() => setActiveTab('pdfs')}\n                className={`flex-1 py-2 px-3 text-sm rounded-lg transition-colors ${\n                  activeTab === 'pdfs'\n                    ? 'bg-blue-600 text-white'\n                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                }`}\n              >\n                الملفات\n              </button>\n              <button\n                onClick={() => setActiveTab('quizzes')}\n                className={`flex-1 py-2 px-3 text-sm rounded-lg transition-colors ${\n                  activeTab === 'quizzes'\n                    ? 'bg-blue-600 text-white'\n                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                }`}\n              >\n                الاختبارات\n              </button>\n            </div>\n          </div>\n\n          {/* Content */}\n          <motion.div\n            key={activeTab}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            className=\"bg-white rounded-lg shadow-sm p-4\"\n          >\n            {activeTab === 'videos' && (\n              <div className=\"space-y-3\">\n                <h3 className=\"font-semibold text-gray-900 mb-4\">قائمة الفيديوهات</h3>\n                {course.videos.map((video, index) => (\n                  <div\n                    key={video.id}\n                    onClick={() => setSelectedVideo(video.id)}\n                    className={`p-3 rounded-lg cursor-pointer transition-colors ${\n                      selectedVideo === video.id\n                        ? 'bg-blue-50 border border-blue-200'\n                        : 'bg-gray-50 hover:bg-gray-100'\n                    }`}\n                  >\n                    <div className=\"flex items-center space-x-3 space-x-reverse\">\n                      <div className=\"flex-shrink-0\">\n                        {video.completed ? (\n                          <CheckCircleIcon className=\"w-5 h-5 text-green-600\" />\n                        ) : (\n                          <PlayIcon className=\"w-5 h-5 text-gray-400\" />\n                        )}\n                      </div>\n                      <div className=\"flex-1 min-w-0\">\n                        <p className=\"text-sm font-medium text-gray-900 truncate\">\n                          {index + 1}. {video.title}\n                        </p>\n                        <p className=\"text-xs text-gray-500\">{video.duration}</p>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n\n            {activeTab === 'pdfs' && (\n              <div className=\"space-y-3\">\n                <h3 className=\"font-semibold text-gray-900 mb-4\">الملفات والمراجع</h3>\n                {course.pdfs.map((pdf) => (\n                  <div\n                    key={pdf.id}\n                    className=\"p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors\"\n                  >\n                    <div className=\"flex items-center space-x-3 space-x-reverse\">\n                      <DocumentIcon className=\"w-5 h-5 text-red-600\" />\n                      <div className=\"flex-1 min-w-0\">\n                        <p className=\"text-sm font-medium text-gray-900 truncate\">\n                          {pdf.title}\n                        </p>\n                        <p className=\"text-xs text-gray-500\">{pdf.size}</p>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n\n            {activeTab === 'quizzes' && (\n              <div className=\"space-y-3\">\n                <h3 className=\"font-semibold text-gray-900 mb-4\">الاختبارات</h3>\n                {course.quizzes.map((quiz) => (\n                  <div\n                    key={quiz.id}\n                    className=\"p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors\"\n                  >\n                    <div className=\"flex items-center space-x-3 space-x-reverse\">\n                      <ClipboardDocumentListIcon className=\"w-5 h-5 text-purple-600\" />\n                      <div className=\"flex-1 min-w-0\">\n                        <p className=\"text-sm font-medium text-gray-900 truncate\">\n                          {quiz.title}\n                        </p>\n                        <p className=\"text-xs text-gray-500\">\n                          {quiz.questions} سؤال\n                          {quiz.completed && quiz.score && (\n                            <span className=\"text-green-600 mr-2\">• النتيجة: {quiz.score}%</span>\n                          )}\n                        </p>\n                      </div>\n                      {quiz.completed && (\n                        <CheckCircleIcon className=\"w-5 h-5 text-green-600\" />\n                      )}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </motion.div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CourseViewer;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,MAAM,KAAQ,eAAe,CACtC,OACEC,QAAQ,CACRC,YAAY,CACZC,yBAAyB,CACzBC,eAAe,CACfC,aAAa,CACbC,cAAc,KACT,6BAA6B,CAEpC;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBASA,KAAM,CAAAC,YAAyC,CAAGC,IAAA,EAAgC,IAA/B,CAAEC,IAAI,CAAEC,QAAQ,CAAEC,MAAO,CAAC,CAAAH,IAAA,CAC3E,KAAM,CAACI,SAAS,CAAEC,YAAY,CAAC,CAAGlB,QAAQ,CAAC,QAAQ,CAAC,CACpD,KAAM,CAACmB,aAAa,CAAEC,gBAAgB,CAAC,CAAGpB,QAAQ,CAAC,GAAG,CAAC,CAEvD;AACA,KAAM,CAAAqB,MAAM,CAAG,CACbC,EAAE,CAAEP,QAAQ,CACZQ,KAAK,CAAE,iBAAiB,CACxBC,WAAW,CAAE,+BAA+B,CAC5CC,MAAM,CAAE,CACN,CAAEH,EAAE,CAAE,GAAG,CAAEC,KAAK,CAAE,kBAAkB,CAAEG,QAAQ,CAAE,OAAO,CAAEC,SAAS,CAAE,IAAK,CAAC,CAC1E,CAAEL,EAAE,CAAE,GAAG,CAAEC,KAAK,CAAE,oBAAoB,CAAEG,QAAQ,CAAE,OAAO,CAAEC,SAAS,CAAE,IAAK,CAAC,CAC5E,CAAEL,EAAE,CAAE,GAAG,CAAEC,KAAK,CAAE,mBAAmB,CAAEG,QAAQ,CAAE,OAAO,CAAEC,SAAS,CAAE,KAAM,CAAC,CAC5E,CAAEL,EAAE,CAAE,GAAG,CAAEC,KAAK,CAAE,QAAQ,CAAEG,QAAQ,CAAE,OAAO,CAAEC,SAAS,CAAE,KAAM,CAAC,CAClE,CACDC,IAAI,CAAE,CACJ,CAAEN,EAAE,CAAE,GAAG,CAAEC,KAAK,CAAE,sBAAsB,CAAEM,IAAI,CAAE,QAAS,CAAC,CAC1D,CAAEP,EAAE,CAAE,GAAG,CAAEC,KAAK,CAAE,cAAc,CAAEM,IAAI,CAAE,QAAS,CAAC,CACnD,CACDC,OAAO,CAAE,CACP,CAAER,EAAE,CAAE,GAAG,CAAEC,KAAK,CAAE,kBAAkB,CAAEQ,SAAS,CAAE,EAAE,CAAEJ,SAAS,CAAE,IAAI,CAAEK,KAAK,CAAE,EAAG,CAAC,CACjF,CAAEV,EAAE,CAAE,GAAG,CAAEC,KAAK,CAAE,gBAAgB,CAAEQ,SAAS,CAAE,CAAC,CAAEJ,SAAS,CAAE,KAAK,CAAEK,KAAK,CAAE,IAAK,CAAC,CAErF,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGZ,MAAM,CAACI,MAAM,CAACS,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACb,EAAE,GAAKH,aAAa,CAAC,CAEpE,KAAM,CAAAiB,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAAC,YAAY,CAAGhB,MAAM,CAACI,MAAM,CAACa,SAAS,CAACH,CAAC,EAAIA,CAAC,CAACb,EAAE,GAAKH,aAAa,CAAC,CACzE,GAAIkB,YAAY,CAAGhB,MAAM,CAACI,MAAM,CAACc,MAAM,CAAG,CAAC,CAAE,CAC3CnB,gBAAgB,CAACC,MAAM,CAACI,MAAM,CAACY,YAAY,CAAG,CAAC,CAAC,CAACf,EAAE,CAAC,CACtD,CACF,CAAC,CAED,KAAM,CAAAkB,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAAH,YAAY,CAAGhB,MAAM,CAACI,MAAM,CAACa,SAAS,CAACH,CAAC,EAAIA,CAAC,CAACb,EAAE,GAAKH,aAAa,CAAC,CACzE,GAAIkB,YAAY,CAAG,CAAC,CAAE,CACpBjB,gBAAgB,CAACC,MAAM,CAACI,MAAM,CAACY,YAAY,CAAG,CAAC,CAAC,CAACf,EAAE,CAAC,CACtD,CACF,CAAC,CAED,mBACEX,KAAA,QAAK8B,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExB/B,KAAA,QAAK8B,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DjC,IAAA,WACEkC,OAAO,CAAE3B,MAAO,CAChByB,SAAS,CAAC,yDAAyD,CAAAC,QAAA,cAEnEjC,IAAA,QAAKgC,SAAS,CAAC,SAAS,CAACG,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAJ,QAAA,cAC5EjC,IAAA,SAAMsC,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,WAAW,CAAE,CAAE,CAACC,CAAC,CAAC,iBAAiB,CAAE,CAAC,CACtF,CAAC,CACA,CAAC,cACTvC,KAAA,QAAA+B,QAAA,eACEjC,IAAA,OAAIgC,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAErB,MAAM,CAACE,KAAK,CAAK,CAAC,cACpEd,IAAA,MAAGgC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAErB,MAAM,CAACG,WAAW,CAAI,CAAC,EAClD,CAAC,EACH,CAAC,cAENb,KAAA,QAAK8B,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eAEpDjC,IAAA,QAAKgC,SAAS,CAAC,yBAAyB,CAAAC,QAAA,cACtC/B,KAAA,CAACV,MAAM,CAACkD,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9Bb,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eAGzDjC,IAAA,QAAKgC,SAAS,CAAC,2DAA2D,CAAAC,QAAA,cACxE/B,KAAA,QAAK8B,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCjC,IAAA,CAACP,QAAQ,EAACuC,SAAS,CAAC,mCAAmC,CAAE,CAAC,cAC1DhC,IAAA,OAAIgC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAET,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEV,KAAK,CAAK,CAAC,cACrEZ,KAAA,MAAG8B,SAAS,CAAC,eAAe,CAAAC,QAAA,EAAC,iEAAa,CAACT,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEP,QAAQ,EAAI,CAAC,EACnE,CAAC,CACH,CAAC,cAGNjB,IAAA,QAAKgC,SAAS,CAAC,8BAA8B,CAAAC,QAAA,cAC3C/B,KAAA,QAAK8B,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD/B,KAAA,QAAK8B,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1D/B,KAAA,WACEgC,OAAO,CAAEH,eAAgB,CACzBgB,QAAQ,CAAEnC,MAAM,CAACI,MAAM,CAACa,SAAS,CAACH,CAAC,EAAIA,CAAC,CAACb,EAAE,GAAKH,aAAa,CAAC,GAAK,CAAE,CACrEsB,SAAS,CAAC,0KAA0K,CAAAC,QAAA,eAEpLjC,IAAA,CAACF,cAAc,EAACkC,SAAS,CAAC,SAAS,CAAE,CAAC,cACtChC,IAAA,SAAAiC,QAAA,CAAM,sCAAM,CAAM,CAAC,EACb,CAAC,cACT/B,KAAA,WACEgC,OAAO,CAAEP,eAAgB,CACzBoB,QAAQ,CAAEnC,MAAM,CAACI,MAAM,CAACa,SAAS,CAACH,CAAC,EAAIA,CAAC,CAACb,EAAE,GAAKH,aAAa,CAAC,GAAKE,MAAM,CAACI,MAAM,CAACc,MAAM,CAAG,CAAE,CAC5FE,SAAS,CAAC,qLAAqL,CAAAC,QAAA,eAE/LjC,IAAA,SAAAiC,QAAA,CAAM,sCAAM,CAAM,CAAC,cACnBjC,IAAA,CAACH,aAAa,EAACmC,SAAS,CAAC,SAAS,CAAE,CAAC,EAC/B,CAAC,EACN,CAAC,cACNhC,IAAA,WAAQgC,SAAS,CAAC,mFAAmF,CAAAC,QAAA,CAAC,+DAEtG,CAAQ,CAAC,EACN,CAAC,CACH,CAAC,EACI,CAAC,CACV,CAAC,cAGN/B,KAAA,QAAK8B,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBjC,IAAA,QAAKgC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,cAChD/B,KAAA,QAAK8B,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7CjC,IAAA,WACEkC,OAAO,CAAEA,CAAA,GAAMzB,YAAY,CAAC,QAAQ,CAAE,CACtCuB,SAAS,CAAE,yDACTxB,SAAS,GAAK,QAAQ,CAClB,wBAAwB,CACxB,6CAA6C,EAChD,CAAAyB,QAAA,CACJ,8DAED,CAAQ,CAAC,cACTjC,IAAA,WACEkC,OAAO,CAAEA,CAAA,GAAMzB,YAAY,CAAC,MAAM,CAAE,CACpCuB,SAAS,CAAE,yDACTxB,SAAS,GAAK,MAAM,CAChB,wBAAwB,CACxB,6CAA6C,EAChD,CAAAyB,QAAA,CACJ,4CAED,CAAQ,CAAC,cACTjC,IAAA,WACEkC,OAAO,CAAEA,CAAA,GAAMzB,YAAY,CAAC,SAAS,CAAE,CACvCuB,SAAS,CAAE,yDACTxB,SAAS,GAAK,SAAS,CACnB,wBAAwB,CACxB,6CAA6C,EAChD,CAAAyB,QAAA,CACJ,8DAED,CAAQ,CAAC,EACN,CAAC,CACH,CAAC,cAGN/B,KAAA,CAACV,MAAM,CAACkD,GAAG,EAETC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9Bb,SAAS,CAAC,mCAAmC,CAAAC,QAAA,EAE5CzB,SAAS,GAAK,QAAQ,eACrBN,KAAA,QAAK8B,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBjC,IAAA,OAAIgC,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,6FAAgB,CAAI,CAAC,CACrErB,MAAM,CAACI,MAAM,CAACgC,GAAG,CAAC,CAACC,KAAK,CAAEC,KAAK,gBAC9BlD,IAAA,QAEEkC,OAAO,CAAEA,CAAA,GAAMvB,gBAAgB,CAACsC,KAAK,CAACpC,EAAE,CAAE,CAC1CmB,SAAS,CAAE,mDACTtB,aAAa,GAAKuC,KAAK,CAACpC,EAAE,CACtB,mCAAmC,CACnC,8BAA8B,EACjC,CAAAoB,QAAA,cAEH/B,KAAA,QAAK8B,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DjC,IAAA,QAAKgC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAC3BgB,KAAK,CAAC/B,SAAS,cACdlB,IAAA,CAACJ,eAAe,EAACoC,SAAS,CAAC,wBAAwB,CAAE,CAAC,cAEtDhC,IAAA,CAACP,QAAQ,EAACuC,SAAS,CAAC,uBAAuB,CAAE,CAC9C,CACE,CAAC,cACN9B,KAAA,QAAK8B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B/B,KAAA,MAAG8B,SAAS,CAAC,4CAA4C,CAAAC,QAAA,EACtDiB,KAAK,CAAG,CAAC,CAAC,IAAE,CAACD,KAAK,CAACnC,KAAK,EACxB,CAAC,cACJd,IAAA,MAAGgC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEgB,KAAK,CAAChC,QAAQ,CAAI,CAAC,EACtD,CAAC,EACH,CAAC,EAtBDgC,KAAK,CAACpC,EAuBR,CACN,CAAC,EACC,CACN,CAEAL,SAAS,GAAK,MAAM,eACnBN,KAAA,QAAK8B,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBjC,IAAA,OAAIgC,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,6FAAgB,CAAI,CAAC,CACrErB,MAAM,CAACO,IAAI,CAAC6B,GAAG,CAAEG,GAAG,eACnBnD,IAAA,QAEEgC,SAAS,CAAC,8EAA8E,CAAAC,QAAA,cAExF/B,KAAA,QAAK8B,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DjC,IAAA,CAACN,YAAY,EAACsC,SAAS,CAAC,sBAAsB,CAAE,CAAC,cACjD9B,KAAA,QAAK8B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BjC,IAAA,MAAGgC,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CACtDkB,GAAG,CAACrC,KAAK,CACT,CAAC,cACJd,IAAA,MAAGgC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEkB,GAAG,CAAC/B,IAAI,CAAI,CAAC,EAChD,CAAC,EACH,CAAC,EAXD+B,GAAG,CAACtC,EAYN,CACN,CAAC,EACC,CACN,CAEAL,SAAS,GAAK,SAAS,eACtBN,KAAA,QAAK8B,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBjC,IAAA,OAAIgC,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,8DAAU,CAAI,CAAC,CAC/DrB,MAAM,CAACS,OAAO,CAAC2B,GAAG,CAAEI,IAAI,eACvBpD,IAAA,QAEEgC,SAAS,CAAC,8EAA8E,CAAAC,QAAA,cAExF/B,KAAA,QAAK8B,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DjC,IAAA,CAACL,yBAAyB,EAACqC,SAAS,CAAC,yBAAyB,CAAE,CAAC,cACjE9B,KAAA,QAAK8B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BjC,IAAA,MAAGgC,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CACtDmB,IAAI,CAACtC,KAAK,CACV,CAAC,cACJZ,KAAA,MAAG8B,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EACjCmB,IAAI,CAAC9B,SAAS,CAAC,2BAChB,CAAC8B,IAAI,CAAClC,SAAS,EAAIkC,IAAI,CAAC7B,KAAK,eAC3BrB,KAAA,SAAM8B,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAC,qDAAW,CAACmB,IAAI,CAAC7B,KAAK,CAAC,GAAC,EAAM,CACrE,EACA,CAAC,EACD,CAAC,CACL6B,IAAI,CAAClC,SAAS,eACblB,IAAA,CAACJ,eAAe,EAACoC,SAAS,CAAC,wBAAwB,CAAE,CACtD,EACE,CAAC,EAnBDoB,IAAI,CAACvC,EAoBP,CACN,CAAC,EACC,CACN,GAxFIL,SAyFK,CAAC,EACV,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAL,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}