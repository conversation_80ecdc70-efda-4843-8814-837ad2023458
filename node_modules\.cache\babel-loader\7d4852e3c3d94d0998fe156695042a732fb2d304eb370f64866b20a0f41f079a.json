{"ast": null, "code": "import { signInWithEmailAndPassword, signOut, onAuthStateChanged } from 'firebase/auth';\nimport { doc, getDoc, setDoc } from 'firebase/firestore';\nimport { auth, db } from '../config/firebase';\nimport { defaultAdmin, adminCredentials } from '../data/defaultAdmin';\nimport { mockStudents } from '../data/mockStudents';\nclass AuthService {\n  // Admin login\n  async loginAdmin(email, password) {\n    try {\n      var _adminData$createdAt;\n      // Check for default admin credentials\n      if (email === adminCredentials.email && password === adminCredentials.password) {\n        return defaultAdmin;\n      }\n      const userCredential = await signInWithEmailAndPassword(auth, email, password);\n      const user = userCredential.user;\n\n      // Get admin data from Firestore\n      const adminDoc = await getDoc(doc(db, 'admins', user.uid));\n      if (!adminDoc.exists()) {\n        throw new Error('المستخدم غير مخول كمدير');\n      }\n      const adminData = adminDoc.data();\n      return {\n        id: user.uid,\n        email: user.email,\n        role: 'admin',\n        name: adminData.name,\n        avatar: adminData.avatar,\n        permissions: adminData.permissions || [],\n        createdAt: ((_adminData$createdAt = adminData.createdAt) === null || _adminData$createdAt === void 0 ? void 0 : _adminData$createdAt.toDate()) || new Date()\n      };\n    } catch (error) {\n      throw new Error(this.getErrorMessage(error.code));\n    }\n  }\n\n  // Student login with access code\n  async loginStudent(accessCode) {\n    try {\n      var _studentData$createdA;\n      // Check for mock students first\n      const mockStudent = mockStudents.find(student => student.accessCode === accessCode);\n      if (mockStudent) {\n        return mockStudent;\n      }\n\n      // Find student by access code in Firestore\n      const studentsRef = doc(db, 'students', accessCode);\n      const studentDoc = await getDoc(studentsRef);\n      if (!studentDoc.exists()) {\n        throw new Error('كود الدخول غير صحيح');\n      }\n      const studentData = studentDoc.data();\n      if (!studentData.isActive) {\n        throw new Error('الحساب غير مفعل');\n      }\n      return {\n        id: studentData.id,\n        email: studentData.email || '',\n        role: 'student',\n        name: studentData.name,\n        avatar: studentData.avatar,\n        accessCode: accessCode,\n        enrolledCourses: studentData.enrolledCourses || [],\n        completedCourses: studentData.completedCourses || [],\n        certificates: studentData.certificates || [],\n        createdAt: ((_studentData$createdA = studentData.createdAt) === null || _studentData$createdA === void 0 ? void 0 : _studentData$createdA.toDate()) || new Date()\n      };\n    } catch (error) {\n      throw new Error(error.message || 'حدث خطأ في تسجيل الدخول');\n    }\n  }\n\n  // Generate access code for student\n  generateAccessCode() {\n    return Math.floor(1000000 + Math.random() * 9000000).toString();\n  }\n\n  // Create student account\n  async createStudent(studentData) {\n    try {\n      const accessCode = this.generateAccessCode();\n\n      // Check if access code already exists\n      const existingStudent = await getDoc(doc(db, 'students', accessCode));\n      if (existingStudent.exists()) {\n        // Generate new code if exists\n        return this.createStudent(studentData);\n      }\n      const student = {\n        id: accessCode,\n        name: studentData.name,\n        email: studentData.email || '',\n        accessCode: accessCode,\n        enrolledCourses: studentData.enrolledCourses || [],\n        completedCourses: [],\n        certificates: [],\n        isActive: true,\n        createdAt: new Date()\n      };\n      await setDoc(doc(db, 'students', accessCode), student);\n      return accessCode;\n    } catch (error) {\n      throw new Error('فشل في إنشاء حساب الطالب');\n    }\n  }\n\n  // Logout\n  async logout() {\n    try {\n      await signOut(auth);\n    } catch (error) {\n      throw new Error('فشل في تسجيل الخروج');\n    }\n  }\n\n  // Get current user\n  getCurrentUser() {\n    return new Promise(resolve => {\n      const unsubscribe = onAuthStateChanged(auth, user => {\n        unsubscribe();\n        resolve(user);\n      });\n    });\n  }\n\n  // Auth state listener\n  onAuthStateChange(callback) {\n    return onAuthStateChanged(auth, callback);\n  }\n  getErrorMessage(errorCode) {\n    switch (errorCode) {\n      case 'auth/user-not-found':\n        return 'المستخدم غير موجود';\n      case 'auth/wrong-password':\n        return 'كلمة المرور غير صحيحة';\n      case 'auth/invalid-email':\n        return 'البريد الإلكتروني غير صحيح';\n      case 'auth/user-disabled':\n        return 'الحساب معطل';\n      case 'auth/too-many-requests':\n        return 'محاولات كثيرة، حاول مرة أخرى لاحقاً';\n      default:\n        return 'حدث خطأ في تسجيل الدخول';\n    }\n  }\n}\nexport const authService = new AuthService();", "map": {"version": 3, "names": ["signInWithEmailAndPassword", "signOut", "onAuthStateChanged", "doc", "getDoc", "setDoc", "auth", "db", "defaultAdmin", "adminCredentials", "mockStudents", "AuthService", "loginAdmin", "email", "password", "_adminData$createdAt", "userCredential", "user", "adminDoc", "uid", "exists", "Error", "adminData", "data", "id", "role", "name", "avatar", "permissions", "createdAt", "toDate", "Date", "error", "getErrorMessage", "code", "loginStudent", "accessCode", "_studentData$createdA", "mockStudent", "find", "student", "studentsRef", "studentDoc", "studentData", "isActive", "enrolledCourses", "completedCourses", "certificates", "message", "generateAccessCode", "Math", "floor", "random", "toString", "createStudent", "existingStudent", "logout", "getCurrentUser", "Promise", "resolve", "unsubscribe", "onAuthStateChange", "callback", "errorCode", "authService"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/services/authService.ts"], "sourcesContent": ["import { \n  signInWithEmailAndPassword, \n  signOut, \n  onAuthStateChanged,\n  User as FirebaseUser\n} from 'firebase/auth';\nimport { doc, getDoc, setDoc } from 'firebase/firestore';\nimport { auth, db } from '../config/firebase';\nimport { User, Student, Admin } from '../types';\nimport { defaultAdmin, adminCredentials } from '../data/defaultAdmin';\nimport { mockStudents, studentCredentials } from '../data/mockStudents';\n\nclass AuthService {\n  // Admin login\n  async loginAdmin(email: string, password: string): Promise<Admin> {\n    try {\n      // Check for default admin credentials\n      if (email === adminCredentials.email && password === adminCredentials.password) {\n        return defaultAdmin as Admin;\n      }\n\n      const userCredential = await signInWithEmailAndPassword(auth, email, password);\n      const user = userCredential.user;\n      \n      // Get admin data from Firestore\n      const adminDoc = await getDoc(doc(db, 'admins', user.uid));\n      if (!adminDoc.exists()) {\n        throw new Error('المستخدم غير مخول كمدير');\n      }\n      \n      const adminData = adminDoc.data();\n      return {\n        id: user.uid,\n        email: user.email!,\n        role: 'admin',\n        name: adminData.name,\n        avatar: adminData.avatar,\n        permissions: adminData.permissions || [],\n        createdAt: adminData.createdAt?.toDate() || new Date()\n      };\n    } catch (error: any) {\n      throw new Error(this.getErrorMessage(error.code));\n    }\n  }\n\n  // Student login with access code\n  async loginStudent(accessCode: string): Promise<Student> {\n    try {\n      // Check for mock students first\n      const mockStudent = mockStudents.find(student => student.accessCode === accessCode);\n      if (mockStudent) {\n        return mockStudent;\n      }\n\n      // Find student by access code in Firestore\n      const studentsRef = doc(db, 'students', accessCode);\n      const studentDoc = await getDoc(studentsRef);\n\n      if (!studentDoc.exists()) {\n        throw new Error('كود الدخول غير صحيح');\n      }\n      \n      const studentData = studentDoc.data();\n      if (!studentData.isActive) {\n        throw new Error('الحساب غير مفعل');\n      }\n      \n      return {\n        id: studentData.id,\n        email: studentData.email || '',\n        role: 'student',\n        name: studentData.name,\n        avatar: studentData.avatar,\n        accessCode: accessCode,\n        enrolledCourses: studentData.enrolledCourses || [],\n        completedCourses: studentData.completedCourses || [],\n        certificates: studentData.certificates || [],\n        createdAt: studentData.createdAt?.toDate() || new Date()\n      };\n    } catch (error: any) {\n      throw new Error(error.message || 'حدث خطأ في تسجيل الدخول');\n    }\n  }\n\n  // Generate access code for student\n  generateAccessCode(): string {\n    return Math.floor(1000000 + Math.random() * 9000000).toString();\n  }\n\n  // Create student account\n  async createStudent(studentData: {\n    name: string;\n    email?: string;\n    enrolledCourses?: string[];\n  }): Promise<string> {\n    try {\n      const accessCode = this.generateAccessCode();\n      \n      // Check if access code already exists\n      const existingStudent = await getDoc(doc(db, 'students', accessCode));\n      if (existingStudent.exists()) {\n        // Generate new code if exists\n        return this.createStudent(studentData);\n      }\n      \n      const student = {\n        id: accessCode,\n        name: studentData.name,\n        email: studentData.email || '',\n        accessCode: accessCode,\n        enrolledCourses: studentData.enrolledCourses || [],\n        completedCourses: [],\n        certificates: [],\n        isActive: true,\n        createdAt: new Date()\n      };\n      \n      await setDoc(doc(db, 'students', accessCode), student);\n      return accessCode;\n    } catch (error: any) {\n      throw new Error('فشل في إنشاء حساب الطالب');\n    }\n  }\n\n  // Logout\n  async logout(): Promise<void> {\n    try {\n      await signOut(auth);\n    } catch (error: any) {\n      throw new Error('فشل في تسجيل الخروج');\n    }\n  }\n\n  // Get current user\n  getCurrentUser(): Promise<FirebaseUser | null> {\n    return new Promise((resolve) => {\n      const unsubscribe = onAuthStateChanged(auth, (user) => {\n        unsubscribe();\n        resolve(user);\n      });\n    });\n  }\n\n  // Auth state listener\n  onAuthStateChange(callback: (user: FirebaseUser | null) => void) {\n    return onAuthStateChanged(auth, callback);\n  }\n\n  private getErrorMessage(errorCode: string): string {\n    switch (errorCode) {\n      case 'auth/user-not-found':\n        return 'المستخدم غير موجود';\n      case 'auth/wrong-password':\n        return 'كلمة المرور غير صحيحة';\n      case 'auth/invalid-email':\n        return 'البريد الإلكتروني غير صحيح';\n      case 'auth/user-disabled':\n        return 'الحساب معطل';\n      case 'auth/too-many-requests':\n        return 'محاولات كثيرة، حاول مرة أخرى لاحقاً';\n      default:\n        return 'حدث خطأ في تسجيل الدخول';\n    }\n  }\n}\n\nexport const authService = new AuthService();\n"], "mappings": "AAAA,SACEA,0BAA0B,EAC1BC,OAAO,EACPC,kBAAkB,QAEb,eAAe;AACtB,SAASC,GAAG,EAAEC,MAAM,EAAEC,MAAM,QAAQ,oBAAoB;AACxD,SAASC,IAAI,EAAEC,EAAE,QAAQ,oBAAoB;AAE7C,SAASC,YAAY,EAAEC,gBAAgB,QAAQ,sBAAsB;AACrE,SAASC,YAAY,QAA4B,sBAAsB;AAEvE,MAAMC,WAAW,CAAC;EAChB;EACA,MAAMC,UAAUA,CAACC,KAAa,EAAEC,QAAgB,EAAkB;IAChE,IAAI;MAAA,IAAAC,oBAAA;MACF;MACA,IAAIF,KAAK,KAAKJ,gBAAgB,CAACI,KAAK,IAAIC,QAAQ,KAAKL,gBAAgB,CAACK,QAAQ,EAAE;QAC9E,OAAON,YAAY;MACrB;MAEA,MAAMQ,cAAc,GAAG,MAAMhB,0BAA0B,CAACM,IAAI,EAAEO,KAAK,EAAEC,QAAQ,CAAC;MAC9E,MAAMG,IAAI,GAAGD,cAAc,CAACC,IAAI;;MAEhC;MACA,MAAMC,QAAQ,GAAG,MAAMd,MAAM,CAACD,GAAG,CAACI,EAAE,EAAE,QAAQ,EAAEU,IAAI,CAACE,GAAG,CAAC,CAAC;MAC1D,IAAI,CAACD,QAAQ,CAACE,MAAM,CAAC,CAAC,EAAE;QACtB,MAAM,IAAIC,KAAK,CAAC,yBAAyB,CAAC;MAC5C;MAEA,MAAMC,SAAS,GAAGJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MACjC,OAAO;QACLC,EAAE,EAAEP,IAAI,CAACE,GAAG;QACZN,KAAK,EAAEI,IAAI,CAACJ,KAAM;QAClBY,IAAI,EAAE,OAAO;QACbC,IAAI,EAAEJ,SAAS,CAACI,IAAI;QACpBC,MAAM,EAAEL,SAAS,CAACK,MAAM;QACxBC,WAAW,EAAEN,SAAS,CAACM,WAAW,IAAI,EAAE;QACxCC,SAAS,EAAE,EAAAd,oBAAA,GAAAO,SAAS,CAACO,SAAS,cAAAd,oBAAA,uBAAnBA,oBAAA,CAAqBe,MAAM,CAAC,CAAC,KAAI,IAAIC,IAAI,CAAC;MACvD,CAAC;IACH,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnB,MAAM,IAAIX,KAAK,CAAC,IAAI,CAACY,eAAe,CAACD,KAAK,CAACE,IAAI,CAAC,CAAC;IACnD;EACF;;EAEA;EACA,MAAMC,YAAYA,CAACC,UAAkB,EAAoB;IACvD,IAAI;MAAA,IAAAC,qBAAA;MACF;MACA,MAAMC,WAAW,GAAG5B,YAAY,CAAC6B,IAAI,CAACC,OAAO,IAAIA,OAAO,CAACJ,UAAU,KAAKA,UAAU,CAAC;MACnF,IAAIE,WAAW,EAAE;QACf,OAAOA,WAAW;MACpB;;MAEA;MACA,MAAMG,WAAW,GAAGtC,GAAG,CAACI,EAAE,EAAE,UAAU,EAAE6B,UAAU,CAAC;MACnD,MAAMM,UAAU,GAAG,MAAMtC,MAAM,CAACqC,WAAW,CAAC;MAE5C,IAAI,CAACC,UAAU,CAACtB,MAAM,CAAC,CAAC,EAAE;QACxB,MAAM,IAAIC,KAAK,CAAC,qBAAqB,CAAC;MACxC;MAEA,MAAMsB,WAAW,GAAGD,UAAU,CAACnB,IAAI,CAAC,CAAC;MACrC,IAAI,CAACoB,WAAW,CAACC,QAAQ,EAAE;QACzB,MAAM,IAAIvB,KAAK,CAAC,iBAAiB,CAAC;MACpC;MAEA,OAAO;QACLG,EAAE,EAAEmB,WAAW,CAACnB,EAAE;QAClBX,KAAK,EAAE8B,WAAW,CAAC9B,KAAK,IAAI,EAAE;QAC9BY,IAAI,EAAE,SAAS;QACfC,IAAI,EAAEiB,WAAW,CAACjB,IAAI;QACtBC,MAAM,EAAEgB,WAAW,CAAChB,MAAM;QAC1BS,UAAU,EAAEA,UAAU;QACtBS,eAAe,EAAEF,WAAW,CAACE,eAAe,IAAI,EAAE;QAClDC,gBAAgB,EAAEH,WAAW,CAACG,gBAAgB,IAAI,EAAE;QACpDC,YAAY,EAAEJ,WAAW,CAACI,YAAY,IAAI,EAAE;QAC5ClB,SAAS,EAAE,EAAAQ,qBAAA,GAAAM,WAAW,CAACd,SAAS,cAAAQ,qBAAA,uBAArBA,qBAAA,CAAuBP,MAAM,CAAC,CAAC,KAAI,IAAIC,IAAI,CAAC;MACzD,CAAC;IACH,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnB,MAAM,IAAIX,KAAK,CAACW,KAAK,CAACgB,OAAO,IAAI,yBAAyB,CAAC;IAC7D;EACF;;EAEA;EACAC,kBAAkBA,CAAA,EAAW;IAC3B,OAAOC,IAAI,CAACC,KAAK,CAAC,OAAO,GAAGD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,OAAO,CAAC,CAACC,QAAQ,CAAC,CAAC;EACjE;;EAEA;EACA,MAAMC,aAAaA,CAACX,WAInB,EAAmB;IAClB,IAAI;MACF,MAAMP,UAAU,GAAG,IAAI,CAACa,kBAAkB,CAAC,CAAC;;MAE5C;MACA,MAAMM,eAAe,GAAG,MAAMnD,MAAM,CAACD,GAAG,CAACI,EAAE,EAAE,UAAU,EAAE6B,UAAU,CAAC,CAAC;MACrE,IAAImB,eAAe,CAACnC,MAAM,CAAC,CAAC,EAAE;QAC5B;QACA,OAAO,IAAI,CAACkC,aAAa,CAACX,WAAW,CAAC;MACxC;MAEA,MAAMH,OAAO,GAAG;QACdhB,EAAE,EAAEY,UAAU;QACdV,IAAI,EAAEiB,WAAW,CAACjB,IAAI;QACtBb,KAAK,EAAE8B,WAAW,CAAC9B,KAAK,IAAI,EAAE;QAC9BuB,UAAU,EAAEA,UAAU;QACtBS,eAAe,EAAEF,WAAW,CAACE,eAAe,IAAI,EAAE;QAClDC,gBAAgB,EAAE,EAAE;QACpBC,YAAY,EAAE,EAAE;QAChBH,QAAQ,EAAE,IAAI;QACdf,SAAS,EAAE,IAAIE,IAAI,CAAC;MACtB,CAAC;MAED,MAAM1B,MAAM,CAACF,GAAG,CAACI,EAAE,EAAE,UAAU,EAAE6B,UAAU,CAAC,EAAEI,OAAO,CAAC;MACtD,OAAOJ,UAAU;IACnB,CAAC,CAAC,OAAOJ,KAAU,EAAE;MACnB,MAAM,IAAIX,KAAK,CAAC,0BAA0B,CAAC;IAC7C;EACF;;EAEA;EACA,MAAMmC,MAAMA,CAAA,EAAkB;IAC5B,IAAI;MACF,MAAMvD,OAAO,CAACK,IAAI,CAAC;IACrB,CAAC,CAAC,OAAO0B,KAAU,EAAE;MACnB,MAAM,IAAIX,KAAK,CAAC,qBAAqB,CAAC;IACxC;EACF;;EAEA;EACAoC,cAAcA,CAAA,EAAiC;IAC7C,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;MAC9B,MAAMC,WAAW,GAAG1D,kBAAkB,CAACI,IAAI,EAAGW,IAAI,IAAK;QACrD2C,WAAW,CAAC,CAAC;QACbD,OAAO,CAAC1C,IAAI,CAAC;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;;EAEA;EACA4C,iBAAiBA,CAACC,QAA6C,EAAE;IAC/D,OAAO5D,kBAAkB,CAACI,IAAI,EAAEwD,QAAQ,CAAC;EAC3C;EAEQ7B,eAAeA,CAAC8B,SAAiB,EAAU;IACjD,QAAQA,SAAS;MACf,KAAK,qBAAqB;QACxB,OAAO,oBAAoB;MAC7B,KAAK,qBAAqB;QACxB,OAAO,uBAAuB;MAChC,KAAK,oBAAoB;QACvB,OAAO,4BAA4B;MACrC,KAAK,oBAAoB;QACvB,OAAO,aAAa;MACtB,KAAK,wBAAwB;QAC3B,OAAO,qCAAqC;MAC9C;QACE,OAAO,yBAAyB;IACpC;EACF;AACF;AAEA,OAAO,MAAMC,WAAW,GAAG,IAAIrD,WAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}