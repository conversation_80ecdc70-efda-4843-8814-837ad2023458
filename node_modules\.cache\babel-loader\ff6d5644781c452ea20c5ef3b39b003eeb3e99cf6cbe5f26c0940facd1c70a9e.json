{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0634\\u0631\\u0648\\u0639\\\\src\\\\components\\\\Student\\\\StudentOverview.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useNavigate } from 'react-router-dom';\nimport { AcademicCapIcon, ClipboardDocumentListIcon, DocumentTextIcon, TrophyIcon, PlayIcon, BookOpenIcon, ChartBarIcon, ClockIcon } from '@heroicons/react/24/outline';\n\n// Types\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StudentOverview = ({\n  user\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const [stats, setStats] = useState({\n    enrolledCourses: 0,\n    completedCourses: 0,\n    certificates: 0,\n    totalWatchTime: 0,\n    completedQuizzes: 0,\n    averageScore: 0\n  });\n  useEffect(() => {\n    var _user$enrolledCourses, _user$completedCourse, _user$certificates;\n    // Simulate loading stats\n    setStats({\n      enrolledCourses: ((_user$enrolledCourses = user.enrolledCourses) === null || _user$enrolledCourses === void 0 ? void 0 : _user$enrolledCourses.length) || 3,\n      completedCourses: ((_user$completedCourse = user.completedCourses) === null || _user$completedCourse === void 0 ? void 0 : _user$completedCourse.length) || 1,\n      certificates: ((_user$certificates = user.certificates) === null || _user$certificates === void 0 ? void 0 : _user$certificates.length) || 1,\n      totalWatchTime: 240,\n      // minutes\n      completedQuizzes: 5,\n      averageScore: 85\n    });\n  }, [user]);\n  const statsCards = [{\n    title: 'الكورسات المسجلة',\n    value: stats.enrolledCourses,\n    icon: AcademicCapIcon,\n    color: 'blue',\n    onClick: () => navigate('/student/courses')\n  }, {\n    title: 'الكورسات المكتملة',\n    value: stats.completedCourses,\n    icon: BookOpenIcon,\n    color: 'green',\n    onClick: () => navigate('/student/courses?filter=completed')\n  }, {\n    title: 'الشهادات',\n    value: stats.certificates,\n    icon: DocumentTextIcon,\n    color: 'purple',\n    onClick: () => navigate('/student/certificates')\n  }, {\n    title: 'الاختبارات المكتملة',\n    value: stats.completedQuizzes,\n    icon: ClipboardDocumentListIcon,\n    color: 'orange',\n    onClick: () => navigate('/student/quizzes')\n  }];\n  const recentCourses = [{\n    id: '1',\n    title: 'أساسيات البرمجة',\n    progress: 75,\n    thumbnail: '/api/placeholder/300/200',\n    instructor: 'أ. محمد أحمد',\n    lastAccessed: 'منذ يوم'\n  }, {\n    id: '2',\n    title: 'تطوير المواقع',\n    progress: 45,\n    thumbnail: '/api/placeholder/300/200',\n    instructor: 'أ. سارة محمد',\n    lastAccessed: 'منذ 3 أيام'\n  }, {\n    id: '3',\n    title: 'قواعد البيانات',\n    progress: 20,\n    thumbnail: '/api/placeholder/300/200',\n    instructor: 'أ. أحمد علي',\n    lastAccessed: 'منذ أسبوع'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"bg-gradient-to-l from-primary-600 to-primary-700 rounded-xl p-6 text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold mb-2\",\n            children: [\"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B \\u0628\\u0643\\u060C \", user.name || 'الطالب', \"!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-primary-100 mb-4\",\n            children: \"\\u0627\\u0633\\u062A\\u0645\\u0631 \\u0641\\u064A \\u0631\\u062D\\u0644\\u0629 \\u0627\\u0644\\u062A\\u0639\\u0644\\u0645 \\u0648\\u0627\\u0643\\u062A\\u0634\\u0641 \\u0627\\u0644\\u0645\\u0632\\u064A\\u062F \\u0645\\u0646 \\u0627\\u0644\\u0645\\u0639\\u0631\\u0641\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4 space-x-reverse text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(ClockIcon, {\n                className: \"w-4 h-4 ml-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [Math.floor(stats.totalWatchTime / 60), \"\\u0633 \", stats.totalWatchTime % 60, \"\\u062F \\u0645\\u0646 \\u0627\\u0644\\u0645\\u0634\\u0627\\u0647\\u062F\\u0629\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(ChartBarIcon, {\n                className: \"w-4 h-4 ml-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"\\u0645\\u062A\\u0648\\u0633\\u0637 \\u0627\\u0644\\u0646\\u062A\\u0627\\u0626\\u062C: \", stats.averageScore, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:block\",\n          children: /*#__PURE__*/_jsxDEV(TrophyIcon, {\n            className: \"w-20 h-20 text-primary-200\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n      children: statsCards.map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: index * 0.1\n        },\n        whileHover: {\n          y: -2\n        },\n        onClick: stat.onClick,\n        className: \"bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: stat.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-3xl font-bold text-gray-900 mt-1\",\n              children: stat.value\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `\n                w-12 h-12 rounded-lg flex items-center justify-center\n                ${stat.color === 'blue' ? 'bg-blue-100' : ''}\n                ${stat.color === 'green' ? 'bg-green-100' : ''}\n                ${stat.color === 'purple' ? 'bg-purple-100' : ''}\n                ${stat.color === 'orange' ? 'bg-orange-100' : ''}\n              `,\n            children: /*#__PURE__*/_jsxDEV(stat.icon, {\n              className: `\n                  w-6 h-6\n                  ${stat.color === 'blue' ? 'text-blue-600' : ''}\n                  ${stat.color === 'green' ? 'text-green-600' : ''}\n                  ${stat.color === 'purple' ? 'text-purple-600' : ''}\n                  ${stat.color === 'orange' ? 'text-orange-600' : ''}\n                `\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 13\n        }, this)\n      }, stat.title, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.3\n      },\n      className: \"bg-white rounded-xl p-6 shadow-sm\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-gray-900\",\n          children: \"\\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A \\u0627\\u0644\\u0623\\u062E\\u064A\\u0631\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/student/courses'),\n          className: \"text-primary-600 hover:text-primary-700 font-medium text-sm\",\n          children: \"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u0643\\u0644\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n        children: recentCourses.map((course, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            scale: 0.9\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            delay: 0.4 + index * 0.1\n          },\n          whileHover: {\n            y: -4\n          },\n          onClick: () => navigate(`/student/course/${course.id}`),\n          className: \"bg-gray-50 rounded-lg p-4 hover:shadow-md transition-all duration-200 cursor-pointer\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"aspect-video bg-gradient-to-br from-primary-400 to-primary-600 rounded-lg mb-4 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(PlayIcon, {\n              className: \"w-12 h-12 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"font-semibold text-gray-900 mb-2\",\n            children: course.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-600 mb-3\",\n            children: course.instructor\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between text-sm mb-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600\",\n                children: \"\\u0627\\u0644\\u062A\\u0642\\u062F\\u0645\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium text-gray-900\",\n                children: [course.progress, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full bg-gray-200 rounded-full h-2\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-primary-600 h-2 rounded-full transition-all duration-300\",\n                style: {\n                  width: `${course.progress}%`\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-500\",\n            children: course.lastAccessed\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 15\n          }, this)]\n        }, course.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.5\n      },\n      className: \"bg-white rounded-xl p-6 shadow-sm\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-xl font-bold text-gray-900 mb-4\",\n        children: \"\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A \\u0633\\u0631\\u064A\\u0639\\u0629\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/student/courses'),\n          className: \"flex items-center p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200\",\n          children: [/*#__PURE__*/_jsxDEV(AcademicCapIcon, {\n            className: \"w-8 h-8 text-blue-600 ml-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-right\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold text-blue-900\",\n              children: \"\\u062A\\u0635\\u0641\\u062D \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-blue-700\",\n              children: \"\\u0627\\u0643\\u062A\\u0634\\u0641 \\u0643\\u0648\\u0631\\u0633\\u0627\\u062A \\u062C\\u062F\\u064A\\u062F\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/student/quizzes'),\n          className: \"flex items-center p-4 bg-green-50 hover:bg-green-100 rounded-lg transition-colors duration-200\",\n          children: [/*#__PURE__*/_jsxDEV(ClipboardDocumentListIcon, {\n            className: \"w-8 h-8 text-green-600 ml-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-right\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold text-green-900\",\n              children: \"\\u0623\\u062F\\u0627\\u0621 \\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-green-700\",\n              children: \"\\u0627\\u062E\\u062A\\u0628\\u0631 \\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A\\u0643\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/student/certificates'),\n          className: \"flex items-center p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors duration-200\",\n          children: [/*#__PURE__*/_jsxDEV(DocumentTextIcon, {\n            className: \"w-8 h-8 text-purple-600 ml-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-right\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold text-purple-900\",\n              children: \"\\u0634\\u0647\\u0627\\u062F\\u0627\\u062A\\u064A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-purple-700\",\n              children: \"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u0625\\u0646\\u062C\\u0627\\u0632\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 104,\n    columnNumber: 5\n  }, this);\n};\n_s(StudentOverview, \"yBaeebGeaRVqJ7rEp5e22ZNPhLE=\", false, function () {\n  return [useNavigate];\n});\n_c = StudentOverview;\nexport default StudentOverview;\nvar _c;\n$RefreshReg$(_c, \"StudentOverview\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "useNavigate", "AcademicCapIcon", "ClipboardDocumentListIcon", "DocumentTextIcon", "TrophyIcon", "PlayIcon", "BookOpenIcon", "ChartBarIcon", "ClockIcon", "jsxDEV", "_jsxDEV", "StudentOverview", "user", "_s", "navigate", "stats", "setStats", "enrolledCourses", "completedCourses", "certificates", "totalWatchTime", "completedQuizzes", "averageScore", "_user$enrolledCourses", "_user$completedCourse", "_user$certificates", "length", "statsCards", "title", "value", "icon", "color", "onClick", "recentCourses", "id", "progress", "thumbnail", "instructor", "lastAccessed", "className", "children", "div", "initial", "opacity", "y", "animate", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Math", "floor", "map", "stat", "index", "transition", "delay", "whileHover", "course", "scale", "style", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Student/StudentOverview.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  AcademicCapIcon,\n  ClipboardDocumentListIcon,\n  DocumentTextIcon,\n  TrophyIcon,\n  PlayIcon,\n  BookOpenIcon,\n  ChartBarIcon,\n  ClockIcon\n} from '@heroicons/react/24/outline';\n\n// Types\nimport { Student } from '../../types';\n\ninterface StudentOverviewProps {\n  user: Student;\n}\n\nconst StudentOverview: React.FC<StudentOverviewProps> = ({ user }) => {\n  const navigate = useNavigate();\n  const [stats, setStats] = useState({\n    enrolledCourses: 0,\n    completedCourses: 0,\n    certificates: 0,\n    totalWatchTime: 0,\n    completedQuizzes: 0,\n    averageScore: 0\n  });\n\n  useEffect(() => {\n    // Simulate loading stats\n    setStats({\n      enrolledCourses: user.enrolledCourses?.length || 3,\n      completedCourses: user.completedCourses?.length || 1,\n      certificates: user.certificates?.length || 1,\n      totalWatchTime: 240, // minutes\n      completedQuizzes: 5,\n      averageScore: 85\n    });\n  }, [user]);\n\n  const statsCards = [\n    {\n      title: 'الكورسات المسجلة',\n      value: stats.enrolledCourses,\n      icon: AcademicCapIcon,\n      color: 'blue',\n      onClick: () => navigate('/student/courses')\n    },\n    {\n      title: 'الكورسات المكتملة',\n      value: stats.completedCourses,\n      icon: BookOpenIcon,\n      color: 'green',\n      onClick: () => navigate('/student/courses?filter=completed')\n    },\n    {\n      title: 'الشهادات',\n      value: stats.certificates,\n      icon: DocumentTextIcon,\n      color: 'purple',\n      onClick: () => navigate('/student/certificates')\n    },\n    {\n      title: 'الاختبارات المكتملة',\n      value: stats.completedQuizzes,\n      icon: ClipboardDocumentListIcon,\n      color: 'orange',\n      onClick: () => navigate('/student/quizzes')\n    }\n  ];\n\n  const recentCourses = [\n    {\n      id: '1',\n      title: 'أساسيات البرمجة',\n      progress: 75,\n      thumbnail: '/api/placeholder/300/200',\n      instructor: 'أ. محمد أحمد',\n      lastAccessed: 'منذ يوم'\n    },\n    {\n      id: '2',\n      title: 'تطوير المواقع',\n      progress: 45,\n      thumbnail: '/api/placeholder/300/200',\n      instructor: 'أ. سارة محمد',\n      lastAccessed: 'منذ 3 أيام'\n    },\n    {\n      id: '3',\n      title: 'قواعد البيانات',\n      progress: 20,\n      thumbnail: '/api/placeholder/300/200',\n      instructor: 'أ. أحمد علي',\n      lastAccessed: 'منذ أسبوع'\n    }\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Welcome Section */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        className=\"bg-gradient-to-l from-primary-600 to-primary-700 rounded-xl p-6 text-white\"\n      >\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold mb-2\">\n              مرحباً بك، {user.name || 'الطالب'}!\n            </h1>\n            <p className=\"text-primary-100 mb-4\">\n              استمر في رحلة التعلم واكتشف المزيد من المعرفة\n            </p>\n            <div className=\"flex items-center space-x-4 space-x-reverse text-sm\">\n              <div className=\"flex items-center\">\n                <ClockIcon className=\"w-4 h-4 ml-1\" />\n                <span>{Math.floor(stats.totalWatchTime / 60)}س {stats.totalWatchTime % 60}د من المشاهدة</span>\n              </div>\n              <div className=\"flex items-center\">\n                <ChartBarIcon className=\"w-4 h-4 ml-1\" />\n                <span>متوسط النتائج: {stats.averageScore}%</span>\n              </div>\n            </div>\n          </div>\n          <div className=\"hidden md:block\">\n            <TrophyIcon className=\"w-20 h-20 text-primary-200\" />\n          </div>\n        </div>\n      </motion.div>\n\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {statsCards.map((stat, index) => (\n          <motion.div\n            key={stat.title}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: index * 0.1 }}\n            whileHover={{ y: -2 }}\n            onClick={stat.onClick}\n            className=\"bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer\"\n          >\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">{stat.title}</p>\n                <p className=\"text-3xl font-bold text-gray-900 mt-1\">\n                  {stat.value}\n                </p>\n              </div>\n              <div className={`\n                w-12 h-12 rounded-lg flex items-center justify-center\n                ${stat.color === 'blue' ? 'bg-blue-100' : ''}\n                ${stat.color === 'green' ? 'bg-green-100' : ''}\n                ${stat.color === 'purple' ? 'bg-purple-100' : ''}\n                ${stat.color === 'orange' ? 'bg-orange-100' : ''}\n              `}>\n                <stat.icon className={`\n                  w-6 h-6\n                  ${stat.color === 'blue' ? 'text-blue-600' : ''}\n                  ${stat.color === 'green' ? 'text-green-600' : ''}\n                  ${stat.color === 'purple' ? 'text-purple-600' : ''}\n                  ${stat.color === 'orange' ? 'text-orange-600' : ''}\n                `} />\n              </div>\n            </div>\n          </motion.div>\n        ))}\n      </div>\n\n      {/* Recent Courses */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.3 }}\n        className=\"bg-white rounded-xl p-6 shadow-sm\"\n      >\n        <div className=\"flex items-center justify-between mb-6\">\n          <h2 className=\"text-xl font-bold text-gray-900\">الكورسات الأخيرة</h2>\n          <button\n            onClick={() => navigate('/student/courses')}\n            className=\"text-primary-600 hover:text-primary-700 font-medium text-sm\"\n          >\n            عرض الكل\n          </button>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {recentCourses.map((course, index) => (\n            <motion.div\n              key={course.id}\n              initial={{ opacity: 0, scale: 0.9 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ delay: 0.4 + index * 0.1 }}\n              whileHover={{ y: -4 }}\n              onClick={() => navigate(`/student/course/${course.id}`)}\n              className=\"bg-gray-50 rounded-lg p-4 hover:shadow-md transition-all duration-200 cursor-pointer\"\n            >\n              <div className=\"aspect-video bg-gradient-to-br from-primary-400 to-primary-600 rounded-lg mb-4 flex items-center justify-center\">\n                <PlayIcon className=\"w-12 h-12 text-white\" />\n              </div>\n              \n              <h3 className=\"font-semibold text-gray-900 mb-2\">{course.title}</h3>\n              <p className=\"text-sm text-gray-600 mb-3\">{course.instructor}</p>\n              \n              {/* Progress Bar */}\n              <div className=\"mb-3\">\n                <div className=\"flex items-center justify-between text-sm mb-1\">\n                  <span className=\"text-gray-600\">التقدم</span>\n                  <span className=\"font-medium text-gray-900\">{course.progress}%</span>\n                </div>\n                <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                  <div\n                    className=\"bg-primary-600 h-2 rounded-full transition-all duration-300\"\n                    style={{ width: `${course.progress}%` }}\n                  />\n                </div>\n              </div>\n              \n              <p className=\"text-xs text-gray-500\">{course.lastAccessed}</p>\n            </motion.div>\n          ))}\n        </div>\n      </motion.div>\n\n      {/* Quick Actions */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.5 }}\n        className=\"bg-white rounded-xl p-6 shadow-sm\"\n      >\n        <h2 className=\"text-xl font-bold text-gray-900 mb-4\">إجراءات سريعة</h2>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <button\n            onClick={() => navigate('/student/courses')}\n            className=\"flex items-center p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200\"\n          >\n            <AcademicCapIcon className=\"w-8 h-8 text-blue-600 ml-3\" />\n            <div className=\"text-right\">\n              <h3 className=\"font-semibold text-blue-900\">تصفح الكورسات</h3>\n              <p className=\"text-sm text-blue-700\">اكتشف كورسات جديدة</p>\n            </div>\n          </button>\n          \n          <button\n            onClick={() => navigate('/student/quizzes')}\n            className=\"flex items-center p-4 bg-green-50 hover:bg-green-100 rounded-lg transition-colors duration-200\"\n          >\n            <ClipboardDocumentListIcon className=\"w-8 h-8 text-green-600 ml-3\" />\n            <div className=\"text-right\">\n              <h3 className=\"font-semibold text-green-900\">أداء اختبار</h3>\n              <p className=\"text-sm text-green-700\">اختبر معلوماتك</p>\n            </div>\n          </button>\n          \n          <button\n            onClick={() => navigate('/student/certificates')}\n            className=\"flex items-center p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors duration-200\"\n          >\n            <DocumentTextIcon className=\"w-8 h-8 text-purple-600 ml-3\" />\n            <div className=\"text-right\">\n              <h3 className=\"font-semibold text-purple-900\">شهاداتي</h3>\n              <p className=\"text-sm text-purple-700\">عرض الإنجازات</p>\n            </div>\n          </button>\n        </div>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default StudentOverview;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,eAAe,EACfC,yBAAyB,EACzBC,gBAAgB,EAChBC,UAAU,EACVC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,SAAS,QACJ,6BAA6B;;AAEpC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAOA,MAAMC,eAA+C,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EACpE,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC;IACjCoB,eAAe,EAAE,CAAC;IAClBC,gBAAgB,EAAE,CAAC;IACnBC,YAAY,EAAE,CAAC;IACfC,cAAc,EAAE,CAAC;IACjBC,gBAAgB,EAAE,CAAC;IACnBC,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFxB,SAAS,CAAC,MAAM;IAAA,IAAAyB,qBAAA,EAAAC,qBAAA,EAAAC,kBAAA;IACd;IACAT,QAAQ,CAAC;MACPC,eAAe,EAAE,EAAAM,qBAAA,GAAAX,IAAI,CAACK,eAAe,cAAAM,qBAAA,uBAApBA,qBAAA,CAAsBG,MAAM,KAAI,CAAC;MAClDR,gBAAgB,EAAE,EAAAM,qBAAA,GAAAZ,IAAI,CAACM,gBAAgB,cAAAM,qBAAA,uBAArBA,qBAAA,CAAuBE,MAAM,KAAI,CAAC;MACpDP,YAAY,EAAE,EAAAM,kBAAA,GAAAb,IAAI,CAACO,YAAY,cAAAM,kBAAA,uBAAjBA,kBAAA,CAAmBC,MAAM,KAAI,CAAC;MAC5CN,cAAc,EAAE,GAAG;MAAE;MACrBC,gBAAgB,EAAE,CAAC;MACnBC,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACV,IAAI,CAAC,CAAC;EAEV,MAAMe,UAAU,GAAG,CACjB;IACEC,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAEd,KAAK,CAACE,eAAe;IAC5Ba,IAAI,EAAE7B,eAAe;IACrB8B,KAAK,EAAE,MAAM;IACbC,OAAO,EAAEA,CAAA,KAAMlB,QAAQ,CAAC,kBAAkB;EAC5C,CAAC,EACD;IACEc,KAAK,EAAE,mBAAmB;IAC1BC,KAAK,EAAEd,KAAK,CAACG,gBAAgB;IAC7BY,IAAI,EAAExB,YAAY;IAClByB,KAAK,EAAE,OAAO;IACdC,OAAO,EAAEA,CAAA,KAAMlB,QAAQ,CAAC,mCAAmC;EAC7D,CAAC,EACD;IACEc,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAEd,KAAK,CAACI,YAAY;IACzBW,IAAI,EAAE3B,gBAAgB;IACtB4B,KAAK,EAAE,QAAQ;IACfC,OAAO,EAAEA,CAAA,KAAMlB,QAAQ,CAAC,uBAAuB;EACjD,CAAC,EACD;IACEc,KAAK,EAAE,qBAAqB;IAC5BC,KAAK,EAAEd,KAAK,CAACM,gBAAgB;IAC7BS,IAAI,EAAE5B,yBAAyB;IAC/B6B,KAAK,EAAE,QAAQ;IACfC,OAAO,EAAEA,CAAA,KAAMlB,QAAQ,CAAC,kBAAkB;EAC5C,CAAC,CACF;EAED,MAAMmB,aAAa,GAAG,CACpB;IACEC,EAAE,EAAE,GAAG;IACPN,KAAK,EAAE,iBAAiB;IACxBO,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,0BAA0B;IACrCC,UAAU,EAAE,cAAc;IAC1BC,YAAY,EAAE;EAChB,CAAC,EACD;IACEJ,EAAE,EAAE,GAAG;IACPN,KAAK,EAAE,eAAe;IACtBO,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,0BAA0B;IACrCC,UAAU,EAAE,cAAc;IAC1BC,YAAY,EAAE;EAChB,CAAC,EACD;IACEJ,EAAE,EAAE,GAAG;IACPN,KAAK,EAAE,gBAAgB;IACvBO,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,0BAA0B;IACrCC,UAAU,EAAE,aAAa;IACzBC,YAAY,EAAE;EAChB,CAAC,CACF;EAED,oBACE5B,OAAA;IAAK6B,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB9B,OAAA,CAACX,MAAM,CAAC0C,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BL,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eAEtF9B,OAAA;QAAK6B,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChD9B,OAAA;UAAA8B,QAAA,gBACE9B,OAAA;YAAI6B,SAAS,EAAC,yBAAyB;YAAAC,QAAA,GAAC,0DAC3B,EAAC5B,IAAI,CAACkC,IAAI,IAAI,QAAQ,EAAC,GACpC;UAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLxC,OAAA;YAAG6B,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAErC;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJxC,OAAA;YAAK6B,SAAS,EAAC,qDAAqD;YAAAC,QAAA,gBAClE9B,OAAA;cAAK6B,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC9B,OAAA,CAACF,SAAS;gBAAC+B,SAAS,EAAC;cAAc;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtCxC,OAAA;gBAAA8B,QAAA,GAAOW,IAAI,CAACC,KAAK,CAACrC,KAAK,CAACK,cAAc,GAAG,EAAE,CAAC,EAAC,SAAE,EAACL,KAAK,CAACK,cAAc,GAAG,EAAE,EAAC,sEAAa;cAAA;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F,CAAC,eACNxC,OAAA;cAAK6B,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC9B,OAAA,CAACH,YAAY;gBAACgC,SAAS,EAAC;cAAc;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzCxC,OAAA;gBAAA8B,QAAA,GAAM,6EAAe,EAACzB,KAAK,CAACO,YAAY,EAAC,GAAC;cAAA;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNxC,OAAA;UAAK6B,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B9B,OAAA,CAACN,UAAU;YAACmC,SAAS,EAAC;UAA4B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGbxC,OAAA;MAAK6B,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClEb,UAAU,CAAC0B,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC1B7C,OAAA,CAACX,MAAM,CAAC0C,GAAG;QAETC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BY,UAAU,EAAE;UAAEC,KAAK,EAAEF,KAAK,GAAG;QAAI,CAAE;QACnCG,UAAU,EAAE;UAAEd,CAAC,EAAE,CAAC;QAAE,CAAE;QACtBZ,OAAO,EAAEsB,IAAI,CAACtB,OAAQ;QACtBO,SAAS,EAAC,8FAA8F;QAAAC,QAAA,eAExG9B,OAAA;UAAK6B,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD9B,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAG6B,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAEc,IAAI,CAAC1B;YAAK;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjExC,OAAA;cAAG6B,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EACjDc,IAAI,CAACzB;YAAK;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNxC,OAAA;YAAK6B,SAAS,EAAE;AAC9B;AACA,kBAAkBe,IAAI,CAACvB,KAAK,KAAK,MAAM,GAAG,aAAa,GAAG,EAAE;AAC5D,kBAAkBuB,IAAI,CAACvB,KAAK,KAAK,OAAO,GAAG,cAAc,GAAG,EAAE;AAC9D,kBAAkBuB,IAAI,CAACvB,KAAK,KAAK,QAAQ,GAAG,eAAe,GAAG,EAAE;AAChE,kBAAkBuB,IAAI,CAACvB,KAAK,KAAK,QAAQ,GAAG,eAAe,GAAG,EAAE;AAChE,eAAgB;YAAAS,QAAA,eACA9B,OAAA,CAAC4C,IAAI,CAACxB,IAAI;cAACS,SAAS,EAAE;AACtC;AACA,oBAAoBe,IAAI,CAACvB,KAAK,KAAK,MAAM,GAAG,eAAe,GAAG,EAAE;AAChE,oBAAoBuB,IAAI,CAACvB,KAAK,KAAK,OAAO,GAAG,gBAAgB,GAAG,EAAE;AAClE,oBAAoBuB,IAAI,CAACvB,KAAK,KAAK,QAAQ,GAAG,iBAAiB,GAAG,EAAE;AACpE,oBAAoBuB,IAAI,CAACvB,KAAK,KAAK,QAAQ,GAAG,iBAAiB,GAAG,EAAE;AACpE;YAAkB;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GA9BDI,IAAI,CAAC1B,KAAK;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA+BL,CACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNxC,OAAA,CAACX,MAAM,CAAC0C,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BY,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAC3BlB,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAE7C9B,OAAA;QAAK6B,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD9B,OAAA;UAAI6B,SAAS,EAAC,iCAAiC;UAAAC,QAAA,EAAC;QAAgB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrExC,OAAA;UACEsB,OAAO,EAAEA,CAAA,KAAMlB,QAAQ,CAAC,kBAAkB,CAAE;UAC5CyB,SAAS,EAAC,6DAA6D;UAAAC,QAAA,EACxE;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENxC,OAAA;QAAK6B,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClEP,aAAa,CAACoB,GAAG,CAAC,CAACM,MAAM,EAAEJ,KAAK,kBAC/B7C,OAAA,CAACX,MAAM,CAAC0C,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEiB,KAAK,EAAE;UAAI,CAAE;UACpCf,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEiB,KAAK,EAAE;UAAE,CAAE;UAClCJ,UAAU,EAAE;YAAEC,KAAK,EAAE,GAAG,GAAGF,KAAK,GAAG;UAAI,CAAE;UACzCG,UAAU,EAAE;YAAEd,CAAC,EAAE,CAAC;UAAE,CAAE;UACtBZ,OAAO,EAAEA,CAAA,KAAMlB,QAAQ,CAAC,mBAAmB6C,MAAM,CAACzB,EAAE,EAAE,CAAE;UACxDK,SAAS,EAAC,sFAAsF;UAAAC,QAAA,gBAEhG9B,OAAA;YAAK6B,SAAS,EAAC,iHAAiH;YAAAC,QAAA,eAC9H9B,OAAA,CAACL,QAAQ;cAACkC,SAAS,EAAC;YAAsB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eAENxC,OAAA;YAAI6B,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAEmB,MAAM,CAAC/B;UAAK;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpExC,OAAA;YAAG6B,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAEmB,MAAM,CAACtB;UAAU;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAGjExC,OAAA;YAAK6B,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB9B,OAAA;cAAK6B,SAAS,EAAC,gDAAgD;cAAAC,QAAA,gBAC7D9B,OAAA;gBAAM6B,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAM;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7CxC,OAAA;gBAAM6B,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,GAAEmB,MAAM,CAACxB,QAAQ,EAAC,GAAC;cAAA;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,eACNxC,OAAA;cAAK6B,SAAS,EAAC,qCAAqC;cAAAC,QAAA,eAClD9B,OAAA;gBACE6B,SAAS,EAAC,6DAA6D;gBACvEsB,KAAK,EAAE;kBAAEC,KAAK,EAAE,GAAGH,MAAM,CAACxB,QAAQ;gBAAI;cAAE;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxC,OAAA;YAAG6B,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAEmB,MAAM,CAACrB;UAAY;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA,GA7BzDS,MAAM,CAACzB,EAAE;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA8BJ,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGbxC,OAAA,CAACX,MAAM,CAAC0C,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BY,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAC3BlB,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAE7C9B,OAAA;QAAI6B,SAAS,EAAC,sCAAsC;QAAAC,QAAA,EAAC;MAAa;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEvExC,OAAA;QAAK6B,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpD9B,OAAA;UACEsB,OAAO,EAAEA,CAAA,KAAMlB,QAAQ,CAAC,kBAAkB,CAAE;UAC5CyB,SAAS,EAAC,8FAA8F;UAAAC,QAAA,gBAExG9B,OAAA,CAACT,eAAe;YAACsC,SAAS,EAAC;UAA4B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1DxC,OAAA;YAAK6B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9B,OAAA;cAAI6B,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAa;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9DxC,OAAA;cAAG6B,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAkB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAETxC,OAAA;UACEsB,OAAO,EAAEA,CAAA,KAAMlB,QAAQ,CAAC,kBAAkB,CAAE;UAC5CyB,SAAS,EAAC,gGAAgG;UAAAC,QAAA,gBAE1G9B,OAAA,CAACR,yBAAyB;YAACqC,SAAS,EAAC;UAA6B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrExC,OAAA;YAAK6B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9B,OAAA;cAAI6B,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAAW;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7DxC,OAAA;cAAG6B,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAc;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAETxC,OAAA;UACEsB,OAAO,EAAEA,CAAA,KAAMlB,QAAQ,CAAC,uBAAuB,CAAE;UACjDyB,SAAS,EAAC,kGAAkG;UAAAC,QAAA,gBAE5G9B,OAAA,CAACP,gBAAgB;YAACoC,SAAS,EAAC;UAA8B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7DxC,OAAA;YAAK6B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9B,OAAA;cAAI6B,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAC;YAAO;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1DxC,OAAA;cAAG6B,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAC;YAAa;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAACrC,EAAA,CA9PIF,eAA+C;EAAA,QAClCX,WAAW;AAAA;AAAA+D,EAAA,GADxBpD,eAA+C;AAgQrD,eAAeA,eAAe;AAAC,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}