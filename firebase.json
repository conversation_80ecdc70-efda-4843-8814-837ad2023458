{"hosting": {"public": "build", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "functions": {"predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint", "npm --prefix \"$RESOURCE_DIR\" run build"], "source": "functions"}, "storage": {"rules": "storage.rules"}}