import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import { 
  UserIcon, 
  KeyIcon, 
  EyeIcon, 
  EyeSlashIcon,
  AcademicCapIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';

// Services
import { authService } from '../services/authService';

// Types
import { User } from '../types';

interface LoginPageProps {
  onLogin: (user: User) => void;
}

const LoginPage: React.FC<LoginPageProps> = ({ onLogin }) => {
  const [loginType, setLoginType] = useState<'admin' | 'student'>('student');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  
  // Admin login form
  const [adminForm, setAdminForm] = useState({
    email: '',
    password: ''
  });
  
  // Student login form
  const [studentForm, setStudentForm] = useState({
    accessCode: ''
  });

  const handleAdminLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!adminForm.email || !adminForm.password) {
      toast.error('يرجى ملء جميع الحقول');
      return;
    }

    setLoading(true);
    try {
      const user = await authService.loginAdmin(adminForm.email, adminForm.password);
      toast.success(`مرحباً ${user.name || 'المدير'}`);
      onLogin(user);
    } catch (error: any) {
      toast.error(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleStudentLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!studentForm.accessCode || studentForm.accessCode.length !== 7) {
      toast.error('يرجى إدخال كود دخول صحيح مكون من 7 أرقام');
      return;
    }

    setLoading(true);
    try {
      const user = await authService.loginStudent(studentForm.accessCode);
      toast.success(`مرحباً ${user.name || 'الطالب'}`);
      onLogin(user);
    } catch (error: any) {
      toast.error(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen gradient-primary flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Logo and Title */}
        <motion.div 
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <div className="bg-white rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center shadow-lg">
            <AcademicCapIcon className="w-10 h-10 text-primary-600" />
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">
            منصة ALaa Abd Hamied
          </h1>
          <p className="text-blue-100">
            للكورسات الإلكترونية والتعلم التفاعلي
          </p>
        </motion.div>

        {/* Login Type Selector */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-xl shadow-xl p-6 mb-6"
        >
          <div className="flex rounded-lg bg-gray-100 p-1 mb-6">
            <button
              type="button"
              onClick={() => setLoginType('student')}
              className={`
                flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all duration-200
                ${loginType === 'student' 
                  ? 'bg-primary-600 text-white shadow-sm' 
                  : 'text-gray-600 hover:text-gray-800'
                }
              `}
            >
              <UserIcon className="w-4 h-4 inline-block ml-2" />
              دخول الطالب
            </button>
            <button
              type="button"
              onClick={() => setLoginType('admin')}
              className={`
                flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all duration-200
                ${loginType === 'admin' 
                  ? 'bg-primary-600 text-white shadow-sm' 
                  : 'text-gray-600 hover:text-gray-800'
                }
              `}
            >
              <ShieldCheckIcon className="w-4 h-4 inline-block ml-2" />
              دخول المدير
            </button>
          </div>

          {/* Student Login Form */}
          {loginType === 'student' && (
            <motion.form 
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              onSubmit={handleStudentLogin}
              className="space-y-4"
            >
              <div>
                <label className="form-label">
                  كود الدخول (7 أرقام)
                </label>
                <div className="relative">
                  <KeyIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="text"
                    value={studentForm.accessCode}
                    onChange={(e) => {
                      const value = e.target.value.replace(/\D/g, '').slice(0, 7);
                      setStudentForm({ accessCode: value });
                    }}
                    placeholder="1234567"
                    className="form-input pr-10 text-center text-lg font-mono tracking-wider"
                    maxLength={7}
                    required
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  يمكنك الحصول على كود الدخول من المدير
                </p>
              </div>

              <button
                type="submit"
                disabled={loading || studentForm.accessCode.length !== 7}
                className="w-full btn-primary py-3 text-lg disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <div className="flex items-center justify-center">
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin ml-2" />
                    جاري تسجيل الدخول...
                  </div>
                ) : (
                  'دخول'
                )}
              </button>
            </motion.form>
          )}

          {/* Admin Login Form */}
          {loginType === 'admin' && (
            <motion.form 
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              onSubmit={handleAdminLogin}
              className="space-y-4"
            >
              <div>
                <label className="form-label">
                  البريد الإلكتروني
                </label>
                <div className="relative">
                  <UserIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="email"
                    value={adminForm.email}
                    onChange={(e) => setAdminForm({ ...adminForm, email: e.target.value })}
                    placeholder="<EMAIL>"
                    className="form-input pr-10"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="form-label">
                  كلمة المرور
                </label>
                <div className="relative">
                  <KeyIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={adminForm.password}
                    onChange={(e) => setAdminForm({ ...adminForm, password: e.target.value })}
                    placeholder="••••••••"
                    className="form-input pr-10 pl-10"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? (
                      <EyeSlashIcon className="w-5 h-5" />
                    ) : (
                      <EyeIcon className="w-5 h-5" />
                    )}
                  </button>
                </div>
              </div>

              <button
                type="submit"
                disabled={loading || !adminForm.email || !adminForm.password}
                className="w-full btn-primary py-3 text-lg disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <div className="flex items-center justify-center">
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin ml-2" />
                    جاري تسجيل الدخول...
                  </div>
                ) : (
                  'دخول'
                )}
              </button>
            </motion.form>
          )}
        </motion.div>

        {/* Footer */}
        <motion.div 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="text-center text-blue-100 text-sm"
        >
          <p>© 2024 منصة ALaa Abd Hamied. جميع الحقوق محفوظة.</p>
        </motion.div>
      </div>
    </div>
  );
};

export default LoginPage;
