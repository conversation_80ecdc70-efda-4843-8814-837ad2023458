{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0634\\u0631\\u0648\\u0639\\\\src\\\\components\\\\Admin\\\\QuizzesManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { PlusIcon, PencilIcon, TrashIcon, EyeIcon, ClipboardDocumentListIcon, QuestionMarkCircleIcon } from '@heroicons/react/24/outline';\n\n// Types\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizzesManagement = ({\n  onBack\n}) => {\n  _s();\n  const [quizzes, setQuizzes] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // Mock data for demonstration\n  const mockQuizzes = [{\n    id: '1',\n    courseId: '1',\n    title: 'اختبار أساسيات البرمجة',\n    description: 'اختبار شامل لأساسيات البرمجة',\n    questions: [{\n      id: '1',\n      question: 'ما هو المتغير؟',\n      type: 'multiple-choice',\n      options: ['مكان لتخزين البيانات', 'نوع من الدوال', 'أمر برمجي'],\n      correctAnswer: 0,\n      points: 10\n    }],\n    passingScore: 70,\n    timeLimit: 30,\n    attempts: 3,\n    isActive: true,\n    createdAt: new Date()\n  }, {\n    id: '2',\n    courseId: '2',\n    title: 'اختبار تطوير المواقع',\n    description: 'اختبار في HTML و CSS',\n    questions: [],\n    passingScore: 80,\n    timeLimit: 45,\n    attempts: 2,\n    isActive: true,\n    createdAt: new Date()\n  }];\n  React.useEffect(() => {\n    setQuizzes(mockQuizzes);\n  }, []);\n  const filteredQuizzes = quizzes.filter(quiz => {\n    var _quiz$description;\n    return quiz.title.toLowerCase().includes(searchTerm.toLowerCase()) || ((_quiz$description = quiz.description) === null || _quiz$description === void 0 ? void 0 : _quiz$description.toLowerCase().includes(searchTerm.toLowerCase()));\n  });\n  const handleAddQuiz = () => {\n    console.log('Add quiz');\n  };\n  const handleEditQuiz = quizId => {\n    console.log('Edit quiz:', quizId);\n  };\n  const handleDeleteQuiz = quizId => {\n    console.log('Delete quiz:', quizId);\n  };\n  const handleViewQuiz = quizId => {\n    console.log('View quiz:', quizId);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4 space-x-reverse\",\n        children: [onBack && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onBack,\n          className: \"p-2 text-gray-600 hover:text-gray-900 transition-colors\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M15 19l-7-7 7-7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"\\u0625\\u0646\\u0634\\u0627\\u0621 \\u0648\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\\u0627\\u062A \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleAddQuiz,\n        className: \"flex items-center space-x-2 space-x-reverse bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n        children: [/*#__PURE__*/_jsxDEV(PlusIcon, {\n          className: \"w-5 h-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0627\\u062E\\u062A\\u0628\\u0627\\u0631 \\u062C\\u062F\\u064A\\u062F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"\\u0627\\u0644\\u0628\\u062D\\u062B \\u0641\\u064A \\u0627\\u0644\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          placeholder: \"\\u0627\\u0628\\u062D\\u062B \\u0639\\u0646 \\u0627\\u062E\\u062A\\u0628\\u0627\\u0631...\",\n          className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n      children: filteredQuizzes.map((quiz, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: index * 0.1\n        },\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start justify-between mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3 space-x-reverse\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-2 bg-purple-100 rounded-lg\",\n                children: /*#__PURE__*/_jsxDEV(ClipboardDocumentListIcon, {\n                  className: \"w-6 h-6 text-purple-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-semibold text-gray-900\",\n                  children: quiz.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: quiz.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `px-2 py-1 text-xs rounded-full ${quiz.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n              children: quiz.isActive ? 'نشط' : 'غير نشط'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2 text-sm text-gray-600 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0639\\u062F\\u062F \\u0627\\u0644\\u0623\\u0633\\u0626\\u0644\\u0629:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: quiz.questions.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u062F\\u0631\\u062C\\u0629 \\u0627\\u0644\\u0646\\u062C\\u0627\\u062D:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: [quiz.passingScore, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0627\\u0644\\u0648\\u0642\\u062A \\u0627\\u0644\\u0645\\u062D\\u062F\\u062F:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: [quiz.timeLimit, \" \\u062F\\u0642\\u064A\\u0642\\u0629\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0639\\u062F\\u062F \\u0627\\u0644\\u0645\\u062D\\u0627\\u0648\\u0644\\u0627\\u062A:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: quiz.attempts\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between pt-4 border-t border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 space-x-reverse\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleViewQuiz(quiz.id),\n                className: \"p-2 text-gray-600 hover:text-blue-600 transition-colors\",\n                title: \"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\",\n                children: /*#__PURE__*/_jsxDEV(EyeIcon, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleEditQuiz(quiz.id),\n                className: \"p-2 text-gray-600 hover:text-green-600 transition-colors\",\n                title: \"\\u062A\\u0639\\u062F\\u064A\\u0644 \\u0627\\u0644\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\",\n                children: /*#__PURE__*/_jsxDEV(PencilIcon, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleDeleteQuiz(quiz.id),\n                className: \"p-2 text-gray-600 hover:text-red-600 transition-colors\",\n                title: \"\\u062D\\u0630\\u0641 \\u0627\\u0644\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\",\n                children: /*#__PURE__*/_jsxDEV(TrashIcon, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs text-gray-500\",\n              children: new Date(quiz.createdAt).toLocaleDateString('ar-SA')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 13\n        }, this)\n      }, quiz.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this), filteredQuizzes.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(QuestionMarkCircleIcon, {\n        className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-2\",\n        children: \"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\\u0627\\u062A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: \"\\u0644\\u0645 \\u064A\\u062A\\u0645 \\u0627\\u0644\\u0639\\u062B\\u0648\\u0631 \\u0639\\u0644\\u0649 \\u0623\\u064A \\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\\u0627\\u062A \\u062A\\u0637\\u0627\\u0628\\u0642 \\u0627\\u0644\\u0628\\u062D\\u062B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizzesManagement, \"vf7GNw8QKpcrlRULEUpzQWs2MDg=\");\n_c = QuizzesManagement;\nexport default QuizzesManagement;\nvar _c;\n$RefreshReg$(_c, \"QuizzesManagement\");", "map": {"version": 3, "names": ["React", "useState", "motion", "PlusIcon", "PencilIcon", "TrashIcon", "EyeIcon", "ClipboardDocumentListIcon", "QuestionMarkCircleIcon", "jsxDEV", "_jsxDEV", "QuizzesManagement", "onBack", "_s", "quizzes", "setQuizzes", "searchTerm", "setSearchTerm", "mockQuizzes", "id", "courseId", "title", "description", "questions", "question", "type", "options", "<PERSON><PERSON><PERSON><PERSON>", "points", "passingScore", "timeLimit", "attempts", "isActive", "createdAt", "Date", "useEffect", "filteredQuizzes", "filter", "quiz", "_quiz$description", "toLowerCase", "includes", "handleAddQuiz", "console", "log", "handleEditQuiz", "quizId", "handleDeleteQuiz", "handleViewQuiz", "className", "children", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "onChange", "e", "target", "placeholder", "map", "index", "div", "initial", "opacity", "y", "animate", "transition", "delay", "length", "toLocaleDateString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Admin/QuizzesManagement.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  PlusIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n  ClipboardDocumentListIcon,\n  QuestionMarkCircleIcon\n} from '@heroicons/react/24/outline';\n\n// Types\nimport { Quiz } from '../../types';\n\ninterface QuizzesManagementProps {\n  onBack?: () => void;\n}\n\nconst QuizzesManagement: React.FC<QuizzesManagementProps> = ({ onBack }) => {\n  const [quizzes, setQuizzes] = useState<Quiz[]>([]);\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // Mock data for demonstration\n  const mockQuizzes: Quiz[] = [\n    {\n      id: '1',\n      courseId: '1',\n      title: 'اختبار أساسيات البرمجة',\n      description: 'اختبار شامل لأساسيات البرمجة',\n      questions: [\n        {\n          id: '1',\n          question: 'ما هو المتغير؟',\n          type: 'multiple-choice',\n          options: ['مكان لتخزين البيانات', 'نوع من الدوال', 'أمر برمجي'],\n          correctAnswer: 0,\n          points: 10\n        }\n      ],\n      passingScore: 70,\n      timeLimit: 30,\n      attempts: 3,\n      isActive: true,\n      createdAt: new Date()\n    },\n    {\n      id: '2',\n      courseId: '2',\n      title: 'اختبار تطوير المواقع',\n      description: 'اختبار في HTML و CSS',\n      questions: [],\n      passingScore: 80,\n      timeLimit: 45,\n      attempts: 2,\n      isActive: true,\n      createdAt: new Date()\n    }\n  ];\n\n  React.useEffect(() => {\n    setQuizzes(mockQuizzes);\n  }, []);\n\n  const filteredQuizzes = quizzes.filter(quiz =>\n    quiz.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    quiz.description?.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  const handleAddQuiz = () => {\n    console.log('Add quiz');\n  };\n\n  const handleEditQuiz = (quizId: string) => {\n    console.log('Edit quiz:', quizId);\n  };\n\n  const handleDeleteQuiz = (quizId: string) => {\n    console.log('Delete quiz:', quizId);\n  };\n\n  const handleViewQuiz = (quizId: string) => {\n    console.log('View quiz:', quizId);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4 space-x-reverse\">\n          {onBack && (\n            <button\n              onClick={onBack}\n              className=\"p-2 text-gray-600 hover:text-gray-900 transition-colors\"\n            >\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n              </svg>\n            </button>\n          )}\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">إدارة الاختبارات</h1>\n            <p className=\"text-gray-600\">إنشاء وإدارة اختبارات الكورسات</p>\n          </div>\n        </div>\n        <button\n          onClick={handleAddQuiz}\n          className=\"flex items-center space-x-2 space-x-reverse bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n        >\n          <PlusIcon className=\"w-5 h-5\" />\n          <span>إضافة اختبار جديد</span>\n        </button>\n      </div>\n\n      {/* Search */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            البحث في الاختبارات\n          </label>\n          <input\n            type=\"text\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            placeholder=\"ابحث عن اختبار...\"\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          />\n        </div>\n      </div>\n\n      {/* Quizzes Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {filteredQuizzes.map((quiz, index) => (\n          <motion.div\n            key={quiz.id}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: index * 0.1 }}\n            className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow\"\n          >\n            <div className=\"p-6\">\n              <div className=\"flex items-start justify-between mb-4\">\n                <div className=\"flex items-center space-x-3 space-x-reverse\">\n                  <div className=\"p-2 bg-purple-100 rounded-lg\">\n                    <ClipboardDocumentListIcon className=\"w-6 h-6 text-purple-600\" />\n                  </div>\n                  <div>\n                    <h3 className=\"font-semibold text-gray-900\">{quiz.title}</h3>\n                    <p className=\"text-sm text-gray-600\">{quiz.description}</p>\n                  </div>\n                </div>\n                <span className={`px-2 py-1 text-xs rounded-full ${\n                  quiz.isActive \n                    ? 'bg-green-100 text-green-800' \n                    : 'bg-red-100 text-red-800'\n                }`}>\n                  {quiz.isActive ? 'نشط' : 'غير نشط'}\n                </span>\n              </div>\n\n              <div className=\"space-y-2 text-sm text-gray-600 mb-4\">\n                <div className=\"flex items-center justify-between\">\n                  <span>عدد الأسئلة:</span>\n                  <span className=\"font-medium\">{quiz.questions.length}</span>\n                </div>\n                <div className=\"flex items-center justify-between\">\n                  <span>درجة النجاح:</span>\n                  <span className=\"font-medium\">{quiz.passingScore}%</span>\n                </div>\n                <div className=\"flex items-center justify-between\">\n                  <span>الوقت المحدد:</span>\n                  <span className=\"font-medium\">{quiz.timeLimit} دقيقة</span>\n                </div>\n                <div className=\"flex items-center justify-between\">\n                  <span>عدد المحاولات:</span>\n                  <span className=\"font-medium\">{quiz.attempts}</span>\n                </div>\n              </div>\n\n              <div className=\"flex items-center justify-between pt-4 border-t border-gray-200\">\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <button\n                    onClick={() => handleViewQuiz(quiz.id)}\n                    className=\"p-2 text-gray-600 hover:text-blue-600 transition-colors\"\n                    title=\"عرض الاختبار\"\n                  >\n                    <EyeIcon className=\"w-4 h-4\" />\n                  </button>\n                  <button\n                    onClick={() => handleEditQuiz(quiz.id)}\n                    className=\"p-2 text-gray-600 hover:text-green-600 transition-colors\"\n                    title=\"تعديل الاختبار\"\n                  >\n                    <PencilIcon className=\"w-4 h-4\" />\n                  </button>\n                  <button\n                    onClick={() => handleDeleteQuiz(quiz.id)}\n                    className=\"p-2 text-gray-600 hover:text-red-600 transition-colors\"\n                    title=\"حذف الاختبار\"\n                  >\n                    <TrashIcon className=\"w-4 h-4\" />\n                  </button>\n                </div>\n                <span className=\"text-xs text-gray-500\">\n                  {new Date(quiz.createdAt).toLocaleDateString('ar-SA')}\n                </span>\n              </div>\n            </div>\n          </motion.div>\n        ))}\n      </div>\n\n      {filteredQuizzes.length === 0 && (\n        <div className=\"text-center py-12\">\n          <QuestionMarkCircleIcon className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">لا توجد اختبارات</h3>\n          <p className=\"text-gray-600\">لم يتم العثور على أي اختبارات تطابق البحث</p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default QuizzesManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,QAAQ,EACRC,UAAU,EACVC,SAAS,EACTC,OAAO,EACPC,yBAAyB,EACzBC,sBAAsB,QACjB,6BAA6B;;AAEpC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAOA,MAAMC,iBAAmD,GAAGA,CAAC;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAC1E,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAS,EAAE,CAAC;EAClD,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;;EAEhD;EACA,MAAMiB,WAAmB,GAAG,CAC1B;IACEC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,GAAG;IACbC,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAE,8BAA8B;IAC3CC,SAAS,EAAE,CACT;MACEJ,EAAE,EAAE,GAAG;MACPK,QAAQ,EAAE,gBAAgB;MAC1BC,IAAI,EAAE,iBAAiB;MACvBC,OAAO,EAAE,CAAC,sBAAsB,EAAE,eAAe,EAAE,WAAW,CAAC;MAC/DC,aAAa,EAAE,CAAC;MAChBC,MAAM,EAAE;IACV,CAAC,CACF;IACDC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE,IAAIC,IAAI,CAAC;EACtB,CAAC,EACD;IACEf,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,GAAG;IACbC,KAAK,EAAE,sBAAsB;IAC7BC,WAAW,EAAE,sBAAsB;IACnCC,SAAS,EAAE,EAAE;IACbM,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE,IAAIC,IAAI,CAAC;EACtB,CAAC,CACF;EAEDlC,KAAK,CAACmC,SAAS,CAAC,MAAM;IACpBpB,UAAU,CAACG,WAAW,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMkB,eAAe,GAAGtB,OAAO,CAACuB,MAAM,CAACC,IAAI;IAAA,IAAAC,iBAAA;IAAA,OACzCD,IAAI,CAACjB,KAAK,CAACmB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzB,UAAU,CAACwB,WAAW,CAAC,CAAC,CAAC,MAAAD,iBAAA,GAC3DD,IAAI,CAAChB,WAAW,cAAAiB,iBAAA,uBAAhBA,iBAAA,CAAkBC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzB,UAAU,CAACwB,WAAW,CAAC,CAAC,CAAC;EAAA,CACpE,CAAC;EAED,MAAME,aAAa,GAAGA,CAAA,KAAM;IAC1BC,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;EACzB,CAAC;EAED,MAAMC,cAAc,GAAIC,MAAc,IAAK;IACzCH,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEE,MAAM,CAAC;EACnC,CAAC;EAED,MAAMC,gBAAgB,GAAID,MAAc,IAAK;IAC3CH,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEE,MAAM,CAAC;EACrC,CAAC;EAED,MAAME,cAAc,GAAIF,MAAc,IAAK;IACzCH,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEE,MAAM,CAAC;EACnC,CAAC;EAED,oBACEpC,OAAA;IAAKuC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBxC,OAAA;MAAKuC,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDxC,OAAA;QAAKuC,SAAS,EAAC,6CAA6C;QAAAC,QAAA,GACzDtC,MAAM,iBACLF,OAAA;UACEyC,OAAO,EAAEvC,MAAO;UAChBqC,SAAS,EAAC,yDAAyD;UAAAC,QAAA,eAEnExC,OAAA;YAAKuC,SAAS,EAAC,SAAS;YAACG,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAJ,QAAA,eAC5ExC,OAAA;cAAM6C,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACT,eACDpD,OAAA;UAAAwC,QAAA,gBACExC,OAAA;YAAIuC,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAgB;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtEpD,OAAA;YAAGuC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAA8B;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNpD,OAAA;QACEyC,OAAO,EAAET,aAAc;QACvBO,SAAS,EAAC,6HAA6H;QAAAC,QAAA,gBAEvIxC,OAAA,CAACP,QAAQ;UAAC8C,SAAS,EAAC;QAAS;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChCpD,OAAA;UAAAwC,QAAA,EAAM;QAAiB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNpD,OAAA;MAAKuC,SAAS,EAAC,mCAAmC;MAAAC,QAAA,eAChDxC,OAAA;QAAAwC,QAAA,gBACExC,OAAA;UAAOuC,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEhE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRpD,OAAA;UACEe,IAAI,EAAC,MAAM;UACXsC,KAAK,EAAE/C,UAAW;UAClBgD,QAAQ,EAAGC,CAAC,IAAKhD,aAAa,CAACgD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAC/CI,WAAW,EAAC,+EAAmB;UAC/BlB,SAAS,EAAC;QAAwG;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpD,OAAA;MAAKuC,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClEd,eAAe,CAACgC,GAAG,CAAC,CAAC9B,IAAI,EAAE+B,KAAK,kBAC/B3D,OAAA,CAACR,MAAM,CAACoE,GAAG;QAETC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAEP,KAAK,GAAG;QAAI,CAAE;QACnCpB,SAAS,EAAC,wGAAwG;QAAAC,QAAA,eAElHxC,OAAA;UAAKuC,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBxC,OAAA;YAAKuC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDxC,OAAA;cAAKuC,SAAS,EAAC,6CAA6C;cAAAC,QAAA,gBAC1DxC,OAAA;gBAAKuC,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,eAC3CxC,OAAA,CAACH,yBAAyB;kBAAC0C,SAAS,EAAC;gBAAyB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACNpD,OAAA;gBAAAwC,QAAA,gBACExC,OAAA;kBAAIuC,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EAAEZ,IAAI,CAACjB;gBAAK;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC7DpD,OAAA;kBAAGuC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEZ,IAAI,CAAChB;gBAAW;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNpD,OAAA;cAAMuC,SAAS,EAAE,kCACfX,IAAI,CAACN,QAAQ,GACT,6BAA6B,GAC7B,yBAAyB,EAC5B;cAAAkB,QAAA,EACAZ,IAAI,CAACN,QAAQ,GAAG,KAAK,GAAG;YAAS;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAENpD,OAAA;YAAKuC,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBACnDxC,OAAA;cAAKuC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDxC,OAAA;gBAAAwC,QAAA,EAAM;cAAY;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzBpD,OAAA;gBAAMuC,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEZ,IAAI,CAACf,SAAS,CAACsD;cAAM;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,eACNpD,OAAA;cAAKuC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDxC,OAAA;gBAAAwC,QAAA,EAAM;cAAY;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzBpD,OAAA;gBAAMuC,SAAS,EAAC,aAAa;gBAAAC,QAAA,GAAEZ,IAAI,CAACT,YAAY,EAAC,GAAC;cAAA;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eACNpD,OAAA;cAAKuC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDxC,OAAA;gBAAAwC,QAAA,EAAM;cAAa;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1BpD,OAAA;gBAAMuC,SAAS,EAAC,aAAa;gBAAAC,QAAA,GAAEZ,IAAI,CAACR,SAAS,EAAC,iCAAM;cAAA;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACNpD,OAAA;cAAKuC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDxC,OAAA;gBAAAwC,QAAA,EAAM;cAAc;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3BpD,OAAA;gBAAMuC,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEZ,IAAI,CAACP;cAAQ;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpD,OAAA;YAAKuC,SAAS,EAAC,iEAAiE;YAAAC,QAAA,gBAC9ExC,OAAA;cAAKuC,SAAS,EAAC,6CAA6C;cAAAC,QAAA,gBAC1DxC,OAAA;gBACEyC,OAAO,EAAEA,CAAA,KAAMH,cAAc,CAACV,IAAI,CAACnB,EAAE,CAAE;gBACvC8B,SAAS,EAAC,yDAAyD;gBACnE5B,KAAK,EAAC,qEAAc;gBAAA6B,QAAA,eAEpBxC,OAAA,CAACJ,OAAO;kBAAC2C,SAAS,EAAC;gBAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eACTpD,OAAA;gBACEyC,OAAO,EAAEA,CAAA,KAAMN,cAAc,CAACP,IAAI,CAACnB,EAAE,CAAE;gBACvC8B,SAAS,EAAC,0DAA0D;gBACpE5B,KAAK,EAAC,iFAAgB;gBAAA6B,QAAA,eAEtBxC,OAAA,CAACN,UAAU;kBAAC6C,SAAS,EAAC;gBAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACTpD,OAAA;gBACEyC,OAAO,EAAEA,CAAA,KAAMJ,gBAAgB,CAACT,IAAI,CAACnB,EAAE,CAAE;gBACzC8B,SAAS,EAAC,wDAAwD;gBAClE5B,KAAK,EAAC,qEAAc;gBAAA6B,QAAA,eAEpBxC,OAAA,CAACL,SAAS;kBAAC4C,SAAS,EAAC;gBAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNpD,OAAA;cAAMuC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EACpC,IAAIhB,IAAI,CAACI,IAAI,CAACL,SAAS,CAAC,CAAC6C,kBAAkB,CAAC,OAAO;YAAC;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAzEDxB,IAAI,CAACnB,EAAE;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA0EF,CACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAEL1B,eAAe,CAACyC,MAAM,KAAK,CAAC,iBAC3BnE,OAAA;MAAKuC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCxC,OAAA,CAACF,sBAAsB;QAACyC,SAAS,EAAC;MAAsC;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3EpD,OAAA;QAAIuC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAgB;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5EpD,OAAA;QAAGuC,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAyC;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACjD,EAAA,CA1MIF,iBAAmD;AAAAoE,EAAA,GAAnDpE,iBAAmD;AA4MzD,eAAeA,iBAAiB;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}