{"ast": null, "code": "import React,{useState,useEffect}from'react';import{motion}from'framer-motion';import{useNavigate}from'react-router-dom';import{AcademicCapIcon,ClipboardDocumentListIcon,DocumentTextIcon,TrophyIcon,PlayIcon,BookOpenIcon,ChartBarIcon,ClockIcon}from'@heroicons/react/24/outline';// Types\nimport{jsxs as _jsxs,jsx as _jsx}from\"react/jsx-runtime\";const StudentOverview=_ref=>{let{user}=_ref;const navigate=useNavigate();const[stats,setStats]=useState({enrolledCourses:0,completedCourses:0,certificates:0,totalWatchTime:0,completedQuizzes:0,averageScore:0});useEffect(()=>{var _user$enrolledCourses,_user$completedCourse,_user$certificates;// Simulate loading stats\nsetStats({enrolledCourses:((_user$enrolledCourses=user.enrolledCourses)===null||_user$enrolledCourses===void 0?void 0:_user$enrolledCourses.length)||3,completedCourses:((_user$completedCourse=user.completedCourses)===null||_user$completedCourse===void 0?void 0:_user$completedCourse.length)||1,certificates:((_user$certificates=user.certificates)===null||_user$certificates===void 0?void 0:_user$certificates.length)||1,totalWatchTime:240,// minutes\ncompletedQuizzes:5,averageScore:85});},[user]);const statsCards=[{title:'الكورسات المسجلة',value:stats.enrolledCourses,icon:AcademicCapIcon,color:'blue',onClick:()=>navigate('/student/courses')},{title:'الكورسات المكتملة',value:stats.completedCourses,icon:BookOpenIcon,color:'green',onClick:()=>navigate('/student/courses?filter=completed')},{title:'الشهادات',value:stats.certificates,icon:DocumentTextIcon,color:'purple',onClick:()=>navigate('/student/certificates')},{title:'الاختبارات المكتملة',value:stats.completedQuizzes,icon:ClipboardDocumentListIcon,color:'orange',onClick:()=>navigate('/student/quizzes')}];const recentCourses=[{id:'1',title:'أساسيات البرمجة',progress:75,thumbnail:'/api/placeholder/300/200',instructor:'أ. محمد أحمد',lastAccessed:'منذ يوم'},{id:'2',title:'تطوير المواقع',progress:45,thumbnail:'/api/placeholder/300/200',instructor:'أ. سارة محمد',lastAccessed:'منذ 3 أيام'},{id:'3',title:'قواعد البيانات',progress:20,thumbnail:'/api/placeholder/300/200',instructor:'أ. أحمد علي',lastAccessed:'منذ أسبوع'}];return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:\"bg-gradient-to-l from-primary-600 to-primary-700 rounded-xl p-6 text-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"h1\",{className:\"text-2xl font-bold mb-2\",children:[\"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B \\u0628\\u0643\\u060C \",user.name||'الطالب',\"!\"]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-primary-100 mb-4\",children:\"\\u0627\\u0633\\u062A\\u0645\\u0631 \\u0641\\u064A \\u0631\\u062D\\u0644\\u0629 \\u0627\\u0644\\u062A\\u0639\\u0644\\u0645 \\u0648\\u0627\\u0643\\u062A\\u0634\\u0641 \\u0627\\u0644\\u0645\\u0632\\u064A\\u062F \\u0645\\u0646 \\u0627\\u0644\\u0645\\u0639\\u0631\\u0641\\u0629\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4 space-x-reverse text-sm\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(ClockIcon,{className:\"w-4 h-4 ml-1\"}),/*#__PURE__*/_jsxs(\"span\",{children:[Math.floor(stats.totalWatchTime/60),\"\\u0633 \",stats.totalWatchTime%60,\"\\u062F \\u0645\\u0646 \\u0627\\u0644\\u0645\\u0634\\u0627\\u0647\\u062F\\u0629\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(ChartBarIcon,{className:\"w-4 h-4 ml-1\"}),/*#__PURE__*/_jsxs(\"span\",{children:[\"\\u0645\\u062A\\u0648\\u0633\\u0637 \\u0627\\u0644\\u0646\\u062A\\u0627\\u0626\\u062C: \",stats.averageScore,\"%\"]})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"hidden md:block\",children:/*#__PURE__*/_jsx(TrophyIcon,{className:\"w-20 h-20 text-primary-200\"})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",children:statsCards.map((stat,index)=>/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:index*0.1},whileHover:{y:-2},onClick:stat.onClick,className:\"bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-600\",children:stat.title}),/*#__PURE__*/_jsx(\"p\",{className:\"text-3xl font-bold text-gray-900 mt-1\",children:stat.value})]}),/*#__PURE__*/_jsx(\"div\",{className:`\n                w-12 h-12 rounded-lg flex items-center justify-center\n                ${stat.color==='blue'?'bg-blue-100':''}\n                ${stat.color==='green'?'bg-green-100':''}\n                ${stat.color==='purple'?'bg-purple-100':''}\n                ${stat.color==='orange'?'bg-orange-100':''}\n              `,children:/*#__PURE__*/_jsx(stat.icon,{className:`\n                  w-6 h-6\n                  ${stat.color==='blue'?'text-blue-600':''}\n                  ${stat.color==='green'?'text-green-600':''}\n                  ${stat.color==='purple'?'text-purple-600':''}\n                  ${stat.color==='orange'?'text-orange-600':''}\n                `})})]})},stat.title))}),/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0.3},className:\"bg-white rounded-xl p-6 shadow-sm\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-6\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-bold text-gray-900\",children:\"\\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A \\u0627\\u0644\\u0623\\u062E\\u064A\\u0631\\u0629\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>navigate('/student/courses'),className:\"text-primary-600 hover:text-primary-700 font-medium text-sm\",children:\"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u0643\\u0644\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",children:recentCourses.map((course,index)=>/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,scale:0.9},animate:{opacity:1,scale:1},transition:{delay:0.4+index*0.1},whileHover:{y:-4},onClick:()=>navigate(`/student/course/${course.id}`),className:\"bg-gray-50 rounded-lg p-4 hover:shadow-md transition-all duration-200 cursor-pointer\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"aspect-video bg-gradient-to-br from-primary-400 to-primary-600 rounded-lg mb-4 flex items-center justify-center\",children:/*#__PURE__*/_jsx(PlayIcon,{className:\"w-12 h-12 text-white\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold text-gray-900 mb-2\",children:course.title}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600 mb-3\",children:course.instructor}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between text-sm mb-1\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"\\u0627\\u0644\\u062A\\u0642\\u062F\\u0645\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"font-medium text-gray-900\",children:[course.progress,\"%\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"w-full bg-gray-200 rounded-full h-2\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-primary-600 h-2 rounded-full transition-all duration-300\",style:{width:`${course.progress}%`}})})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500\",children:course.lastAccessed})]},course.id))})]}),/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0.5},className:\"bg-white rounded-xl p-6 shadow-sm\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-bold text-gray-900 mb-4\",children:\"\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A \\u0633\\u0631\\u064A\\u0639\\u0629\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-3 gap-4\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>navigate('/student/courses'),className:\"flex items-center p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200\",children:[/*#__PURE__*/_jsx(AcademicCapIcon,{className:\"w-8 h-8 text-blue-600 ml-3\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-right\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold text-blue-900\",children:\"\\u062A\\u0635\\u0641\\u062D \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-blue-700\",children:\"\\u0627\\u0643\\u062A\\u0634\\u0641 \\u0643\\u0648\\u0631\\u0633\\u0627\\u062A \\u062C\\u062F\\u064A\\u062F\\u0629\"})]})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>navigate('/student/quizzes'),className:\"flex items-center p-4 bg-green-50 hover:bg-green-100 rounded-lg transition-colors duration-200\",children:[/*#__PURE__*/_jsx(ClipboardDocumentListIcon,{className:\"w-8 h-8 text-green-600 ml-3\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-right\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold text-green-900\",children:\"\\u0623\\u062F\\u0627\\u0621 \\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-green-700\",children:\"\\u0627\\u062E\\u062A\\u0628\\u0631 \\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A\\u0643\"})]})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>navigate('/student/certificates'),className:\"flex items-center p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors duration-200\",children:[/*#__PURE__*/_jsx(DocumentTextIcon,{className:\"w-8 h-8 text-purple-600 ml-3\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-right\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold text-purple-900\",children:\"\\u0634\\u0647\\u0627\\u062F\\u0627\\u062A\\u064A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-purple-700\",children:\"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u0625\\u0646\\u062C\\u0627\\u0632\\u0627\\u062A\"})]})]})]})]})]});};export default StudentOverview;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "useNavigate", "AcademicCapIcon", "ClipboardDocumentListIcon", "DocumentTextIcon", "TrophyIcon", "PlayIcon", "BookOpenIcon", "ChartBarIcon", "ClockIcon", "jsxs", "_jsxs", "jsx", "_jsx", "StudentOverview", "_ref", "user", "navigate", "stats", "setStats", "enrolledCourses", "completedCourses", "certificates", "totalWatchTime", "completedQuizzes", "averageScore", "_user$enrolledCourses", "_user$completedCourse", "_user$certificates", "length", "statsCards", "title", "value", "icon", "color", "onClick", "recentCourses", "id", "progress", "thumbnail", "instructor", "lastAccessed", "className", "children", "div", "initial", "opacity", "y", "animate", "name", "Math", "floor", "map", "stat", "index", "transition", "delay", "whileHover", "course", "scale", "style", "width"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Student/StudentOverview.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  AcademicCapIcon,\n  ClipboardDocumentListIcon,\n  DocumentTextIcon,\n  TrophyIcon,\n  PlayIcon,\n  BookOpenIcon,\n  ChartBarIcon,\n  ClockIcon\n} from '@heroicons/react/24/outline';\n\n// Types\nimport { Student } from '../../types';\n\ninterface StudentOverviewProps {\n  user: Student;\n}\n\nconst StudentOverview: React.FC<StudentOverviewProps> = ({ user }) => {\n  const navigate = useNavigate();\n  const [stats, setStats] = useState({\n    enrolledCourses: 0,\n    completedCourses: 0,\n    certificates: 0,\n    totalWatchTime: 0,\n    completedQuizzes: 0,\n    averageScore: 0\n  });\n\n  useEffect(() => {\n    // Simulate loading stats\n    setStats({\n      enrolledCourses: user.enrolledCourses?.length || 3,\n      completedCourses: user.completedCourses?.length || 1,\n      certificates: user.certificates?.length || 1,\n      totalWatchTime: 240, // minutes\n      completedQuizzes: 5,\n      averageScore: 85\n    });\n  }, [user]);\n\n  const statsCards = [\n    {\n      title: 'الكورسات المسجلة',\n      value: stats.enrolledCourses,\n      icon: AcademicCapIcon,\n      color: 'blue',\n      onClick: () => navigate('/student/courses')\n    },\n    {\n      title: 'الكورسات المكتملة',\n      value: stats.completedCourses,\n      icon: BookOpenIcon,\n      color: 'green',\n      onClick: () => navigate('/student/courses?filter=completed')\n    },\n    {\n      title: 'الشهادات',\n      value: stats.certificates,\n      icon: DocumentTextIcon,\n      color: 'purple',\n      onClick: () => navigate('/student/certificates')\n    },\n    {\n      title: 'الاختبارات المكتملة',\n      value: stats.completedQuizzes,\n      icon: ClipboardDocumentListIcon,\n      color: 'orange',\n      onClick: () => navigate('/student/quizzes')\n    }\n  ];\n\n  const recentCourses = [\n    {\n      id: '1',\n      title: 'أساسيات البرمجة',\n      progress: 75,\n      thumbnail: '/api/placeholder/300/200',\n      instructor: 'أ. محمد أحمد',\n      lastAccessed: 'منذ يوم'\n    },\n    {\n      id: '2',\n      title: 'تطوير المواقع',\n      progress: 45,\n      thumbnail: '/api/placeholder/300/200',\n      instructor: 'أ. سارة محمد',\n      lastAccessed: 'منذ 3 أيام'\n    },\n    {\n      id: '3',\n      title: 'قواعد البيانات',\n      progress: 20,\n      thumbnail: '/api/placeholder/300/200',\n      instructor: 'أ. أحمد علي',\n      lastAccessed: 'منذ أسبوع'\n    }\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Welcome Section */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        className=\"bg-gradient-to-l from-primary-600 to-primary-700 rounded-xl p-6 text-white\"\n      >\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold mb-2\">\n              مرحباً بك، {user.name || 'الطالب'}!\n            </h1>\n            <p className=\"text-primary-100 mb-4\">\n              استمر في رحلة التعلم واكتشف المزيد من المعرفة\n            </p>\n            <div className=\"flex items-center space-x-4 space-x-reverse text-sm\">\n              <div className=\"flex items-center\">\n                <ClockIcon className=\"w-4 h-4 ml-1\" />\n                <span>{Math.floor(stats.totalWatchTime / 60)}س {stats.totalWatchTime % 60}د من المشاهدة</span>\n              </div>\n              <div className=\"flex items-center\">\n                <ChartBarIcon className=\"w-4 h-4 ml-1\" />\n                <span>متوسط النتائج: {stats.averageScore}%</span>\n              </div>\n            </div>\n          </div>\n          <div className=\"hidden md:block\">\n            <TrophyIcon className=\"w-20 h-20 text-primary-200\" />\n          </div>\n        </div>\n      </motion.div>\n\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {statsCards.map((stat, index) => (\n          <motion.div\n            key={stat.title}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: index * 0.1 }}\n            whileHover={{ y: -2 }}\n            onClick={stat.onClick}\n            className=\"bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer\"\n          >\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">{stat.title}</p>\n                <p className=\"text-3xl font-bold text-gray-900 mt-1\">\n                  {stat.value}\n                </p>\n              </div>\n              <div className={`\n                w-12 h-12 rounded-lg flex items-center justify-center\n                ${stat.color === 'blue' ? 'bg-blue-100' : ''}\n                ${stat.color === 'green' ? 'bg-green-100' : ''}\n                ${stat.color === 'purple' ? 'bg-purple-100' : ''}\n                ${stat.color === 'orange' ? 'bg-orange-100' : ''}\n              `}>\n                <stat.icon className={`\n                  w-6 h-6\n                  ${stat.color === 'blue' ? 'text-blue-600' : ''}\n                  ${stat.color === 'green' ? 'text-green-600' : ''}\n                  ${stat.color === 'purple' ? 'text-purple-600' : ''}\n                  ${stat.color === 'orange' ? 'text-orange-600' : ''}\n                `} />\n              </div>\n            </div>\n          </motion.div>\n        ))}\n      </div>\n\n      {/* Recent Courses */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.3 }}\n        className=\"bg-white rounded-xl p-6 shadow-sm\"\n      >\n        <div className=\"flex items-center justify-between mb-6\">\n          <h2 className=\"text-xl font-bold text-gray-900\">الكورسات الأخيرة</h2>\n          <button\n            onClick={() => navigate('/student/courses')}\n            className=\"text-primary-600 hover:text-primary-700 font-medium text-sm\"\n          >\n            عرض الكل\n          </button>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {recentCourses.map((course, index) => (\n            <motion.div\n              key={course.id}\n              initial={{ opacity: 0, scale: 0.9 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ delay: 0.4 + index * 0.1 }}\n              whileHover={{ y: -4 }}\n              onClick={() => navigate(`/student/course/${course.id}`)}\n              className=\"bg-gray-50 rounded-lg p-4 hover:shadow-md transition-all duration-200 cursor-pointer\"\n            >\n              <div className=\"aspect-video bg-gradient-to-br from-primary-400 to-primary-600 rounded-lg mb-4 flex items-center justify-center\">\n                <PlayIcon className=\"w-12 h-12 text-white\" />\n              </div>\n              \n              <h3 className=\"font-semibold text-gray-900 mb-2\">{course.title}</h3>\n              <p className=\"text-sm text-gray-600 mb-3\">{course.instructor}</p>\n              \n              {/* Progress Bar */}\n              <div className=\"mb-3\">\n                <div className=\"flex items-center justify-between text-sm mb-1\">\n                  <span className=\"text-gray-600\">التقدم</span>\n                  <span className=\"font-medium text-gray-900\">{course.progress}%</span>\n                </div>\n                <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                  <div\n                    className=\"bg-primary-600 h-2 rounded-full transition-all duration-300\"\n                    style={{ width: `${course.progress}%` }}\n                  />\n                </div>\n              </div>\n              \n              <p className=\"text-xs text-gray-500\">{course.lastAccessed}</p>\n            </motion.div>\n          ))}\n        </div>\n      </motion.div>\n\n      {/* Quick Actions */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.5 }}\n        className=\"bg-white rounded-xl p-6 shadow-sm\"\n      >\n        <h2 className=\"text-xl font-bold text-gray-900 mb-4\">إجراءات سريعة</h2>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <button\n            onClick={() => navigate('/student/courses')}\n            className=\"flex items-center p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200\"\n          >\n            <AcademicCapIcon className=\"w-8 h-8 text-blue-600 ml-3\" />\n            <div className=\"text-right\">\n              <h3 className=\"font-semibold text-blue-900\">تصفح الكورسات</h3>\n              <p className=\"text-sm text-blue-700\">اكتشف كورسات جديدة</p>\n            </div>\n          </button>\n          \n          <button\n            onClick={() => navigate('/student/quizzes')}\n            className=\"flex items-center p-4 bg-green-50 hover:bg-green-100 rounded-lg transition-colors duration-200\"\n          >\n            <ClipboardDocumentListIcon className=\"w-8 h-8 text-green-600 ml-3\" />\n            <div className=\"text-right\">\n              <h3 className=\"font-semibold text-green-900\">أداء اختبار</h3>\n              <p className=\"text-sm text-green-700\">اختبر معلوماتك</p>\n            </div>\n          </button>\n          \n          <button\n            onClick={() => navigate('/student/certificates')}\n            className=\"flex items-center p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors duration-200\"\n          >\n            <DocumentTextIcon className=\"w-8 h-8 text-purple-600 ml-3\" />\n            <div className=\"text-right\">\n              <h3 className=\"font-semibold text-purple-900\">شهاداتي</h3>\n              <p className=\"text-sm text-purple-700\">عرض الإنجازات</p>\n            </div>\n          </button>\n        </div>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default StudentOverview;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,MAAM,KAAQ,eAAe,CACtC,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OACEC,eAAe,CACfC,yBAAyB,CACzBC,gBAAgB,CAChBC,UAAU,CACVC,QAAQ,CACRC,YAAY,CACZC,YAAY,CACZC,SAAS,KACJ,6BAA6B,CAEpC;AAAA,OAAAC,IAAA,IAAAC,KAAA,CAAAC,GAAA,IAAAC,IAAA,yBAOA,KAAM,CAAAC,eAA+C,CAAGC,IAAA,EAAc,IAAb,CAAEC,IAAK,CAAC,CAAAD,IAAA,CAC/D,KAAM,CAAAE,QAAQ,CAAGhB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACiB,KAAK,CAAEC,QAAQ,CAAC,CAAGrB,QAAQ,CAAC,CACjCsB,eAAe,CAAE,CAAC,CAClBC,gBAAgB,CAAE,CAAC,CACnBC,YAAY,CAAE,CAAC,CACfC,cAAc,CAAE,CAAC,CACjBC,gBAAgB,CAAE,CAAC,CACnBC,YAAY,CAAE,CAChB,CAAC,CAAC,CAEF1B,SAAS,CAAC,IAAM,KAAA2B,qBAAA,CAAAC,qBAAA,CAAAC,kBAAA,CACd;AACAT,QAAQ,CAAC,CACPC,eAAe,CAAE,EAAAM,qBAAA,CAAAV,IAAI,CAACI,eAAe,UAAAM,qBAAA,iBAApBA,qBAAA,CAAsBG,MAAM,GAAI,CAAC,CAClDR,gBAAgB,CAAE,EAAAM,qBAAA,CAAAX,IAAI,CAACK,gBAAgB,UAAAM,qBAAA,iBAArBA,qBAAA,CAAuBE,MAAM,GAAI,CAAC,CACpDP,YAAY,CAAE,EAAAM,kBAAA,CAAAZ,IAAI,CAACM,YAAY,UAAAM,kBAAA,iBAAjBA,kBAAA,CAAmBC,MAAM,GAAI,CAAC,CAC5CN,cAAc,CAAE,GAAG,CAAE;AACrBC,gBAAgB,CAAE,CAAC,CACnBC,YAAY,CAAE,EAChB,CAAC,CAAC,CACJ,CAAC,CAAE,CAACT,IAAI,CAAC,CAAC,CAEV,KAAM,CAAAc,UAAU,CAAG,CACjB,CACEC,KAAK,CAAE,kBAAkB,CACzBC,KAAK,CAAEd,KAAK,CAACE,eAAe,CAC5Ba,IAAI,CAAE/B,eAAe,CACrBgC,KAAK,CAAE,MAAM,CACbC,OAAO,CAAEA,CAAA,GAAMlB,QAAQ,CAAC,kBAAkB,CAC5C,CAAC,CACD,CACEc,KAAK,CAAE,mBAAmB,CAC1BC,KAAK,CAAEd,KAAK,CAACG,gBAAgB,CAC7BY,IAAI,CAAE1B,YAAY,CAClB2B,KAAK,CAAE,OAAO,CACdC,OAAO,CAAEA,CAAA,GAAMlB,QAAQ,CAAC,mCAAmC,CAC7D,CAAC,CACD,CACEc,KAAK,CAAE,UAAU,CACjBC,KAAK,CAAEd,KAAK,CAACI,YAAY,CACzBW,IAAI,CAAE7B,gBAAgB,CACtB8B,KAAK,CAAE,QAAQ,CACfC,OAAO,CAAEA,CAAA,GAAMlB,QAAQ,CAAC,uBAAuB,CACjD,CAAC,CACD,CACEc,KAAK,CAAE,qBAAqB,CAC5BC,KAAK,CAAEd,KAAK,CAACM,gBAAgB,CAC7BS,IAAI,CAAE9B,yBAAyB,CAC/B+B,KAAK,CAAE,QAAQ,CACfC,OAAO,CAAEA,CAAA,GAAMlB,QAAQ,CAAC,kBAAkB,CAC5C,CAAC,CACF,CAED,KAAM,CAAAmB,aAAa,CAAG,CACpB,CACEC,EAAE,CAAE,GAAG,CACPN,KAAK,CAAE,iBAAiB,CACxBO,QAAQ,CAAE,EAAE,CACZC,SAAS,CAAE,0BAA0B,CACrCC,UAAU,CAAE,cAAc,CAC1BC,YAAY,CAAE,SAChB,CAAC,CACD,CACEJ,EAAE,CAAE,GAAG,CACPN,KAAK,CAAE,eAAe,CACtBO,QAAQ,CAAE,EAAE,CACZC,SAAS,CAAE,0BAA0B,CACrCC,UAAU,CAAE,cAAc,CAC1BC,YAAY,CAAE,YAChB,CAAC,CACD,CACEJ,EAAE,CAAE,GAAG,CACPN,KAAK,CAAE,gBAAgB,CACvBO,QAAQ,CAAE,EAAE,CACZC,SAAS,CAAE,0BAA0B,CACrCC,UAAU,CAAE,aAAa,CACzBC,YAAY,CAAE,WAChB,CAAC,CACF,CAED,mBACE9B,KAAA,QAAK+B,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExB9B,IAAA,CAACb,MAAM,CAAC4C,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BL,SAAS,CAAC,4EAA4E,CAAAC,QAAA,cAEtFhC,KAAA,QAAK+B,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDhC,KAAA,QAAAgC,QAAA,eACEhC,KAAA,OAAI+B,SAAS,CAAC,yBAAyB,CAAAC,QAAA,EAAC,0DAC3B,CAAC3B,IAAI,CAACiC,IAAI,EAAI,QAAQ,CAAC,GACpC,EAAI,CAAC,cACLpC,IAAA,MAAG6B,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,6OAErC,CAAG,CAAC,cACJhC,KAAA,QAAK+B,SAAS,CAAC,qDAAqD,CAAAC,QAAA,eAClEhC,KAAA,QAAK+B,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC9B,IAAA,CAACJ,SAAS,EAACiC,SAAS,CAAC,cAAc,CAAE,CAAC,cACtC/B,KAAA,SAAAgC,QAAA,EAAOO,IAAI,CAACC,KAAK,CAACjC,KAAK,CAACK,cAAc,CAAG,EAAE,CAAC,CAAC,SAAE,CAACL,KAAK,CAACK,cAAc,CAAG,EAAE,CAAC,sEAAa,EAAM,CAAC,EAC3F,CAAC,cACNZ,KAAA,QAAK+B,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC9B,IAAA,CAACL,YAAY,EAACkC,SAAS,CAAC,cAAc,CAAE,CAAC,cACzC/B,KAAA,SAAAgC,QAAA,EAAM,6EAAe,CAACzB,KAAK,CAACO,YAAY,CAAC,GAAC,EAAM,CAAC,EAC9C,CAAC,EACH,CAAC,EACH,CAAC,cACNZ,IAAA,QAAK6B,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B9B,IAAA,CAACR,UAAU,EAACqC,SAAS,CAAC,4BAA4B,CAAE,CAAC,CAClD,CAAC,EACH,CAAC,CACI,CAAC,cAGb7B,IAAA,QAAK6B,SAAS,CAAC,sDAAsD,CAAAC,QAAA,CAClEb,UAAU,CAACsB,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,gBAC1BzC,IAAA,CAACb,MAAM,CAAC4C,GAAG,EAETC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BQ,UAAU,CAAE,CAAEC,KAAK,CAAEF,KAAK,CAAG,GAAI,CAAE,CACnCG,UAAU,CAAE,CAAEV,CAAC,CAAE,CAAC,CAAE,CAAE,CACtBZ,OAAO,CAAEkB,IAAI,CAAClB,OAAQ,CACtBO,SAAS,CAAC,8FAA8F,CAAAC,QAAA,cAExGhC,KAAA,QAAK+B,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDhC,KAAA,QAAAgC,QAAA,eACE9B,IAAA,MAAG6B,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAEU,IAAI,CAACtB,KAAK,CAAI,CAAC,cACjElB,IAAA,MAAG6B,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CACjDU,IAAI,CAACrB,KAAK,CACV,CAAC,EACD,CAAC,cACNnB,IAAA,QAAK6B,SAAS,CAAE;AAC9B;AACA,kBAAkBW,IAAI,CAACnB,KAAK,GAAK,MAAM,CAAG,aAAa,CAAG,EAAE;AAC5D,kBAAkBmB,IAAI,CAACnB,KAAK,GAAK,OAAO,CAAG,cAAc,CAAG,EAAE;AAC9D,kBAAkBmB,IAAI,CAACnB,KAAK,GAAK,QAAQ,CAAG,eAAe,CAAG,EAAE;AAChE,kBAAkBmB,IAAI,CAACnB,KAAK,GAAK,QAAQ,CAAG,eAAe,CAAG,EAAE;AAChE,eAAgB,CAAAS,QAAA,cACA9B,IAAA,CAACwC,IAAI,CAACpB,IAAI,EAACS,SAAS,CAAE;AACtC;AACA,oBAAoBW,IAAI,CAACnB,KAAK,GAAK,MAAM,CAAG,eAAe,CAAG,EAAE;AAChE,oBAAoBmB,IAAI,CAACnB,KAAK,GAAK,OAAO,CAAG,gBAAgB,CAAG,EAAE;AAClE,oBAAoBmB,IAAI,CAACnB,KAAK,GAAK,QAAQ,CAAG,iBAAiB,CAAG,EAAE;AACpE,oBAAoBmB,IAAI,CAACnB,KAAK,GAAK,QAAQ,CAAG,iBAAiB,CAAG,EAAE;AACpE,iBAAkB,CAAE,CAAC,CACF,CAAC,EACH,CAAC,EA9BDmB,IAAI,CAACtB,KA+BA,CACb,CAAC,CACC,CAAC,cAGNpB,KAAA,CAACX,MAAM,CAAC4C,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BQ,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3Bd,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAE7ChC,KAAA,QAAK+B,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrD9B,IAAA,OAAI6B,SAAS,CAAC,iCAAiC,CAAAC,QAAA,CAAC,6FAAgB,CAAI,CAAC,cACrE9B,IAAA,WACEsB,OAAO,CAAEA,CAAA,GAAMlB,QAAQ,CAAC,kBAAkB,CAAE,CAC5CyB,SAAS,CAAC,6DAA6D,CAAAC,QAAA,CACxE,6CAED,CAAQ,CAAC,EACN,CAAC,cAEN9B,IAAA,QAAK6B,SAAS,CAAC,sDAAsD,CAAAC,QAAA,CAClEP,aAAa,CAACgB,GAAG,CAAC,CAACM,MAAM,CAAEJ,KAAK,gBAC/B3C,KAAA,CAACX,MAAM,CAAC4C,GAAG,EAETC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEa,KAAK,CAAE,GAAI,CAAE,CACpCX,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEa,KAAK,CAAE,CAAE,CAAE,CAClCJ,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAG,CAAGF,KAAK,CAAG,GAAI,CAAE,CACzCG,UAAU,CAAE,CAAEV,CAAC,CAAE,CAAC,CAAE,CAAE,CACtBZ,OAAO,CAAEA,CAAA,GAAMlB,QAAQ,CAAC,mBAAmByC,MAAM,CAACrB,EAAE,EAAE,CAAE,CACxDK,SAAS,CAAC,sFAAsF,CAAAC,QAAA,eAEhG9B,IAAA,QAAK6B,SAAS,CAAC,iHAAiH,CAAAC,QAAA,cAC9H9B,IAAA,CAACP,QAAQ,EAACoC,SAAS,CAAC,sBAAsB,CAAE,CAAC,CAC1C,CAAC,cAEN7B,IAAA,OAAI6B,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAEe,MAAM,CAAC3B,KAAK,CAAK,CAAC,cACpElB,IAAA,MAAG6B,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAEe,MAAM,CAAClB,UAAU,CAAI,CAAC,cAGjE7B,KAAA,QAAK+B,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBhC,KAAA,QAAK+B,SAAS,CAAC,gDAAgD,CAAAC,QAAA,eAC7D9B,IAAA,SAAM6B,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,sCAAM,CAAM,CAAC,cAC7ChC,KAAA,SAAM+B,SAAS,CAAC,2BAA2B,CAAAC,QAAA,EAAEe,MAAM,CAACpB,QAAQ,CAAC,GAAC,EAAM,CAAC,EAClE,CAAC,cACNzB,IAAA,QAAK6B,SAAS,CAAC,qCAAqC,CAAAC,QAAA,cAClD9B,IAAA,QACE6B,SAAS,CAAC,6DAA6D,CACvEkB,KAAK,CAAE,CAAEC,KAAK,CAAE,GAAGH,MAAM,CAACpB,QAAQ,GAAI,CAAE,CACzC,CAAC,CACC,CAAC,EACH,CAAC,cAENzB,IAAA,MAAG6B,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEe,MAAM,CAACjB,YAAY,CAAI,CAAC,GA7BzDiB,MAAM,CAACrB,EA8BF,CACb,CAAC,CACC,CAAC,EACI,CAAC,cAGb1B,KAAA,CAACX,MAAM,CAAC4C,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BQ,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3Bd,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAE7C9B,IAAA,OAAI6B,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,2EAAa,CAAI,CAAC,cAEvEhC,KAAA,QAAK+B,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDhC,KAAA,WACEwB,OAAO,CAAEA,CAAA,GAAMlB,QAAQ,CAAC,kBAAkB,CAAE,CAC5CyB,SAAS,CAAC,8FAA8F,CAAAC,QAAA,eAExG9B,IAAA,CAACX,eAAe,EAACwC,SAAS,CAAC,4BAA4B,CAAE,CAAC,cAC1D/B,KAAA,QAAK+B,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB9B,IAAA,OAAI6B,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,2EAAa,CAAI,CAAC,cAC9D9B,IAAA,MAAG6B,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,oGAAkB,CAAG,CAAC,EACxD,CAAC,EACA,CAAC,cAEThC,KAAA,WACEwB,OAAO,CAAEA,CAAA,GAAMlB,QAAQ,CAAC,kBAAkB,CAAE,CAC5CyB,SAAS,CAAC,gGAAgG,CAAAC,QAAA,eAE1G9B,IAAA,CAACV,yBAAyB,EAACuC,SAAS,CAAC,6BAA6B,CAAE,CAAC,cACrE/B,KAAA,QAAK+B,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB9B,IAAA,OAAI6B,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,+DAAW,CAAI,CAAC,cAC7D9B,IAAA,MAAG6B,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAC,iFAAc,CAAG,CAAC,EACrD,CAAC,EACA,CAAC,cAEThC,KAAA,WACEwB,OAAO,CAAEA,CAAA,GAAMlB,QAAQ,CAAC,uBAAuB,CAAE,CACjDyB,SAAS,CAAC,kGAAkG,CAAAC,QAAA,eAE5G9B,IAAA,CAACT,gBAAgB,EAACsC,SAAS,CAAC,8BAA8B,CAAE,CAAC,cAC7D/B,KAAA,QAAK+B,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB9B,IAAA,OAAI6B,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CAAC,4CAAO,CAAI,CAAC,cAC1D9B,IAAA,MAAG6B,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,2EAAa,CAAG,CAAC,EACrD,CAAC,EACA,CAAC,EACN,CAAC,EACI,CAAC,EACV,CAAC,CAEV,CAAC,CAED,cAAe,CAAA7B,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}