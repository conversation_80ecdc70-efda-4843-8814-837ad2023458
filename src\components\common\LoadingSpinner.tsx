import React from 'react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  color?: 'primary' | 'white' | 'gray';
  text?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'md', 
  color = 'primary',
  text = 'جاري التحميل...'
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  const colorClasses = {
    primary: 'border-primary-600',
    white: 'border-white',
    gray: 'border-gray-600'
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <div className="relative">
        <div 
          className={`
            ${sizeClasses[size]} 
            border-4 
            ${colorClasses[color]} 
            border-t-transparent 
            rounded-full 
            animate-spin
          `}
        />
        <div 
          className={`
            absolute 
            inset-0 
            ${sizeClasses[size]} 
            border-4 
            border-transparent 
            border-t-${color === 'primary' ? 'primary-300' : color === 'white' ? 'gray-300' : 'gray-400'} 
            rounded-full 
            animate-spin 
            animation-delay-150
          `}
        />
      </div>
      
      {text && (
        <p className={`
          mt-4 
          text-sm 
          font-medium 
          ${color === 'white' ? 'text-white' : 'text-gray-600'}
          animate-pulse
        `}>
          {text}
        </p>
      )}
    </div>
  );
};

export default LoadingSpinner;
