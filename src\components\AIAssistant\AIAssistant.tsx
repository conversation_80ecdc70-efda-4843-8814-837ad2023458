import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ChatBubbleLeftRightIcon,
  XMarkIcon,
  PaperAirplaneIcon,
  SparklesIcon,
  MinusIcon
} from '@heroicons/react/24/outline';

// Types
import { ChatMessage } from '../../types';

const AIAssistant: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      type: 'assistant',
      content: 'مرحباً! أنا المساعد الذكي لمنصة ALaa Abd Hamied. كيف يمكنني مساعدتك اليوم؟',
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const quickReplies = [
    'كيف أشاهد الكورسات؟',
    'كيف أؤدي الاختبارات؟',
    'كيف أحصل على الشهادة؟',
    'مشكلة في تشغيل الفيديو',
    'تغيير كلمة المرور',
    'التواصل مع الدعم'
  ];

  const getAIResponse = (userMessage: string): string => {
    const message = userMessage.toLowerCase();
    
    if (message.includes('كورس') || message.includes('مشاهدة')) {
      return 'لمشاهدة الكورسات، اذهب إلى قسم "كورساتي" من القائمة الرئيسية. ستجد جميع الكورسات المتاحة لك مع إمكانية متابعة التقدم.';
    }
    
    if (message.includes('اختبار') || message.includes('امتحان')) {
      return 'يمكنك الوصول للاختبارات من داخل كل كورس. بعد مشاهدة الفيديوهات المطلوبة، ستظهر لك الاختبارات المتاحة. تأكد من قراءة التعليمات قبل البدء.';
    }
    
    if (message.includes('شهادة')) {
      return 'للحصول على الشهادة، يجب إكمال جميع فيديوهات الكورس واجتياز الاختبارات بنجاح. ستظهر الشهادة تلقائياً في قسم "شهاداتي".';
    }
    
    if (message.includes('فيديو') || message.includes('تشغيل')) {
      return 'إذا واجهت مشكلة في تشغيل الفيديو، تأكد من اتصالك بالإنترنت وحاول تحديث الصفحة. إذا استمرت المشكلة، جرب متصفح آخر.';
    }
    
    if (message.includes('مرور') || message.includes('كلمة')) {
      return 'لا يمكن تغيير كلمة المرور للطلاب حيث يتم الدخول بكود الوصول. إذا نسيت الكود، تواصل مع المدير للحصول على كود جديد.';
    }
    
    if (message.includes('دعم') || message.includes('مساعدة') || message.includes('مشكلة')) {
      return 'يمكنك التواصل مع فريق الدعم من خلال النقر على "اتصل بنا" في أسفل الصفحة، أو إرسال رسالة مباشرة للمدير.';
    }
    
    if (message.includes('مرحبا') || message.includes('السلام') || message.includes('أهلا')) {
      return 'أهلاً وسهلاً بك! أنا هنا لمساعدتك في أي استفسار حول المنصة. ما الذي تحتاج مساعدة فيه؟';
    }
    
    if (message.includes('شكرا') || message.includes('شكراً')) {
      return 'العفو! أتمنى أن أكون قد ساعدتك. لا تتردد في سؤالي عن أي شيء آخر.';
    }
    
    return 'أعتذر، لم أفهم سؤالك بوضوح. يمكنك تجربة إحدى الأسئلة الشائعة أدناه، أو إعادة صياغة سؤالك بطريقة أخرى.';
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsTyping(true);

    // Simulate AI thinking time
    setTimeout(() => {
      const aiResponse: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: getAIResponse(inputMessage),
        timestamp: new Date()
      };

      setMessages(prev => [...prev, aiResponse]);
      setIsTyping(false);
    }, 1000 + Math.random() * 1000);
  };

  const handleQuickReply = (reply: string) => {
    setInputMessage(reply);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <>
      {/* Chat Button */}
      <AnimatePresence>
        {!isOpen && (
          <motion.button
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => setIsOpen(true)}
            className="fixed bottom-6 left-6 w-14 h-14 bg-gradient-to-r from-primary-600 to-primary-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center z-50"
          >
            <ChatBubbleLeftRightIcon className="w-6 h-6" />
            <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full animate-pulse" />
          </motion.button>
        )}
      </AnimatePresence>

      {/* Chat Window */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 100, scale: 0.9 }}
            animate={{ 
              opacity: 1, 
              y: 0, 
              scale: 1,
              height: isMinimized ? 60 : 500
            }}
            exit={{ opacity: 0, y: 100, scale: 0.9 }}
            className="fixed bottom-6 left-6 w-80 bg-white rounded-xl shadow-2xl border border-gray-200 z-50 overflow-hidden"
          >
            {/* Header */}
            <div className="bg-gradient-to-r from-primary-600 to-primary-700 text-white p-4 flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center ml-3">
                  <SparklesIcon className="w-4 h-4" />
                </div>
                <div>
                  <h3 className="font-semibold text-sm">المساعد الذكي</h3>
                  <p className="text-xs opacity-90">متاح الآن</p>
                </div>
              </div>
              <div className="flex items-center space-x-2 space-x-reverse">
                <button
                  onClick={() => setIsMinimized(!isMinimized)}
                  className="p-1 hover:bg-white hover:bg-opacity-20 rounded"
                >
                  <MinusIcon className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setIsOpen(false)}
                  className="p-1 hover:bg-white hover:bg-opacity-20 rounded"
                >
                  <XMarkIcon className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Chat Content */}
            {!isMinimized && (
              <>
                {/* Messages */}
                <div className="h-80 overflow-y-auto p-4 space-y-4">
                  {messages.map((message) => (
                    <motion.div
                      key={message.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className={`flex ${message.type === 'user' ? 'justify-start' : 'justify-end'}`}
                    >
                      <div className={`
                        max-w-xs px-4 py-2 rounded-lg text-sm
                        ${message.type === 'user'
                          ? 'bg-primary-600 text-white rounded-br-sm'
                          : 'bg-gray-100 text-gray-800 rounded-bl-sm'
                        }
                      `}>
                        {message.content}
                      </div>
                    </motion.div>
                  ))}

                  {/* Typing Indicator */}
                  {isTyping && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="flex justify-end"
                    >
                      <div className="bg-gray-100 px-4 py-2 rounded-lg rounded-bl-sm">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                        </div>
                      </div>
                    </motion.div>
                  )}

                  <div ref={messagesEndRef} />
                </div>

                {/* Quick Replies */}
                {messages.length <= 2 && (
                  <div className="px-4 pb-2">
                    <p className="text-xs text-gray-500 mb-2">أسئلة شائعة:</p>
                    <div className="flex flex-wrap gap-1">
                      {quickReplies.slice(0, 3).map((reply) => (
                        <button
                          key={reply}
                          onClick={() => handleQuickReply(reply)}
                          className="text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded-full transition-colors duration-200"
                        >
                          {reply}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Input */}
                <div className="p-4 border-t border-gray-200">
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <input
                      type="text"
                      value={inputMessage}
                      onChange={(e) => setInputMessage(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="اكتب رسالتك..."
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                      disabled={isTyping}
                    />
                    <button
                      onClick={handleSendMessage}
                      disabled={!inputMessage.trim() || isTyping}
                      className="p-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                    >
                      <PaperAirplaneIcon className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default AIAssistant;
