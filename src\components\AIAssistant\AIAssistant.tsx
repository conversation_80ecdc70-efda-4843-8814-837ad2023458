import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ChatBubbleLeftRightIcon,
  XMarkIcon,
  PaperAirplaneIcon,
  SparklesIcon,
  MinusIcon,
  StarIcon
} from '@heroicons/react/24/outline';
import { SparklesIcon as SparklesIconSolid } from '@heroicons/react/24/solid';

// Types
import { ChatMessage } from '../../types';

// Custom AI Assistant Icon Component with Animation
const AIAssistantIcon: React.FC<{ className?: string; animated?: boolean }> = ({
  className = "w-7 h-7",
  animated = false
}) => (
  <motion.svg
    className={className}
    viewBox="0 0 64 64"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    animate={animated ? {
      scale: [1, 1.05, 1],
    } : {}}
    transition={animated ? {
      duration: 2,
      repeat: Infinity,
      ease: "easeInOut"
    } : {}}
  >
    {/* Robot Head - Main */}
    <rect
      x="18"
      y="16"
      width="28"
      height="24"
      rx="8"
      fill="currentColor"
      stroke="rgba(255,255,255,0.4)"
      strokeWidth="1.5"
    />

    {/* Robot Head - Inner glow */}
    <rect
      x="20"
      y="18"
      width="24"
      height="20"
      rx="6"
      fill="rgba(255,255,255,0.1)"
    />

    {/* Robot Eyes - Outer glow */}
    <circle cx="26" cy="26" r="4" fill="rgba(255,255,255,0.3)" />
    <circle cx="38" cy="26" r="4" fill="rgba(255,255,255,0.3)" />

    {/* Robot Eyes - Main */}
    <circle cx="26" cy="26" r="3" fill="rgba(255,255,255,0.95)" />
    <circle cx="38" cy="26" r="3" fill="rgba(255,255,255,0.95)" />

    {/* Robot Eyes - Pupils with glow */}
    <motion.circle
      cx="26"
      cy="26"
      r="1.5"
      fill="rgba(59, 130, 246, 0.9)"
      animate={animated ? {
        fill: ["rgba(59, 130, 246, 0.9)", "rgba(34, 197, 94, 0.9)", "rgba(59, 130, 246, 0.9)"]
      } : {}}
      transition={animated ? {
        duration: 3,
        repeat: Infinity,
        ease: "easeInOut"
      } : {}}
    />
    <motion.circle
      cx="38"
      cy="26"
      r="1.5"
      fill="rgba(59, 130, 246, 0.9)"
      animate={animated ? {
        fill: ["rgba(59, 130, 246, 0.9)", "rgba(34, 197, 94, 0.9)", "rgba(59, 130, 246, 0.9)"]
      } : {}}
      transition={animated ? {
        duration: 3,
        repeat: Infinity,
        ease: "easeInOut"
      } : {}}
    />
    <circle cx="26" cy="25" r="0.5" fill="rgba(255,255,255,0.8)" />
    <circle cx="38" cy="25" r="0.5" fill="rgba(255,255,255,0.8)" />

    {/* Robot Mouth - Smile */}
    <path
      d="M 28 32 Q 32 35 36 32"
      stroke="rgba(255,255,255,0.8)"
      strokeWidth="2"
      strokeLinecap="round"
      fill="none"
    />

    {/* Robot Antennas */}
    <line x1="28" y1="16" x2="28" y2="10" stroke="rgba(255,255,255,0.7)" strokeWidth="2.5" strokeLinecap="round" />
    <line x1="36" y1="16" x2="36" y2="10" stroke="rgba(255,255,255,0.7)" strokeWidth="2.5" strokeLinecap="round" />

    {/* Antenna Tips with glow */}
    <circle cx="28" cy="8" r="2.5" fill="rgba(255,255,255,0.9)" />
    <circle cx="36" cy="8" r="2.5" fill="rgba(255,255,255,0.9)" />
    <motion.circle
      cx="28"
      cy="8"
      r="1.5"
      fill="rgba(34, 197, 94, 0.8)"
      animate={animated ? {
        opacity: [0.8, 1, 0.8],
        scale: [1, 1.1, 1]
      } : {}}
      transition={animated ? {
        duration: 1.5,
        repeat: Infinity,
        ease: "easeInOut"
      } : {}}
    />
    <motion.circle
      cx="36"
      cy="8"
      r="1.5"
      fill="rgba(34, 197, 94, 0.8)"
      animate={animated ? {
        opacity: [0.8, 1, 0.8],
        scale: [1, 1.1, 1]
      } : {}}
      transition={animated ? {
        duration: 1.5,
        repeat: Infinity,
        ease: "easeInOut",
        delay: 0.3
      } : {}}
    />

    {/* Robot Body */}
    <rect
      x="22"
      y="40"
      width="20"
      height="16"
      rx="4"
      fill="currentColor"
      stroke="rgba(255,255,255,0.3)"
      strokeWidth="1.5"
    />

    {/* Body Inner glow */}
    <rect
      x="24"
      y="42"
      width="16"
      height="12"
      rx="3"
      fill="rgba(255,255,255,0.1)"
    />

    {/* Robot Arms */}
    <rect x="14" y="44" width="8" height="6" rx="3" fill="currentColor" stroke="rgba(255,255,255,0.2)" strokeWidth="1" />
    <rect x="42" y="44" width="8" height="6" rx="3" fill="currentColor" stroke="rgba(255,255,255,0.2)" strokeWidth="1" />

    {/* Body Details */}
    <circle cx="32" cy="46" r="1.5" fill="rgba(255,255,255,0.6)" />
    <rect x="29" y="50" width="6" height="1" rx="0.5" fill="rgba(255,255,255,0.5)" />
    <rect x="30" y="52" width="4" height="1" rx="0.5" fill="rgba(255,255,255,0.4)" />

    {/* Decorative circuits */}
    <path d="M 24 20 L 26 20 L 26 22" stroke="rgba(255,255,255,0.3)" strokeWidth="1" fill="none" />
    <path d="M 40 20 L 38 20 L 38 22" stroke="rgba(255,255,255,0.3)" strokeWidth="1" fill="none" />
  </motion.svg>
);



interface AIAssistantProps {
  context?: 'login' | 'student' | 'admin';
}

const AIAssistant: React.FC<AIAssistantProps> = ({ context = 'student' }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);

  const getWelcomeMessage = () => {
    switch (context) {
      case 'login':
        return 'مرحباً! أنا المساعد الذكي لمنصة ALaa Abd Hamied. هل تحتاج مساعدة في تسجيل الدخول أو لديك أسئلة حول المنصة؟';
      case 'admin':
        return 'مرحباً أيها المدير! أنا هنا لمساعدتك في إدارة المنصة والإجابة على أي استفسارات تقنية.';
      default:
        return 'مرحباً عزيزي الطالب! أنا المساعد الذكي لمنصة ALaa Abd Hamied. كيف يمكنني مساعدتك في رحلتك التعليمية اليوم؟';
    }
  };

  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      type: 'assistant',
      content: getWelcomeMessage(),
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const getQuickReplies = () => {
    switch (context) {
      case 'login':
        return [
          'كيف أسجل الدخول كطالب؟',
          'نسيت كود الوصول',
          'مشكلة في تسجيل الدخول',
          'ما هي متطلبات النظام؟',
          'كيف أحصل على حساب؟',
          'التواصل مع الدعم'
        ];
      case 'admin':
        return [
          'كيف أضيف كورس جديد؟',
          'إدارة الطلاب',
          'إنشاء اختبارات',
          'تقارير الأداء',
          'إعدادات النظام',
          'النسخ الاحتياطي'
        ];
      default:
        return [
          'كيف أشاهد الكورسات؟',
          'كيف أؤدي الاختبارات؟',
          'كيف أحصل على الشهادة؟',
          'مشكلة في تشغيل الفيديو',
          'عرض تقدمي',
          'التواصل مع الدعم'
        ];
    }
  };

  const quickReplies = getQuickReplies();

  const getAIResponse = (userMessage: string): string => {
    const message = userMessage.toLowerCase();

    // Login context responses
    if (context === 'login') {
      if (message.includes('دخول') || message.includes('تسجيل')) {
        return 'للطلاب: استخدم كود الوصول الخاص بك في خانة "كود الوصول". للمدراء: استخدم البريد الإلكتروني وكلمة المرور. تأكد من اختيار النوع الصحيح من أعلى الصفحة.';
      }
      if (message.includes('كود') || message.includes('نسيت')) {
        return 'إذا نسيت كود الوصول، تواصل مع المدير أو فريق الدعم للحصول على كود جديد. كود الوصول يتكون من أرقام وحروف ويُعطى لك عند التسجيل.';
      }
      if (message.includes('حساب') || message.includes('تسجيل جديد')) {
        return 'للحصول على حساب جديد، تواصل مع إدارة المنصة. سيتم إنشاء حساب لك وإرسال كود الوصول الخاص بك.';
      }
      if (message.includes('متطلبات') || message.includes('نظام')) {
        return 'المنصة تعمل على جميع المتصفحات الحديثة (Chrome, Firefox, Safari, Edge). تحتاج اتصال إنترنت مستقر لمشاهدة الفيديوهات.';
      }
    }

    if (message.includes('كورس') || message.includes('مشاهدة')) {
      return context === 'login'
        ? 'بعد تسجيل الدخول، ستجد جميع الكورسات المتاحة لك في لوحة التحكم الرئيسية.'
        : 'لمشاهدة الكورسات، اذهب إلى قسم "كورساتي" من القائمة الرئيسية. ستجد جميع الكورسات المتاحة لك مع إمكانية متابعة التقدم.';
    }
    
    if (message.includes('اختبار') || message.includes('امتحان')) {
      return 'يمكنك الوصول للاختبارات من داخل كل كورس. بعد مشاهدة الفيديوهات المطلوبة، ستظهر لك الاختبارات المتاحة. تأكد من قراءة التعليمات قبل البدء.';
    }
    
    if (message.includes('شهادة')) {
      return 'للحصول على الشهادة، يجب إكمال جميع فيديوهات الكورس واجتياز الاختبارات بنجاح. ستظهر الشهادة تلقائياً في قسم "شهاداتي".';
    }
    
    if (message.includes('فيديو') || message.includes('تشغيل')) {
      return 'إذا واجهت مشكلة في تشغيل الفيديو، تأكد من اتصالك بالإنترنت وحاول تحديث الصفحة. إذا استمرت المشكلة، جرب متصفح آخر.';
    }
    
    if (message.includes('مرور') || message.includes('كلمة')) {
      return 'لا يمكن تغيير كلمة المرور للطلاب حيث يتم الدخول بكود الوصول. إذا نسيت الكود، تواصل مع المدير للحصول على كود جديد.';
    }
    
    if (message.includes('دعم') || message.includes('مساعدة') || message.includes('مشكلة')) {
      return 'يمكنك التواصل مع فريق الدعم من خلال النقر على "اتصل بنا" في أسفل الصفحة، أو إرسال رسالة مباشرة للمدير.';
    }
    
    if (message.includes('مرحبا') || message.includes('السلام') || message.includes('أهلا')) {
      return 'أهلاً وسهلاً بك! أنا هنا لمساعدتك في أي استفسار حول المنصة. ما الذي تحتاج مساعدة فيه؟';
    }
    
    if (message.includes('شكرا') || message.includes('شكراً')) {
      return 'العفو! أتمنى أن أكون قد ساعدتك. لا تتردد في سؤالي عن أي شيء آخر.';
    }
    
    return 'أعتذر، لم أفهم سؤالك بوضوح. يمكنك تجربة إحدى الأسئلة الشائعة أدناه، أو إعادة صياغة سؤالك بطريقة أخرى.';
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsTyping(true);

    // Simulate AI thinking time
    setTimeout(() => {
      const aiResponse: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: getAIResponse(inputMessage),
        timestamp: new Date()
      };

      setMessages(prev => [...prev, aiResponse]);
      setIsTyping(false);
    }, 1000 + Math.random() * 1000);
  };

  const handleQuickReply = (reply: string) => {
    setInputMessage(reply);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <>
      {/* Chat Button */}
      <AnimatePresence>
        {!isOpen && (
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
            className="fixed bottom-6 left-6 z-50"
          >
            <motion.button
              whileHover={{ scale: 1.1, rotate: 5 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => setIsOpen(true)}
              className="relative w-16 h-16 bg-gradient-to-br from-yellow-400 via-yellow-500 to-amber-600 text-white rounded-full shadow-2xl hover:shadow-yellow-500/25 transition-all duration-300 flex items-center justify-center group overflow-hidden"
            >
              {/* Background glow effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-yellow-300 to-amber-500 rounded-full opacity-0 group-hover:opacity-20 transition-opacity duration-300" />

              {/* Sparkle animation background */}
              <div className="absolute inset-0 rounded-full">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                  className="absolute inset-2 border-2 border-yellow-300/30 rounded-full"
                />
                <motion.div
                  animate={{ rotate: -360 }}
                  transition={{ duration: 12, repeat: Infinity, ease: "linear" }}
                  className="absolute inset-1 border border-yellow-200/20 rounded-full"
                />
              </div>

              {/* Main icon */}
              <div className="relative z-10 flex items-center justify-center">
                <AIAssistantIcon className="w-8 h-8 text-white drop-shadow-lg" animated={true} />
              </div>

              {/* Floating sparkles */}
              <motion.div
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.7, 1, 0.7]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-300 rounded-full shadow-lg"
              />

              <motion.div
                animate={{
                  scale: [1, 1.3, 1],
                  opacity: [0.5, 1, 0.5]
                }}
                transition={{
                  duration: 2.5,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 0.5
                }}
                className="absolute -bottom-1 -left-1 w-2 h-2 bg-amber-300 rounded-full shadow-lg"
              />

              <motion.div
                animate={{
                  scale: [1, 1.1, 1],
                  opacity: [0.6, 1, 0.6]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 1
                }}
                className="absolute top-1 -left-2 w-1.5 h-1.5 bg-yellow-200 rounded-full shadow-lg"
              />

              {/* Status indicator */}
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full animate-pulse border-2 border-white shadow-lg" />
            </motion.button>

            {/* Tooltip */}
            <motion.div
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 2 }}
              className="absolute right-20 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-gray-900 to-gray-800 text-white px-4 py-2 rounded-lg text-sm whitespace-nowrap shadow-xl border border-gray-700"
            >
              <div className="flex items-center space-x-2 space-x-reverse">
                <AIAssistantIcon className="w-4 h-4 text-yellow-400" />
                <span>المساعد الذكي - اضغط للمساعدة</span>
              </div>
              <div className="absolute left-0 top-1/2 transform -translate-y-1/2 translate-x-1 w-2 h-2 bg-gray-900 rotate-45" />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Chat Window */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 100, scale: 0.9 }}
            animate={{ 
              opacity: 1, 
              y: 0, 
              scale: 1,
              height: isMinimized ? 60 : 500
            }}
            exit={{ opacity: 0, y: 100, scale: 0.9 }}
            className="fixed bottom-6 left-6 w-80 bg-white rounded-xl shadow-2xl border border-gray-200 z-50 overflow-hidden"
          >
            {/* Header */}
            <div className="bg-gradient-to-r from-yellow-500 via-amber-500 to-yellow-600 text-white p-4 flex items-center justify-between relative overflow-hidden">
              {/* Background pattern */}
              <div className="absolute inset-0 opacity-10">
                <div className="absolute top-2 left-4 w-2 h-2 bg-white rounded-full animate-pulse" />
                <div className="absolute top-6 right-8 w-1 h-1 bg-white rounded-full animate-pulse" style={{ animationDelay: '0.5s' }} />
                <div className="absolute bottom-3 left-12 w-1.5 h-1.5 bg-white rounded-full animate-pulse" style={{ animationDelay: '1s' }} />
              </div>

              <div className="flex items-center relative z-10">
                <div className="w-10 h-10 bg-gradient-to-br from-white/30 to-white/10 rounded-full flex items-center justify-center ml-3 backdrop-blur-sm border border-white/20">
                  <AIAssistantIcon className="w-6 h-6 text-white drop-shadow-lg" />
                </div>
                <div>
                  <h3 className="font-bold text-sm flex items-center">
                    المساعد الذكي
                    <StarIcon className="w-3 h-3 mr-1 text-yellow-200" />
                  </h3>
                  <p className="text-xs opacity-90 flex items-center">
                    <div className="w-2 h-2 bg-green-400 rounded-full mr-1 animate-pulse" />
                    متاح الآن للمساعدة
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2 space-x-reverse">
                <button
                  onClick={() => setIsMinimized(!isMinimized)}
                  className="p-1 hover:bg-white hover:bg-opacity-20 rounded"
                >
                  <MinusIcon className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setIsOpen(false)}
                  className="p-1 hover:bg-white hover:bg-opacity-20 rounded"
                >
                  <XMarkIcon className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Chat Content */}
            {!isMinimized && (
              <>
                {/* Messages */}
                <div className="h-80 overflow-y-auto p-4 space-y-4">
                  {messages.map((message) => (
                    <motion.div
                      key={message.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className={`flex ${message.type === 'user' ? 'justify-start' : 'justify-end'}`}
                    >
                      <div className={`
                        max-w-xs px-4 py-3 rounded-lg text-sm shadow-sm
                        ${message.type === 'user'
                          ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-br-sm'
                          : 'bg-gradient-to-r from-yellow-50 to-amber-50 text-gray-800 rounded-bl-sm border border-yellow-200/50'
                        }
                      `}>
                        {message.content}
                      </div>
                    </motion.div>
                  ))}

                  {/* Typing Indicator */}
                  {isTyping && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="flex justify-end"
                    >
                      <div className="bg-gradient-to-r from-yellow-50 to-amber-50 px-4 py-3 rounded-lg rounded-bl-sm border border-yellow-200/50 shadow-sm">
                        <div className="flex space-x-1 items-center">
                          <AIAssistantIcon className="w-4 h-4 text-yellow-600 mr-2" />
                          <div className="w-2 h-2 bg-yellow-500 rounded-full animate-bounce" />
                          <div className="w-2 h-2 bg-amber-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                          <div className="w-2 h-2 bg-yellow-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                          <span className="text-xs text-gray-600 mr-2">يكتب...</span>
                        </div>
                      </div>
                    </motion.div>
                  )}

                  <div ref={messagesEndRef} />
                </div>

                {/* Quick Replies */}
                {messages.length <= 2 && (
                  <div className="px-4 pb-2">
                    <p className="text-xs text-gray-500 mb-2">أسئلة شائعة:</p>
                    <div className="flex flex-wrap gap-1">
                      {quickReplies.slice(0, 3).map((reply) => (
                        <button
                          key={reply}
                          onClick={() => handleQuickReply(reply)}
                          className="text-xs bg-gradient-to-r from-yellow-100 to-amber-100 hover:from-yellow-200 hover:to-amber-200 text-gray-700 px-3 py-1.5 rounded-full transition-all duration-200 border border-yellow-200/50 hover:border-yellow-300 shadow-sm hover:shadow-md"
                        >
                          {reply}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Input */}
                <div className="p-4 border-t border-gray-200">
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <input
                      type="text"
                      value={inputMessage}
                      onChange={(e) => setInputMessage(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="اكتب رسالتك..."
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                      disabled={isTyping}
                    />
                    <motion.button
                      onClick={handleSendMessage}
                      disabled={!inputMessage.trim() || isTyping}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="p-2.5 bg-gradient-to-r from-yellow-500 to-amber-600 text-white rounded-lg hover:from-yellow-600 hover:to-amber-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl disabled:hover:scale-100"
                    >
                      <PaperAirplaneIcon className="w-4 h-4" />
                    </motion.button>
                  </div>
                </div>
              </>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default AIAssistant;
