{"ast": null, "code": "import { cubicBezier } from './cubic-bezier.mjs';\nconst easeIn = cubicBezier(0.42, 0, 1, 1);\nconst easeOut = cubicBezier(0, 0, 0.58, 1);\nconst easeInOut = cubicBezier(0.42, 0, 0.58, 1);\nexport { easeIn, easeInOut, easeOut };", "map": {"version": 3, "names": ["cubicBezier", "easeIn", "easeOut", "easeInOut"], "sources": ["C:/Users/<USER>/Desktop/مشروع/node_modules/framer-motion/dist/es/easing/ease.mjs"], "sourcesContent": ["import { cubicBezier } from './cubic-bezier.mjs';\n\nconst easeIn = cubicBezier(0.42, 0, 1, 1);\nconst easeOut = cubicBezier(0, 0, 0.58, 1);\nconst easeInOut = cubicBezier(0.42, 0, 0.58, 1);\n\nexport { easeIn, easeInOut, easeOut };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAEhD,MAAMC,MAAM,GAAGD,WAAW,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACzC,MAAME,OAAO,GAAGF,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;AAC1C,MAAMG,SAAS,GAAGH,WAAW,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;AAE/C,SAASC,MAAM,EAAEE,SAAS,EAAED,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}