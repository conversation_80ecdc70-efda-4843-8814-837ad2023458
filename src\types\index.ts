// User Types
export interface User {
  id: string;
  email: string;
  role: 'admin' | 'student';
  name?: string;
  avatar?: string;
  createdAt: Date;
}

export interface Student extends User {
  accessCode: string;
  enrolledCourses: string[];
  completedCourses: string[];
  certificates: string[];
}

export interface Admin extends User {
  permissions: string[];
}

// Course Types
export interface Course {
  id: string;
  title: string;
  description: string;
  categoryId: string;
  instructorId: string;
  thumbnailUrl?: string;
  videos: Video[];
  pdfs: PDF[];
  quizzes: Quiz[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Category {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  isActive: boolean;
  createdAt: Date;
}

export interface Video {
  id: string;
  courseId: string;
  title: string;
  description?: string;
  videoUrl: string;
  thumbnailUrl?: string;
  duration?: number;
  orderIndex: number;
  isActive: boolean;
  createdAt: Date;
}

export interface PDF {
  id: string;
  courseId: string;
  title: string;
  description?: string;
  fileUrl: string;
  fileSize: number;
  orderIndex: number;
  isActive: boolean;
  createdAt: Date;
}

// Quiz Types
export interface Quiz {
  id: string;
  courseId: string;
  title: string;
  description?: string;
  questions: QuizQuestion[];
  passingScore: number;
  timeLimit?: number; // in minutes
  attempts: number;
  isActive: boolean;
  createdAt: Date;
}

export interface QuizQuestion {
  id: string;
  question: string;
  type: 'multiple-choice' | 'true-false' | 'short-answer';
  options?: string[];
  correctAnswer: string | number;
  points: number;
  explanation?: string;
}

export interface QuizAttempt {
  id: string;
  studentId: string;
  quizId: string;
  answers: QuizAnswer[];
  score: number;
  passed: boolean;
  startedAt: Date;
  completedAt?: Date;
}

export interface QuizAnswer {
  questionId: string;
  answer: string | number;
  isCorrect: boolean;
  points: number;
}

// Certificate Types
export interface Certificate {
  id: string;
  studentId: string;
  courseId: string;
  templateUrl: string;
  certificateUrl: string;
  issuedAt: Date;
  verificationCode: string;
}

// AI Assistant Types
export interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

export interface AIAssistantConfig {
  isEnabled: boolean;
  welcomeMessage: string;
  fallbackMessage: string;
  supportedLanguages: string[];
}

// Notification Types
export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  isRead: boolean;
  createdAt: Date;
}

// Analytics Types
export interface CourseAnalytics {
  courseId: string;
  totalStudents: number;
  completionRate: number;
  averageScore: number;
  totalWatchTime: number;
  popularVideos: Video[];
}

export interface StudentProgress {
  studentId: string;
  courseId: string;
  completedVideos: string[];
  completedQuizzes: string[];
  overallProgress: number;
  lastAccessedAt: Date;
}
