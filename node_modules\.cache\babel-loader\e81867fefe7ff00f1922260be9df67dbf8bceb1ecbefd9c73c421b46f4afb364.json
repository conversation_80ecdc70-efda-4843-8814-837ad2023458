{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0634\\u0631\\u0648\\u0639\\\\src\\\\components\\\\Admin\\\\CoursesManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { PlusIcon, PencilIcon, TrashIcon, EyeIcon, AcademicCapIcon, PlayIcon, DocumentIcon, ClipboardDocumentListIcon } from '@heroicons/react/24/outline';\n\n// Types\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CoursesManagement = ({\n  onBack\n}) => {\n  _s();\n  const [courses, setCourses] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n\n  // Mock data for demonstration\n  const mockCourses = [{\n    id: '1',\n    title: 'أساسيات البرمجة',\n    description: 'تعلم أساسيات البرمجة من الصفر',\n    categoryId: 'programming',\n    instructorId: 'admin',\n    videos: [],\n    pdfs: [],\n    quizzes: [],\n    isActive: true,\n    createdAt: new Date(),\n    updatedAt: new Date()\n  }, {\n    id: '2',\n    title: 'تطوير المواقع',\n    description: 'تعلم تطوير المواقع الحديثة',\n    categoryId: 'web',\n    instructorId: 'admin',\n    videos: [],\n    pdfs: [],\n    quizzes: [],\n    isActive: true,\n    createdAt: new Date(),\n    updatedAt: new Date()\n  }];\n  React.useEffect(() => {\n    setCourses(mockCourses);\n  }, []);\n  const filteredCourses = courses.filter(course => {\n    const matchesSearch = course.title.toLowerCase().includes(searchTerm.toLowerCase()) || course.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = selectedCategory === 'all' || course.categoryId === selectedCategory;\n    return matchesSearch && matchesCategory;\n  });\n  const handleAddCourse = () => {\n    // TODO: Implement add course functionality\n    console.log('Add course');\n  };\n  const handleEditCourse = courseId => {\n    // TODO: Implement edit course functionality\n    console.log('Edit course:', courseId);\n  };\n  const handleDeleteCourse = courseId => {\n    // TODO: Implement delete course functionality\n    console.log('Delete course:', courseId);\n  };\n  const handleViewCourse = courseId => {\n    // TODO: Implement view course functionality\n    console.log('View course:', courseId);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4 space-x-reverse\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onBack,\n          className: \"p-2 text-gray-600 hover:text-gray-900 transition-colors\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M15 19l-7-7 7-7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0648\\u062A\\u0646\\u0638\\u064A\\u0645 \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A \\u0627\\u0644\\u062A\\u0639\\u0644\\u064A\\u0645\\u064A\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleAddCourse,\n        className: \"flex items-center space-x-2 space-x-reverse bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n        children: [/*#__PURE__*/_jsxDEV(PlusIcon, {\n          className: \"w-5 h-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0643\\u0648\\u0631\\u0633 \\u062C\\u062F\\u064A\\u062F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"\\u0627\\u0644\\u0628\\u062D\\u062B \\u0641\\u064A \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            placeholder: \"\\u0627\\u0628\\u062D\\u062B \\u0639\\u0646 \\u0643\\u0648\\u0631\\u0633...\",\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"\\u0627\\u0644\\u062A\\u0635\\u0646\\u064A\\u0641\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedCategory,\n            onChange: e => setSelectedCategory(e.target.value),\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"all\",\n              children: \"\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u062A\\u0635\\u0646\\u064A\\u0641\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"programming\",\n              children: \"\\u0627\\u0644\\u0628\\u0631\\u0645\\u062C\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"web\",\n              children: \"\\u062A\\u0637\\u0648\\u064A\\u0631 \\u0627\\u0644\\u0645\\u0648\\u0627\\u0642\\u0639\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"mobile\",\n              children: \"\\u062A\\u0637\\u0648\\u064A\\u0631 \\u0627\\u0644\\u062A\\u0637\\u0628\\u064A\\u0642\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n      children: filteredCourses.map((course, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: index * 0.1\n        },\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start justify-between mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3 space-x-reverse\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-2 bg-blue-100 rounded-lg\",\n                children: /*#__PURE__*/_jsxDEV(AcademicCapIcon, {\n                  className: \"w-6 h-6 text-blue-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-semibold text-gray-900\",\n                  children: course.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: course.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `px-2 py-1 text-xs rounded-full ${course.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n              children: course.isActive ? 'نشط' : 'غير نشط'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4 space-x-reverse text-sm text-gray-600 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-1 space-x-reverse\",\n              children: [/*#__PURE__*/_jsxDEV(PlayIcon, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [course.videos.length, \" \\u0641\\u064A\\u062F\\u064A\\u0648\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-1 space-x-reverse\",\n              children: [/*#__PURE__*/_jsxDEV(DocumentIcon, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [course.pdfs.length, \" \\u0645\\u0644\\u0641\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-1 space-x-reverse\",\n              children: [/*#__PURE__*/_jsxDEV(ClipboardDocumentListIcon, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [course.quizzes.length, \" \\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between pt-4 border-t border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 space-x-reverse\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleViewCourse(course.id),\n                className: \"p-2 text-gray-600 hover:text-blue-600 transition-colors\",\n                title: \"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\",\n                children: /*#__PURE__*/_jsxDEV(EyeIcon, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleEditCourse(course.id),\n                className: \"p-2 text-gray-600 hover:text-green-600 transition-colors\",\n                title: \"\\u062A\\u0639\\u062F\\u064A\\u0644 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\",\n                children: /*#__PURE__*/_jsxDEV(PencilIcon, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleDeleteCourse(course.id),\n                className: \"p-2 text-gray-600 hover:text-red-600 transition-colors\",\n                title: \"\\u062D\\u0630\\u0641 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\",\n                children: /*#__PURE__*/_jsxDEV(TrashIcon, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs text-gray-500\",\n              children: new Date(course.createdAt).toLocaleDateString('ar-SA')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 13\n        }, this)\n      }, course.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this), filteredCourses.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(AcademicCapIcon, {\n        className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-2\",\n        children: \"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0643\\u0648\\u0631\\u0633\\u0627\\u062A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: \"\\u0644\\u0645 \\u064A\\u062A\\u0645 \\u0627\\u0644\\u0639\\u062B\\u0648\\u0631 \\u0639\\u0644\\u0649 \\u0623\\u064A \\u0643\\u0648\\u0631\\u0633\\u0627\\u062A \\u062A\\u0637\\u0627\\u0628\\u0642 \\u0627\\u0644\\u0628\\u062D\\u062B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 88,\n    columnNumber: 5\n  }, this);\n};\n_s(CoursesManagement, \"gLZEijE0wnh3G43bi6zwA5RWulM=\");\n_c = CoursesManagement;\nexport default CoursesManagement;\nvar _c;\n$RefreshReg$(_c, \"CoursesManagement\");", "map": {"version": 3, "names": ["React", "useState", "motion", "PlusIcon", "PencilIcon", "TrashIcon", "EyeIcon", "AcademicCapIcon", "PlayIcon", "DocumentIcon", "ClipboardDocumentListIcon", "jsxDEV", "_jsxDEV", "CoursesManagement", "onBack", "_s", "courses", "setCourses", "searchTerm", "setSearchTerm", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "mockCourses", "id", "title", "description", "categoryId", "instructorId", "videos", "pdfs", "quizzes", "isActive", "createdAt", "Date", "updatedAt", "useEffect", "filteredCourses", "filter", "course", "matchesSearch", "toLowerCase", "includes", "matchesCategory", "handleAddCourse", "console", "log", "handleEditCourse", "courseId", "handleDeleteCourse", "handleViewCourse", "className", "children", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "value", "onChange", "e", "target", "placeholder", "map", "index", "div", "initial", "opacity", "y", "animate", "transition", "delay", "length", "toLocaleDateString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Admin/CoursesManagement.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  PlusIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n  AcademicCapIcon,\n  PlayIcon,\n  DocumentIcon,\n  ClipboardDocumentListIcon\n} from '@heroicons/react/24/outline';\n\n// Types\nimport { Course } from '../../types';\n\ninterface CoursesManagementProps {\n  onBack?: () => void;\n}\n\nconst CoursesManagement: React.FC<CoursesManagementProps> = ({ onBack }) => {\n  const [courses, setCourses] = useState<Course[]>([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n\n  // Mock data for demonstration\n  const mockCourses: Course[] = [\n    {\n      id: '1',\n      title: 'أساسيات البرمجة',\n      description: 'تعلم أساسيات البرمجة من الصفر',\n      categoryId: 'programming',\n      instructorId: 'admin',\n      videos: [],\n      pdfs: [],\n      quizzes: [],\n      isActive: true,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    },\n    {\n      id: '2',\n      title: 'تطوير المواقع',\n      description: 'تعلم تطوير المواقع الحديثة',\n      categoryId: 'web',\n      instructorId: 'admin',\n      videos: [],\n      pdfs: [],\n      quizzes: [],\n      isActive: true,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    }\n  ];\n\n  React.useEffect(() => {\n    setCourses(mockCourses);\n  }, []);\n\n  const filteredCourses = courses.filter(course => {\n    const matchesSearch = course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         course.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = selectedCategory === 'all' || course.categoryId === selectedCategory;\n    return matchesSearch && matchesCategory;\n  });\n\n  const handleAddCourse = () => {\n    // TODO: Implement add course functionality\n    console.log('Add course');\n  };\n\n  const handleEditCourse = (courseId: string) => {\n    // TODO: Implement edit course functionality\n    console.log('Edit course:', courseId);\n  };\n\n  const handleDeleteCourse = (courseId: string) => {\n    // TODO: Implement delete course functionality\n    console.log('Delete course:', courseId);\n  };\n\n  const handleViewCourse = (courseId: string) => {\n    // TODO: Implement view course functionality\n    console.log('View course:', courseId);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4 space-x-reverse\">\n          <button\n            onClick={onBack}\n            className=\"p-2 text-gray-600 hover:text-gray-900 transition-colors\"\n          >\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n            </svg>\n          </button>\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">إدارة الكورسات</h1>\n            <p className=\"text-gray-600\">إدارة وتنظيم جميع الكورسات التعليمية</p>\n          </div>\n        </div>\n        <button\n          onClick={handleAddCourse}\n          className=\"flex items-center space-x-2 space-x-reverse bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n        >\n          <PlusIcon className=\"w-5 h-5\" />\n          <span>إضافة كورس جديد</span>\n        </button>\n      </div>\n\n      {/* Filters */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              البحث في الكورسات\n            </label>\n            <input\n              type=\"text\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              placeholder=\"ابحث عن كورس...\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            />\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              التصنيف\n            </label>\n            <select\n              value={selectedCategory}\n              onChange={(e) => setSelectedCategory(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              <option value=\"all\">جميع التصنيفات</option>\n              <option value=\"programming\">البرمجة</option>\n              <option value=\"web\">تطوير المواقع</option>\n              <option value=\"mobile\">تطوير التطبيقات</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* Courses Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {filteredCourses.map((course, index) => (\n          <motion.div\n            key={course.id}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: index * 0.1 }}\n            className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow\"\n          >\n            <div className=\"p-6\">\n              <div className=\"flex items-start justify-between mb-4\">\n                <div className=\"flex items-center space-x-3 space-x-reverse\">\n                  <div className=\"p-2 bg-blue-100 rounded-lg\">\n                    <AcademicCapIcon className=\"w-6 h-6 text-blue-600\" />\n                  </div>\n                  <div>\n                    <h3 className=\"font-semibold text-gray-900\">{course.title}</h3>\n                    <p className=\"text-sm text-gray-600\">{course.description}</p>\n                  </div>\n                </div>\n                <span className={`px-2 py-1 text-xs rounded-full ${\n                  course.isActive \n                    ? 'bg-green-100 text-green-800' \n                    : 'bg-red-100 text-red-800'\n                }`}>\n                  {course.isActive ? 'نشط' : 'غير نشط'}\n                </span>\n              </div>\n\n              <div className=\"flex items-center space-x-4 space-x-reverse text-sm text-gray-600 mb-4\">\n                <div className=\"flex items-center space-x-1 space-x-reverse\">\n                  <PlayIcon className=\"w-4 h-4\" />\n                  <span>{course.videos.length} فيديو</span>\n                </div>\n                <div className=\"flex items-center space-x-1 space-x-reverse\">\n                  <DocumentIcon className=\"w-4 h-4\" />\n                  <span>{course.pdfs.length} ملف</span>\n                </div>\n                <div className=\"flex items-center space-x-1 space-x-reverse\">\n                  <ClipboardDocumentListIcon className=\"w-4 h-4\" />\n                  <span>{course.quizzes.length} اختبار</span>\n                </div>\n              </div>\n\n              <div className=\"flex items-center justify-between pt-4 border-t border-gray-200\">\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <button\n                    onClick={() => handleViewCourse(course.id)}\n                    className=\"p-2 text-gray-600 hover:text-blue-600 transition-colors\"\n                    title=\"عرض الكورس\"\n                  >\n                    <EyeIcon className=\"w-4 h-4\" />\n                  </button>\n                  <button\n                    onClick={() => handleEditCourse(course.id)}\n                    className=\"p-2 text-gray-600 hover:text-green-600 transition-colors\"\n                    title=\"تعديل الكورس\"\n                  >\n                    <PencilIcon className=\"w-4 h-4\" />\n                  </button>\n                  <button\n                    onClick={() => handleDeleteCourse(course.id)}\n                    className=\"p-2 text-gray-600 hover:text-red-600 transition-colors\"\n                    title=\"حذف الكورس\"\n                  >\n                    <TrashIcon className=\"w-4 h-4\" />\n                  </button>\n                </div>\n                <span className=\"text-xs text-gray-500\">\n                  {new Date(course.createdAt).toLocaleDateString('ar-SA')}\n                </span>\n              </div>\n            </div>\n          </motion.div>\n        ))}\n      </div>\n\n      {filteredCourses.length === 0 && (\n        <div className=\"text-center py-12\">\n          <AcademicCapIcon className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">لا توجد كورسات</h3>\n          <p className=\"text-gray-600\">لم يتم العثور على أي كورسات تطابق البحث</p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default CoursesManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,QAAQ,EACRC,UAAU,EACVC,SAAS,EACTC,OAAO,EACPC,eAAe,EACfC,QAAQ,EACRC,YAAY,EACZC,yBAAyB,QACpB,6BAA6B;;AAEpC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAOA,MAAMC,iBAAmD,GAAGA,CAAC;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAC1E,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAW,EAAE,CAAC;EACpD,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAMqB,WAAqB,GAAG,CAC5B;IACEC,EAAE,EAAE,GAAG;IACPC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,+BAA+B;IAC5CC,UAAU,EAAE,aAAa;IACzBC,YAAY,EAAE,OAAO;IACrBC,MAAM,EAAE,EAAE;IACVC,IAAI,EAAE,EAAE;IACRC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;IACrBC,SAAS,EAAE,IAAID,IAAI,CAAC;EACtB,CAAC,EACD;IACEV,EAAE,EAAE,GAAG;IACPC,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE,4BAA4B;IACzCC,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE,OAAO;IACrBC,MAAM,EAAE,EAAE;IACVC,IAAI,EAAE,EAAE;IACRC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;IACrBC,SAAS,EAAE,IAAID,IAAI,CAAC;EACtB,CAAC,CACF;EAEDjC,KAAK,CAACmC,SAAS,CAAC,MAAM;IACpBlB,UAAU,CAACK,WAAW,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMc,eAAe,GAAGpB,OAAO,CAACqB,MAAM,CAACC,MAAM,IAAI;IAC/C,MAAMC,aAAa,GAAGD,MAAM,CAACd,KAAK,CAACgB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvB,UAAU,CAACsB,WAAW,CAAC,CAAC,CAAC,IAC9DF,MAAM,CAACb,WAAW,CAACe,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvB,UAAU,CAACsB,WAAW,CAAC,CAAC,CAAC;IACxF,MAAME,eAAe,GAAGtB,gBAAgB,KAAK,KAAK,IAAIkB,MAAM,CAACZ,UAAU,KAAKN,gBAAgB;IAC5F,OAAOmB,aAAa,IAAIG,eAAe;EACzC,CAAC,CAAC;EAEF,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACAC,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;EAC3B,CAAC;EAED,MAAMC,gBAAgB,GAAIC,QAAgB,IAAK;IAC7C;IACAH,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEE,QAAQ,CAAC;EACvC,CAAC;EAED,MAAMC,kBAAkB,GAAID,QAAgB,IAAK;IAC/C;IACAH,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEE,QAAQ,CAAC;EACzC,CAAC;EAED,MAAME,gBAAgB,GAAIF,QAAgB,IAAK;IAC7C;IACAH,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEE,QAAQ,CAAC;EACvC,CAAC;EAED,oBACEnC,OAAA;IAAKsC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBvC,OAAA;MAAKsC,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDvC,OAAA;QAAKsC,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAC1DvC,OAAA;UACEwC,OAAO,EAAEtC,MAAO;UAChBoC,SAAS,EAAC,yDAAyD;UAAAC,QAAA,eAEnEvC,OAAA;YAAKsC,SAAS,EAAC,SAAS;YAACG,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAJ,QAAA,eAC5EvC,OAAA;cAAM4C,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACTnD,OAAA;UAAAuC,QAAA,gBACEvC,OAAA;YAAIsC,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAc;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpEnD,OAAA;YAAGsC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAoC;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnD,OAAA;QACEwC,OAAO,EAAET,eAAgB;QACzBO,SAAS,EAAC,6HAA6H;QAAAC,QAAA,gBAEvIvC,OAAA,CAACT,QAAQ;UAAC+C,SAAS,EAAC;QAAS;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChCnD,OAAA;UAAAuC,QAAA,EAAM;QAAe;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNnD,OAAA;MAAKsC,SAAS,EAAC,mCAAmC;MAAAC,QAAA,eAChDvC,OAAA;QAAKsC,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDvC,OAAA;UAAAuC,QAAA,gBACEvC,OAAA;YAAOsC,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRnD,OAAA;YACEoD,IAAI,EAAC,MAAM;YACXC,KAAK,EAAE/C,UAAW;YAClBgD,QAAQ,EAAGC,CAAC,IAAKhD,aAAa,CAACgD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC/CI,WAAW,EAAC,mEAAiB;YAC7BnB,SAAS,EAAC;UAAwG;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNnD,OAAA;UAAAuC,QAAA,gBACEvC,OAAA;YAAOsC,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRnD,OAAA;YACEqD,KAAK,EAAE7C,gBAAiB;YACxB8C,QAAQ,EAAGC,CAAC,IAAK9C,mBAAmB,CAAC8C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YACrDf,SAAS,EAAC,wGAAwG;YAAAC,QAAA,gBAElHvC,OAAA;cAAQqD,KAAK,EAAC,KAAK;cAAAd,QAAA,EAAC;YAAc;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC3CnD,OAAA;cAAQqD,KAAK,EAAC,aAAa;cAAAd,QAAA,EAAC;YAAO;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5CnD,OAAA;cAAQqD,KAAK,EAAC,KAAK;cAAAd,QAAA,EAAC;YAAa;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1CnD,OAAA;cAAQqD,KAAK,EAAC,QAAQ;cAAAd,QAAA,EAAC;YAAe;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnD,OAAA;MAAKsC,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClEf,eAAe,CAACkC,GAAG,CAAC,CAAChC,MAAM,EAAEiC,KAAK,kBACjC3D,OAAA,CAACV,MAAM,CAACsE,GAAG;QAETC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAEP,KAAK,GAAG;QAAI,CAAE;QACnCrB,SAAS,EAAC,wGAAwG;QAAAC,QAAA,eAElHvC,OAAA;UAAKsC,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBvC,OAAA;YAAKsC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDvC,OAAA;cAAKsC,SAAS,EAAC,6CAA6C;cAAAC,QAAA,gBAC1DvC,OAAA;gBAAKsC,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,eACzCvC,OAAA,CAACL,eAAe;kBAAC2C,SAAS,EAAC;gBAAuB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eACNnD,OAAA;gBAAAuC,QAAA,gBACEvC,OAAA;kBAAIsC,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EAAEb,MAAM,CAACd;gBAAK;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC/DnD,OAAA;kBAAGsC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEb,MAAM,CAACb;gBAAW;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNnD,OAAA;cAAMsC,SAAS,EAAE,kCACfZ,MAAM,CAACP,QAAQ,GACX,6BAA6B,GAC7B,yBAAyB,EAC5B;cAAAoB,QAAA,EACAb,MAAM,CAACP,QAAQ,GAAG,KAAK,GAAG;YAAS;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAENnD,OAAA;YAAKsC,SAAS,EAAC,wEAAwE;YAAAC,QAAA,gBACrFvC,OAAA;cAAKsC,SAAS,EAAC,6CAA6C;cAAAC,QAAA,gBAC1DvC,OAAA,CAACJ,QAAQ;gBAAC0C,SAAS,EAAC;cAAS;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChCnD,OAAA;gBAAAuC,QAAA,GAAOb,MAAM,CAACV,MAAM,CAACmD,MAAM,EAAC,iCAAM;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACNnD,OAAA;cAAKsC,SAAS,EAAC,6CAA6C;cAAAC,QAAA,gBAC1DvC,OAAA,CAACH,YAAY;gBAACyC,SAAS,EAAC;cAAS;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpCnD,OAAA;gBAAAuC,QAAA,GAAOb,MAAM,CAACT,IAAI,CAACkD,MAAM,EAAC,qBAAI;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACNnD,OAAA;cAAKsC,SAAS,EAAC,6CAA6C;cAAAC,QAAA,gBAC1DvC,OAAA,CAACF,yBAAyB;gBAACwC,SAAS,EAAC;cAAS;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjDnD,OAAA;gBAAAuC,QAAA,GAAOb,MAAM,CAACR,OAAO,CAACiD,MAAM,EAAC,uCAAO;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnD,OAAA;YAAKsC,SAAS,EAAC,iEAAiE;YAAAC,QAAA,gBAC9EvC,OAAA;cAAKsC,SAAS,EAAC,6CAA6C;cAAAC,QAAA,gBAC1DvC,OAAA;gBACEwC,OAAO,EAAEA,CAAA,KAAMH,gBAAgB,CAACX,MAAM,CAACf,EAAE,CAAE;gBAC3C2B,SAAS,EAAC,yDAAyD;gBACnE1B,KAAK,EAAC,yDAAY;gBAAA2B,QAAA,eAElBvC,OAAA,CAACN,OAAO;kBAAC4C,SAAS,EAAC;gBAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eACTnD,OAAA;gBACEwC,OAAO,EAAEA,CAAA,KAAMN,gBAAgB,CAACR,MAAM,CAACf,EAAE,CAAE;gBAC3C2B,SAAS,EAAC,0DAA0D;gBACpE1B,KAAK,EAAC,qEAAc;gBAAA2B,QAAA,eAEpBvC,OAAA,CAACR,UAAU;kBAAC8C,SAAS,EAAC;gBAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACTnD,OAAA;gBACEwC,OAAO,EAAEA,CAAA,KAAMJ,kBAAkB,CAACV,MAAM,CAACf,EAAE,CAAE;gBAC7C2B,SAAS,EAAC,wDAAwD;gBAClE1B,KAAK,EAAC,yDAAY;gBAAA2B,QAAA,eAElBvC,OAAA,CAACP,SAAS;kBAAC6C,SAAS,EAAC;gBAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNnD,OAAA;cAAMsC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EACpC,IAAIlB,IAAI,CAACK,MAAM,CAACN,SAAS,CAAC,CAACgD,kBAAkB,CAAC,OAAO;YAAC;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GArEDzB,MAAM,CAACf,EAAE;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAsEJ,CACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAEL3B,eAAe,CAAC2C,MAAM,KAAK,CAAC,iBAC3BnE,OAAA;MAAKsC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCvC,OAAA,CAACL,eAAe;QAAC2C,SAAS,EAAC;MAAsC;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpEnD,OAAA;QAAIsC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAc;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1EnD,OAAA;QAAGsC,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAuC;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAChD,EAAA,CArNIF,iBAAmD;AAAAoE,EAAA,GAAnDpE,iBAAmD;AAuNzD,eAAeA,iBAAiB;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}