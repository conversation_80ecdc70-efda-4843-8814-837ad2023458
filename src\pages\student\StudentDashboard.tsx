import React, { useState } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { motion } from 'framer-motion';

// Components
import StudentSidebar from '../../components/Student/StudentSidebar';
import StudentHeader from '../../components/Student/StudentHeader';
import StudentOverview from '../../components/Student/StudentOverview';
import MyCourses from '../../components/Student/MyCourses';
import CourseViewer from '../../components/Student/CourseViewer';
import QuizPage from '../../components/Student/QuizPage';
import MyCertificates from '../../components/Student/MyCertificates';
import StudentProfile from '../../components/Student/StudentProfile';

// Types
import { Student } from '../../types';

interface StudentDashboardProps {
  user: Student;
  onLogout: () => void;
}

const StudentDashboard: React.FC<StudentDashboardProps> = ({ user, onLogout }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <div className="flex h-screen bg-gray-50" dir="rtl">
      {/* Sidebar */}
      <StudentSidebar 
        isOpen={sidebarOpen} 
        onClose={() => setSidebarOpen(false)} 
      />
      
      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <StudentHeader 
          user={user}
          onMenuClick={() => setSidebarOpen(true)}
          onLogout={onLogout}
        />
        
        {/* Page Content */}
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Routes>
              <Route path="/" element={<StudentOverview user={user} />} />
              <Route path="/courses" element={<MyCourses user={user} />} />
              <Route path="/course/:courseId" element={<CourseViewer user={user} />} />
              <Route path="/quiz/:quizId" element={<QuizPage user={user} />} />
              <Route path="/certificates" element={<MyCertificates user={user} />} />
              <Route path="/profile" element={<StudentProfile user={user} />} />
              <Route path="*" element={<Navigate to="/student" replace />} />
            </Routes>
          </motion.div>
        </main>
      </div>
      
      {/* Mobile Sidebar Overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
};

export default StudentDashboard;
