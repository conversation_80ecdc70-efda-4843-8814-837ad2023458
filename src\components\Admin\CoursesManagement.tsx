import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  AcademicCapIcon,
  PlayIcon,
  DocumentIcon,
  ClipboardDocumentListIcon
} from '@heroicons/react/24/outline';
import { dataService } from '../../services/dataService';
import { Course } from '../../types';



interface CoursesManagementProps {
  onBack?: () => void;
}

const CoursesManagement: React.FC<CoursesManagementProps> = ({ onBack }) => {
  const [courses, setCourses] = useState<Course[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadCourses = async () => {
      try {
        const coursesData = await dataService.getCourses();
        setCourses(coursesData);
        setLoading(false);
      } catch (error) {
        console.error('Error loading courses:', error);
        setLoading(false);
      }
    };

    loadCourses();
  }, []);





  const filteredCourses = courses.filter(course => {
    const matchesSearch = course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || course.categoryId === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handleAddCourse = () => {
    // TODO: Implement add course functionality
    console.log('Add course');
  };

  const handleEditCourse = (courseId: string) => {
    // TODO: Implement edit course functionality
    console.log('Edit course:', courseId);
  };

  const handleDeleteCourse = (courseId: string) => {
    // TODO: Implement delete course functionality
    console.log('Delete course:', courseId);
  };

  const handleViewCourse = (courseId: string) => {
    // TODO: Implement view course functionality
    console.log('View course:', courseId);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 space-x-reverse">
          {onBack && (
            <button
              onClick={onBack}
              className="p-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
          )}
          <div>
            <h1 className="text-2xl font-bold text-gray-900">إدارة الكورسات</h1>
            <p className="text-gray-600">إدارة وتنظيم جميع الكورسات التعليمية</p>
          </div>
        </div>
        <button
          onClick={handleAddCourse}
          className="flex items-center space-x-2 space-x-reverse bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          <PlusIcon className="w-5 h-5" />
          <span>إضافة كورس جديد</span>
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              البحث في الكورسات
            </label>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="ابحث عن كورس..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              التصنيف
            </label>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">جميع التصنيفات</option>
              <option value="programming">البرمجة</option>
              <option value="web">تطوير المواقع</option>
              <option value="mobile">تطوير التطبيقات</option>
            </select>
          </div>
        </div>
      </div>

      {/* Courses Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredCourses.map((course, index) => (
          <motion.div
            key={course.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow"
          >
            <div className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <AcademicCapIcon className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{course.title}</h3>
                    <p className="text-sm text-gray-600">{course.description}</p>
                  </div>
                </div>
                <span className={`px-2 py-1 text-xs rounded-full ${
                  course.isActive 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {course.isActive ? 'نشط' : 'غير نشط'}
                </span>
              </div>

              <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-600 mb-4">
                <div className="flex items-center space-x-1 space-x-reverse">
                  <PlayIcon className="w-4 h-4" />
                  <span>{course.videos.length} فيديو</span>
                </div>
                <div className="flex items-center space-x-1 space-x-reverse">
                  <DocumentIcon className="w-4 h-4" />
                  <span>{course.pdfs.length} ملف</span>
                </div>
                <div className="flex items-center space-x-1 space-x-reverse">
                  <ClipboardDocumentListIcon className="w-4 h-4" />
                  <span>{course.quizzes.length} اختبار</span>
                </div>
              </div>

              <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <button
                    onClick={() => handleViewCourse(course.id)}
                    className="p-2 text-gray-600 hover:text-blue-600 transition-colors"
                    title="عرض الكورس"
                  >
                    <EyeIcon className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleEditCourse(course.id)}
                    className="p-2 text-gray-600 hover:text-green-600 transition-colors"
                    title="تعديل الكورس"
                  >
                    <PencilIcon className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleDeleteCourse(course.id)}
                    className="p-2 text-gray-600 hover:text-red-600 transition-colors"
                    title="حذف الكورس"
                  >
                    <TrashIcon className="w-4 h-4" />
                  </button>
                </div>
                <span className="text-xs text-gray-500">
                  {new Date(course.createdAt).toLocaleDateString('ar-SA')}
                </span>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {filteredCourses.length === 0 && (
        <div className="text-center py-12">
          <AcademicCapIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد كورسات</h3>
          <p className="text-gray-600">لم يتم العثور على أي كورسات تطابق البحث</p>
        </div>
      )}
    </div>
  );
};

export default CoursesManagement;
