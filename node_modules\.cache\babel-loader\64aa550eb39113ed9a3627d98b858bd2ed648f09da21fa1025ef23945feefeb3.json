{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0634\\u0631\\u0648\\u0639\\\\src\\\\components\\\\Admin\\\\StatsCard.tsx\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { ArrowUpIcon, ArrowDownIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StatsCard = ({\n  title,\n  value,\n  change,\n  changeType = 'increase',\n  icon: Icon,\n  color,\n  delay = 0\n}) => {\n  const colorClasses = {\n    blue: {\n      bg: 'bg-blue-100',\n      text: 'text-blue-600',\n      border: 'border-blue-200'\n    },\n    green: {\n      bg: 'bg-green-100',\n      text: 'text-green-600',\n      border: 'border-green-200'\n    },\n    purple: {\n      bg: 'bg-purple-100',\n      text: 'text-purple-600',\n      border: 'border-purple-200'\n    },\n    orange: {\n      bg: 'bg-orange-100',\n      text: 'text-orange-600',\n      border: 'border-orange-200'\n    },\n    red: {\n      bg: 'bg-red-100',\n      text: 'text-red-600',\n      border: 'border-red-200'\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      opacity: 0,\n      y: 20\n    },\n    animate: {\n      opacity: 1,\n      y: 0\n    },\n    transition: {\n      delay,\n      duration: 0.3\n    },\n    whileHover: {\n      y: -2\n    },\n    className: \"bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-200 border border-gray-100\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm font-medium text-gray-600 mb-1\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-3xl font-bold text-gray-900\",\n          children: typeof value === 'number' ? value.toLocaleString() : value\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), change && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center mt-2\",\n          children: [changeType === 'increase' ? /*#__PURE__*/_jsxDEV(ArrowUpIcon, {\n            className: \"w-4 h-4 text-green-500 ml-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(ArrowDownIcon, {\n            className: \"w-4 h-4 text-red-500 ml-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `text-sm font-medium ${changeType === 'increase' ? 'text-green-600' : 'text-red-600'}`,\n            children: change\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-500 mr-1\",\n            children: \"\\u0645\\u0646 \\u0627\\u0644\\u0634\\u0647\\u0631 \\u0627\\u0644\\u0645\\u0627\\u0636\\u064A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `\n          w-14 h-14 rounded-xl flex items-center justify-center\n          ${colorClasses[color].bg}\n          ${colorClasses[color].border}\n          border\n        `,\n        children: /*#__PURE__*/_jsxDEV(Icon, {\n          className: `w-7 h-7 ${colorClasses[color].text}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n};\n_c = StatsCard;\nexport default StatsCard;\nvar _c;\n$RefreshReg$(_c, \"StatsCard\");", "map": {"version": 3, "names": ["React", "motion", "ArrowUpIcon", "ArrowDownIcon", "jsxDEV", "_jsxDEV", "StatsCard", "title", "value", "change", "changeType", "icon", "Icon", "color", "delay", "colorClasses", "blue", "bg", "text", "border", "green", "purple", "orange", "red", "div", "initial", "opacity", "y", "animate", "transition", "duration", "whileHover", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toLocaleString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Admin/StatsCard.tsx"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { ArrowUpIcon, ArrowDownIcon } from '@heroicons/react/24/outline';\n\ninterface StatsCardProps {\n  title: string;\n  value: number | string;\n  change?: string;\n  changeType?: 'increase' | 'decrease';\n  icon: React.ComponentType<{ className?: string }>;\n  color: 'blue' | 'green' | 'purple' | 'orange' | 'red';\n  delay?: number;\n}\n\nconst StatsCard: React.FC<StatsCardProps> = ({\n  title,\n  value,\n  change,\n  changeType = 'increase',\n  icon: Icon,\n  color,\n  delay = 0\n}) => {\n  const colorClasses = {\n    blue: {\n      bg: 'bg-blue-100',\n      text: 'text-blue-600',\n      border: 'border-blue-200'\n    },\n    green: {\n      bg: 'bg-green-100',\n      text: 'text-green-600',\n      border: 'border-green-200'\n    },\n    purple: {\n      bg: 'bg-purple-100',\n      text: 'text-purple-600',\n      border: 'border-purple-200'\n    },\n    orange: {\n      bg: 'bg-orange-100',\n      text: 'text-orange-600',\n      border: 'border-orange-200'\n    },\n    red: {\n      bg: 'bg-red-100',\n      text: 'text-red-600',\n      border: 'border-red-200'\n    }\n  };\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ delay, duration: 0.3 }}\n      whileHover={{ y: -2 }}\n      className=\"bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-200 border border-gray-100\"\n    >\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex-1\">\n          <p className=\"text-sm font-medium text-gray-600 mb-1\">{title}</p>\n          <p className=\"text-3xl font-bold text-gray-900\">\n            {typeof value === 'number' ? value.toLocaleString() : value}\n          </p>\n          \n          {change && (\n            <div className=\"flex items-center mt-2\">\n              {changeType === 'increase' ? (\n                <ArrowUpIcon className=\"w-4 h-4 text-green-500 ml-1\" />\n              ) : (\n                <ArrowDownIcon className=\"w-4 h-4 text-red-500 ml-1\" />\n              )}\n              <span className={`text-sm font-medium ${\n                changeType === 'increase' ? 'text-green-600' : 'text-red-600'\n              }`}>\n                {change}\n              </span>\n              <span className=\"text-sm text-gray-500 mr-1\">من الشهر الماضي</span>\n            </div>\n          )}\n        </div>\n        \n        <div className={`\n          w-14 h-14 rounded-xl flex items-center justify-center\n          ${colorClasses[color].bg}\n          ${colorClasses[color].border}\n          border\n        `}>\n          <Icon className={`w-7 h-7 ${colorClasses[color].text}`} />\n        </div>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default StatsCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,EAAEC,aAAa,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAYzE,MAAMC,SAAmC,GAAGA,CAAC;EAC3CC,KAAK;EACLC,KAAK;EACLC,MAAM;EACNC,UAAU,GAAG,UAAU;EACvBC,IAAI,EAAEC,IAAI;EACVC,KAAK;EACLC,KAAK,GAAG;AACV,CAAC,KAAK;EACJ,MAAMC,YAAY,GAAG;IACnBC,IAAI,EAAE;MACJC,EAAE,EAAE,aAAa;MACjBC,IAAI,EAAE,eAAe;MACrBC,MAAM,EAAE;IACV,CAAC;IACDC,KAAK,EAAE;MACLH,EAAE,EAAE,cAAc;MAClBC,IAAI,EAAE,gBAAgB;MACtBC,MAAM,EAAE;IACV,CAAC;IACDE,MAAM,EAAE;MACNJ,EAAE,EAAE,eAAe;MACnBC,IAAI,EAAE,iBAAiB;MACvBC,MAAM,EAAE;IACV,CAAC;IACDG,MAAM,EAAE;MACNL,EAAE,EAAE,eAAe;MACnBC,IAAI,EAAE,iBAAiB;MACvBC,MAAM,EAAE;IACV,CAAC;IACDI,GAAG,EAAE;MACHN,EAAE,EAAE,YAAY;MAChBC,IAAI,EAAE,cAAc;MACpBC,MAAM,EAAE;IACV;EACF,CAAC;EAED,oBACEd,OAAA,CAACJ,MAAM,CAACuB,GAAG;IACTC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAE;IAC/BC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAE;IAC9BE,UAAU,EAAE;MAAEf,KAAK;MAAEgB,QAAQ,EAAE;IAAI,CAAE;IACrCC,UAAU,EAAE;MAAEJ,CAAC,EAAE,CAAC;IAAE,CAAE;IACtBK,SAAS,EAAC,sGAAsG;IAAAC,QAAA,eAEhH5B,OAAA;MAAK2B,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD5B,OAAA;QAAK2B,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACrB5B,OAAA;UAAG2B,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAE1B;QAAK;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjEhC,OAAA;UAAG2B,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAC5C,OAAOzB,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAAC8B,cAAc,CAAC,CAAC,GAAG9B;QAAK;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,EAEH5B,MAAM,iBACLJ,OAAA;UAAK2B,SAAS,EAAC,wBAAwB;UAAAC,QAAA,GACpCvB,UAAU,KAAK,UAAU,gBACxBL,OAAA,CAACH,WAAW;YAAC8B,SAAS,EAAC;UAA6B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEvDhC,OAAA,CAACF,aAAa;YAAC6B,SAAS,EAAC;UAA2B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CACvD,eACDhC,OAAA;YAAM2B,SAAS,EAAE,uBACftB,UAAU,KAAK,UAAU,GAAG,gBAAgB,GAAG,cAAc,EAC5D;YAAAuB,QAAA,EACAxB;UAAM;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACPhC,OAAA;YAAM2B,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENhC,OAAA;QAAK2B,SAAS,EAAE;AACxB;AACA,YAAYjB,YAAY,CAACF,KAAK,CAAC,CAACI,EAAE;AAClC,YAAYF,YAAY,CAACF,KAAK,CAAC,CAACM,MAAM;AACtC;AACA,SAAU;QAAAc,QAAA,eACA5B,OAAA,CAACO,IAAI;UAACoB,SAAS,EAAE,WAAWjB,YAAY,CAACF,KAAK,CAAC,CAACK,IAAI;QAAG;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEjB,CAAC;AAACE,EAAA,GAhFIjC,SAAmC;AAkFzC,eAAeA,SAAS;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}