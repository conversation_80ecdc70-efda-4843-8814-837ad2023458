{"ast": null, "code": "import React from'react';import{motion,AnimatePresence}from'framer-motion';import{ExclamationTriangleIcon,InformationCircleIcon,CheckCircleIcon,XCircleIcon}from'@heroicons/react/24/outline';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ConfirmDialog=_ref=>{let{isOpen,onClose,onConfirm,title,message,confirmText='تأكيد',cancelText='إلغاء',type='warning',loading=false}=_ref;const getIcon=()=>{switch(type){case'danger':return XCircleIcon;case'warning':return ExclamationTriangleIcon;case'info':return InformationCircleIcon;case'success':return CheckCircleIcon;default:return ExclamationTriangleIcon;}};const getIconColor=()=>{switch(type){case'danger':return'text-red-600';case'warning':return'text-yellow-600';case'info':return'text-blue-600';case'success':return'text-green-600';default:return'text-yellow-600';}};const getConfirmButtonColor=()=>{switch(type){case'danger':return'bg-red-600 hover:bg-red-700 focus:ring-red-500';case'warning':return'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500';case'info':return'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500';case'success':return'bg-green-600 hover:bg-green-700 focus:ring-green-500';default:return'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500';}};const Icon=getIcon();return/*#__PURE__*/_jsx(AnimatePresence,{children:isOpen&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 z-50 overflow-y-auto\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\",children:[/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\",onClick:onClose}),/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,scale:0.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:0.95,y:20},className:\"inline-block align-bottom bg-white rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"sm:flex sm:items-start\",children:[/*#__PURE__*/_jsx(\"div\",{className:`\n                    mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full sm:mx-0 sm:h-10 sm:w-10\n                    ${type==='danger'?'bg-red-100':''}\n                    ${type==='warning'?'bg-yellow-100':''}\n                    ${type==='info'?'bg-blue-100':''}\n                    ${type==='success'?'bg-green-100':''}\n                  `,children:/*#__PURE__*/_jsx(Icon,{className:`h-6 w-6 ${getIconColor()}`})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3 text-center sm:mt-0 sm:mr-4 sm:text-right\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg leading-6 font-medium text-gray-900\",children:title}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-2\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500\",children:message})})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:onConfirm,disabled:loading,className:`\n                    w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed\n                    ${getConfirmButtonColor()}\n                  `,children:loading?/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ml-2\"}),\"\\u062C\\u0627\\u0631\\u064A \\u0627\\u0644\\u062A\\u0646\\u0641\\u064A\\u0630...\"]}):confirmText}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:onClose,disabled:loading,className:\"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:mr-3 sm:w-auto sm:text-sm disabled:opacity-50\",children:cancelText})]})]})]})})});};export default ConfirmDialog;", "map": {"version": 3, "names": ["React", "motion", "AnimatePresence", "ExclamationTriangleIcon", "InformationCircleIcon", "CheckCircleIcon", "XCircleIcon", "jsx", "_jsx", "jsxs", "_jsxs", "ConfirmDialog", "_ref", "isOpen", "onClose", "onConfirm", "title", "message", "confirmText", "cancelText", "type", "loading", "getIcon", "getIconColor", "getConfirmButtonColor", "Icon", "children", "className", "div", "initial", "opacity", "animate", "exit", "onClick", "scale", "y", "disabled"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/common/ConfirmDialog.tsx"], "sourcesContent": ["import React from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { \n  ExclamationTriangleIcon,\n  InformationCircleIcon,\n  CheckCircleIcon,\n  XCircleIcon\n} from '@heroicons/react/24/outline';\n\ninterface ConfirmDialogProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onConfirm: () => void;\n  title: string;\n  message: string;\n  confirmText?: string;\n  cancelText?: string;\n  type?: 'danger' | 'warning' | 'info' | 'success';\n  loading?: boolean;\n}\n\nconst ConfirmDialog: React.FC<ConfirmDialogProps> = ({\n  isOpen,\n  onClose,\n  onConfirm,\n  title,\n  message,\n  confirmText = 'تأكيد',\n  cancelText = 'إلغاء',\n  type = 'warning',\n  loading = false\n}) => {\n  const getIcon = () => {\n    switch (type) {\n      case 'danger':\n        return XCircleIcon;\n      case 'warning':\n        return ExclamationTriangleIcon;\n      case 'info':\n        return InformationCircleIcon;\n      case 'success':\n        return CheckCircleIcon;\n      default:\n        return ExclamationTriangleIcon;\n    }\n  };\n\n  const getIconColor = () => {\n    switch (type) {\n      case 'danger':\n        return 'text-red-600';\n      case 'warning':\n        return 'text-yellow-600';\n      case 'info':\n        return 'text-blue-600';\n      case 'success':\n        return 'text-green-600';\n      default:\n        return 'text-yellow-600';\n    }\n  };\n\n  const getConfirmButtonColor = () => {\n    switch (type) {\n      case 'danger':\n        return 'bg-red-600 hover:bg-red-700 focus:ring-red-500';\n      case 'warning':\n        return 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500';\n      case 'info':\n        return 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500';\n      case 'success':\n        return 'bg-green-600 hover:bg-green-700 focus:ring-green-500';\n      default:\n        return 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500';\n    }\n  };\n\n  const Icon = getIcon();\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <div className=\"fixed inset-0 z-50 overflow-y-auto\">\n          <div className=\"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\">\n            {/* Backdrop */}\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              exit={{ opacity: 0 }}\n              className=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\"\n              onClick={onClose}\n            />\n\n            {/* Modal */}\n            <motion.div\n              initial={{ opacity: 0, scale: 0.95, y: 20 }}\n              animate={{ opacity: 1, scale: 1, y: 0 }}\n              exit={{ opacity: 0, scale: 0.95, y: 20 }}\n              className=\"inline-block align-bottom bg-white rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full\"\n            >\n              <div className=\"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\">\n                <div className=\"sm:flex sm:items-start\">\n                  <div className={`\n                    mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full sm:mx-0 sm:h-10 sm:w-10\n                    ${type === 'danger' ? 'bg-red-100' : ''}\n                    ${type === 'warning' ? 'bg-yellow-100' : ''}\n                    ${type === 'info' ? 'bg-blue-100' : ''}\n                    ${type === 'success' ? 'bg-green-100' : ''}\n                  `}>\n                    <Icon className={`h-6 w-6 ${getIconColor()}`} />\n                  </div>\n                  <div className=\"mt-3 text-center sm:mt-0 sm:mr-4 sm:text-right\">\n                    <h3 className=\"text-lg leading-6 font-medium text-gray-900\">\n                      {title}\n                    </h3>\n                    <div className=\"mt-2\">\n                      <p className=\"text-sm text-gray-500\">\n                        {message}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n              \n              <div className=\"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\">\n                <button\n                  type=\"button\"\n                  onClick={onConfirm}\n                  disabled={loading}\n                  className={`\n                    w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed\n                    ${getConfirmButtonColor()}\n                  `}\n                >\n                  {loading ? (\n                    <div className=\"flex items-center\">\n                      <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ml-2\" />\n                      جاري التنفيذ...\n                    </div>\n                  ) : (\n                    confirmText\n                  )}\n                </button>\n                <button\n                  type=\"button\"\n                  onClick={onClose}\n                  disabled={loading}\n                  className=\"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:mr-3 sm:w-auto sm:text-sm disabled:opacity-50\"\n                >\n                  {cancelText}\n                </button>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      )}\n    </AnimatePresence>\n  );\n};\n\nexport default ConfirmDialog;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,CAAEC,eAAe,KAAQ,eAAe,CACvD,OACEC,uBAAuB,CACvBC,qBAAqB,CACrBC,eAAe,CACfC,WAAW,KACN,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAcrC,KAAM,CAAAC,aAA2C,CAAGC,IAAA,EAU9C,IAV+C,CACnDC,MAAM,CACNC,OAAO,CACPC,SAAS,CACTC,KAAK,CACLC,OAAO,CACPC,WAAW,CAAG,OAAO,CACrBC,UAAU,CAAG,OAAO,CACpBC,IAAI,CAAG,SAAS,CAChBC,OAAO,CAAG,KACZ,CAAC,CAAAT,IAAA,CACC,KAAM,CAAAU,OAAO,CAAGA,CAAA,GAAM,CACpB,OAAQF,IAAI,EACV,IAAK,QAAQ,CACX,MAAO,CAAAd,WAAW,CACpB,IAAK,SAAS,CACZ,MAAO,CAAAH,uBAAuB,CAChC,IAAK,MAAM,CACT,MAAO,CAAAC,qBAAqB,CAC9B,IAAK,SAAS,CACZ,MAAO,CAAAC,eAAe,CACxB,QACE,MAAO,CAAAF,uBAAuB,CAClC,CACF,CAAC,CAED,KAAM,CAAAoB,YAAY,CAAGA,CAAA,GAAM,CACzB,OAAQH,IAAI,EACV,IAAK,QAAQ,CACX,MAAO,cAAc,CACvB,IAAK,SAAS,CACZ,MAAO,iBAAiB,CAC1B,IAAK,MAAM,CACT,MAAO,eAAe,CACxB,IAAK,SAAS,CACZ,MAAO,gBAAgB,CACzB,QACE,MAAO,iBAAiB,CAC5B,CACF,CAAC,CAED,KAAM,CAAAI,qBAAqB,CAAGA,CAAA,GAAM,CAClC,OAAQJ,IAAI,EACV,IAAK,QAAQ,CACX,MAAO,gDAAgD,CACzD,IAAK,SAAS,CACZ,MAAO,yDAAyD,CAClE,IAAK,MAAM,CACT,MAAO,mDAAmD,CAC5D,IAAK,SAAS,CACZ,MAAO,sDAAsD,CAC/D,QACE,MAAO,yDAAyD,CACpE,CACF,CAAC,CAED,KAAM,CAAAK,IAAI,CAAGH,OAAO,CAAC,CAAC,CAEtB,mBACEd,IAAA,CAACN,eAAe,EAAAwB,QAAA,CACbb,MAAM,eACLL,IAAA,QAAKmB,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjDhB,KAAA,QAAKiB,SAAS,CAAC,wFAAwF,CAAAD,QAAA,eAErGlB,IAAA,CAACP,MAAM,CAAC2B,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAE,CAAE,CACxBC,OAAO,CAAE,CAAED,OAAO,CAAE,CAAE,CAAE,CACxBE,IAAI,CAAE,CAAEF,OAAO,CAAE,CAAE,CAAE,CACrBH,SAAS,CAAC,4DAA4D,CACtEM,OAAO,CAAEnB,OAAQ,CAClB,CAAC,cAGFJ,KAAA,CAACT,MAAM,CAAC2B,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEI,KAAK,CAAE,IAAI,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC5CJ,OAAO,CAAE,CAAED,OAAO,CAAE,CAAC,CAAEI,KAAK,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CACxCH,IAAI,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEI,KAAK,CAAE,IAAI,CAAEC,CAAC,CAAE,EAAG,CAAE,CACzCR,SAAS,CAAC,2JAA2J,CAAAD,QAAA,eAErKlB,IAAA,QAAKmB,SAAS,CAAC,wCAAwC,CAAAD,QAAA,cACrDhB,KAAA,QAAKiB,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrClB,IAAA,QAAKmB,SAAS,CAAE;AAClC;AACA,sBAAsBP,IAAI,GAAK,QAAQ,CAAG,YAAY,CAAG,EAAE;AAC3D,sBAAsBA,IAAI,GAAK,SAAS,CAAG,eAAe,CAAG,EAAE;AAC/D,sBAAsBA,IAAI,GAAK,MAAM,CAAG,aAAa,CAAG,EAAE;AAC1D,sBAAsBA,IAAI,GAAK,SAAS,CAAG,cAAc,CAAG,EAAE;AAC9D,mBAAoB,CAAAM,QAAA,cACAlB,IAAA,CAACiB,IAAI,EAACE,SAAS,CAAE,WAAWJ,YAAY,CAAC,CAAC,EAAG,CAAE,CAAC,CAC7C,CAAC,cACNb,KAAA,QAAKiB,SAAS,CAAC,gDAAgD,CAAAD,QAAA,eAC7DlB,IAAA,OAAImB,SAAS,CAAC,6CAA6C,CAAAD,QAAA,CACxDV,KAAK,CACJ,CAAC,cACLR,IAAA,QAAKmB,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnBlB,IAAA,MAAGmB,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CACjCT,OAAO,CACP,CAAC,CACD,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAENP,KAAA,QAAKiB,SAAS,CAAC,0DAA0D,CAAAD,QAAA,eACvElB,IAAA,WACEY,IAAI,CAAC,QAAQ,CACba,OAAO,CAAElB,SAAU,CACnBqB,QAAQ,CAAEf,OAAQ,CAClBM,SAAS,CAAE;AAC7B;AACA,sBAAsBH,qBAAqB,CAAC,CAAC;AAC7C,mBAAoB,CAAAE,QAAA,CAEDL,OAAO,cACNX,KAAA,QAAKiB,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChClB,IAAA,QAAKmB,SAAS,CAAC,mFAAmF,CAAE,CAAC,yEAEvG,EAAK,CAAC,CAENT,WACD,CACK,CAAC,cACTV,IAAA,WACEY,IAAI,CAAC,QAAQ,CACba,OAAO,CAAEnB,OAAQ,CACjBsB,QAAQ,CAAEf,OAAQ,CAClBM,SAAS,CAAC,gSAAgS,CAAAD,QAAA,CAEzSP,UAAU,CACL,CAAC,EACN,CAAC,EACI,CAAC,EACV,CAAC,CACH,CACN,CACc,CAAC,CAEtB,CAAC,CAED,cAAe,CAAAR,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}