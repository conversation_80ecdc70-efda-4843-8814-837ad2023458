import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  DocumentIcon,
  CheckBadgeIcon
} from '@heroicons/react/24/outline';

// Types
import { Certificate } from '../../types';

interface CertificatesManagementProps {
  onBack: () => void;
}

const CertificatesManagement: React.FC<CertificatesManagementProps> = ({ onBack }) => {
  const [certificates, setCertificates] = useState<Certificate[]>([]);
  const [searchTerm, setSearchTerm] = useState('');

  // Mock data for demonstration
  const mockCertificates: Certificate[] = [
    {
      id: '1',
      studentId: '1',
      courseId: '1',
      templateUrl: '/templates/cert1.pdf',
      certificateUrl: '/certificates/cert1.pdf',
      issuedAt: new Date('2024-03-01'),
      verificationCode: 'CERT-2024-001'
    },
    {
      id: '2',
      studentId: '3',
      courseId: '1',
      templateUrl: '/templates/cert1.pdf',
      certificateUrl: '/certificates/cert2.pdf',
      issuedAt: new Date('2024-03-05'),
      verificationCode: 'CERT-2024-002'
    }
  ];

  React.useEffect(() => {
    setCertificates(mockCertificates);
  }, []);

  const filteredCertificates = certificates.filter(cert =>
    cert.verificationCode.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAddCertificate = () => {
    console.log('Add certificate');
  };

  const handleEditCertificate = (certId: string) => {
    console.log('Edit certificate:', certId);
  };

  const handleDeleteCertificate = (certId: string) => {
    console.log('Delete certificate:', certId);
  };

  const handleViewCertificate = (certId: string) => {
    console.log('View certificate:', certId);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 space-x-reverse">
          <button
            onClick={onBack}
            className="p-2 text-gray-600 hover:text-gray-900 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">إدارة الشهادات</h1>
            <p className="text-gray-600">إصدار وإدارة شهادات إتمام الكورسات</p>
          </div>
        </div>
        <button
          onClick={handleAddCertificate}
          className="flex items-center space-x-2 space-x-reverse bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          <PlusIcon className="w-5 h-5" />
          <span>إصدار شهادة جديدة</span>
        </button>
      </div>

      {/* Search */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            البحث في الشهادات
          </label>
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="ابحث برمز التحقق..."
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      {/* Certificates Table */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  رمز التحقق
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الطالب
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الكورس
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  تاريخ الإصدار
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredCertificates.map((certificate, index) => (
                <motion.tr
                  key={certificate.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className="hover:bg-gray-50"
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <CheckBadgeIcon className="w-5 h-5 text-green-600" />
                      <span className="text-sm font-mono text-gray-900 bg-gray-100 px-2 py-1 rounded">
                        {certificate.verificationCode}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm text-gray-900">طالب {certificate.studentId}</span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm text-gray-900">كورس {certificate.courseId}</span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(certificate.issuedAt).toLocaleDateString('ar-SA')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <button
                        onClick={() => handleViewCertificate(certificate.id)}
                        className="text-blue-600 hover:text-blue-900"
                        title="عرض الشهادة"
                      >
                        <EyeIcon className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleEditCertificate(certificate.id)}
                        className="text-green-600 hover:text-green-900"
                        title="تعديل الشهادة"
                      >
                        <PencilIcon className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteCertificate(certificate.id)}
                        className="text-red-600 hover:text-red-900"
                        title="حذف الشهادة"
                      >
                        <TrashIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {filteredCertificates.length === 0 && (
        <div className="text-center py-12">
          <DocumentIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد شهادات</h3>
          <p className="text-gray-600">لم يتم العثور على أي شهادات تطابق البحث</p>
        </div>
      )}
    </div>
  );
};

export default CertificatesManagement;
