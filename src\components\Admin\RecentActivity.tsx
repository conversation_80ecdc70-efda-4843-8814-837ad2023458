import React from 'react';
import { motion } from 'framer-motion';
import {
  UserPlusIcon,
  AcademicCapIcon,
  ClipboardDocumentCheckIcon,
  DocumentTextIcon,
  EyeIcon
} from '@heroicons/react/24/outline';

interface Activity {
  id: string;
  type: 'student_joined' | 'course_completed' | 'quiz_taken' | 'certificate_issued' | 'video_watched';
  title: string;
  description: string;
  time: string;
  user?: string;
}

const RecentActivity: React.FC = () => {
  const activities: Activity[] = [
    {
      id: '1',
      type: 'student_joined',
      title: 'طالب جديد',
      description: 'انضم أحمد محمد إلى المنصة',
      time: 'منذ 5 دقائق',
      user: 'أحمد محمد'
    },
    {
      id: '2',
      type: 'course_completed',
      title: 'كورس مكتمل',
      description: 'أكملت سارة أحمد كورس "أساسيات البرمجة"',
      time: 'منذ 15 دقيقة',
      user: 'سارة أحمد'
    },
    {
      id: '3',
      type: 'quiz_taken',
      title: 'اختبار جديد',
      description: 'أدى محمد علي اختبار JavaScript',
      time: 'منذ 30 دقيقة',
      user: 'محمد علي'
    },
    {
      id: '4',
      type: 'certificate_issued',
      title: 'شهادة جديدة',
      description: 'تم إصدار شهادة لفاطمة حسن',
      time: 'منذ ساعة',
      user: 'فاطمة حسن'
    },
    {
      id: '5',
      type: 'video_watched',
      title: 'مشاهدة فيديو',
      description: 'شاهد خالد أحمد فيديو "المتغيرات في JavaScript"',
      time: 'منذ ساعتين',
      user: 'خالد أحمد'
    }
  ];

  const getActivityIcon = (type: Activity['type']) => {
    switch (type) {
      case 'student_joined':
        return UserPlusIcon;
      case 'course_completed':
        return AcademicCapIcon;
      case 'quiz_taken':
        return ClipboardDocumentCheckIcon;
      case 'certificate_issued':
        return DocumentTextIcon;
      case 'video_watched':
        return EyeIcon;
      default:
        return UserPlusIcon;
    }
  };

  const getActivityColor = (type: Activity['type']) => {
    switch (type) {
      case 'student_joined':
        return 'bg-blue-100 text-blue-600';
      case 'course_completed':
        return 'bg-green-100 text-green-600';
      case 'quiz_taken':
        return 'bg-orange-100 text-orange-600';
      case 'certificate_issued':
        return 'bg-purple-100 text-purple-600';
      case 'video_watched':
        return 'bg-indigo-100 text-indigo-600';
      default:
        return 'bg-gray-100 text-gray-600';
    }
  };

  return (
    <div className="bg-white rounded-xl p-6 shadow-sm">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-bold text-gray-900">النشاط الأخير</h3>
        <button className="text-sm text-primary-600 hover:text-primary-700 font-medium">
          عرض الكل
        </button>
      </div>
      
      <div className="space-y-4">
        {activities.map((activity, index) => {
          const Icon = getActivityIcon(activity.type);
          const colorClass = getActivityColor(activity.type);
          
          return (
            <motion.div
              key={activity.id}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-start space-x-3 space-x-reverse p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200"
            >
              <div className={`w-10 h-10 rounded-full flex items-center justify-center ${colorClass}`}>
                <Icon className="w-5 h-5" />
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium text-gray-900">
                    {activity.title}
                  </h4>
                  <span className="text-xs text-gray-500">
                    {activity.time}
                  </span>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  {activity.description}
                </p>
                {activity.user && (
                  <span className="inline-block mt-2 px-2 py-1 bg-gray-100 text-xs text-gray-700 rounded-full">
                    {activity.user}
                  </span>
                )}
              </div>
            </motion.div>
          );
        })}
      </div>
      
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="flex items-center justify-center">
          <button className="text-sm text-primary-600 hover:text-primary-700 font-medium">
            تحديث النشاط
          </button>
        </div>
      </div>
    </div>
  );
};

export default RecentActivity;
