{"ast": null, "code": "import{signInWithEmailAndPassword,signOut,onAuthStateChanged}from'firebase/auth';import{doc,getDoc,setDoc}from'firebase/firestore';import{auth,db}from'../config/firebase';class AuthService{// Admin login\nasync loginAdmin(email,password){try{var _adminData$createdAt;const userCredential=await signInWithEmailAndPassword(auth,email,password);const user=userCredential.user;// Get admin data from Firestore\nconst adminDoc=await getDoc(doc(db,'admins',user.uid));if(!adminDoc.exists()){throw new Error('المستخدم غير مخول كمدير');}const adminData=adminDoc.data();return{id:user.uid,email:user.email,role:'admin',name:adminData.name,avatar:adminData.avatar,permissions:adminData.permissions||[],createdAt:((_adminData$createdAt=adminData.createdAt)===null||_adminData$createdAt===void 0?void 0:_adminData$createdAt.toDate())||new Date()};}catch(error){throw new Error(this.getErrorMessage(error.code));}}// Student login with access code\nasync loginStudent(accessCode){try{var _studentData$createdA;// Find student by access code in Firestore\nconst studentsRef=doc(db,'students',accessCode);const studentDoc=await getDoc(studentsRef);if(!studentDoc.exists()){throw new Error('كود الدخول غير صحيح');}const studentData=studentDoc.data();if(!studentData.isActive){throw new Error('الحساب غير مفعل');}return{id:studentData.id,email:studentData.email||'',role:'student',name:studentData.name,avatar:studentData.avatar,accessCode:accessCode,enrolledCourses:studentData.enrolledCourses||[],completedCourses:studentData.completedCourses||[],certificates:studentData.certificates||[],createdAt:((_studentData$createdA=studentData.createdAt)===null||_studentData$createdA===void 0?void 0:_studentData$createdA.toDate())||new Date()};}catch(error){throw new Error(error.message||'حدث خطأ في تسجيل الدخول');}}// Generate access code for student\ngenerateAccessCode(){return Math.floor(1000000+Math.random()*9000000).toString();}// Create student account\nasync createStudent(studentData){try{const accessCode=this.generateAccessCode();// Check if access code already exists\nconst existingStudent=await getDoc(doc(db,'students',accessCode));if(existingStudent.exists()){// Generate new code if exists\nreturn this.createStudent(studentData);}const student={id:accessCode,name:studentData.name,email:studentData.email||'',accessCode:accessCode,enrolledCourses:studentData.enrolledCourses||[],completedCourses:[],certificates:[],isActive:true,createdAt:new Date()};await setDoc(doc(db,'students',accessCode),student);return accessCode;}catch(error){throw new Error('فشل في إنشاء حساب الطالب');}}// Logout\nasync logout(){try{await signOut(auth);}catch(error){throw new Error('فشل في تسجيل الخروج');}}// Get current user\ngetCurrentUser(){return new Promise(resolve=>{const unsubscribe=onAuthStateChanged(auth,user=>{unsubscribe();resolve(user);});});}// Auth state listener\nonAuthStateChange(callback){return onAuthStateChanged(auth,callback);}getErrorMessage(errorCode){switch(errorCode){case'auth/user-not-found':return'المستخدم غير موجود';case'auth/wrong-password':return'كلمة المرور غير صحيحة';case'auth/invalid-email':return'البريد الإلكتروني غير صحيح';case'auth/user-disabled':return'الحساب معطل';case'auth/too-many-requests':return'محاولات كثيرة، حاول مرة أخرى لاحقاً';default:return'حدث خطأ في تسجيل الدخول';}}}export const authService=new AuthService();", "map": {"version": 3, "names": ["signInWithEmailAndPassword", "signOut", "onAuthStateChanged", "doc", "getDoc", "setDoc", "auth", "db", "AuthService", "loginAdmin", "email", "password", "_adminData$createdAt", "userCredential", "user", "adminDoc", "uid", "exists", "Error", "adminData", "data", "id", "role", "name", "avatar", "permissions", "createdAt", "toDate", "Date", "error", "getErrorMessage", "code", "loginStudent", "accessCode", "_studentData$createdA", "studentsRef", "studentDoc", "studentData", "isActive", "enrolledCourses", "completedCourses", "certificates", "message", "generateAccessCode", "Math", "floor", "random", "toString", "createStudent", "existingStudent", "student", "logout", "getCurrentUser", "Promise", "resolve", "unsubscribe", "onAuthStateChange", "callback", "errorCode", "authService"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/services/authService.ts"], "sourcesContent": ["import { \n  signInWithEmailAndPassword, \n  signOut, \n  onAuthStateChanged,\n  User as FirebaseUser\n} from 'firebase/auth';\nimport { doc, getDoc, setDoc } from 'firebase/firestore';\nimport { auth, db } from '../config/firebase';\nimport { User, Student, Admin } from '../types';\n\nclass AuthService {\n  // Admin login\n  async loginAdmin(email: string, password: string): Promise<Admin> {\n    try {\n      const userCredential = await signInWithEmailAndPassword(auth, email, password);\n      const user = userCredential.user;\n      \n      // Get admin data from Firestore\n      const adminDoc = await getDoc(doc(db, 'admins', user.uid));\n      if (!adminDoc.exists()) {\n        throw new Error('المستخدم غير مخول كمدير');\n      }\n      \n      const adminData = adminDoc.data();\n      return {\n        id: user.uid,\n        email: user.email!,\n        role: 'admin',\n        name: adminData.name,\n        avatar: adminData.avatar,\n        permissions: adminData.permissions || [],\n        createdAt: adminData.createdAt?.toDate() || new Date()\n      };\n    } catch (error: any) {\n      throw new Error(this.getErrorMessage(error.code));\n    }\n  }\n\n  // Student login with access code\n  async loginStudent(accessCode: string): Promise<Student> {\n    try {\n      // Find student by access code in Firestore\n      const studentsRef = doc(db, 'students', accessCode);\n      const studentDoc = await getDoc(studentsRef);\n      \n      if (!studentDoc.exists()) {\n        throw new Error('كود الدخول غير صحيح');\n      }\n      \n      const studentData = studentDoc.data();\n      if (!studentData.isActive) {\n        throw new Error('الحساب غير مفعل');\n      }\n      \n      return {\n        id: studentData.id,\n        email: studentData.email || '',\n        role: 'student',\n        name: studentData.name,\n        avatar: studentData.avatar,\n        accessCode: accessCode,\n        enrolledCourses: studentData.enrolledCourses || [],\n        completedCourses: studentData.completedCourses || [],\n        certificates: studentData.certificates || [],\n        createdAt: studentData.createdAt?.toDate() || new Date()\n      };\n    } catch (error: any) {\n      throw new Error(error.message || 'حدث خطأ في تسجيل الدخول');\n    }\n  }\n\n  // Generate access code for student\n  generateAccessCode(): string {\n    return Math.floor(1000000 + Math.random() * 9000000).toString();\n  }\n\n  // Create student account\n  async createStudent(studentData: {\n    name: string;\n    email?: string;\n    enrolledCourses?: string[];\n  }): Promise<string> {\n    try {\n      const accessCode = this.generateAccessCode();\n      \n      // Check if access code already exists\n      const existingStudent = await getDoc(doc(db, 'students', accessCode));\n      if (existingStudent.exists()) {\n        // Generate new code if exists\n        return this.createStudent(studentData);\n      }\n      \n      const student = {\n        id: accessCode,\n        name: studentData.name,\n        email: studentData.email || '',\n        accessCode: accessCode,\n        enrolledCourses: studentData.enrolledCourses || [],\n        completedCourses: [],\n        certificates: [],\n        isActive: true,\n        createdAt: new Date()\n      };\n      \n      await setDoc(doc(db, 'students', accessCode), student);\n      return accessCode;\n    } catch (error: any) {\n      throw new Error('فشل في إنشاء حساب الطالب');\n    }\n  }\n\n  // Logout\n  async logout(): Promise<void> {\n    try {\n      await signOut(auth);\n    } catch (error: any) {\n      throw new Error('فشل في تسجيل الخروج');\n    }\n  }\n\n  // Get current user\n  getCurrentUser(): Promise<FirebaseUser | null> {\n    return new Promise((resolve) => {\n      const unsubscribe = onAuthStateChanged(auth, (user) => {\n        unsubscribe();\n        resolve(user);\n      });\n    });\n  }\n\n  // Auth state listener\n  onAuthStateChange(callback: (user: FirebaseUser | null) => void) {\n    return onAuthStateChanged(auth, callback);\n  }\n\n  private getErrorMessage(errorCode: string): string {\n    switch (errorCode) {\n      case 'auth/user-not-found':\n        return 'المستخدم غير موجود';\n      case 'auth/wrong-password':\n        return 'كلمة المرور غير صحيحة';\n      case 'auth/invalid-email':\n        return 'البريد الإلكتروني غير صحيح';\n      case 'auth/user-disabled':\n        return 'الحساب معطل';\n      case 'auth/too-many-requests':\n        return 'محاولات كثيرة، حاول مرة أخرى لاحقاً';\n      default:\n        return 'حدث خطأ في تسجيل الدخول';\n    }\n  }\n}\n\nexport const authService = new AuthService();\n"], "mappings": "AAAA,OACEA,0BAA0B,CAC1BC,OAAO,CACPC,kBAAkB,KAEb,eAAe,CACtB,OAASC,GAAG,CAAEC,MAAM,CAAEC,MAAM,KAAQ,oBAAoB,CACxD,OAASC,IAAI,CAAEC,EAAE,KAAQ,oBAAoB,CAG7C,KAAM,CAAAC,WAAY,CAChB;AACA,KAAM,CAAAC,UAAUA,CAACC,KAAa,CAAEC,QAAgB,CAAkB,CAChE,GAAI,KAAAC,oBAAA,CACF,KAAM,CAAAC,cAAc,CAAG,KAAM,CAAAb,0BAA0B,CAACM,IAAI,CAAEI,KAAK,CAAEC,QAAQ,CAAC,CAC9E,KAAM,CAAAG,IAAI,CAAGD,cAAc,CAACC,IAAI,CAEhC;AACA,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAX,MAAM,CAACD,GAAG,CAACI,EAAE,CAAE,QAAQ,CAAEO,IAAI,CAACE,GAAG,CAAC,CAAC,CAC1D,GAAI,CAACD,QAAQ,CAACE,MAAM,CAAC,CAAC,CAAE,CACtB,KAAM,IAAI,CAAAC,KAAK,CAAC,yBAAyB,CAAC,CAC5C,CAEA,KAAM,CAAAC,SAAS,CAAGJ,QAAQ,CAACK,IAAI,CAAC,CAAC,CACjC,MAAO,CACLC,EAAE,CAAEP,IAAI,CAACE,GAAG,CACZN,KAAK,CAAEI,IAAI,CAACJ,KAAM,CAClBY,IAAI,CAAE,OAAO,CACbC,IAAI,CAAEJ,SAAS,CAACI,IAAI,CACpBC,MAAM,CAAEL,SAAS,CAACK,MAAM,CACxBC,WAAW,CAAEN,SAAS,CAACM,WAAW,EAAI,EAAE,CACxCC,SAAS,CAAE,EAAAd,oBAAA,CAAAO,SAAS,CAACO,SAAS,UAAAd,oBAAA,iBAAnBA,oBAAA,CAAqBe,MAAM,CAAC,CAAC,GAAI,GAAI,CAAAC,IAAI,CAAC,CACvD,CAAC,CACH,CAAE,MAAOC,KAAU,CAAE,CACnB,KAAM,IAAI,CAAAX,KAAK,CAAC,IAAI,CAACY,eAAe,CAACD,KAAK,CAACE,IAAI,CAAC,CAAC,CACnD,CACF,CAEA;AACA,KAAM,CAAAC,YAAYA,CAACC,UAAkB,CAAoB,CACvD,GAAI,KAAAC,qBAAA,CACF;AACA,KAAM,CAAAC,WAAW,CAAGhC,GAAG,CAACI,EAAE,CAAE,UAAU,CAAE0B,UAAU,CAAC,CACnD,KAAM,CAAAG,UAAU,CAAG,KAAM,CAAAhC,MAAM,CAAC+B,WAAW,CAAC,CAE5C,GAAI,CAACC,UAAU,CAACnB,MAAM,CAAC,CAAC,CAAE,CACxB,KAAM,IAAI,CAAAC,KAAK,CAAC,qBAAqB,CAAC,CACxC,CAEA,KAAM,CAAAmB,WAAW,CAAGD,UAAU,CAAChB,IAAI,CAAC,CAAC,CACrC,GAAI,CAACiB,WAAW,CAACC,QAAQ,CAAE,CACzB,KAAM,IAAI,CAAApB,KAAK,CAAC,iBAAiB,CAAC,CACpC,CAEA,MAAO,CACLG,EAAE,CAAEgB,WAAW,CAAChB,EAAE,CAClBX,KAAK,CAAE2B,WAAW,CAAC3B,KAAK,EAAI,EAAE,CAC9BY,IAAI,CAAE,SAAS,CACfC,IAAI,CAAEc,WAAW,CAACd,IAAI,CACtBC,MAAM,CAAEa,WAAW,CAACb,MAAM,CAC1BS,UAAU,CAAEA,UAAU,CACtBM,eAAe,CAAEF,WAAW,CAACE,eAAe,EAAI,EAAE,CAClDC,gBAAgB,CAAEH,WAAW,CAACG,gBAAgB,EAAI,EAAE,CACpDC,YAAY,CAAEJ,WAAW,CAACI,YAAY,EAAI,EAAE,CAC5Cf,SAAS,CAAE,EAAAQ,qBAAA,CAAAG,WAAW,CAACX,SAAS,UAAAQ,qBAAA,iBAArBA,qBAAA,CAAuBP,MAAM,CAAC,CAAC,GAAI,GAAI,CAAAC,IAAI,CAAC,CACzD,CAAC,CACH,CAAE,MAAOC,KAAU,CAAE,CACnB,KAAM,IAAI,CAAAX,KAAK,CAACW,KAAK,CAACa,OAAO,EAAI,yBAAyB,CAAC,CAC7D,CACF,CAEA;AACAC,kBAAkBA,CAAA,CAAW,CAC3B,MAAO,CAAAC,IAAI,CAACC,KAAK,CAAC,OAAO,CAAGD,IAAI,CAACE,MAAM,CAAC,CAAC,CAAG,OAAO,CAAC,CAACC,QAAQ,CAAC,CAAC,CACjE,CAEA;AACA,KAAM,CAAAC,aAAaA,CAACX,WAInB,CAAmB,CAClB,GAAI,CACF,KAAM,CAAAJ,UAAU,CAAG,IAAI,CAACU,kBAAkB,CAAC,CAAC,CAE5C;AACA,KAAM,CAAAM,eAAe,CAAG,KAAM,CAAA7C,MAAM,CAACD,GAAG,CAACI,EAAE,CAAE,UAAU,CAAE0B,UAAU,CAAC,CAAC,CACrE,GAAIgB,eAAe,CAAChC,MAAM,CAAC,CAAC,CAAE,CAC5B;AACA,MAAO,KAAI,CAAC+B,aAAa,CAACX,WAAW,CAAC,CACxC,CAEA,KAAM,CAAAa,OAAO,CAAG,CACd7B,EAAE,CAAEY,UAAU,CACdV,IAAI,CAAEc,WAAW,CAACd,IAAI,CACtBb,KAAK,CAAE2B,WAAW,CAAC3B,KAAK,EAAI,EAAE,CAC9BuB,UAAU,CAAEA,UAAU,CACtBM,eAAe,CAAEF,WAAW,CAACE,eAAe,EAAI,EAAE,CAClDC,gBAAgB,CAAE,EAAE,CACpBC,YAAY,CAAE,EAAE,CAChBH,QAAQ,CAAE,IAAI,CACdZ,SAAS,CAAE,GAAI,CAAAE,IAAI,CAAC,CACtB,CAAC,CAED,KAAM,CAAAvB,MAAM,CAACF,GAAG,CAACI,EAAE,CAAE,UAAU,CAAE0B,UAAU,CAAC,CAAEiB,OAAO,CAAC,CACtD,MAAO,CAAAjB,UAAU,CACnB,CAAE,MAAOJ,KAAU,CAAE,CACnB,KAAM,IAAI,CAAAX,KAAK,CAAC,0BAA0B,CAAC,CAC7C,CACF,CAEA;AACA,KAAM,CAAAiC,MAAMA,CAAA,CAAkB,CAC5B,GAAI,CACF,KAAM,CAAAlD,OAAO,CAACK,IAAI,CAAC,CACrB,CAAE,MAAOuB,KAAU,CAAE,CACnB,KAAM,IAAI,CAAAX,KAAK,CAAC,qBAAqB,CAAC,CACxC,CACF,CAEA;AACAkC,cAAcA,CAAA,CAAiC,CAC7C,MAAO,IAAI,CAAAC,OAAO,CAAEC,OAAO,EAAK,CAC9B,KAAM,CAAAC,WAAW,CAAGrD,kBAAkB,CAACI,IAAI,CAAGQ,IAAI,EAAK,CACrDyC,WAAW,CAAC,CAAC,CACbD,OAAO,CAACxC,IAAI,CAAC,CACf,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAEA;AACA0C,iBAAiBA,CAACC,QAA6C,CAAE,CAC/D,MAAO,CAAAvD,kBAAkB,CAACI,IAAI,CAAEmD,QAAQ,CAAC,CAC3C,CAEQ3B,eAAeA,CAAC4B,SAAiB,CAAU,CACjD,OAAQA,SAAS,EACf,IAAK,qBAAqB,CACxB,MAAO,oBAAoB,CAC7B,IAAK,qBAAqB,CACxB,MAAO,uBAAuB,CAChC,IAAK,oBAAoB,CACvB,MAAO,4BAA4B,CACrC,IAAK,oBAAoB,CACvB,MAAO,aAAa,CACtB,IAAK,wBAAwB,CAC3B,MAAO,qCAAqC,CAC9C,QACE,MAAO,yBAAyB,CACpC,CACF,CACF,CAEA,MAAO,MAAM,CAAAC,WAAW,CAAG,GAAI,CAAAnD,WAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}