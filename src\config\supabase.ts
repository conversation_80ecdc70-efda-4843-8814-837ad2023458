import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'YOUR_SUPABASE_URL';
const supabaseAnonKey = 'YOUR_SUPABASE_ANON_KEY';

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database types
export interface Course {
  id: string;
  title: string;
  description: string;
  category_id: string;
  instructor_id: string;
  thumbnail_url?: string;
  created_at: string;
  updated_at: string;
  is_active: boolean;
}

export interface Category {
  id: string;
  name: string;
  description?: string;
  created_at: string;
  is_active: boolean;
}

export interface Video {
  id: string;
  course_id: string;
  title: string;
  description?: string;
  video_url: string;
  duration?: number;
  order_index: number;
  created_at: string;
}

export interface Student {
  id: string;
  access_code: string;
  name?: string;
  email?: string;
  enrolled_courses: string[];
  created_at: string;
  is_active: boolean;
}

export interface Quiz {
  id: string;
  course_id: string;
  title: string;
  description?: string;
  questions: QuizQuestion[];
  passing_score: number;
  created_at: string;
}

export interface QuizQuestion {
  id: string;
  question: string;
  options: string[];
  correct_answer: number;
  points: number;
}

export interface Certificate {
  id: string;
  student_id: string;
  course_id: string;
  certificate_url: string;
  issued_at: string;
}
