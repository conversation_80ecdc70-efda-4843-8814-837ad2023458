{"ast": null, "code": "import { SubscriptionManager } from '../utils/subscription-manager.mjs';\nimport { velocityPerSecond } from '../utils/velocity-per-second.mjs';\nimport { warnOnce } from '../utils/warn-once.mjs';\nimport { frame, frameData } from '../frameloop/frame.mjs';\nconst isFloat = value => {\n  return !isNaN(parseFloat(value));\n};\nconst collectMotionValues = {\n  current: undefined\n};\n/**\n * `MotionValue` is used to track the state and velocity of motion values.\n *\n * @public\n */\nclass MotionValue {\n  /**\n   * @param init - The initiating value\n   * @param config - Optional configuration options\n   *\n   * -  `transformer`: A function to transform incoming values with.\n   *\n   * @internal\n   */\n  constructor(init, options = {}) {\n    /**\n     * This will be replaced by the build step with the latest version number.\n     * When MotionValues are provided to motion components, warn if versions are mixed.\n     */\n    this.version = \"10.18.0\";\n    /**\n     * Duration, in milliseconds, since last updating frame.\n     *\n     * @internal\n     */\n    this.timeDelta = 0;\n    /**\n     * Timestamp of the last time this `MotionValue` was updated.\n     *\n     * @internal\n     */\n    this.lastUpdated = 0;\n    /**\n     * Tracks whether this value can output a velocity. Currently this is only true\n     * if the value is numerical, but we might be able to widen the scope here and support\n     * other value types.\n     *\n     * @internal\n     */\n    this.canTrackVelocity = false;\n    /**\n     * An object containing a SubscriptionManager for each active event.\n     */\n    this.events = {};\n    this.updateAndNotify = (v, render = true) => {\n      this.prev = this.current;\n      this.current = v;\n      // Update timestamp\n      const {\n        delta,\n        timestamp\n      } = frameData;\n      if (this.lastUpdated !== timestamp) {\n        this.timeDelta = delta;\n        this.lastUpdated = timestamp;\n        frame.postRender(this.scheduleVelocityCheck);\n      }\n      // Update update subscribers\n      if (this.prev !== this.current && this.events.change) {\n        this.events.change.notify(this.current);\n      }\n      // Update velocity subscribers\n      if (this.events.velocityChange) {\n        this.events.velocityChange.notify(this.getVelocity());\n      }\n      // Update render subscribers\n      if (render && this.events.renderRequest) {\n        this.events.renderRequest.notify(this.current);\n      }\n    };\n    /**\n     * Schedule a velocity check for the next frame.\n     *\n     * This is an instanced and bound function to prevent generating a new\n     * function once per frame.\n     *\n     * @internal\n     */\n    this.scheduleVelocityCheck = () => frame.postRender(this.velocityCheck);\n    /**\n     * Updates `prev` with `current` if the value hasn't been updated this frame.\n     * This ensures velocity calculations return `0`.\n     *\n     * This is an instanced and bound function to prevent generating a new\n     * function once per frame.\n     *\n     * @internal\n     */\n    this.velocityCheck = ({\n      timestamp\n    }) => {\n      if (timestamp !== this.lastUpdated) {\n        this.prev = this.current;\n        if (this.events.velocityChange) {\n          this.events.velocityChange.notify(this.getVelocity());\n        }\n      }\n    };\n    this.hasAnimated = false;\n    this.prev = this.current = init;\n    this.canTrackVelocity = isFloat(this.current);\n    this.owner = options.owner;\n  }\n  /**\n   * Adds a function that will be notified when the `MotionValue` is updated.\n   *\n   * It returns a function that, when called, will cancel the subscription.\n   *\n   * When calling `onChange` inside a React component, it should be wrapped with the\n   * `useEffect` hook. As it returns an unsubscribe function, this should be returned\n   * from the `useEffect` function to ensure you don't add duplicate subscribers..\n   *\n   * ```jsx\n   * export const MyComponent = () => {\n   *   const x = useMotionValue(0)\n   *   const y = useMotionValue(0)\n   *   const opacity = useMotionValue(1)\n   *\n   *   useEffect(() => {\n   *     function updateOpacity() {\n   *       const maxXY = Math.max(x.get(), y.get())\n   *       const newOpacity = transform(maxXY, [0, 100], [1, 0])\n   *       opacity.set(newOpacity)\n   *     }\n   *\n   *     const unsubscribeX = x.on(\"change\", updateOpacity)\n   *     const unsubscribeY = y.on(\"change\", updateOpacity)\n   *\n   *     return () => {\n   *       unsubscribeX()\n   *       unsubscribeY()\n   *     }\n   *   }, [])\n   *\n   *   return <motion.div style={{ x }} />\n   * }\n   * ```\n   *\n   * @param subscriber - A function that receives the latest value.\n   * @returns A function that, when called, will cancel this subscription.\n   *\n   * @deprecated\n   */\n  onChange(subscription) {\n    if (process.env.NODE_ENV !== \"production\") {\n      warnOnce(false, `value.onChange(callback) is deprecated. Switch to value.on(\"change\", callback).`);\n    }\n    return this.on(\"change\", subscription);\n  }\n  on(eventName, callback) {\n    if (!this.events[eventName]) {\n      this.events[eventName] = new SubscriptionManager();\n    }\n    const unsubscribe = this.events[eventName].add(callback);\n    if (eventName === \"change\") {\n      return () => {\n        unsubscribe();\n        /**\n         * If we have no more change listeners by the start\n         * of the next frame, stop active animations.\n         */\n        frame.read(() => {\n          if (!this.events.change.getSize()) {\n            this.stop();\n          }\n        });\n      };\n    }\n    return unsubscribe;\n  }\n  clearListeners() {\n    for (const eventManagers in this.events) {\n      this.events[eventManagers].clear();\n    }\n  }\n  /**\n   * Attaches a passive effect to the `MotionValue`.\n   *\n   * @internal\n   */\n  attach(passiveEffect, stopPassiveEffect) {\n    this.passiveEffect = passiveEffect;\n    this.stopPassiveEffect = stopPassiveEffect;\n  }\n  /**\n   * Sets the state of the `MotionValue`.\n   *\n   * @remarks\n   *\n   * ```jsx\n   * const x = useMotionValue(0)\n   * x.set(10)\n   * ```\n   *\n   * @param latest - Latest value to set.\n   * @param render - Whether to notify render subscribers. Defaults to `true`\n   *\n   * @public\n   */\n  set(v, render = true) {\n    if (!render || !this.passiveEffect) {\n      this.updateAndNotify(v, render);\n    } else {\n      this.passiveEffect(v, this.updateAndNotify);\n    }\n  }\n  setWithVelocity(prev, current, delta) {\n    this.set(current);\n    this.prev = prev;\n    this.timeDelta = delta;\n  }\n  /**\n   * Set the state of the `MotionValue`, stopping any active animations,\n   * effects, and resets velocity to `0`.\n   */\n  jump(v) {\n    this.updateAndNotify(v);\n    this.prev = v;\n    this.stop();\n    if (this.stopPassiveEffect) this.stopPassiveEffect();\n  }\n  /**\n   * Returns the latest state of `MotionValue`\n   *\n   * @returns - The latest state of `MotionValue`\n   *\n   * @public\n   */\n  get() {\n    if (collectMotionValues.current) {\n      collectMotionValues.current.push(this);\n    }\n    return this.current;\n  }\n  /**\n   * @public\n   */\n  getPrevious() {\n    return this.prev;\n  }\n  /**\n   * Returns the latest velocity of `MotionValue`\n   *\n   * @returns - The latest velocity of `MotionValue`. Returns `0` if the state is non-numerical.\n   *\n   * @public\n   */\n  getVelocity() {\n    // This could be isFloat(this.prev) && isFloat(this.current), but that would be wasteful\n    return this.canTrackVelocity ?\n    // These casts could be avoided if parseFloat would be typed better\n    velocityPerSecond(parseFloat(this.current) - parseFloat(this.prev), this.timeDelta) : 0;\n  }\n  /**\n   * Registers a new animation to control this `MotionValue`. Only one\n   * animation can drive a `MotionValue` at one time.\n   *\n   * ```jsx\n   * value.start()\n   * ```\n   *\n   * @param animation - A function that starts the provided animation\n   *\n   * @internal\n   */\n  start(startAnimation) {\n    this.stop();\n    return new Promise(resolve => {\n      this.hasAnimated = true;\n      this.animation = startAnimation(resolve);\n      if (this.events.animationStart) {\n        this.events.animationStart.notify();\n      }\n    }).then(() => {\n      if (this.events.animationComplete) {\n        this.events.animationComplete.notify();\n      }\n      this.clearAnimation();\n    });\n  }\n  /**\n   * Stop the currently active animation.\n   *\n   * @public\n   */\n  stop() {\n    if (this.animation) {\n      this.animation.stop();\n      if (this.events.animationCancel) {\n        this.events.animationCancel.notify();\n      }\n    }\n    this.clearAnimation();\n  }\n  /**\n   * Returns `true` if this value is currently animating.\n   *\n   * @public\n   */\n  isAnimating() {\n    return !!this.animation;\n  }\n  clearAnimation() {\n    delete this.animation;\n  }\n  /**\n   * Destroy and clean up subscribers to this `MotionValue`.\n   *\n   * The `MotionValue` hooks like `useMotionValue` and `useTransform` automatically\n   * handle the lifecycle of the returned `MotionValue`, so this method is only necessary if you've manually\n   * created a `MotionValue` via the `motionValue` function.\n   *\n   * @public\n   */\n  destroy() {\n    this.clearListeners();\n    this.stop();\n    if (this.stopPassiveEffect) {\n      this.stopPassiveEffect();\n    }\n  }\n}\nfunction motionValue(init, options) {\n  return new MotionValue(init, options);\n}\nexport { MotionValue, collectMotionValues, motionValue };", "map": {"version": 3, "names": ["SubscriptionManager", "velocityPerSecond", "warnOnce", "frame", "frameData", "isFloat", "value", "isNaN", "parseFloat", "collectMotionValues", "current", "undefined", "MotionValue", "constructor", "init", "options", "version", "<PERSON><PERSON><PERSON><PERSON>", "lastUpdated", "canTrackVelocity", "events", "updateAndNotify", "v", "render", "prev", "delta", "timestamp", "postRender", "scheduleVelocityCheck", "change", "notify", "velocityChange", "getVelocity", "renderRequest", "velocityCheck", "hasAnimated", "owner", "onChange", "subscription", "process", "env", "NODE_ENV", "on", "eventName", "callback", "unsubscribe", "add", "read", "getSize", "stop", "clearListeners", "eventManagers", "clear", "attach", "passiveEffect", "stopPassiveEffect", "set", "setWithVelocity", "jump", "get", "push", "getPrevious", "start", "startAnimation", "Promise", "resolve", "animation", "animationStart", "then", "animationComplete", "clearAnimation", "animationCancel", "isAnimating", "destroy", "motionValue"], "sources": ["C:/Users/<USER>/Desktop/مشروع/node_modules/framer-motion/dist/es/value/index.mjs"], "sourcesContent": ["import { SubscriptionManager } from '../utils/subscription-manager.mjs';\nimport { velocityPerSecond } from '../utils/velocity-per-second.mjs';\nimport { warnOnce } from '../utils/warn-once.mjs';\nimport { frame, frameData } from '../frameloop/frame.mjs';\n\nconst isFloat = (value) => {\n    return !isNaN(parseFloat(value));\n};\nconst collectMotionValues = {\n    current: undefined,\n};\n/**\n * `MotionValue` is used to track the state and velocity of motion values.\n *\n * @public\n */\nclass MotionValue {\n    /**\n     * @param init - The initiating value\n     * @param config - Optional configuration options\n     *\n     * -  `transformer`: A function to transform incoming values with.\n     *\n     * @internal\n     */\n    constructor(init, options = {}) {\n        /**\n         * This will be replaced by the build step with the latest version number.\n         * When MotionValues are provided to motion components, warn if versions are mixed.\n         */\n        this.version = \"10.18.0\";\n        /**\n         * Duration, in milliseconds, since last updating frame.\n         *\n         * @internal\n         */\n        this.timeDelta = 0;\n        /**\n         * Timestamp of the last time this `MotionValue` was updated.\n         *\n         * @internal\n         */\n        this.lastUpdated = 0;\n        /**\n         * Tracks whether this value can output a velocity. Currently this is only true\n         * if the value is numerical, but we might be able to widen the scope here and support\n         * other value types.\n         *\n         * @internal\n         */\n        this.canTrackVelocity = false;\n        /**\n         * An object containing a SubscriptionManager for each active event.\n         */\n        this.events = {};\n        this.updateAndNotify = (v, render = true) => {\n            this.prev = this.current;\n            this.current = v;\n            // Update timestamp\n            const { delta, timestamp } = frameData;\n            if (this.lastUpdated !== timestamp) {\n                this.timeDelta = delta;\n                this.lastUpdated = timestamp;\n                frame.postRender(this.scheduleVelocityCheck);\n            }\n            // Update update subscribers\n            if (this.prev !== this.current && this.events.change) {\n                this.events.change.notify(this.current);\n            }\n            // Update velocity subscribers\n            if (this.events.velocityChange) {\n                this.events.velocityChange.notify(this.getVelocity());\n            }\n            // Update render subscribers\n            if (render && this.events.renderRequest) {\n                this.events.renderRequest.notify(this.current);\n            }\n        };\n        /**\n         * Schedule a velocity check for the next frame.\n         *\n         * This is an instanced and bound function to prevent generating a new\n         * function once per frame.\n         *\n         * @internal\n         */\n        this.scheduleVelocityCheck = () => frame.postRender(this.velocityCheck);\n        /**\n         * Updates `prev` with `current` if the value hasn't been updated this frame.\n         * This ensures velocity calculations return `0`.\n         *\n         * This is an instanced and bound function to prevent generating a new\n         * function once per frame.\n         *\n         * @internal\n         */\n        this.velocityCheck = ({ timestamp }) => {\n            if (timestamp !== this.lastUpdated) {\n                this.prev = this.current;\n                if (this.events.velocityChange) {\n                    this.events.velocityChange.notify(this.getVelocity());\n                }\n            }\n        };\n        this.hasAnimated = false;\n        this.prev = this.current = init;\n        this.canTrackVelocity = isFloat(this.current);\n        this.owner = options.owner;\n    }\n    /**\n     * Adds a function that will be notified when the `MotionValue` is updated.\n     *\n     * It returns a function that, when called, will cancel the subscription.\n     *\n     * When calling `onChange` inside a React component, it should be wrapped with the\n     * `useEffect` hook. As it returns an unsubscribe function, this should be returned\n     * from the `useEffect` function to ensure you don't add duplicate subscribers..\n     *\n     * ```jsx\n     * export const MyComponent = () => {\n     *   const x = useMotionValue(0)\n     *   const y = useMotionValue(0)\n     *   const opacity = useMotionValue(1)\n     *\n     *   useEffect(() => {\n     *     function updateOpacity() {\n     *       const maxXY = Math.max(x.get(), y.get())\n     *       const newOpacity = transform(maxXY, [0, 100], [1, 0])\n     *       opacity.set(newOpacity)\n     *     }\n     *\n     *     const unsubscribeX = x.on(\"change\", updateOpacity)\n     *     const unsubscribeY = y.on(\"change\", updateOpacity)\n     *\n     *     return () => {\n     *       unsubscribeX()\n     *       unsubscribeY()\n     *     }\n     *   }, [])\n     *\n     *   return <motion.div style={{ x }} />\n     * }\n     * ```\n     *\n     * @param subscriber - A function that receives the latest value.\n     * @returns A function that, when called, will cancel this subscription.\n     *\n     * @deprecated\n     */\n    onChange(subscription) {\n        if (process.env.NODE_ENV !== \"production\") {\n            warnOnce(false, `value.onChange(callback) is deprecated. Switch to value.on(\"change\", callback).`);\n        }\n        return this.on(\"change\", subscription);\n    }\n    on(eventName, callback) {\n        if (!this.events[eventName]) {\n            this.events[eventName] = new SubscriptionManager();\n        }\n        const unsubscribe = this.events[eventName].add(callback);\n        if (eventName === \"change\") {\n            return () => {\n                unsubscribe();\n                /**\n                 * If we have no more change listeners by the start\n                 * of the next frame, stop active animations.\n                 */\n                frame.read(() => {\n                    if (!this.events.change.getSize()) {\n                        this.stop();\n                    }\n                });\n            };\n        }\n        return unsubscribe;\n    }\n    clearListeners() {\n        for (const eventManagers in this.events) {\n            this.events[eventManagers].clear();\n        }\n    }\n    /**\n     * Attaches a passive effect to the `MotionValue`.\n     *\n     * @internal\n     */\n    attach(passiveEffect, stopPassiveEffect) {\n        this.passiveEffect = passiveEffect;\n        this.stopPassiveEffect = stopPassiveEffect;\n    }\n    /**\n     * Sets the state of the `MotionValue`.\n     *\n     * @remarks\n     *\n     * ```jsx\n     * const x = useMotionValue(0)\n     * x.set(10)\n     * ```\n     *\n     * @param latest - Latest value to set.\n     * @param render - Whether to notify render subscribers. Defaults to `true`\n     *\n     * @public\n     */\n    set(v, render = true) {\n        if (!render || !this.passiveEffect) {\n            this.updateAndNotify(v, render);\n        }\n        else {\n            this.passiveEffect(v, this.updateAndNotify);\n        }\n    }\n    setWithVelocity(prev, current, delta) {\n        this.set(current);\n        this.prev = prev;\n        this.timeDelta = delta;\n    }\n    /**\n     * Set the state of the `MotionValue`, stopping any active animations,\n     * effects, and resets velocity to `0`.\n     */\n    jump(v) {\n        this.updateAndNotify(v);\n        this.prev = v;\n        this.stop();\n        if (this.stopPassiveEffect)\n            this.stopPassiveEffect();\n    }\n    /**\n     * Returns the latest state of `MotionValue`\n     *\n     * @returns - The latest state of `MotionValue`\n     *\n     * @public\n     */\n    get() {\n        if (collectMotionValues.current) {\n            collectMotionValues.current.push(this);\n        }\n        return this.current;\n    }\n    /**\n     * @public\n     */\n    getPrevious() {\n        return this.prev;\n    }\n    /**\n     * Returns the latest velocity of `MotionValue`\n     *\n     * @returns - The latest velocity of `MotionValue`. Returns `0` if the state is non-numerical.\n     *\n     * @public\n     */\n    getVelocity() {\n        // This could be isFloat(this.prev) && isFloat(this.current), but that would be wasteful\n        return this.canTrackVelocity\n            ? // These casts could be avoided if parseFloat would be typed better\n                velocityPerSecond(parseFloat(this.current) -\n                    parseFloat(this.prev), this.timeDelta)\n            : 0;\n    }\n    /**\n     * Registers a new animation to control this `MotionValue`. Only one\n     * animation can drive a `MotionValue` at one time.\n     *\n     * ```jsx\n     * value.start()\n     * ```\n     *\n     * @param animation - A function that starts the provided animation\n     *\n     * @internal\n     */\n    start(startAnimation) {\n        this.stop();\n        return new Promise((resolve) => {\n            this.hasAnimated = true;\n            this.animation = startAnimation(resolve);\n            if (this.events.animationStart) {\n                this.events.animationStart.notify();\n            }\n        }).then(() => {\n            if (this.events.animationComplete) {\n                this.events.animationComplete.notify();\n            }\n            this.clearAnimation();\n        });\n    }\n    /**\n     * Stop the currently active animation.\n     *\n     * @public\n     */\n    stop() {\n        if (this.animation) {\n            this.animation.stop();\n            if (this.events.animationCancel) {\n                this.events.animationCancel.notify();\n            }\n        }\n        this.clearAnimation();\n    }\n    /**\n     * Returns `true` if this value is currently animating.\n     *\n     * @public\n     */\n    isAnimating() {\n        return !!this.animation;\n    }\n    clearAnimation() {\n        delete this.animation;\n    }\n    /**\n     * Destroy and clean up subscribers to this `MotionValue`.\n     *\n     * The `MotionValue` hooks like `useMotionValue` and `useTransform` automatically\n     * handle the lifecycle of the returned `MotionValue`, so this method is only necessary if you've manually\n     * created a `MotionValue` via the `motionValue` function.\n     *\n     * @public\n     */\n    destroy() {\n        this.clearListeners();\n        this.stop();\n        if (this.stopPassiveEffect) {\n            this.stopPassiveEffect();\n        }\n    }\n}\nfunction motionValue(init, options) {\n    return new MotionValue(init, options);\n}\n\nexport { MotionValue, collectMotionValues, motionValue };\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,KAAK,EAAEC,SAAS,QAAQ,wBAAwB;AAEzD,MAAMC,OAAO,GAAIC,KAAK,IAAK;EACvB,OAAO,CAACC,KAAK,CAACC,UAAU,CAACF,KAAK,CAAC,CAAC;AACpC,CAAC;AACD,MAAMG,mBAAmB,GAAG;EACxBC,OAAO,EAAEC;AACb,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EACd;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,WAAWA,CAACC,IAAI,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC5B;AACR;AACA;AACA;IACQ,IAAI,CAACC,OAAO,GAAG,SAAS;IACxB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B;AACR;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAACC,eAAe,GAAG,CAACC,CAAC,EAAEC,MAAM,GAAG,IAAI,KAAK;MACzC,IAAI,CAACC,IAAI,GAAG,IAAI,CAACd,OAAO;MACxB,IAAI,CAACA,OAAO,GAAGY,CAAC;MAChB;MACA,MAAM;QAAEG,KAAK;QAAEC;MAAU,CAAC,GAAGtB,SAAS;MACtC,IAAI,IAAI,CAACc,WAAW,KAAKQ,SAAS,EAAE;QAChC,IAAI,CAACT,SAAS,GAAGQ,KAAK;QACtB,IAAI,CAACP,WAAW,GAAGQ,SAAS;QAC5BvB,KAAK,CAACwB,UAAU,CAAC,IAAI,CAACC,qBAAqB,CAAC;MAChD;MACA;MACA,IAAI,IAAI,CAACJ,IAAI,KAAK,IAAI,CAACd,OAAO,IAAI,IAAI,CAACU,MAAM,CAACS,MAAM,EAAE;QAClD,IAAI,CAACT,MAAM,CAACS,MAAM,CAACC,MAAM,CAAC,IAAI,CAACpB,OAAO,CAAC;MAC3C;MACA;MACA,IAAI,IAAI,CAACU,MAAM,CAACW,cAAc,EAAE;QAC5B,IAAI,CAACX,MAAM,CAACW,cAAc,CAACD,MAAM,CAAC,IAAI,CAACE,WAAW,CAAC,CAAC,CAAC;MACzD;MACA;MACA,IAAIT,MAAM,IAAI,IAAI,CAACH,MAAM,CAACa,aAAa,EAAE;QACrC,IAAI,CAACb,MAAM,CAACa,aAAa,CAACH,MAAM,CAAC,IAAI,CAACpB,OAAO,CAAC;MAClD;IACJ,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACkB,qBAAqB,GAAG,MAAMzB,KAAK,CAACwB,UAAU,CAAC,IAAI,CAACO,aAAa,CAAC;IACvE;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACA,aAAa,GAAG,CAAC;MAAER;IAAU,CAAC,KAAK;MACpC,IAAIA,SAAS,KAAK,IAAI,CAACR,WAAW,EAAE;QAChC,IAAI,CAACM,IAAI,GAAG,IAAI,CAACd,OAAO;QACxB,IAAI,IAAI,CAACU,MAAM,CAACW,cAAc,EAAE;UAC5B,IAAI,CAACX,MAAM,CAACW,cAAc,CAACD,MAAM,CAAC,IAAI,CAACE,WAAW,CAAC,CAAC,CAAC;QACzD;MACJ;IACJ,CAAC;IACD,IAAI,CAACG,WAAW,GAAG,KAAK;IACxB,IAAI,CAACX,IAAI,GAAG,IAAI,CAACd,OAAO,GAAGI,IAAI;IAC/B,IAAI,CAACK,gBAAgB,GAAGd,OAAO,CAAC,IAAI,CAACK,OAAO,CAAC;IAC7C,IAAI,CAAC0B,KAAK,GAAGrB,OAAO,CAACqB,KAAK;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,QAAQA,CAACC,YAAY,EAAE;IACnB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACvCvC,QAAQ,CAAC,KAAK,EAAE,iFAAiF,CAAC;IACtG;IACA,OAAO,IAAI,CAACwC,EAAE,CAAC,QAAQ,EAAEJ,YAAY,CAAC;EAC1C;EACAI,EAAEA,CAACC,SAAS,EAAEC,QAAQ,EAAE;IACpB,IAAI,CAAC,IAAI,CAACxB,MAAM,CAACuB,SAAS,CAAC,EAAE;MACzB,IAAI,CAACvB,MAAM,CAACuB,SAAS,CAAC,GAAG,IAAI3C,mBAAmB,CAAC,CAAC;IACtD;IACA,MAAM6C,WAAW,GAAG,IAAI,CAACzB,MAAM,CAACuB,SAAS,CAAC,CAACG,GAAG,CAACF,QAAQ,CAAC;IACxD,IAAID,SAAS,KAAK,QAAQ,EAAE;MACxB,OAAO,MAAM;QACTE,WAAW,CAAC,CAAC;QACb;AAChB;AACA;AACA;QACgB1C,KAAK,CAAC4C,IAAI,CAAC,MAAM;UACb,IAAI,CAAC,IAAI,CAAC3B,MAAM,CAACS,MAAM,CAACmB,OAAO,CAAC,CAAC,EAAE;YAC/B,IAAI,CAACC,IAAI,CAAC,CAAC;UACf;QACJ,CAAC,CAAC;MACN,CAAC;IACL;IACA,OAAOJ,WAAW;EACtB;EACAK,cAAcA,CAAA,EAAG;IACb,KAAK,MAAMC,aAAa,IAAI,IAAI,CAAC/B,MAAM,EAAE;MACrC,IAAI,CAACA,MAAM,CAAC+B,aAAa,CAAC,CAACC,KAAK,CAAC,CAAC;IACtC;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIC,MAAMA,CAACC,aAAa,EAAEC,iBAAiB,EAAE;IACrC,IAAI,CAACD,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;EAC9C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,GAAGA,CAAClC,CAAC,EAAEC,MAAM,GAAG,IAAI,EAAE;IAClB,IAAI,CAACA,MAAM,IAAI,CAAC,IAAI,CAAC+B,aAAa,EAAE;MAChC,IAAI,CAACjC,eAAe,CAACC,CAAC,EAAEC,MAAM,CAAC;IACnC,CAAC,MACI;MACD,IAAI,CAAC+B,aAAa,CAAChC,CAAC,EAAE,IAAI,CAACD,eAAe,CAAC;IAC/C;EACJ;EACAoC,eAAeA,CAACjC,IAAI,EAAEd,OAAO,EAAEe,KAAK,EAAE;IAClC,IAAI,CAAC+B,GAAG,CAAC9C,OAAO,CAAC;IACjB,IAAI,CAACc,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACP,SAAS,GAAGQ,KAAK;EAC1B;EACA;AACJ;AACA;AACA;EACIiC,IAAIA,CAACpC,CAAC,EAAE;IACJ,IAAI,CAACD,eAAe,CAACC,CAAC,CAAC;IACvB,IAAI,CAACE,IAAI,GAAGF,CAAC;IACb,IAAI,CAAC2B,IAAI,CAAC,CAAC;IACX,IAAI,IAAI,CAACM,iBAAiB,EACtB,IAAI,CAACA,iBAAiB,CAAC,CAAC;EAChC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACII,GAAGA,CAAA,EAAG;IACF,IAAIlD,mBAAmB,CAACC,OAAO,EAAE;MAC7BD,mBAAmB,CAACC,OAAO,CAACkD,IAAI,CAAC,IAAI,CAAC;IAC1C;IACA,OAAO,IAAI,CAAClD,OAAO;EACvB;EACA;AACJ;AACA;EACImD,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACrC,IAAI;EACpB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIQ,WAAWA,CAAA,EAAG;IACV;IACA,OAAO,IAAI,CAACb,gBAAgB;IACtB;IACElB,iBAAiB,CAACO,UAAU,CAAC,IAAI,CAACE,OAAO,CAAC,GACtCF,UAAU,CAAC,IAAI,CAACgB,IAAI,CAAC,EAAE,IAAI,CAACP,SAAS,CAAC,GAC5C,CAAC;EACX;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI6C,KAAKA,CAACC,cAAc,EAAE;IAClB,IAAI,CAACd,IAAI,CAAC,CAAC;IACX,OAAO,IAAIe,OAAO,CAAEC,OAAO,IAAK;MAC5B,IAAI,CAAC9B,WAAW,GAAG,IAAI;MACvB,IAAI,CAAC+B,SAAS,GAAGH,cAAc,CAACE,OAAO,CAAC;MACxC,IAAI,IAAI,CAAC7C,MAAM,CAAC+C,cAAc,EAAE;QAC5B,IAAI,CAAC/C,MAAM,CAAC+C,cAAc,CAACrC,MAAM,CAAC,CAAC;MACvC;IACJ,CAAC,CAAC,CAACsC,IAAI,CAAC,MAAM;MACV,IAAI,IAAI,CAAChD,MAAM,CAACiD,iBAAiB,EAAE;QAC/B,IAAI,CAACjD,MAAM,CAACiD,iBAAiB,CAACvC,MAAM,CAAC,CAAC;MAC1C;MACA,IAAI,CAACwC,cAAc,CAAC,CAAC;IACzB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIrB,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACiB,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACjB,IAAI,CAAC,CAAC;MACrB,IAAI,IAAI,CAAC7B,MAAM,CAACmD,eAAe,EAAE;QAC7B,IAAI,CAACnD,MAAM,CAACmD,eAAe,CAACzC,MAAM,CAAC,CAAC;MACxC;IACJ;IACA,IAAI,CAACwC,cAAc,CAAC,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;EACIE,WAAWA,CAAA,EAAG;IACV,OAAO,CAAC,CAAC,IAAI,CAACN,SAAS;EAC3B;EACAI,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACJ,SAAS;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIO,OAAOA,CAAA,EAAG;IACN,IAAI,CAACvB,cAAc,CAAC,CAAC;IACrB,IAAI,CAACD,IAAI,CAAC,CAAC;IACX,IAAI,IAAI,CAACM,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAAC,CAAC;IAC5B;EACJ;AACJ;AACA,SAASmB,WAAWA,CAAC5D,IAAI,EAAEC,OAAO,EAAE;EAChC,OAAO,IAAIH,WAAW,CAACE,IAAI,EAAEC,OAAO,CAAC;AACzC;AAEA,SAASH,WAAW,EAAEH,mBAAmB,EAAEiE,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}