{"ast": null, "code": "import { Group } from './Group.mjs';\nimport { Item } from './Item.mjs';\nconst Reorder = {\n  Group,\n  Item\n};\nexport { Reorder };", "map": {"version": 3, "names": ["Group", "<PERSON><PERSON>", "Reorder"], "sources": ["C:/Users/<USER>/Desktop/مشروع/node_modules/framer-motion/dist/es/components/Reorder/index.mjs"], "sourcesContent": ["import { Group } from './Group.mjs';\nimport { Item } from './Item.mjs';\n\nconst Reorder = {\n    Group,\n    Item,\n};\n\nexport { Reorder };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,aAAa;AACnC,SAASC,IAAI,QAAQ,YAAY;AAEjC,MAAMC,OAAO,GAAG;EACZF,KAAK;EACLC;AACJ,CAAC;AAED,SAASC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}