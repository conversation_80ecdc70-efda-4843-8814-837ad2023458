{"ast": null, "code": "import { createContext } from 'react';\nconst LazyContext = createContext({\n  strict: false\n});\nexport { LazyContext };", "map": {"version": 3, "names": ["createContext", "LazyContext", "strict"], "sources": ["C:/Users/<USER>/Desktop/مشروع/node_modules/framer-motion/dist/es/context/LazyContext.mjs"], "sourcesContent": ["import { createContext } from 'react';\n\nconst LazyContext = createContext({ strict: false });\n\nexport { LazyContext };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;AAErC,MAAMC,WAAW,GAAGD,aAAa,CAAC;EAAEE,MAAM,EAAE;AAAM,CAAC,CAAC;AAEpD,SAASD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}