{"ast": null, "code": "const isEasingArray = ease => {\n  return Array.isArray(ease) && typeof ease[0] !== \"number\";\n};\nexport { isEasingArray };", "map": {"version": 3, "names": ["isEasingArray", "ease", "Array", "isArray"], "sources": ["C:/Users/<USER>/Desktop/مشروع/node_modules/framer-motion/dist/es/easing/utils/is-easing-array.mjs"], "sourcesContent": ["const isEasingArray = (ease) => {\n    return Array.isArray(ease) && typeof ease[0] !== \"number\";\n};\n\nexport { isEasingArray };\n"], "mappings": "AAAA,MAAMA,aAAa,GAAIC,IAAI,IAAK;EAC5B,OAAOC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ;AAC7D,CAAC;AAED,SAASD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}