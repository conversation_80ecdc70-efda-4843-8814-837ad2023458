{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0634\\u0631\\u0648\\u0639\\\\src\\\\components\\\\Student\\\\QuizPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { ClipboardDocumentListIcon, ClockIcon, CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/outline';\n\n// Types\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizPage = ({\n  user,\n  quizId,\n  onBack\n}) => {\n  _s();\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [answers, setAnswers] = useState({});\n  const [timeLeft, setTimeLeft] = useState(1800); // 30 minutes in seconds\n  const [quizCompleted, setQuizCompleted] = useState(false);\n  const [score, setScore] = useState(null);\n\n  // Mock quiz data\n  const quiz = {\n    id: quizId,\n    title: 'اختبار أساسيات البرمجة',\n    description: 'اختبار شامل لأساسيات البرمجة',\n    timeLimit: 30,\n    passingScore: 70,\n    questions: [{\n      id: 1,\n      question: 'ما هو المتغير في البرمجة؟',\n      type: 'multiple-choice',\n      options: ['مكان لتخزين البيانات', 'نوع من الدوال', 'أمر برمجي', 'لا شيء مما سبق'],\n      correctAnswer: 0\n    }, {\n      id: 2,\n      question: 'أي من التالي يُستخدم لإنشاء حلقة تكرارية؟',\n      type: 'multiple-choice',\n      options: ['if', 'for', 'function', 'variable'],\n      correctAnswer: 1\n    }, {\n      id: 3,\n      question: 'البرمجة الكائنية تعتمد على مفهوم الكلاسات',\n      type: 'true-false',\n      options: ['صحيح', 'خطأ'],\n      correctAnswer: 0\n    }]\n  };\n\n  // Format time\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n  const handleAnswerSelect = answerIndex => {\n    setAnswers(prev => ({\n      ...prev,\n      [currentQuestion]: answerIndex\n    }));\n  };\n  const handleNextQuestion = () => {\n    if (currentQuestion < quiz.questions.length - 1) {\n      setCurrentQuestion(prev => prev + 1);\n    }\n  };\n  const handlePrevQuestion = () => {\n    if (currentQuestion > 0) {\n      setCurrentQuestion(prev => prev - 1);\n    }\n  };\n  const handleSubmitQuiz = () => {\n    // Calculate score\n    let correctAnswers = 0;\n    quiz.questions.forEach((question, index) => {\n      if (answers[index] === question.correctAnswer) {\n        correctAnswers++;\n      }\n    });\n    const finalScore = Math.round(correctAnswers / quiz.questions.length * 100);\n    setScore(finalScore);\n    setQuizCompleted(true);\n  };\n  const currentQuestionData = quiz.questions[currentQuestion];\n  const progress = (currentQuestion + 1) / quiz.questions.length * 100;\n  if (quizCompleted) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4 space-x-reverse\",\n        children: [onBack && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onBack,\n          className: \"p-2 text-gray-600 hover:text-gray-900 transition-colors\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M15 19l-7-7 7-7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: \"\\u0646\\u062A\\u064A\\u062C\\u0629 \\u0627\\u0644\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: quiz.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"bg-white rounded-lg shadow-sm p-8 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [score >= quiz.passingScore ? /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n            className: \"w-16 h-16 text-green-600 mx-auto mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(XCircleIcon, {\n            className: \"w-16 h-16 text-red-600 mx-auto mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl font-bold text-gray-900 mb-2\",\n            children: [score, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: `text-lg ${score >= quiz.passingScore ? 'text-green-600' : 'text-red-600'}`,\n            children: score >= quiz.passingScore ? 'مبروك! لقد نجحت في الاختبار' : 'للأسف، لم تحقق الدرجة المطلوبة'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-50 rounded-lg p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: \"\\u0627\\u0644\\u0646\\u062A\\u064A\\u062C\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: [score, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-50 rounded-lg p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: \"\\u0627\\u0644\\u062F\\u0631\\u062C\\u0629 \\u0627\\u0644\\u0645\\u0637\\u0644\\u0648\\u0628\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: [quiz.passingScore, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-50 rounded-lg p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: \"\\u0627\\u0644\\u0623\\u0633\\u0626\\u0644\\u0629 \\u0627\\u0644\\u0635\\u062D\\u064A\\u062D\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: [Math.round(score / 100 * quiz.questions.length), \"/\", quiz.questions.length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center space-x-4 space-x-reverse\",\n          children: [onBack && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onBack,\n            className: \"px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n            children: \"\\u0627\\u0644\\u0639\\u0648\\u062F\\u0629 \\u0644\\u0644\\u0643\\u0648\\u0631\\u0633\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this), score < quiz.passingScore && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setQuizCompleted(false);\n              setCurrentQuestion(0);\n              setAnswers({});\n              setScore(null);\n            },\n            className: \"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n            children: \"\\u0625\\u0639\\u0627\\u062F\\u0629 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0648\\u0644\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4 space-x-reverse\",\n        children: [onBack && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onBack,\n          className: \"p-2 text-gray-600 hover:text-gray-900 transition-colors\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M15 19l-7-7 7-7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: quiz.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: quiz.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2 space-x-reverse bg-red-50 px-4 py-2 rounded-lg\",\n        children: [/*#__PURE__*/_jsxDEV(ClockIcon, {\n          className: \"w-5 h-5 text-red-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-red-600 font-medium\",\n          children: formatTime(timeLeft)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm text-gray-600\",\n          children: [\"\\u0627\\u0644\\u0633\\u0624\\u0627\\u0644 \", currentQuestion + 1, \" \\u0645\\u0646 \", quiz.questions.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm text-gray-600\",\n          children: [Math.round(progress), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full bg-gray-200 rounded-full h-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n          style: {\n            width: `${progress}%`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        x: 20\n      },\n      animate: {\n        opacity: 1,\n        x: 0\n      },\n      className: \"bg-white rounded-lg shadow-sm p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3 space-x-reverse mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(ClipboardDocumentListIcon, {\n            className: \"w-6 h-6 text-blue-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: [\"\\u0627\\u0644\\u0633\\u0624\\u0627\\u0644 \", currentQuestion + 1]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-900 text-lg leading-relaxed\",\n          children: currentQuestionData.question\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3 mb-6\",\n        children: currentQuestionData.options.map((option, index) => /*#__PURE__*/_jsxDEV(\"label\", {\n          className: `flex items-center p-4 rounded-lg border-2 cursor-pointer transition-colors ${answers[currentQuestion] === index ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"radio\",\n            name: `question-${currentQuestion}`,\n            value: index,\n            checked: answers[currentQuestion] === index,\n            onChange: () => handleAnswerSelect(index),\n            className: \"sr-only\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `w-4 h-4 rounded-full border-2 mr-3 ${answers[currentQuestion] === index ? 'border-blue-500 bg-blue-500' : 'border-gray-300'}`,\n            children: answers[currentQuestion] === index && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-2 h-2 bg-white rounded-full mx-auto mt-0.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-900\",\n            children: option\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handlePrevQuestion,\n          disabled: currentQuestion === 0,\n          className: \"px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n          children: \"\\u0627\\u0644\\u0633\\u0624\\u0627\\u0644 \\u0627\\u0644\\u0633\\u0627\\u0628\\u0642\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-2 space-x-reverse\",\n          children: currentQuestion === quiz.questions.length - 1 ? /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSubmitQuiz,\n            className: \"px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n            children: \"\\u0625\\u0646\\u0647\\u0627\\u0621 \\u0627\\u0644\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleNextQuestion,\n            className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n            children: \"\\u0627\\u0644\\u0633\\u0624\\u0627\\u0644 \\u0627\\u0644\\u062A\\u0627\\u0644\\u064A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 9\n      }, this)]\n    }, currentQuestion, true, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 197,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizPage, \"vbGefPxzWgCg8UZ2bpFJ39QOB68=\");\n_c = QuizPage;\nexport default QuizPage;\nvar _c;\n$RefreshReg$(_c, \"QuizPage\");", "map": {"version": 3, "names": ["React", "useState", "motion", "ClipboardDocumentListIcon", "ClockIcon", "CheckCircleIcon", "XCircleIcon", "jsxDEV", "_jsxDEV", "QuizPage", "user", "quizId", "onBack", "_s", "currentQuestion", "setCurrentQuestion", "answers", "setAnswers", "timeLeft", "setTimeLeft", "quizCompleted", "setQuizCompleted", "score", "setScore", "quiz", "id", "title", "description", "timeLimit", "passingScore", "questions", "question", "type", "options", "<PERSON><PERSON><PERSON><PERSON>", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "toString", "padStart", "handleAnswerSelect", "answerIndex", "prev", "handleNextQuestion", "length", "handlePrevQuestion", "handleSubmitQuiz", "correctAnswers", "for<PERSON>ach", "index", "finalScore", "round", "currentQuestionData", "progress", "className", "children", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "y", "animate", "style", "width", "x", "map", "option", "name", "value", "checked", "onChange", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Student/QuizPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  ClipboardDocumentListIcon,\n  ClockIcon,\n  CheckCircleIcon,\n  XCircleIcon\n} from '@heroicons/react/24/outline';\n\n// Types\nimport { Student } from '../../types';\n\ninterface QuizPageProps {\n  user?: Student;\n  quizId?: string;\n  onBack?: () => void;\n}\n\nconst QuizPage: React.FC<QuizPageProps> = ({ user, quizId, onBack }) => {\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [answers, setAnswers] = useState<{ [key: number]: any }>({});\n  const [timeLeft, setTimeLeft] = useState(1800); // 30 minutes in seconds\n  const [quizCompleted, setQuizCompleted] = useState(false);\n  const [score, setScore] = useState<number | null>(null);\n\n  // Mock quiz data\n  const quiz = {\n    id: quizId,\n    title: 'اختبار أساسيات البرمجة',\n    description: 'اختبار شامل لأساسيات البرمجة',\n    timeLimit: 30,\n    passingScore: 70,\n    questions: [\n      {\n        id: 1,\n        question: 'ما هو المتغير في البرمجة؟',\n        type: 'multiple-choice',\n        options: [\n          'مكان لتخزين البيانات',\n          'نوع من الدوال',\n          'أمر برمجي',\n          'لا شيء مما سبق'\n        ],\n        correctAnswer: 0\n      },\n      {\n        id: 2,\n        question: 'أي من التالي يُستخدم لإنشاء حلقة تكرارية؟',\n        type: 'multiple-choice',\n        options: [\n          'if',\n          'for',\n          'function',\n          'variable'\n        ],\n        correctAnswer: 1\n      },\n      {\n        id: 3,\n        question: 'البرمجة الكائنية تعتمد على مفهوم الكلاسات',\n        type: 'true-false',\n        options: ['صحيح', 'خطأ'],\n        correctAnswer: 0\n      }\n    ]\n  };\n\n  // Format time\n  const formatTime = (seconds: number) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  const handleAnswerSelect = (answerIndex: number) => {\n    setAnswers(prev => ({\n      ...prev,\n      [currentQuestion]: answerIndex\n    }));\n  };\n\n  const handleNextQuestion = () => {\n    if (currentQuestion < quiz.questions.length - 1) {\n      setCurrentQuestion(prev => prev + 1);\n    }\n  };\n\n  const handlePrevQuestion = () => {\n    if (currentQuestion > 0) {\n      setCurrentQuestion(prev => prev - 1);\n    }\n  };\n\n  const handleSubmitQuiz = () => {\n    // Calculate score\n    let correctAnswers = 0;\n    quiz.questions.forEach((question, index) => {\n      if (answers[index] === question.correctAnswer) {\n        correctAnswers++;\n      }\n    });\n    \n    const finalScore = Math.round((correctAnswers / quiz.questions.length) * 100);\n    setScore(finalScore);\n    setQuizCompleted(true);\n  };\n\n  const currentQuestionData = quiz.questions[currentQuestion];\n  const progress = ((currentQuestion + 1) / quiz.questions.length) * 100;\n\n  if (quizCompleted) {\n    return (\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"flex items-center space-x-4 space-x-reverse\">\n          {onBack && (\n            <button\n              onClick={onBack}\n              className=\"p-2 text-gray-600 hover:text-gray-900 transition-colors\"\n            >\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n              </svg>\n            </button>\n          )}\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">نتيجة الاختبار</h1>\n            <p className=\"text-gray-600\">{quiz.title}</p>\n          </div>\n        </div>\n\n        {/* Results */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"bg-white rounded-lg shadow-sm p-8 text-center\"\n        >\n          <div className=\"mb-6\">\n            {score! >= quiz.passingScore ? (\n              <CheckCircleIcon className=\"w-16 h-16 text-green-600 mx-auto mb-4\" />\n            ) : (\n              <XCircleIcon className=\"w-16 h-16 text-red-600 mx-auto mb-4\" />\n            )}\n            \n            <h2 className=\"text-3xl font-bold text-gray-900 mb-2\">{score}%</h2>\n            <p className={`text-lg ${score! >= quiz.passingScore ? 'text-green-600' : 'text-red-600'}`}>\n              {score! >= quiz.passingScore ? 'مبروك! لقد نجحت في الاختبار' : 'للأسف، لم تحقق الدرجة المطلوبة'}\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\">\n            <div className=\"bg-gray-50 rounded-lg p-4\">\n              <p className=\"text-sm text-gray-600\">النتيجة</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{score}%</p>\n            </div>\n            <div className=\"bg-gray-50 rounded-lg p-4\">\n              <p className=\"text-sm text-gray-600\">الدرجة المطلوبة</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{quiz.passingScore}%</p>\n            </div>\n            <div className=\"bg-gray-50 rounded-lg p-4\">\n              <p className=\"text-sm text-gray-600\">الأسئلة الصحيحة</p>\n              <p className=\"text-2xl font-bold text-gray-900\">\n                {Math.round((score! / 100) * quiz.questions.length)}/{quiz.questions.length}\n              </p>\n            </div>\n          </div>\n\n          <div className=\"flex justify-center space-x-4 space-x-reverse\">\n            {onBack && (\n              <button\n                onClick={onBack}\n                className=\"px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\"\n              >\n                العودة للكورس\n              </button>\n            )}\n            {score! < quiz.passingScore && (\n              <button\n                onClick={() => {\n                  setQuizCompleted(false);\n                  setCurrentQuestion(0);\n                  setAnswers({});\n                  setScore(null);\n                }}\n                className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n              >\n                إعادة المحاولة\n              </button>\n            )}\n          </div>\n        </motion.div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4 space-x-reverse\">\n          {onBack && (\n            <button\n              onClick={onBack}\n              className=\"p-2 text-gray-600 hover:text-gray-900 transition-colors\"\n            >\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n              </svg>\n            </button>\n          )}\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">{quiz.title}</h1>\n            <p className=\"text-gray-600\">{quiz.description}</p>\n          </div>\n        </div>\n        \n        {/* Timer */}\n        <div className=\"flex items-center space-x-2 space-x-reverse bg-red-50 px-4 py-2 rounded-lg\">\n          <ClockIcon className=\"w-5 h-5 text-red-600\" />\n          <span className=\"text-red-600 font-medium\">{formatTime(timeLeft)}</span>\n        </div>\n      </div>\n\n      {/* Progress */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <div className=\"flex items-center justify-between mb-2\">\n          <span className=\"text-sm text-gray-600\">السؤال {currentQuestion + 1} من {quiz.questions.length}</span>\n          <span className=\"text-sm text-gray-600\">{Math.round(progress)}%</span>\n        </div>\n        <div className=\"w-full bg-gray-200 rounded-full h-2\">\n          <div\n            className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n            style={{ width: `${progress}%` }}\n          ></div>\n        </div>\n      </div>\n\n      {/* Question */}\n      <motion.div\n        key={currentQuestion}\n        initial={{ opacity: 0, x: 20 }}\n        animate={{ opacity: 1, x: 0 }}\n        className=\"bg-white rounded-lg shadow-sm p-6\"\n      >\n        <div className=\"mb-6\">\n          <div className=\"flex items-center space-x-3 space-x-reverse mb-4\">\n            <ClipboardDocumentListIcon className=\"w-6 h-6 text-blue-600\" />\n            <h2 className=\"text-lg font-semibold text-gray-900\">\n              السؤال {currentQuestion + 1}\n            </h2>\n          </div>\n          <p className=\"text-gray-900 text-lg leading-relaxed\">\n            {currentQuestionData.question}\n          </p>\n        </div>\n\n        {/* Answer Options */}\n        <div className=\"space-y-3 mb-6\">\n          {currentQuestionData.options.map((option, index) => (\n            <label\n              key={index}\n              className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-colors ${\n                answers[currentQuestion] === index\n                  ? 'border-blue-500 bg-blue-50'\n                  : 'border-gray-200 hover:border-gray-300'\n              }`}\n            >\n              <input\n                type=\"radio\"\n                name={`question-${currentQuestion}`}\n                value={index}\n                checked={answers[currentQuestion] === index}\n                onChange={() => handleAnswerSelect(index)}\n                className=\"sr-only\"\n              />\n              <div className={`w-4 h-4 rounded-full border-2 mr-3 ${\n                answers[currentQuestion] === index\n                  ? 'border-blue-500 bg-blue-500'\n                  : 'border-gray-300'\n              }`}>\n                {answers[currentQuestion] === index && (\n                  <div className=\"w-2 h-2 bg-white rounded-full mx-auto mt-0.5\"></div>\n                )}\n              </div>\n              <span className=\"text-gray-900\">{option}</span>\n            </label>\n          ))}\n        </div>\n\n        {/* Navigation */}\n        <div className=\"flex items-center justify-between\">\n          <button\n            onClick={handlePrevQuestion}\n            disabled={currentQuestion === 0}\n            className=\"px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          >\n            السؤال السابق\n          </button>\n\n          <div className=\"flex space-x-2 space-x-reverse\">\n            {currentQuestion === quiz.questions.length - 1 ? (\n              <button\n                onClick={handleSubmitQuiz}\n                className=\"px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"\n              >\n                إنهاء الاختبار\n              </button>\n            ) : (\n              <button\n                onClick={handleNextQuestion}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n              >\n                السؤال التالي\n              </button>\n            )}\n          </div>\n        </div>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default QuizPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,yBAAyB,EACzBC,SAAS,EACTC,eAAe,EACfC,WAAW,QACN,6BAA6B;;AAEpC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AASA,MAAMC,QAAiC,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EACtE,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGd,QAAQ,CAAC,CAAC,CAAC;EACzD,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAyB,CAAC,CAAC,CAAC;EAClE,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAChD,MAAM,CAACmB,aAAa,EAAEC,gBAAgB,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAgB,IAAI,CAAC;;EAEvD;EACA,MAAMuB,IAAI,GAAG;IACXC,EAAE,EAAEd,MAAM;IACVe,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAE,8BAA8B;IAC3CC,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE,CACT;MACEL,EAAE,EAAE,CAAC;MACLM,QAAQ,EAAE,2BAA2B;MACrCC,IAAI,EAAE,iBAAiB;MACvBC,OAAO,EAAE,CACP,sBAAsB,EACtB,eAAe,EACf,WAAW,EACX,gBAAgB,CACjB;MACDC,aAAa,EAAE;IACjB,CAAC,EACD;MACET,EAAE,EAAE,CAAC;MACLM,QAAQ,EAAE,2CAA2C;MACrDC,IAAI,EAAE,iBAAiB;MACvBC,OAAO,EAAE,CACP,IAAI,EACJ,KAAK,EACL,UAAU,EACV,UAAU,CACX;MACDC,aAAa,EAAE;IACjB,CAAC,EACD;MACET,EAAE,EAAE,CAAC;MACLM,QAAQ,EAAE,2CAA2C;MACrDC,IAAI,EAAE,YAAY;MAClBC,OAAO,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;MACxBC,aAAa,EAAE;IACjB,CAAC;EAEL,CAAC;;EAED;EACA,MAAMC,UAAU,GAAIC,OAAe,IAAK;IACtC,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;IACrC,OAAO,GAAGC,OAAO,IAAIG,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACrE,CAAC;EAED,MAAMC,kBAAkB,GAAIC,WAAmB,IAAK;IAClD3B,UAAU,CAAC4B,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAAC/B,eAAe,GAAG8B;IACrB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIhC,eAAe,GAAGU,IAAI,CAACM,SAAS,CAACiB,MAAM,GAAG,CAAC,EAAE;MAC/ChC,kBAAkB,CAAC8B,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IACtC;EACF,CAAC;EAED,MAAMG,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIlC,eAAe,GAAG,CAAC,EAAE;MACvBC,kBAAkB,CAAC8B,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IACtC;EACF,CAAC;EAED,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;IAC7B;IACA,IAAIC,cAAc,GAAG,CAAC;IACtB1B,IAAI,CAACM,SAAS,CAACqB,OAAO,CAAC,CAACpB,QAAQ,EAAEqB,KAAK,KAAK;MAC1C,IAAIpC,OAAO,CAACoC,KAAK,CAAC,KAAKrB,QAAQ,CAACG,aAAa,EAAE;QAC7CgB,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IAEF,MAAMG,UAAU,GAAGf,IAAI,CAACgB,KAAK,CAAEJ,cAAc,GAAG1B,IAAI,CAACM,SAAS,CAACiB,MAAM,GAAI,GAAG,CAAC;IAC7ExB,QAAQ,CAAC8B,UAAU,CAAC;IACpBhC,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMkC,mBAAmB,GAAG/B,IAAI,CAACM,SAAS,CAAChB,eAAe,CAAC;EAC3D,MAAM0C,QAAQ,GAAI,CAAC1C,eAAe,GAAG,CAAC,IAAIU,IAAI,CAACM,SAAS,CAACiB,MAAM,GAAI,GAAG;EAEtE,IAAI3B,aAAa,EAAE;IACjB,oBACEZ,OAAA;MAAKiD,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAExBlD,OAAA;QAAKiD,SAAS,EAAC,6CAA6C;QAAAC,QAAA,GACzD9C,MAAM,iBACLJ,OAAA;UACEmD,OAAO,EAAE/C,MAAO;UAChB6C,SAAS,EAAC,yDAAyD;UAAAC,QAAA,eAEnElD,OAAA;YAAKiD,SAAS,EAAC,SAAS;YAACG,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAJ,QAAA,eAC5ElD,OAAA;cAAMuD,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACT,eACD9D,OAAA;UAAAkD,QAAA,gBACElD,OAAA;YAAIiD,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAc;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpE9D,OAAA;YAAGiD,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAElC,IAAI,CAACE;UAAK;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN9D,OAAA,CAACN,MAAM,CAACqE,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BjB,SAAS,EAAC,+CAA+C;QAAAC,QAAA,gBAEzDlD,OAAA;UAAKiD,SAAS,EAAC,MAAM;UAAAC,QAAA,GAClBpC,KAAK,IAAKE,IAAI,CAACK,YAAY,gBAC1BrB,OAAA,CAACH,eAAe;YAACoD,SAAS,EAAC;UAAuC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAErE9D,OAAA,CAACF,WAAW;YAACmD,SAAS,EAAC;UAAqC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAC/D,eAED9D,OAAA;YAAIiD,SAAS,EAAC,uCAAuC;YAAAC,QAAA,GAAEpC,KAAK,EAAC,GAAC;UAAA;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnE9D,OAAA;YAAGiD,SAAS,EAAE,WAAWnC,KAAK,IAAKE,IAAI,CAACK,YAAY,GAAG,gBAAgB,GAAG,cAAc,EAAG;YAAA6B,QAAA,EACxFpC,KAAK,IAAKE,IAAI,CAACK,YAAY,GAAG,6BAA6B,GAAG;UAAgC;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN9D,OAAA;UAAKiD,SAAS,EAAC,4CAA4C;UAAAC,QAAA,gBACzDlD,OAAA;YAAKiD,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACxClD,OAAA;cAAGiD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAO;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAChD9D,OAAA;cAAGiD,SAAS,EAAC,kCAAkC;cAAAC,QAAA,GAAEpC,KAAK,EAAC,GAAC;YAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACN9D,OAAA;YAAKiD,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACxClD,OAAA;cAAGiD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAe;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACxD9D,OAAA;cAAGiD,SAAS,EAAC,kCAAkC;cAAAC,QAAA,GAAElC,IAAI,CAACK,YAAY,EAAC,GAAC;YAAA;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,eACN9D,OAAA;YAAKiD,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACxClD,OAAA;cAAGiD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAe;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACxD9D,OAAA;cAAGiD,SAAS,EAAC,kCAAkC;cAAAC,QAAA,GAC5CpB,IAAI,CAACgB,KAAK,CAAEhC,KAAK,GAAI,GAAG,GAAIE,IAAI,CAACM,SAAS,CAACiB,MAAM,CAAC,EAAC,GAAC,EAACvB,IAAI,CAACM,SAAS,CAACiB,MAAM;YAAA;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN9D,OAAA;UAAKiD,SAAS,EAAC,+CAA+C;UAAAC,QAAA,GAC3D9C,MAAM,iBACLJ,OAAA;YACEmD,OAAO,EAAE/C,MAAO;YAChB6C,SAAS,EAAC,iFAAiF;YAAAC,QAAA,EAC5F;UAED;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT,EACAhD,KAAK,GAAIE,IAAI,CAACK,YAAY,iBACzBrB,OAAA;YACEmD,OAAO,EAAEA,CAAA,KAAM;cACbtC,gBAAgB,CAAC,KAAK,CAAC;cACvBN,kBAAkB,CAAC,CAAC,CAAC;cACrBE,UAAU,CAAC,CAAC,CAAC,CAAC;cACdM,QAAQ,CAAC,IAAI,CAAC;YAChB,CAAE;YACFkC,SAAS,EAAC,iFAAiF;YAAAC,QAAA,EAC5F;UAED;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,oBACE9D,OAAA;IAAKiD,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBlD,OAAA;MAAKiD,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDlD,OAAA;QAAKiD,SAAS,EAAC,6CAA6C;QAAAC,QAAA,GACzD9C,MAAM,iBACLJ,OAAA;UACEmD,OAAO,EAAE/C,MAAO;UAChB6C,SAAS,EAAC,yDAAyD;UAAAC,QAAA,eAEnElD,OAAA;YAAKiD,SAAS,EAAC,SAAS;YAACG,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAJ,QAAA,eAC5ElD,OAAA;cAAMuD,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACT,eACD9D,OAAA;UAAAkD,QAAA,gBACElD,OAAA;YAAIiD,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAElC,IAAI,CAACE;UAAK;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAClE9D,OAAA;YAAGiD,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAElC,IAAI,CAACG;UAAW;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN9D,OAAA;QAAKiD,SAAS,EAAC,4EAA4E;QAAAC,QAAA,gBACzFlD,OAAA,CAACJ,SAAS;UAACqD,SAAS,EAAC;QAAsB;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9C9D,OAAA;UAAMiD,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EAAEvB,UAAU,CAACjB,QAAQ;QAAC;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9D,OAAA;MAAKiD,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDlD,OAAA;QAAKiD,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDlD,OAAA;UAAMiD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GAAC,uCAAO,EAAC5C,eAAe,GAAG,CAAC,EAAC,gBAAI,EAACU,IAAI,CAACM,SAAS,CAACiB,MAAM;QAAA;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACtG9D,OAAA;UAAMiD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GAAEpB,IAAI,CAACgB,KAAK,CAACE,QAAQ,CAAC,EAAC,GAAC;QAAA;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC,eACN9D,OAAA;QAAKiD,SAAS,EAAC,qCAAqC;QAAAC,QAAA,eAClDlD,OAAA;UACEiD,SAAS,EAAC,0DAA0D;UACpEmB,KAAK,EAAE;YAAEC,KAAK,EAAE,GAAGrB,QAAQ;UAAI;QAAE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9D,OAAA,CAACN,MAAM,CAACqE,GAAG;MAETC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEK,CAAC,EAAE;MAAG,CAAE;MAC/BH,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEK,CAAC,EAAE;MAAE,CAAE;MAC9BrB,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAE7ClD,OAAA;QAAKiD,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBlD,OAAA;UAAKiD,SAAS,EAAC,kDAAkD;UAAAC,QAAA,gBAC/DlD,OAAA,CAACL,yBAAyB;YAACsD,SAAS,EAAC;UAAuB;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/D9D,OAAA;YAAIiD,SAAS,EAAC,qCAAqC;YAAAC,QAAA,GAAC,uCAC3C,EAAC5C,eAAe,GAAG,CAAC;UAAA;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACN9D,OAAA;UAAGiD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACjDH,mBAAmB,CAACxB;QAAQ;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGN9D,OAAA;QAAKiD,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAC5BH,mBAAmB,CAACtB,OAAO,CAAC8C,GAAG,CAAC,CAACC,MAAM,EAAE5B,KAAK,kBAC7C5C,OAAA;UAEEiD,SAAS,EAAE,8EACTzC,OAAO,CAACF,eAAe,CAAC,KAAKsC,KAAK,GAC9B,4BAA4B,GAC5B,uCAAuC,EAC1C;UAAAM,QAAA,gBAEHlD,OAAA;YACEwB,IAAI,EAAC,OAAO;YACZiD,IAAI,EAAE,YAAYnE,eAAe,EAAG;YACpCoE,KAAK,EAAE9B,KAAM;YACb+B,OAAO,EAAEnE,OAAO,CAACF,eAAe,CAAC,KAAKsC,KAAM;YAC5CgC,QAAQ,EAAEA,CAAA,KAAMzC,kBAAkB,CAACS,KAAK,CAAE;YAC1CK,SAAS,EAAC;UAAS;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACF9D,OAAA;YAAKiD,SAAS,EAAE,sCACdzC,OAAO,CAACF,eAAe,CAAC,KAAKsC,KAAK,GAC9B,6BAA6B,GAC7B,iBAAiB,EACpB;YAAAM,QAAA,EACA1C,OAAO,CAACF,eAAe,CAAC,KAAKsC,KAAK,iBACjC5C,OAAA;cAAKiD,SAAS,EAAC;YAA8C;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UACpE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACN9D,OAAA;YAAMiD,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEsB;UAAM;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAxB1ClB,KAAK;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAyBL,CACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN9D,OAAA;QAAKiD,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDlD,OAAA;UACEmD,OAAO,EAAEX,kBAAmB;UAC5BqC,QAAQ,EAAEvE,eAAe,KAAK,CAAE;UAChC2C,SAAS,EAAC,oIAAoI;UAAAC,QAAA,EAC/I;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET9D,OAAA;UAAKiD,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EAC5C5C,eAAe,KAAKU,IAAI,CAACM,SAAS,CAACiB,MAAM,GAAG,CAAC,gBAC5CvC,OAAA;YACEmD,OAAO,EAAEV,gBAAiB;YAC1BQ,SAAS,EAAC,mFAAmF;YAAAC,QAAA,EAC9F;UAED;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,gBAET9D,OAAA;YACEmD,OAAO,EAAEb,kBAAmB;YAC5BW,SAAS,EAAC,iFAAiF;YAAAC,QAAA,EAC5F;UAED;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QACT;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA,GA7EDxD,eAAe;MAAAqD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OA8EV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAACzD,EAAA,CA9SIJ,QAAiC;AAAA6E,EAAA,GAAjC7E,QAAiC;AAgTvC,eAAeA,QAAQ;AAAC,IAAA6E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}