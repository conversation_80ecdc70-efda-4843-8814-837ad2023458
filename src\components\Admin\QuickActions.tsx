import React from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import {
  PlusIcon,
  FolderPlusIcon,
  UserPlusIcon,
  ClipboardDocumentListIcon,
  DocumentTextIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';

const QuickActions: React.FC = () => {
  const navigate = useNavigate();

  const actions = [
    {
      title: 'إضافة كورس جديد',
      description: 'إنشاء كورس تعليمي جديد',
      icon: PlusIcon,
      color: 'blue',
      onClick: () => navigate('/admin/courses?action=create')
    },
    {
      title: 'إضافة قسم',
      description: 'إنشاء قسم جديد للكورسات',
      icon: FolderPlusIcon,
      color: 'green',
      onClick: () => navigate('/admin/categories?action=create')
    },
    {
      title: 'إضافة طالب',
      description: 'إنشاء حساب طالب جديد',
      icon: UserPlusIcon,
      color: 'purple',
      onClick: () => navigate('/admin/students?action=create')
    },
    {
      title: 'إنشاء اختبار',
      description: 'إضافة اختبار جديد',
      icon: ClipboardDocumentListIcon,
      color: 'orange',
      onClick: () => navigate('/admin/quizzes?action=create')
    },
    {
      title: 'إصدار شهادة',
      description: 'إنشاء شهادة جديدة',
      icon: DocumentTextIcon,
      color: 'red',
      onClick: () => navigate('/admin/certificates?action=create')
    },
    {
      title: 'عرض التحليلات',
      description: 'مراجعة إحصائيات المنصة',
      icon: ChartBarIcon,
      color: 'indigo',
      onClick: () => navigate('/admin/analytics')
    }
  ];

  const colorClasses = {
    blue: 'from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700',
    green: 'from-green-500 to-green-600 hover:from-green-600 hover:to-green-700',
    purple: 'from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700',
    orange: 'from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700',
    red: 'from-red-500 to-red-600 hover:from-red-600 hover:to-red-700',
    indigo: 'from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700'
  };

  return (
    <div className="bg-white rounded-xl p-6 shadow-sm">
      <h3 className="text-lg font-bold text-gray-900 mb-4">إجراءات سريعة</h3>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {actions.map((action, index) => (
          <motion.button
            key={action.title}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: index * 0.1 }}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={action.onClick}
            className={`
              p-4 rounded-lg text-white text-right transition-all duration-200
              bg-gradient-to-br ${colorClasses[action.color as keyof typeof colorClasses]}
              hover:shadow-lg transform hover:-translate-y-1
            `}
          >
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h4 className="font-semibold text-sm mb-1">{action.title}</h4>
                <p className="text-xs opacity-90">{action.description}</p>
              </div>
              <action.icon className="w-6 h-6 opacity-80" />
            </div>
          </motion.button>
        ))}
      </div>
      
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">آخر تحديث</span>
          <span className="text-gray-900 font-medium">
            {new Date().toLocaleDateString('ar-SA')}
          </span>
        </div>
      </div>
    </div>
  );
};

export default QuickActions;
