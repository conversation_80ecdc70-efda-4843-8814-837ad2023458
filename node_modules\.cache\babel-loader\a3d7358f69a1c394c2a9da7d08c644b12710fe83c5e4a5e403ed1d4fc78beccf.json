{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0634\\u0631\\u0648\\u0639\\\\src\\\\components\\\\AIAssistant\\\\AIAssistant.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { XMarkIcon, PaperAirplaneIcon, MinusIcon, StarIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n// Custom AI Assistant Icon Component\nconst AIAssistantIcon = ({\n  className = \"w-7 h-7\"\n}) => /*#__PURE__*/_jsxDEV(\"svg\", {\n  className: className,\n  viewBox: \"0 0 64 64\",\n  fill: \"none\",\n  xmlns: \"http://www.w3.org/2000/svg\",\n  children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n    x: \"18\",\n    y: \"16\",\n    width: \"28\",\n    height: \"24\",\n    rx: \"8\",\n    fill: \"currentColor\",\n    stroke: \"rgba(255,255,255,0.4)\",\n    strokeWidth: \"1.5\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n    x: \"20\",\n    y: \"18\",\n    width: \"24\",\n    height: \"20\",\n    rx: \"6\",\n    fill: \"rgba(255,255,255,0.1)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n    cx: \"26\",\n    cy: \"26\",\n    r: \"4\",\n    fill: \"rgba(255,255,255,0.3)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n    cx: \"38\",\n    cy: \"26\",\n    r: \"4\",\n    fill: \"rgba(255,255,255,0.3)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n    cx: \"26\",\n    cy: \"26\",\n    r: \"3\",\n    fill: \"rgba(255,255,255,0.95)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n    cx: \"38\",\n    cy: \"26\",\n    r: \"3\",\n    fill: \"rgba(255,255,255,0.95)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n    cx: \"26\",\n    cy: \"26\",\n    r: \"1.5\",\n    fill: \"rgba(59, 130, 246, 0.9)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n    cx: \"38\",\n    cy: \"26\",\n    r: \"1.5\",\n    fill: \"rgba(59, 130, 246, 0.9)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n    cx: \"26\",\n    cy: \"25\",\n    r: \"0.5\",\n    fill: \"rgba(255,255,255,0.8)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n    cx: \"38\",\n    cy: \"25\",\n    r: \"0.5\",\n    fill: \"rgba(255,255,255,0.8)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M 28 32 Q 32 35 36 32\",\n    stroke: \"rgba(255,255,255,0.8)\",\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    fill: \"none\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n    x1: \"28\",\n    y1: \"16\",\n    x2: \"28\",\n    y2: \"10\",\n    stroke: \"rgba(255,255,255,0.7)\",\n    strokeWidth: \"2.5\",\n    strokeLinecap: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n    x1: \"36\",\n    y1: \"16\",\n    x2: \"36\",\n    y2: \"10\",\n    stroke: \"rgba(255,255,255,0.7)\",\n    strokeWidth: \"2.5\",\n    strokeLinecap: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 68,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n    cx: \"28\",\n    cy: \"8\",\n    r: \"2.5\",\n    fill: \"rgba(255,255,255,0.9)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n    cx: \"36\",\n    cy: \"8\",\n    r: \"2.5\",\n    fill: \"rgba(255,255,255,0.9)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n    cx: \"28\",\n    cy: \"8\",\n    r: \"1.5\",\n    fill: \"rgba(34, 197, 94, 0.8)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n    cx: \"36\",\n    cy: \"8\",\n    r: \"1.5\",\n    fill: \"rgba(34, 197, 94, 0.8)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n    x: \"22\",\n    y: \"40\",\n    width: \"20\",\n    height: \"16\",\n    rx: \"4\",\n    fill: \"currentColor\",\n    stroke: \"rgba(255,255,255,0.3)\",\n    strokeWidth: \"1.5\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n    x: \"24\",\n    y: \"42\",\n    width: \"16\",\n    height: \"12\",\n    rx: \"3\",\n    fill: \"rgba(255,255,255,0.1)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 89,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n    x: \"14\",\n    y: \"44\",\n    width: \"8\",\n    height: \"6\",\n    rx: \"3\",\n    fill: \"currentColor\",\n    stroke: \"rgba(255,255,255,0.2)\",\n    strokeWidth: \"1\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n    x: \"42\",\n    y: \"44\",\n    width: \"8\",\n    height: \"6\",\n    rx: \"3\",\n    fill: \"currentColor\",\n    stroke: \"rgba(255,255,255,0.2)\",\n    strokeWidth: \"1\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 100,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n    cx: \"32\",\n    cy: \"46\",\n    r: \"1.5\",\n    fill: \"rgba(255,255,255,0.6)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 103,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n    x: \"29\",\n    y: \"50\",\n    width: \"6\",\n    height: \"1\",\n    rx: \"0.5\",\n    fill: \"rgba(255,255,255,0.5)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 104,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n    x: \"30\",\n    y: \"52\",\n    width: \"4\",\n    height: \"1\",\n    rx: \"0.5\",\n    fill: \"rgba(255,255,255,0.4)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 105,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M 24 20 L 26 20 L 26 22\",\n    stroke: \"rgba(255,255,255,0.3)\",\n    strokeWidth: \"1\",\n    fill: \"none\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 108,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M 40 20 L 38 20 L 38 22\",\n    stroke: \"rgba(255,255,255,0.3)\",\n    strokeWidth: \"1\",\n    fill: \"none\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 109,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 15,\n  columnNumber: 3\n}, this);\n\n// Types\n_c = AIAssistantIcon;\nconst AIAssistant = ({\n  context = 'student'\n}) => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const [isMinimized, setIsMinimized] = useState(false);\n  const getWelcomeMessage = () => {\n    switch (context) {\n      case 'login':\n        return 'مرحباً! أنا المساعد الذكي لمنصة ALaa Abd Hamied. هل تحتاج مساعدة في تسجيل الدخول أو لديك أسئلة حول المنصة؟';\n      case 'admin':\n        return 'مرحباً أيها المدير! أنا هنا لمساعدتك في إدارة المنصة والإجابة على أي استفسارات تقنية.';\n      default:\n        return 'مرحباً عزيزي الطالب! أنا المساعد الذكي لمنصة ALaa Abd Hamied. كيف يمكنني مساعدتك في رحلتك التعليمية اليوم؟';\n    }\n  };\n  const [messages, setMessages] = useState([{\n    id: '1',\n    type: 'assistant',\n    content: getWelcomeMessage(),\n    timestamp: new Date()\n  }]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const messagesEndRef = useRef(null);\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  const getQuickReplies = () => {\n    switch (context) {\n      case 'login':\n        return ['كيف أسجل الدخول كطالب؟', 'نسيت كود الوصول', 'مشكلة في تسجيل الدخول', 'ما هي متطلبات النظام؟', 'كيف أحصل على حساب؟', 'التواصل مع الدعم'];\n      case 'admin':\n        return ['كيف أضيف كورس جديد؟', 'إدارة الطلاب', 'إنشاء اختبارات', 'تقارير الأداء', 'إعدادات النظام', 'النسخ الاحتياطي'];\n      default:\n        return ['كيف أشاهد الكورسات؟', 'كيف أؤدي الاختبارات؟', 'كيف أحصل على الشهادة؟', 'مشكلة في تشغيل الفيديو', 'عرض تقدمي', 'التواصل مع الدعم'];\n    }\n  };\n  const quickReplies = getQuickReplies();\n  const getAIResponse = userMessage => {\n    const message = userMessage.toLowerCase();\n\n    // Login context responses\n    if (context === 'login') {\n      if (message.includes('دخول') || message.includes('تسجيل')) {\n        return 'للطلاب: استخدم كود الوصول الخاص بك في خانة \"كود الوصول\". للمدراء: استخدم البريد الإلكتروني وكلمة المرور. تأكد من اختيار النوع الصحيح من أعلى الصفحة.';\n      }\n      if (message.includes('كود') || message.includes('نسيت')) {\n        return 'إذا نسيت كود الوصول، تواصل مع المدير أو فريق الدعم للحصول على كود جديد. كود الوصول يتكون من أرقام وحروف ويُعطى لك عند التسجيل.';\n      }\n      if (message.includes('حساب') || message.includes('تسجيل جديد')) {\n        return 'للحصول على حساب جديد، تواصل مع إدارة المنصة. سيتم إنشاء حساب لك وإرسال كود الوصول الخاص بك.';\n      }\n      if (message.includes('متطلبات') || message.includes('نظام')) {\n        return 'المنصة تعمل على جميع المتصفحات الحديثة (Chrome, Firefox, Safari, Edge). تحتاج اتصال إنترنت مستقر لمشاهدة الفيديوهات.';\n      }\n    }\n    if (message.includes('كورس') || message.includes('مشاهدة')) {\n      return context === 'login' ? 'بعد تسجيل الدخول، ستجد جميع الكورسات المتاحة لك في لوحة التحكم الرئيسية.' : 'لمشاهدة الكورسات، اذهب إلى قسم \"كورساتي\" من القائمة الرئيسية. ستجد جميع الكورسات المتاحة لك مع إمكانية متابعة التقدم.';\n    }\n    if (message.includes('اختبار') || message.includes('امتحان')) {\n      return 'يمكنك الوصول للاختبارات من داخل كل كورس. بعد مشاهدة الفيديوهات المطلوبة، ستظهر لك الاختبارات المتاحة. تأكد من قراءة التعليمات قبل البدء.';\n    }\n    if (message.includes('شهادة')) {\n      return 'للحصول على الشهادة، يجب إكمال جميع فيديوهات الكورس واجتياز الاختبارات بنجاح. ستظهر الشهادة تلقائياً في قسم \"شهاداتي\".';\n    }\n    if (message.includes('فيديو') || message.includes('تشغيل')) {\n      return 'إذا واجهت مشكلة في تشغيل الفيديو، تأكد من اتصالك بالإنترنت وحاول تحديث الصفحة. إذا استمرت المشكلة، جرب متصفح آخر.';\n    }\n    if (message.includes('مرور') || message.includes('كلمة')) {\n      return 'لا يمكن تغيير كلمة المرور للطلاب حيث يتم الدخول بكود الوصول. إذا نسيت الكود، تواصل مع المدير للحصول على كود جديد.';\n    }\n    if (message.includes('دعم') || message.includes('مساعدة') || message.includes('مشكلة')) {\n      return 'يمكنك التواصل مع فريق الدعم من خلال النقر على \"اتصل بنا\" في أسفل الصفحة، أو إرسال رسالة مباشرة للمدير.';\n    }\n    if (message.includes('مرحبا') || message.includes('السلام') || message.includes('أهلا')) {\n      return 'أهلاً وسهلاً بك! أنا هنا لمساعدتك في أي استفسار حول المنصة. ما الذي تحتاج مساعدة فيه؟';\n    }\n    if (message.includes('شكرا') || message.includes('شكراً')) {\n      return 'العفو! أتمنى أن أكون قد ساعدتك. لا تتردد في سؤالي عن أي شيء آخر.';\n    }\n    return 'أعتذر، لم أفهم سؤالك بوضوح. يمكنك تجربة إحدى الأسئلة الشائعة أدناه، أو إعادة صياغة سؤالك بطريقة أخرى.';\n  };\n  const handleSendMessage = async () => {\n    if (!inputMessage.trim()) return;\n    const userMessage = {\n      id: Date.now().toString(),\n      type: 'user',\n      content: inputMessage,\n      timestamp: new Date()\n    };\n    setMessages(prev => [...prev, userMessage]);\n    setInputMessage('');\n    setIsTyping(true);\n\n    // Simulate AI thinking time\n    setTimeout(() => {\n      const aiResponse = {\n        id: (Date.now() + 1).toString(),\n        type: 'assistant',\n        content: getAIResponse(inputMessage),\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, aiResponse]);\n      setIsTyping(false);\n    }, 1000 + Math.random() * 1000);\n  };\n  const handleQuickReply = reply => {\n    setInputMessage(reply);\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: !isOpen && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          scale: 0,\n          opacity: 0\n        },\n        animate: {\n          scale: 1,\n          opacity: 1\n        },\n        exit: {\n          scale: 0,\n          opacity: 0\n        },\n        className: \"fixed bottom-6 left-6 z-50\",\n        children: [/*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.1,\n            rotate: 5\n          },\n          whileTap: {\n            scale: 0.9\n          },\n          onClick: () => setIsOpen(true),\n          className: \"relative w-16 h-16 bg-gradient-to-br from-yellow-400 via-yellow-500 to-amber-600 text-white rounded-full shadow-2xl hover:shadow-yellow-500/25 transition-all duration-300 flex items-center justify-center group overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-gradient-to-br from-yellow-300 to-amber-500 rounded-full opacity-0 group-hover:opacity-20 transition-opacity duration-300\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 rounded-full\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              animate: {\n                rotate: 360\n              },\n              transition: {\n                duration: 8,\n                repeat: Infinity,\n                ease: \"linear\"\n              },\n              className: \"absolute inset-2 border-2 border-yellow-300/30 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              animate: {\n                rotate: -360\n              },\n              transition: {\n                duration: 12,\n                repeat: Infinity,\n                ease: \"linear\"\n              },\n              className: \"absolute inset-1 border border-yellow-200/20 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative z-10 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(AIAssistantIcon, {\n              className: \"w-8 h-8 text-white drop-shadow-lg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            animate: {\n              scale: [1, 1.2, 1],\n              opacity: [0.7, 1, 0.7]\n            },\n            transition: {\n              duration: 2,\n              repeat: Infinity,\n              ease: \"easeInOut\"\n            },\n            className: \"absolute -top-1 -right-1 w-3 h-3 bg-yellow-300 rounded-full shadow-lg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            animate: {\n              scale: [1, 1.3, 1],\n              opacity: [0.5, 1, 0.5]\n            },\n            transition: {\n              duration: 2.5,\n              repeat: Infinity,\n              ease: \"easeInOut\",\n              delay: 0.5\n            },\n            className: \"absolute -bottom-1 -left-1 w-2 h-2 bg-amber-300 rounded-full shadow-lg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            animate: {\n              scale: [1, 1.1, 1],\n              opacity: [0.6, 1, 0.6]\n            },\n            transition: {\n              duration: 3,\n              repeat: Infinity,\n              ease: \"easeInOut\",\n              delay: 1\n            },\n            className: \"absolute top-1 -left-2 w-1.5 h-1.5 bg-yellow-200 rounded-full shadow-lg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full animate-pulse border-2 border-white shadow-lg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: -10\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            delay: 2\n          },\n          className: \"absolute right-20 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-gray-900 to-gray-800 text-white px-4 py-2 rounded-lg text-sm whitespace-nowrap shadow-xl border border-gray-700\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 space-x-reverse\",\n            children: [/*#__PURE__*/_jsxDEV(AIAssistantIcon, {\n              className: \"w-4 h-4 text-yellow-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u0627\\u0644\\u0645\\u0633\\u0627\\u0639\\u062F \\u0627\\u0644\\u0630\\u0643\\u064A - \\u0627\\u0636\\u063A\\u0637 \\u0644\\u0644\\u0645\\u0633\\u0627\\u0639\\u062F\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute left-0 top-1/2 transform -translate-y-1/2 translate-x-1 w-2 h-2 bg-gray-900 rotate-45\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: isOpen && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 100,\n          scale: 0.9\n        },\n        animate: {\n          opacity: 1,\n          y: 0,\n          scale: 1,\n          height: isMinimized ? 60 : 500\n        },\n        exit: {\n          opacity: 0,\n          y: 100,\n          scale: 0.9\n        },\n        className: \"fixed bottom-6 left-6 w-80 bg-white rounded-xl shadow-2xl border border-gray-200 z-50 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-yellow-500 via-amber-500 to-yellow-600 text-white p-4 flex items-center justify-between relative overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 opacity-10\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute top-2 left-4 w-2 h-2 bg-white rounded-full animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute top-6 right-8 w-1 h-1 bg-white rounded-full animate-pulse\",\n              style: {\n                animationDelay: '0.5s'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute bottom-3 left-12 w-1.5 h-1.5 bg-white rounded-full animate-pulse\",\n              style: {\n                animationDelay: '1s'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center relative z-10\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-10 h-10 bg-gradient-to-br from-white/30 to-white/10 rounded-full flex items-center justify-center ml-3 backdrop-blur-sm border border-white/20\",\n              children: /*#__PURE__*/_jsxDEV(AIAssistantIcon, {\n                className: \"w-6 h-6 text-white drop-shadow-lg\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-bold text-sm flex items-center\",\n                children: [\"\\u0627\\u0644\\u0645\\u0633\\u0627\\u0639\\u062F \\u0627\\u0644\\u0630\\u0643\\u064A\", /*#__PURE__*/_jsxDEV(StarIcon, {\n                  className: \"w-3 h-3 mr-1 text-yellow-200\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs opacity-90 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-2 h-2 bg-green-400 rounded-full mr-1 animate-pulse\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 21\n                }, this), \"\\u0645\\u062A\\u0627\\u062D \\u0627\\u0644\\u0622\\u0646 \\u0644\\u0644\\u0645\\u0633\\u0627\\u0639\\u062F\\u0629\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 space-x-reverse\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsMinimized(!isMinimized),\n              className: \"p-1 hover:bg-white hover:bg-opacity-20 rounded\",\n              children: /*#__PURE__*/_jsxDEV(MinusIcon, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsOpen(false),\n              className: \"p-1 hover:bg-white hover:bg-opacity-20 rounded\",\n              children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 13\n        }, this), !isMinimized && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-80 overflow-y-auto p-4 space-y-4\",\n            children: [messages.map(message => /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 10\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              className: `flex ${message.type === 'user' ? 'justify-start' : 'justify-end'}`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `\n                        max-w-xs px-4 py-3 rounded-lg text-sm shadow-sm\n                        ${message.type === 'user' ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-br-sm' : 'bg-gradient-to-r from-yellow-50 to-amber-50 text-gray-800 rounded-bl-sm border border-yellow-200/50'}\n                      `,\n                children: message.content\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 23\n              }, this)\n            }, message.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 21\n            }, this)), isTyping && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0\n              },\n              animate: {\n                opacity: 1\n              },\n              className: \"flex justify-end\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-yellow-50 to-amber-50 px-4 py-3 rounded-lg rounded-bl-sm border border-yellow-200/50 shadow-sm\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-1 items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(AIAssistantIcon, {\n                    className: \"w-4 h-4 text-yellow-600 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 473,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-yellow-500 rounded-full animate-bounce\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-amber-500 rounded-full animate-bounce\",\n                    style: {\n                      animationDelay: '0.1s'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 475,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-yellow-600 rounded-full animate-bounce\",\n                    style: {\n                      animationDelay: '0.2s'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 476,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-xs text-gray-600 mr-2\",\n                    children: \"\\u064A\\u0643\\u062A\\u0628...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 477,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              ref: messagesEndRef\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 17\n          }, this), messages.length <= 2 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-4 pb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500 mb-2\",\n              children: \"\\u0623\\u0633\\u0626\\u0644\\u0629 \\u0634\\u0627\\u0626\\u0639\\u0629:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-wrap gap-1\",\n              children: quickReplies.slice(0, 3).map(reply => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleQuickReply(reply),\n                className: \"text-xs bg-gradient-to-r from-yellow-100 to-amber-100 hover:from-yellow-200 hover:to-amber-200 text-gray-700 px-3 py-1.5 rounded-full transition-all duration-200 border border-yellow-200/50 hover:border-yellow-300 shadow-sm hover:shadow-md\",\n                children: reply\n              }, reply, false, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 25\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 border-t border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 space-x-reverse\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: inputMessage,\n                onChange: e => setInputMessage(e.target.value),\n                onKeyPress: handleKeyPress,\n                placeholder: \"\\u0627\\u0643\\u062A\\u0628 \\u0631\\u0633\\u0627\\u0644\\u062A\\u0643...\",\n                className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm\",\n                disabled: isTyping\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                onClick: handleSendMessage,\n                disabled: !inputMessage.trim() || isTyping,\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                className: \"p-2.5 bg-gradient-to-r from-yellow-500 to-amber-600 text-white rounded-lg hover:from-yellow-600 hover:to-amber-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl disabled:hover:scale-100\",\n                children: /*#__PURE__*/_jsxDEV(PaperAirplaneIcon, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 523,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(AIAssistant, \"czjJEpDcIy5UgZYwWPaaoufKVMM=\");\n_c2 = AIAssistant;\nexport default AIAssistant;\nvar _c, _c2;\n$RefreshReg$(_c, \"AIAssistantIcon\");\n$RefreshReg$(_c2, \"AIAssistant\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "motion", "AnimatePresence", "XMarkIcon", "PaperAirplaneIcon", "MinusIcon", "StarIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AIAssistantIcon", "className", "viewBox", "fill", "xmlns", "children", "x", "y", "width", "height", "rx", "stroke", "strokeWidth", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "cx", "cy", "r", "d", "strokeLinecap", "x1", "y1", "x2", "y2", "_c", "AIAssistant", "context", "_s", "isOpen", "setIsOpen", "isMinimized", "setIsMinimized", "getWelcomeMessage", "messages", "setMessages", "id", "type", "content", "timestamp", "Date", "inputMessage", "setInputMessage", "isTyping", "setIsTyping", "messagesEndRef", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "getQuickReplies", "quickReplies", "getAIResponse", "userMessage", "message", "toLowerCase", "includes", "handleSendMessage", "trim", "now", "toString", "prev", "setTimeout", "aiResponse", "Math", "random", "handleQuickReply", "reply", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "div", "initial", "scale", "opacity", "animate", "exit", "button", "whileHover", "rotate", "whileTap", "onClick", "transition", "duration", "repeat", "Infinity", "ease", "delay", "style", "animationDelay", "map", "ref", "length", "slice", "value", "onChange", "target", "onKeyPress", "placeholder", "disabled", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/AIAssistant/AIAssistant.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  ChatBubbleLeftRightIcon,\n  XMarkIcon,\n  PaperAirplaneIcon,\n  SparklesIcon,\n  MinusIcon,\n  StarIcon\n} from '@heroicons/react/24/outline';\nimport { SparklesIcon as SparklesIconSolid } from '@heroicons/react/24/solid';\n\n// Custom AI Assistant Icon Component\nconst AIAssistantIcon: React.FC<{ className?: string }> = ({ className = \"w-7 h-7\" }) => (\n  <svg\n    className={className}\n    viewBox=\"0 0 64 64\"\n    fill=\"none\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n  >\n    {/* Robot Head - Main */}\n    <rect\n      x=\"18\"\n      y=\"16\"\n      width=\"28\"\n      height=\"24\"\n      rx=\"8\"\n      fill=\"currentColor\"\n      stroke=\"rgba(255,255,255,0.4)\"\n      strokeWidth=\"1.5\"\n    />\n\n    {/* Robot Head - Inner glow */}\n    <rect\n      x=\"20\"\n      y=\"18\"\n      width=\"24\"\n      height=\"20\"\n      rx=\"6\"\n      fill=\"rgba(255,255,255,0.1)\"\n    />\n\n    {/* Robot Eyes - Outer glow */}\n    <circle cx=\"26\" cy=\"26\" r=\"4\" fill=\"rgba(255,255,255,0.3)\" />\n    <circle cx=\"38\" cy=\"26\" r=\"4\" fill=\"rgba(255,255,255,0.3)\" />\n\n    {/* Robot Eyes - Main */}\n    <circle cx=\"26\" cy=\"26\" r=\"3\" fill=\"rgba(255,255,255,0.95)\" />\n    <circle cx=\"38\" cy=\"26\" r=\"3\" fill=\"rgba(255,255,255,0.95)\" />\n\n    {/* Robot Eyes - Pupils with glow */}\n    <circle cx=\"26\" cy=\"26\" r=\"1.5\" fill=\"rgba(59, 130, 246, 0.9)\" />\n    <circle cx=\"38\" cy=\"26\" r=\"1.5\" fill=\"rgba(59, 130, 246, 0.9)\" />\n    <circle cx=\"26\" cy=\"25\" r=\"0.5\" fill=\"rgba(255,255,255,0.8)\" />\n    <circle cx=\"38\" cy=\"25\" r=\"0.5\" fill=\"rgba(255,255,255,0.8)\" />\n\n    {/* Robot Mouth - Smile */}\n    <path\n      d=\"M 28 32 Q 32 35 36 32\"\n      stroke=\"rgba(255,255,255,0.8)\"\n      strokeWidth=\"2\"\n      strokeLinecap=\"round\"\n      fill=\"none\"\n    />\n\n    {/* Robot Antennas */}\n    <line x1=\"28\" y1=\"16\" x2=\"28\" y2=\"10\" stroke=\"rgba(255,255,255,0.7)\" strokeWidth=\"2.5\" strokeLinecap=\"round\" />\n    <line x1=\"36\" y1=\"16\" x2=\"36\" y2=\"10\" stroke=\"rgba(255,255,255,0.7)\" strokeWidth=\"2.5\" strokeLinecap=\"round\" />\n\n    {/* Antenna Tips with glow */}\n    <circle cx=\"28\" cy=\"8\" r=\"2.5\" fill=\"rgba(255,255,255,0.9)\" />\n    <circle cx=\"36\" cy=\"8\" r=\"2.5\" fill=\"rgba(255,255,255,0.9)\" />\n    <circle cx=\"28\" cy=\"8\" r=\"1.5\" fill=\"rgba(34, 197, 94, 0.8)\" />\n    <circle cx=\"36\" cy=\"8\" r=\"1.5\" fill=\"rgba(34, 197, 94, 0.8)\" />\n\n    {/* Robot Body */}\n    <rect\n      x=\"22\"\n      y=\"40\"\n      width=\"20\"\n      height=\"16\"\n      rx=\"4\"\n      fill=\"currentColor\"\n      stroke=\"rgba(255,255,255,0.3)\"\n      strokeWidth=\"1.5\"\n    />\n\n    {/* Body Inner glow */}\n    <rect\n      x=\"24\"\n      y=\"42\"\n      width=\"16\"\n      height=\"12\"\n      rx=\"3\"\n      fill=\"rgba(255,255,255,0.1)\"\n    />\n\n    {/* Robot Arms */}\n    <rect x=\"14\" y=\"44\" width=\"8\" height=\"6\" rx=\"3\" fill=\"currentColor\" stroke=\"rgba(255,255,255,0.2)\" strokeWidth=\"1\" />\n    <rect x=\"42\" y=\"44\" width=\"8\" height=\"6\" rx=\"3\" fill=\"currentColor\" stroke=\"rgba(255,255,255,0.2)\" strokeWidth=\"1\" />\n\n    {/* Body Details */}\n    <circle cx=\"32\" cy=\"46\" r=\"1.5\" fill=\"rgba(255,255,255,0.6)\" />\n    <rect x=\"29\" y=\"50\" width=\"6\" height=\"1\" rx=\"0.5\" fill=\"rgba(255,255,255,0.5)\" />\n    <rect x=\"30\" y=\"52\" width=\"4\" height=\"1\" rx=\"0.5\" fill=\"rgba(255,255,255,0.4)\" />\n\n    {/* Decorative circuits */}\n    <path d=\"M 24 20 L 26 20 L 26 22\" stroke=\"rgba(255,255,255,0.3)\" strokeWidth=\"1\" fill=\"none\" />\n    <path d=\"M 40 20 L 38 20 L 38 22\" stroke=\"rgba(255,255,255,0.3)\" strokeWidth=\"1\" fill=\"none\" />\n  </svg>\n);\n\n// Types\nimport { ChatMessage } from '../../types';\n\ninterface AIAssistantProps {\n  context?: 'login' | 'student' | 'admin';\n}\n\nconst AIAssistant: React.FC<AIAssistantProps> = ({ context = 'student' }) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [isMinimized, setIsMinimized] = useState(false);\n\n  const getWelcomeMessage = () => {\n    switch (context) {\n      case 'login':\n        return 'مرحباً! أنا المساعد الذكي لمنصة ALaa Abd Hamied. هل تحتاج مساعدة في تسجيل الدخول أو لديك أسئلة حول المنصة؟';\n      case 'admin':\n        return 'مرحباً أيها المدير! أنا هنا لمساعدتك في إدارة المنصة والإجابة على أي استفسارات تقنية.';\n      default:\n        return 'مرحباً عزيزي الطالب! أنا المساعد الذكي لمنصة ALaa Abd Hamied. كيف يمكنني مساعدتك في رحلتك التعليمية اليوم؟';\n    }\n  };\n\n  const [messages, setMessages] = useState<ChatMessage[]>([\n    {\n      id: '1',\n      type: 'assistant',\n      content: getWelcomeMessage(),\n      timestamp: new Date()\n    }\n  ]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const getQuickReplies = () => {\n    switch (context) {\n      case 'login':\n        return [\n          'كيف أسجل الدخول كطالب؟',\n          'نسيت كود الوصول',\n          'مشكلة في تسجيل الدخول',\n          'ما هي متطلبات النظام؟',\n          'كيف أحصل على حساب؟',\n          'التواصل مع الدعم'\n        ];\n      case 'admin':\n        return [\n          'كيف أضيف كورس جديد؟',\n          'إدارة الطلاب',\n          'إنشاء اختبارات',\n          'تقارير الأداء',\n          'إعدادات النظام',\n          'النسخ الاحتياطي'\n        ];\n      default:\n        return [\n          'كيف أشاهد الكورسات؟',\n          'كيف أؤدي الاختبارات؟',\n          'كيف أحصل على الشهادة؟',\n          'مشكلة في تشغيل الفيديو',\n          'عرض تقدمي',\n          'التواصل مع الدعم'\n        ];\n    }\n  };\n\n  const quickReplies = getQuickReplies();\n\n  const getAIResponse = (userMessage: string): string => {\n    const message = userMessage.toLowerCase();\n\n    // Login context responses\n    if (context === 'login') {\n      if (message.includes('دخول') || message.includes('تسجيل')) {\n        return 'للطلاب: استخدم كود الوصول الخاص بك في خانة \"كود الوصول\". للمدراء: استخدم البريد الإلكتروني وكلمة المرور. تأكد من اختيار النوع الصحيح من أعلى الصفحة.';\n      }\n      if (message.includes('كود') || message.includes('نسيت')) {\n        return 'إذا نسيت كود الوصول، تواصل مع المدير أو فريق الدعم للحصول على كود جديد. كود الوصول يتكون من أرقام وحروف ويُعطى لك عند التسجيل.';\n      }\n      if (message.includes('حساب') || message.includes('تسجيل جديد')) {\n        return 'للحصول على حساب جديد، تواصل مع إدارة المنصة. سيتم إنشاء حساب لك وإرسال كود الوصول الخاص بك.';\n      }\n      if (message.includes('متطلبات') || message.includes('نظام')) {\n        return 'المنصة تعمل على جميع المتصفحات الحديثة (Chrome, Firefox, Safari, Edge). تحتاج اتصال إنترنت مستقر لمشاهدة الفيديوهات.';\n      }\n    }\n\n    if (message.includes('كورس') || message.includes('مشاهدة')) {\n      return context === 'login'\n        ? 'بعد تسجيل الدخول، ستجد جميع الكورسات المتاحة لك في لوحة التحكم الرئيسية.'\n        : 'لمشاهدة الكورسات، اذهب إلى قسم \"كورساتي\" من القائمة الرئيسية. ستجد جميع الكورسات المتاحة لك مع إمكانية متابعة التقدم.';\n    }\n    \n    if (message.includes('اختبار') || message.includes('امتحان')) {\n      return 'يمكنك الوصول للاختبارات من داخل كل كورس. بعد مشاهدة الفيديوهات المطلوبة، ستظهر لك الاختبارات المتاحة. تأكد من قراءة التعليمات قبل البدء.';\n    }\n    \n    if (message.includes('شهادة')) {\n      return 'للحصول على الشهادة، يجب إكمال جميع فيديوهات الكورس واجتياز الاختبارات بنجاح. ستظهر الشهادة تلقائياً في قسم \"شهاداتي\".';\n    }\n    \n    if (message.includes('فيديو') || message.includes('تشغيل')) {\n      return 'إذا واجهت مشكلة في تشغيل الفيديو، تأكد من اتصالك بالإنترنت وحاول تحديث الصفحة. إذا استمرت المشكلة، جرب متصفح آخر.';\n    }\n    \n    if (message.includes('مرور') || message.includes('كلمة')) {\n      return 'لا يمكن تغيير كلمة المرور للطلاب حيث يتم الدخول بكود الوصول. إذا نسيت الكود، تواصل مع المدير للحصول على كود جديد.';\n    }\n    \n    if (message.includes('دعم') || message.includes('مساعدة') || message.includes('مشكلة')) {\n      return 'يمكنك التواصل مع فريق الدعم من خلال النقر على \"اتصل بنا\" في أسفل الصفحة، أو إرسال رسالة مباشرة للمدير.';\n    }\n    \n    if (message.includes('مرحبا') || message.includes('السلام') || message.includes('أهلا')) {\n      return 'أهلاً وسهلاً بك! أنا هنا لمساعدتك في أي استفسار حول المنصة. ما الذي تحتاج مساعدة فيه؟';\n    }\n    \n    if (message.includes('شكرا') || message.includes('شكراً')) {\n      return 'العفو! أتمنى أن أكون قد ساعدتك. لا تتردد في سؤالي عن أي شيء آخر.';\n    }\n    \n    return 'أعتذر، لم أفهم سؤالك بوضوح. يمكنك تجربة إحدى الأسئلة الشائعة أدناه، أو إعادة صياغة سؤالك بطريقة أخرى.';\n  };\n\n  const handleSendMessage = async () => {\n    if (!inputMessage.trim()) return;\n\n    const userMessage: ChatMessage = {\n      id: Date.now().toString(),\n      type: 'user',\n      content: inputMessage,\n      timestamp: new Date()\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setInputMessage('');\n    setIsTyping(true);\n\n    // Simulate AI thinking time\n    setTimeout(() => {\n      const aiResponse: ChatMessage = {\n        id: (Date.now() + 1).toString(),\n        type: 'assistant',\n        content: getAIResponse(inputMessage),\n        timestamp: new Date()\n      };\n\n      setMessages(prev => [...prev, aiResponse]);\n      setIsTyping(false);\n    }, 1000 + Math.random() * 1000);\n  };\n\n  const handleQuickReply = (reply: string) => {\n    setInputMessage(reply);\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  return (\n    <>\n      {/* Chat Button */}\n      <AnimatePresence>\n        {!isOpen && (\n          <motion.div\n            initial={{ scale: 0, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            exit={{ scale: 0, opacity: 0 }}\n            className=\"fixed bottom-6 left-6 z-50\"\n          >\n            <motion.button\n              whileHover={{ scale: 1.1, rotate: 5 }}\n              whileTap={{ scale: 0.9 }}\n              onClick={() => setIsOpen(true)}\n              className=\"relative w-16 h-16 bg-gradient-to-br from-yellow-400 via-yellow-500 to-amber-600 text-white rounded-full shadow-2xl hover:shadow-yellow-500/25 transition-all duration-300 flex items-center justify-center group overflow-hidden\"\n            >\n              {/* Background glow effect */}\n              <div className=\"absolute inset-0 bg-gradient-to-br from-yellow-300 to-amber-500 rounded-full opacity-0 group-hover:opacity-20 transition-opacity duration-300\" />\n\n              {/* Sparkle animation background */}\n              <div className=\"absolute inset-0 rounded-full\">\n                <motion.div\n                  animate={{ rotate: 360 }}\n                  transition={{ duration: 8, repeat: Infinity, ease: \"linear\" }}\n                  className=\"absolute inset-2 border-2 border-yellow-300/30 rounded-full\"\n                />\n                <motion.div\n                  animate={{ rotate: -360 }}\n                  transition={{ duration: 12, repeat: Infinity, ease: \"linear\" }}\n                  className=\"absolute inset-1 border border-yellow-200/20 rounded-full\"\n                />\n              </div>\n\n              {/* Main icon */}\n              <div className=\"relative z-10 flex items-center justify-center\">\n                <AIAssistantIcon className=\"w-8 h-8 text-white drop-shadow-lg\" />\n              </div>\n\n              {/* Floating sparkles */}\n              <motion.div\n                animate={{\n                  scale: [1, 1.2, 1],\n                  opacity: [0.7, 1, 0.7]\n                }}\n                transition={{\n                  duration: 2,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                }}\n                className=\"absolute -top-1 -right-1 w-3 h-3 bg-yellow-300 rounded-full shadow-lg\"\n              />\n\n              <motion.div\n                animate={{\n                  scale: [1, 1.3, 1],\n                  opacity: [0.5, 1, 0.5]\n                }}\n                transition={{\n                  duration: 2.5,\n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                  delay: 0.5\n                }}\n                className=\"absolute -bottom-1 -left-1 w-2 h-2 bg-amber-300 rounded-full shadow-lg\"\n              />\n\n              <motion.div\n                animate={{\n                  scale: [1, 1.1, 1],\n                  opacity: [0.6, 1, 0.6]\n                }}\n                transition={{\n                  duration: 3,\n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                  delay: 1\n                }}\n                className=\"absolute top-1 -left-2 w-1.5 h-1.5 bg-yellow-200 rounded-full shadow-lg\"\n              />\n\n              {/* Status indicator */}\n              <div className=\"absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full animate-pulse border-2 border-white shadow-lg\" />\n            </motion.button>\n\n            {/* Tooltip */}\n            <motion.div\n              initial={{ opacity: 0, x: -10 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: 2 }}\n              className=\"absolute right-20 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-gray-900 to-gray-800 text-white px-4 py-2 rounded-lg text-sm whitespace-nowrap shadow-xl border border-gray-700\"\n            >\n              <div className=\"flex items-center space-x-2 space-x-reverse\">\n                <AIAssistantIcon className=\"w-4 h-4 text-yellow-400\" />\n                <span>المساعد الذكي - اضغط للمساعدة</span>\n              </div>\n              <div className=\"absolute left-0 top-1/2 transform -translate-y-1/2 translate-x-1 w-2 h-2 bg-gray-900 rotate-45\" />\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Chat Window */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ opacity: 0, y: 100, scale: 0.9 }}\n            animate={{ \n              opacity: 1, \n              y: 0, \n              scale: 1,\n              height: isMinimized ? 60 : 500\n            }}\n            exit={{ opacity: 0, y: 100, scale: 0.9 }}\n            className=\"fixed bottom-6 left-6 w-80 bg-white rounded-xl shadow-2xl border border-gray-200 z-50 overflow-hidden\"\n          >\n            {/* Header */}\n            <div className=\"bg-gradient-to-r from-yellow-500 via-amber-500 to-yellow-600 text-white p-4 flex items-center justify-between relative overflow-hidden\">\n              {/* Background pattern */}\n              <div className=\"absolute inset-0 opacity-10\">\n                <div className=\"absolute top-2 left-4 w-2 h-2 bg-white rounded-full animate-pulse\" />\n                <div className=\"absolute top-6 right-8 w-1 h-1 bg-white rounded-full animate-pulse\" style={{ animationDelay: '0.5s' }} />\n                <div className=\"absolute bottom-3 left-12 w-1.5 h-1.5 bg-white rounded-full animate-pulse\" style={{ animationDelay: '1s' }} />\n              </div>\n\n              <div className=\"flex items-center relative z-10\">\n                <div className=\"w-10 h-10 bg-gradient-to-br from-white/30 to-white/10 rounded-full flex items-center justify-center ml-3 backdrop-blur-sm border border-white/20\">\n                  <AIAssistantIcon className=\"w-6 h-6 text-white drop-shadow-lg\" />\n                </div>\n                <div>\n                  <h3 className=\"font-bold text-sm flex items-center\">\n                    المساعد الذكي\n                    <StarIcon className=\"w-3 h-3 mr-1 text-yellow-200\" />\n                  </h3>\n                  <p className=\"text-xs opacity-90 flex items-center\">\n                    <div className=\"w-2 h-2 bg-green-400 rounded-full mr-1 animate-pulse\" />\n                    متاح الآن للمساعدة\n                  </p>\n                </div>\n              </div>\n              <div className=\"flex items-center space-x-2 space-x-reverse\">\n                <button\n                  onClick={() => setIsMinimized(!isMinimized)}\n                  className=\"p-1 hover:bg-white hover:bg-opacity-20 rounded\"\n                >\n                  <MinusIcon className=\"w-4 h-4\" />\n                </button>\n                <button\n                  onClick={() => setIsOpen(false)}\n                  className=\"p-1 hover:bg-white hover:bg-opacity-20 rounded\"\n                >\n                  <XMarkIcon className=\"w-4 h-4\" />\n                </button>\n              </div>\n            </div>\n\n            {/* Chat Content */}\n            {!isMinimized && (\n              <>\n                {/* Messages */}\n                <div className=\"h-80 overflow-y-auto p-4 space-y-4\">\n                  {messages.map((message) => (\n                    <motion.div\n                      key={message.id}\n                      initial={{ opacity: 0, y: 10 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      className={`flex ${message.type === 'user' ? 'justify-start' : 'justify-end'}`}\n                    >\n                      <div className={`\n                        max-w-xs px-4 py-3 rounded-lg text-sm shadow-sm\n                        ${message.type === 'user'\n                          ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-br-sm'\n                          : 'bg-gradient-to-r from-yellow-50 to-amber-50 text-gray-800 rounded-bl-sm border border-yellow-200/50'\n                        }\n                      `}>\n                        {message.content}\n                      </div>\n                    </motion.div>\n                  ))}\n\n                  {/* Typing Indicator */}\n                  {isTyping && (\n                    <motion.div\n                      initial={{ opacity: 0 }}\n                      animate={{ opacity: 1 }}\n                      className=\"flex justify-end\"\n                    >\n                      <div className=\"bg-gradient-to-r from-yellow-50 to-amber-50 px-4 py-3 rounded-lg rounded-bl-sm border border-yellow-200/50 shadow-sm\">\n                        <div className=\"flex space-x-1 items-center\">\n                          <AIAssistantIcon className=\"w-4 h-4 text-yellow-600 mr-2\" />\n                          <div className=\"w-2 h-2 bg-yellow-500 rounded-full animate-bounce\" />\n                          <div className=\"w-2 h-2 bg-amber-500 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }} />\n                          <div className=\"w-2 h-2 bg-yellow-600 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }} />\n                          <span className=\"text-xs text-gray-600 mr-2\">يكتب...</span>\n                        </div>\n                      </div>\n                    </motion.div>\n                  )}\n\n                  <div ref={messagesEndRef} />\n                </div>\n\n                {/* Quick Replies */}\n                {messages.length <= 2 && (\n                  <div className=\"px-4 pb-2\">\n                    <p className=\"text-xs text-gray-500 mb-2\">أسئلة شائعة:</p>\n                    <div className=\"flex flex-wrap gap-1\">\n                      {quickReplies.slice(0, 3).map((reply) => (\n                        <button\n                          key={reply}\n                          onClick={() => handleQuickReply(reply)}\n                          className=\"text-xs bg-gradient-to-r from-yellow-100 to-amber-100 hover:from-yellow-200 hover:to-amber-200 text-gray-700 px-3 py-1.5 rounded-full transition-all duration-200 border border-yellow-200/50 hover:border-yellow-300 shadow-sm hover:shadow-md\"\n                        >\n                          {reply}\n                        </button>\n                      ))}\n                    </div>\n                  </div>\n                )}\n\n                {/* Input */}\n                <div className=\"p-4 border-t border-gray-200\">\n                  <div className=\"flex items-center space-x-2 space-x-reverse\">\n                    <input\n                      type=\"text\"\n                      value={inputMessage}\n                      onChange={(e) => setInputMessage(e.target.value)}\n                      onKeyPress={handleKeyPress}\n                      placeholder=\"اكتب رسالتك...\"\n                      className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm\"\n                      disabled={isTyping}\n                    />\n                    <motion.button\n                      onClick={handleSendMessage}\n                      disabled={!inputMessage.trim() || isTyping}\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      className=\"p-2.5 bg-gradient-to-r from-yellow-500 to-amber-600 text-white rounded-lg hover:from-yellow-600 hover:to-amber-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl disabled:hover:scale-100\"\n                    >\n                      <PaperAirplaneIcon className=\"w-4 h-4\" />\n                    </motion.button>\n                  </div>\n                </div>\n              </>\n            )}\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </>\n  );\n};\n\nexport default AIAssistant;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAEEC,SAAS,EACTC,iBAAiB,EAEjBC,SAAS,EACTC,QAAQ,QACH,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGrC;AACA,MAAMC,eAAiD,GAAGA,CAAC;EAAEC,SAAS,GAAG;AAAU,CAAC,kBAClFJ,OAAA;EACEI,SAAS,EAAEA,SAAU;EACrBC,OAAO,EAAC,WAAW;EACnBC,IAAI,EAAC,MAAM;EACXC,KAAK,EAAC,4BAA4B;EAAAC,QAAA,gBAGlCR,OAAA;IACES,CAAC,EAAC,IAAI;IACNC,CAAC,EAAC,IAAI;IACNC,KAAK,EAAC,IAAI;IACVC,MAAM,EAAC,IAAI;IACXC,EAAE,EAAC,GAAG;IACNP,IAAI,EAAC,cAAc;IACnBQ,MAAM,EAAC,uBAAuB;IAC9BC,WAAW,EAAC;EAAK;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClB,CAAC,eAGFnB,OAAA;IACES,CAAC,EAAC,IAAI;IACNC,CAAC,EAAC,IAAI;IACNC,KAAK,EAAC,IAAI;IACVC,MAAM,EAAC,IAAI;IACXC,EAAE,EAAC,GAAG;IACNP,IAAI,EAAC;EAAuB;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC7B,CAAC,eAGFnB,OAAA;IAAQoB,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC,GAAG;IAAChB,IAAI,EAAC;EAAuB;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC7DnB,OAAA;IAAQoB,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC,GAAG;IAAChB,IAAI,EAAC;EAAuB;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAG7DnB,OAAA;IAAQoB,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC,GAAG;IAAChB,IAAI,EAAC;EAAwB;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC9DnB,OAAA;IAAQoB,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC,GAAG;IAAChB,IAAI,EAAC;EAAwB;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAG9DnB,OAAA;IAAQoB,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC,KAAK;IAAChB,IAAI,EAAC;EAAyB;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACjEnB,OAAA;IAAQoB,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC,KAAK;IAAChB,IAAI,EAAC;EAAyB;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACjEnB,OAAA;IAAQoB,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC,KAAK;IAAChB,IAAI,EAAC;EAAuB;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC/DnB,OAAA;IAAQoB,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC,KAAK;IAAChB,IAAI,EAAC;EAAuB;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAG/DnB,OAAA;IACEuB,CAAC,EAAC,uBAAuB;IACzBT,MAAM,EAAC,uBAAuB;IAC9BC,WAAW,EAAC,GAAG;IACfS,aAAa,EAAC,OAAO;IACrBlB,IAAI,EAAC;EAAM;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC,eAGFnB,OAAA;IAAMyB,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACd,MAAM,EAAC,uBAAuB;IAACC,WAAW,EAAC,KAAK;IAACS,aAAa,EAAC;EAAO;IAAAR,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC/GnB,OAAA;IAAMyB,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACd,MAAM,EAAC,uBAAuB;IAACC,WAAW,EAAC,KAAK;IAACS,aAAa,EAAC;EAAO;IAAAR,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAG/GnB,OAAA;IAAQoB,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,GAAG;IAACC,CAAC,EAAC,KAAK;IAAChB,IAAI,EAAC;EAAuB;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC9DnB,OAAA;IAAQoB,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,GAAG;IAACC,CAAC,EAAC,KAAK;IAAChB,IAAI,EAAC;EAAuB;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC9DnB,OAAA;IAAQoB,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,GAAG;IAACC,CAAC,EAAC,KAAK;IAAChB,IAAI,EAAC;EAAwB;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC/DnB,OAAA;IAAQoB,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,GAAG;IAACC,CAAC,EAAC,KAAK;IAAChB,IAAI,EAAC;EAAwB;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAG/DnB,OAAA;IACES,CAAC,EAAC,IAAI;IACNC,CAAC,EAAC,IAAI;IACNC,KAAK,EAAC,IAAI;IACVC,MAAM,EAAC,IAAI;IACXC,EAAE,EAAC,GAAG;IACNP,IAAI,EAAC,cAAc;IACnBQ,MAAM,EAAC,uBAAuB;IAC9BC,WAAW,EAAC;EAAK;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClB,CAAC,eAGFnB,OAAA;IACES,CAAC,EAAC,IAAI;IACNC,CAAC,EAAC,IAAI;IACNC,KAAK,EAAC,IAAI;IACVC,MAAM,EAAC,IAAI;IACXC,EAAE,EAAC,GAAG;IACNP,IAAI,EAAC;EAAuB;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC7B,CAAC,eAGFnB,OAAA;IAAMS,CAAC,EAAC,IAAI;IAACC,CAAC,EAAC,IAAI;IAACC,KAAK,EAAC,GAAG;IAACC,MAAM,EAAC,GAAG;IAACC,EAAE,EAAC,GAAG;IAACP,IAAI,EAAC,cAAc;IAACQ,MAAM,EAAC,uBAAuB;IAACC,WAAW,EAAC;EAAG;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACrHnB,OAAA;IAAMS,CAAC,EAAC,IAAI;IAACC,CAAC,EAAC,IAAI;IAACC,KAAK,EAAC,GAAG;IAACC,MAAM,EAAC,GAAG;IAACC,EAAE,EAAC,GAAG;IAACP,IAAI,EAAC,cAAc;IAACQ,MAAM,EAAC,uBAAuB;IAACC,WAAW,EAAC;EAAG;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAGrHnB,OAAA;IAAQoB,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC,KAAK;IAAChB,IAAI,EAAC;EAAuB;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC/DnB,OAAA;IAAMS,CAAC,EAAC,IAAI;IAACC,CAAC,EAAC,IAAI;IAACC,KAAK,EAAC,GAAG;IAACC,MAAM,EAAC,GAAG;IAACC,EAAE,EAAC,KAAK;IAACP,IAAI,EAAC;EAAuB;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACjFnB,OAAA;IAAMS,CAAC,EAAC,IAAI;IAACC,CAAC,EAAC,IAAI;IAACC,KAAK,EAAC,GAAG;IAACC,MAAM,EAAC,GAAG;IAACC,EAAE,EAAC,KAAK;IAACP,IAAI,EAAC;EAAuB;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAGjFnB,OAAA;IAAMuB,CAAC,EAAC,yBAAyB;IAACT,MAAM,EAAC,uBAAuB;IAACC,WAAW,EAAC,GAAG;IAACT,IAAI,EAAC;EAAM;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC/FnB,OAAA;IAAMuB,CAAC,EAAC,yBAAyB;IAACT,MAAM,EAAC,uBAAuB;IAACC,WAAW,EAAC,GAAG;IAACT,IAAI,EAAC;EAAM;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC5F,CACN;;AAED;AAAAU,EAAA,GAnGM1B,eAAiD;AA0GvD,MAAM2B,WAAuC,GAAGA,CAAC;EAAEC,OAAO,GAAG;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC3E,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC6C,WAAW,EAAEC,cAAc,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAM+C,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQN,OAAO;MACb,KAAK,OAAO;QACV,OAAO,4GAA4G;MACrH,KAAK,OAAO;QACV,OAAO,uFAAuF;MAChG;QACE,OAAO,4GAA4G;IACvH;EACF,CAAC;EAED,MAAM,CAACO,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAgB,CACtD;IACEkD,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,WAAW;IACjBC,OAAO,EAAEL,iBAAiB,CAAC,CAAC;IAC5BM,SAAS,EAAE,IAAIC,IAAI,CAAC;EACtB,CAAC,CACF,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACyD,QAAQ,EAAEC,WAAW,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM2D,cAAc,GAAG1D,MAAM,CAAiB,IAAI,CAAC;EAEnD,MAAM2D,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAF,cAAc,CAACG,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAED9D,SAAS,CAAC,MAAM;IACd0D,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACZ,QAAQ,CAAC,CAAC;EAEd,MAAMiB,eAAe,GAAGA,CAAA,KAAM;IAC5B,QAAQxB,OAAO;MACb,KAAK,OAAO;QACV,OAAO,CACL,wBAAwB,EACxB,iBAAiB,EACjB,uBAAuB,EACvB,uBAAuB,EACvB,oBAAoB,EACpB,kBAAkB,CACnB;MACH,KAAK,OAAO;QACV,OAAO,CACL,qBAAqB,EACrB,cAAc,EACd,gBAAgB,EAChB,eAAe,EACf,gBAAgB,EAChB,iBAAiB,CAClB;MACH;QACE,OAAO,CACL,qBAAqB,EACrB,sBAAsB,EACtB,uBAAuB,EACvB,wBAAwB,EACxB,WAAW,EACX,kBAAkB,CACnB;IACL;EACF,CAAC;EAED,MAAMyB,YAAY,GAAGD,eAAe,CAAC,CAAC;EAEtC,MAAME,aAAa,GAAIC,WAAmB,IAAa;IACrD,MAAMC,OAAO,GAAGD,WAAW,CAACE,WAAW,CAAC,CAAC;;IAEzC;IACA,IAAI7B,OAAO,KAAK,OAAO,EAAE;MACvB,IAAI4B,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;QACzD,OAAO,sJAAsJ;MAC/J;MACA,IAAIF,OAAO,CAACE,QAAQ,CAAC,KAAK,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;QACvD,OAAO,gIAAgI;MACzI;MACA,IAAIF,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,YAAY,CAAC,EAAE;QAC9D,OAAO,6FAA6F;MACtG;MACA,IAAIF,OAAO,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC3D,OAAO,sHAAsH;MAC/H;IACF;IAEA,IAAIF,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAC1D,OAAO9B,OAAO,KAAK,OAAO,GACtB,0EAA0E,GAC1E,uHAAuH;IAC7H;IAEA,IAAI4B,OAAO,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAC5D,OAAO,0IAA0I;IACnJ;IAEA,IAAIF,OAAO,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC7B,OAAO,uHAAuH;IAChI;IAEA,IAAIF,OAAO,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC1D,OAAO,mHAAmH;IAC5H;IAEA,IAAIF,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MACxD,OAAO,mHAAmH;IAC5H;IAEA,IAAIF,OAAO,CAACE,QAAQ,CAAC,KAAK,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;MACtF,OAAO,wGAAwG;IACjH;IAEA,IAAIF,OAAO,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MACvF,OAAO,uFAAuF;IAChG;IAEA,IAAIF,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;MACzD,OAAO,kEAAkE;IAC3E;IAEA,OAAO,uGAAuG;EAChH,CAAC;EAED,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACjB,YAAY,CAACkB,IAAI,CAAC,CAAC,EAAE;IAE1B,MAAML,WAAwB,GAAG;MAC/BlB,EAAE,EAAEI,IAAI,CAACoB,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MACzBxB,IAAI,EAAE,MAAM;MACZC,OAAO,EAAEG,YAAY;MACrBF,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC;IAEDL,WAAW,CAAC2B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAER,WAAW,CAAC,CAAC;IAC3CZ,eAAe,CAAC,EAAE,CAAC;IACnBE,WAAW,CAAC,IAAI,CAAC;;IAEjB;IACAmB,UAAU,CAAC,MAAM;MACf,MAAMC,UAAuB,GAAG;QAC9B5B,EAAE,EAAE,CAACI,IAAI,CAACoB,GAAG,CAAC,CAAC,GAAG,CAAC,EAAEC,QAAQ,CAAC,CAAC;QAC/BxB,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAEe,aAAa,CAACZ,YAAY,CAAC;QACpCF,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC;MAEDL,WAAW,CAAC2B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEE,UAAU,CAAC,CAAC;MAC1CpB,WAAW,CAAC,KAAK,CAAC;IACpB,CAAC,EAAE,IAAI,GAAGqB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC;EACjC,CAAC;EAED,MAAMC,gBAAgB,GAAIC,KAAa,IAAK;IAC1C1B,eAAe,CAAC0B,KAAK,CAAC;EACxB,CAAC;EAED,MAAMC,cAAc,GAAIC,CAAsB,IAAK;IACjD,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBf,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAED,oBACE9D,OAAA,CAAAE,SAAA;IAAAM,QAAA,gBAEER,OAAA,CAACN,eAAe;MAAAc,QAAA,EACb,CAACyB,MAAM,iBACNjC,OAAA,CAACP,MAAM,CAACqF,GAAG;QACTC,OAAO,EAAE;UAAEC,KAAK,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAE,CAAE;QAClCC,OAAO,EAAE;UAAEF,KAAK,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAE,CAAE;QAClCE,IAAI,EAAE;UAAEH,KAAK,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAE,CAAE;QAC/B7E,SAAS,EAAC,4BAA4B;QAAAI,QAAA,gBAEtCR,OAAA,CAACP,MAAM,CAAC2F,MAAM;UACZC,UAAU,EAAE;YAAEL,KAAK,EAAE,GAAG;YAAEM,MAAM,EAAE;UAAE,CAAE;UACtCC,QAAQ,EAAE;YAAEP,KAAK,EAAE;UAAI,CAAE;UACzBQ,OAAO,EAAEA,CAAA,KAAMtD,SAAS,CAAC,IAAI,CAAE;UAC/B9B,SAAS,EAAC,mOAAmO;UAAAI,QAAA,gBAG7OR,OAAA;YAAKI,SAAS,EAAC;UAA+I;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGjKnB,OAAA;YAAKI,SAAS,EAAC,+BAA+B;YAAAI,QAAA,gBAC5CR,OAAA,CAACP,MAAM,CAACqF,GAAG;cACTI,OAAO,EAAE;gBAAEI,MAAM,EAAE;cAAI,CAAE;cACzBG,UAAU,EAAE;gBAAEC,QAAQ,EAAE,CAAC;gBAAEC,MAAM,EAAEC,QAAQ;gBAAEC,IAAI,EAAE;cAAS,CAAE;cAC9DzF,SAAS,EAAC;YAA6D;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC,eACFnB,OAAA,CAACP,MAAM,CAACqF,GAAG;cACTI,OAAO,EAAE;gBAAEI,MAAM,EAAE,CAAC;cAAI,CAAE;cAC1BG,UAAU,EAAE;gBAAEC,QAAQ,EAAE,EAAE;gBAAEC,MAAM,EAAEC,QAAQ;gBAAEC,IAAI,EAAE;cAAS,CAAE;cAC/DzF,SAAS,EAAC;YAA2D;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNnB,OAAA;YAAKI,SAAS,EAAC,gDAAgD;YAAAI,QAAA,eAC7DR,OAAA,CAACG,eAAe;cAACC,SAAS,EAAC;YAAmC;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eAGNnB,OAAA,CAACP,MAAM,CAACqF,GAAG;YACTI,OAAO,EAAE;cACPF,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;cAClBC,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;YACvB,CAAE;YACFQ,UAAU,EAAE;cACVC,QAAQ,EAAE,CAAC;cACXC,MAAM,EAAEC,QAAQ;cAChBC,IAAI,EAAE;YACR,CAAE;YACFzF,SAAS,EAAC;UAAuE;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF,CAAC,eAEFnB,OAAA,CAACP,MAAM,CAACqF,GAAG;YACTI,OAAO,EAAE;cACPF,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;cAClBC,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;YACvB,CAAE;YACFQ,UAAU,EAAE;cACVC,QAAQ,EAAE,GAAG;cACbC,MAAM,EAAEC,QAAQ;cAChBC,IAAI,EAAE,WAAW;cACjBC,KAAK,EAAE;YACT,CAAE;YACF1F,SAAS,EAAC;UAAwE;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF,CAAC,eAEFnB,OAAA,CAACP,MAAM,CAACqF,GAAG;YACTI,OAAO,EAAE;cACPF,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;cAClBC,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;YACvB,CAAE;YACFQ,UAAU,EAAE;cACVC,QAAQ,EAAE,CAAC;cACXC,MAAM,EAAEC,QAAQ;cAChBC,IAAI,EAAE,WAAW;cACjBC,KAAK,EAAE;YACT,CAAE;YACF1F,SAAS,EAAC;UAAyE;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC,eAGFnB,OAAA;YAAKI,SAAS,EAAC;UAA0G;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/G,CAAC,eAGhBnB,OAAA,CAACP,MAAM,CAACqF,GAAG;UACTC,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAExE,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCyE,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAExE,CAAC,EAAE;UAAE,CAAE;UAC9BgF,UAAU,EAAE;YAAEK,KAAK,EAAE;UAAE,CAAE;UACzB1F,SAAS,EAAC,4LAA4L;UAAAI,QAAA,gBAEtMR,OAAA;YAAKI,SAAS,EAAC,6CAA6C;YAAAI,QAAA,gBAC1DR,OAAA,CAACG,eAAe;cAACC,SAAS,EAAC;YAAyB;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvDnB,OAAA;cAAAQ,QAAA,EAAM;YAA6B;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACNnB,OAAA;YAAKI,SAAS,EAAC;UAAgG;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACb;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC,eAGlBnB,OAAA,CAACN,eAAe;MAAAc,QAAA,EACbyB,MAAM,iBACLjC,OAAA,CAACP,MAAM,CAACqF,GAAG;QACTC,OAAO,EAAE;UAAEE,OAAO,EAAE,CAAC;UAAEvE,CAAC,EAAE,GAAG;UAAEsE,KAAK,EAAE;QAAI,CAAE;QAC5CE,OAAO,EAAE;UACPD,OAAO,EAAE,CAAC;UACVvE,CAAC,EAAE,CAAC;UACJsE,KAAK,EAAE,CAAC;UACRpE,MAAM,EAAEuB,WAAW,GAAG,EAAE,GAAG;QAC7B,CAAE;QACFgD,IAAI,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEvE,CAAC,EAAE,GAAG;UAAEsE,KAAK,EAAE;QAAI,CAAE;QACzC5E,SAAS,EAAC,uGAAuG;QAAAI,QAAA,gBAGjHR,OAAA;UAAKI,SAAS,EAAC,wIAAwI;UAAAI,QAAA,gBAErJR,OAAA;YAAKI,SAAS,EAAC,6BAA6B;YAAAI,QAAA,gBAC1CR,OAAA;cAAKI,SAAS,EAAC;YAAmE;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrFnB,OAAA;cAAKI,SAAS,EAAC,oEAAoE;cAAC2F,KAAK,EAAE;gBAAEC,cAAc,EAAE;cAAO;YAAE;cAAAhF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzHnB,OAAA;cAAKI,SAAS,EAAC,2EAA2E;cAAC2F,KAAK,EAAE;gBAAEC,cAAc,EAAE;cAAK;YAAE;cAAAhF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3H,CAAC,eAENnB,OAAA;YAAKI,SAAS,EAAC,iCAAiC;YAAAI,QAAA,gBAC9CR,OAAA;cAAKI,SAAS,EAAC,kJAAkJ;cAAAI,QAAA,eAC/JR,OAAA,CAACG,eAAe;gBAACC,SAAS,EAAC;cAAmC;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC,eACNnB,OAAA;cAAAQ,QAAA,gBACER,OAAA;gBAAII,SAAS,EAAC,qCAAqC;gBAAAI,QAAA,GAAC,2EAElD,eAAAR,OAAA,CAACF,QAAQ;kBAACM,SAAS,EAAC;gBAA8B;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC,eACLnB,OAAA;gBAAGI,SAAS,EAAC,sCAAsC;gBAAAI,QAAA,gBACjDR,OAAA;kBAAKI,SAAS,EAAC;gBAAsD;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,sGAE1E;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNnB,OAAA;YAAKI,SAAS,EAAC,6CAA6C;YAAAI,QAAA,gBAC1DR,OAAA;cACEwF,OAAO,EAAEA,CAAA,KAAMpD,cAAc,CAAC,CAACD,WAAW,CAAE;cAC5C/B,SAAS,EAAC,gDAAgD;cAAAI,QAAA,eAE1DR,OAAA,CAACH,SAAS;gBAACO,SAAS,EAAC;cAAS;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACTnB,OAAA;cACEwF,OAAO,EAAEA,CAAA,KAAMtD,SAAS,CAAC,KAAK,CAAE;cAChC9B,SAAS,EAAC,gDAAgD;cAAAI,QAAA,eAE1DR,OAAA,CAACL,SAAS;gBAACS,SAAS,EAAC;cAAS;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL,CAACgB,WAAW,iBACXnC,OAAA,CAAAE,SAAA;UAAAM,QAAA,gBAEER,OAAA;YAAKI,SAAS,EAAC,oCAAoC;YAAAI,QAAA,GAChD8B,QAAQ,CAAC2D,GAAG,CAAEtC,OAAO,iBACpB3D,OAAA,CAACP,MAAM,CAACqF,GAAG;cAETC,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEvE,CAAC,EAAE;cAAG,CAAE;cAC/BwE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEvE,CAAC,EAAE;cAAE,CAAE;cAC9BN,SAAS,EAAE,QAAQuD,OAAO,CAAClB,IAAI,KAAK,MAAM,GAAG,eAAe,GAAG,aAAa,EAAG;cAAAjC,QAAA,eAE/ER,OAAA;gBAAKI,SAAS,EAAE;AACtC;AACA,0BAA0BuD,OAAO,CAAClB,IAAI,KAAK,MAAM,GACrB,qEAAqE,GACrE,qGAAqG;AACjI,uBACwB;gBAAAjC,QAAA,EACCmD,OAAO,CAACjB;cAAO;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC,GAbDwC,OAAO,CAACnB,EAAE;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAcL,CACb,CAAC,EAGD4B,QAAQ,iBACP/C,OAAA,CAACP,MAAM,CAACqF,GAAG;cACTC,OAAO,EAAE;gBAAEE,OAAO,EAAE;cAAE,CAAE;cACxBC,OAAO,EAAE;gBAAED,OAAO,EAAE;cAAE,CAAE;cACxB7E,SAAS,EAAC,kBAAkB;cAAAI,QAAA,eAE5BR,OAAA;gBAAKI,SAAS,EAAC,sHAAsH;gBAAAI,QAAA,eACnIR,OAAA;kBAAKI,SAAS,EAAC,6BAA6B;kBAAAI,QAAA,gBAC1CR,OAAA,CAACG,eAAe;oBAACC,SAAS,EAAC;kBAA8B;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5DnB,OAAA;oBAAKI,SAAS,EAAC;kBAAmD;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACrEnB,OAAA;oBAAKI,SAAS,EAAC,kDAAkD;oBAAC2F,KAAK,EAAE;sBAAEC,cAAc,EAAE;oBAAO;kBAAE;oBAAAhF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvGnB,OAAA;oBAAKI,SAAS,EAAC,mDAAmD;oBAAC2F,KAAK,EAAE;sBAAEC,cAAc,EAAE;oBAAO;kBAAE;oBAAAhF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACxGnB,OAAA;oBAAMI,SAAS,EAAC,4BAA4B;oBAAAI,QAAA,EAAC;kBAAO;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,eAEDnB,OAAA;cAAKkG,GAAG,EAAEjD;YAAe;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,EAGLmB,QAAQ,CAAC6D,MAAM,IAAI,CAAC,iBACnBnG,OAAA;YAAKI,SAAS,EAAC,WAAW;YAAAI,QAAA,gBACxBR,OAAA;cAAGI,SAAS,EAAC,4BAA4B;cAAAI,QAAA,EAAC;YAAY;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC1DnB,OAAA;cAAKI,SAAS,EAAC,sBAAsB;cAAAI,QAAA,EAClCgD,YAAY,CAAC4C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACH,GAAG,CAAEzB,KAAK,iBAClCxE,OAAA;gBAEEwF,OAAO,EAAEA,CAAA,KAAMjB,gBAAgB,CAACC,KAAK,CAAE;gBACvCpE,SAAS,EAAC,iPAAiP;gBAAAI,QAAA,EAE1PgE;cAAK,GAJDA,KAAK;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKJ,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDnB,OAAA;YAAKI,SAAS,EAAC,8BAA8B;YAAAI,QAAA,eAC3CR,OAAA;cAAKI,SAAS,EAAC,6CAA6C;cAAAI,QAAA,gBAC1DR,OAAA;gBACEyC,IAAI,EAAC,MAAM;gBACX4D,KAAK,EAAExD,YAAa;gBACpByD,QAAQ,EAAG5B,CAAC,IAAK5B,eAAe,CAAC4B,CAAC,CAAC6B,MAAM,CAACF,KAAK,CAAE;gBACjDG,UAAU,EAAE/B,cAAe;gBAC3BgC,WAAW,EAAC,kEAAgB;gBAC5BrG,SAAS,EAAC,yHAAyH;gBACnIsG,QAAQ,EAAE3D;cAAS;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACFnB,OAAA,CAACP,MAAM,CAAC2F,MAAM;gBACZI,OAAO,EAAE1B,iBAAkB;gBAC3B4C,QAAQ,EAAE,CAAC7D,YAAY,CAACkB,IAAI,CAAC,CAAC,IAAIhB,QAAS;gBAC3CsC,UAAU,EAAE;kBAAEL,KAAK,EAAE;gBAAK,CAAE;gBAC5BO,QAAQ,EAAE;kBAAEP,KAAK,EAAE;gBAAK,CAAE;gBAC1B5E,SAAS,EAAC,mPAAmP;gBAAAI,QAAA,eAE7PR,OAAA,CAACJ,iBAAiB;kBAACQ,SAAS,EAAC;gBAAS;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,eACN,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS;IACb;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC;EAAA,eAClB,CAAC;AAEP,CAAC;AAACa,EAAA,CA9ZIF,WAAuC;AAAA6E,GAAA,GAAvC7E,WAAuC;AAga7C,eAAeA,WAAW;AAAC,IAAAD,EAAA,EAAA8E,GAAA;AAAAC,YAAA,CAAA/E,EAAA;AAAA+E,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}