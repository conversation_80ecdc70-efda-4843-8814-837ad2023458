{"ast": null, "code": "import { w as wrap, r as replaceTraps } from './wrap-idb-value.js';\nexport { u as unwrap, w as wrap } from './wrap-idb-value.js';\n\n/**\n * Open a database.\n *\n * @param name Name of the database.\n * @param version Schema version.\n * @param callbacks Additional callbacks.\n */\nfunction openDB(name, version) {\n  let {\n    blocked,\n    upgrade,\n    blocking,\n    terminated\n  } = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  const request = indexedDB.open(name, version);\n  const openPromise = wrap(request);\n  if (upgrade) {\n    request.addEventListener('upgradeneeded', event => {\n      upgrade(wrap(request.result), event.oldVersion, event.newVersion, wrap(request.transaction), event);\n    });\n  }\n  if (blocked) {\n    request.addEventListener('blocked', event => blocked(\n    // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n    event.oldVersion, event.newVersion, event));\n  }\n  openPromise.then(db => {\n    if (terminated) db.addEventListener('close', () => terminated());\n    if (blocking) {\n      db.addEventListener('versionchange', event => blocking(event.oldVersion, event.newVersion, event));\n    }\n  }).catch(() => {});\n  return openPromise;\n}\n/**\n * Delete a database.\n *\n * @param name Name of the database.\n */\nfunction deleteDB(name) {\n  let {\n    blocked\n  } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const request = indexedDB.deleteDatabase(name);\n  if (blocked) {\n    request.addEventListener('blocked', event => blocked(\n    // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n    event.oldVersion, event));\n  }\n  return wrap(request).then(() => undefined);\n}\nconst readMethods = ['get', 'getKey', 'getAll', 'getAllKeys', 'count'];\nconst writeMethods = ['put', 'add', 'delete', 'clear'];\nconst cachedMethods = new Map();\nfunction getMethod(target, prop) {\n  if (!(target instanceof IDBDatabase && !(prop in target) && typeof prop === 'string')) {\n    return;\n  }\n  if (cachedMethods.get(prop)) return cachedMethods.get(prop);\n  const targetFuncName = prop.replace(/FromIndex$/, '');\n  const useIndex = prop !== targetFuncName;\n  const isWrite = writeMethods.includes(targetFuncName);\n  if (\n  // Bail if the target doesn't exist on the target. Eg, getAll isn't in Edge.\n  !(targetFuncName in (useIndex ? IDBIndex : IDBObjectStore).prototype) || !(isWrite || readMethods.includes(targetFuncName))) {\n    return;\n  }\n  const method = async function (storeName) {\n    // isWrite ? 'readwrite' : undefined gzipps better, but fails in Edge :(\n    const tx = this.transaction(storeName, isWrite ? 'readwrite' : 'readonly');\n    let target = tx.store;\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    if (useIndex) target = target.index(args.shift());\n    // Must reject if op rejects.\n    // If it's a write operation, must reject if tx.done rejects.\n    // Must reject with op rejection first.\n    // Must resolve with op value.\n    // Must handle both promises (no unhandled rejections)\n    return (await Promise.all([target[targetFuncName](...args), isWrite && tx.done]))[0];\n  };\n  cachedMethods.set(prop, method);\n  return method;\n}\nreplaceTraps(oldTraps => ({\n  ...oldTraps,\n  get: (target, prop, receiver) => getMethod(target, prop) || oldTraps.get(target, prop, receiver),\n  has: (target, prop) => !!getMethod(target, prop) || oldTraps.has(target, prop)\n}));\nexport { deleteDB, openDB };", "map": {"version": 3, "names": ["w", "wrap", "r", "replaceTraps", "u", "unwrap", "openDB", "name", "version", "blocked", "upgrade", "blocking", "terminated", "arguments", "length", "undefined", "request", "indexedDB", "open", "openPromise", "addEventListener", "event", "result", "oldVersion", "newVersion", "transaction", "then", "db", "catch", "deleteDB", "deleteDatabase", "readMethods", "writeMethods", "cachedMethods", "Map", "getMethod", "target", "prop", "IDBDatabase", "get", "targetFuncName", "replace", "useIndex", "isWrite", "includes", "IDBIndex", "IDBObjectStore", "prototype", "method", "storeName", "tx", "store", "_len", "args", "Array", "_key", "index", "shift", "Promise", "all", "done", "set", "oldTraps", "receiver", "has"], "sources": ["C:/Users/<USER>/Desktop/مشروع/node_modules/idb/build/index.js"], "sourcesContent": ["import { w as wrap, r as replaceTraps } from './wrap-idb-value.js';\nexport { u as unwrap, w as wrap } from './wrap-idb-value.js';\n\n/**\n * Open a database.\n *\n * @param name Name of the database.\n * @param version Schema version.\n * @param callbacks Additional callbacks.\n */\nfunction openDB(name, version, { blocked, upgrade, blocking, terminated } = {}) {\n    const request = indexedDB.open(name, version);\n    const openPromise = wrap(request);\n    if (upgrade) {\n        request.addEventListener('upgradeneeded', (event) => {\n            upgrade(wrap(request.result), event.oldVersion, event.newVersion, wrap(request.transaction), event);\n        });\n    }\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event.newVersion, event));\n    }\n    openPromise\n        .then((db) => {\n        if (terminated)\n            db.addEventListener('close', () => terminated());\n        if (blocking) {\n            db.addEventListener('versionchange', (event) => blocking(event.oldVersion, event.newVersion, event));\n        }\n    })\n        .catch(() => { });\n    return openPromise;\n}\n/**\n * Delete a database.\n *\n * @param name Name of the database.\n */\nfunction deleteDB(name, { blocked } = {}) {\n    const request = indexedDB.deleteDatabase(name);\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event));\n    }\n    return wrap(request).then(() => undefined);\n}\n\nconst readMethods = ['get', 'getKey', 'getAll', 'getAllKeys', 'count'];\nconst writeMethods = ['put', 'add', 'delete', 'clear'];\nconst cachedMethods = new Map();\nfunction getMethod(target, prop) {\n    if (!(target instanceof IDBDatabase &&\n        !(prop in target) &&\n        typeof prop === 'string')) {\n        return;\n    }\n    if (cachedMethods.get(prop))\n        return cachedMethods.get(prop);\n    const targetFuncName = prop.replace(/FromIndex$/, '');\n    const useIndex = prop !== targetFuncName;\n    const isWrite = writeMethods.includes(targetFuncName);\n    if (\n    // Bail if the target doesn't exist on the target. Eg, getAll isn't in Edge.\n    !(targetFuncName in (useIndex ? IDBIndex : IDBObjectStore).prototype) ||\n        !(isWrite || readMethods.includes(targetFuncName))) {\n        return;\n    }\n    const method = async function (storeName, ...args) {\n        // isWrite ? 'readwrite' : undefined gzipps better, but fails in Edge :(\n        const tx = this.transaction(storeName, isWrite ? 'readwrite' : 'readonly');\n        let target = tx.store;\n        if (useIndex)\n            target = target.index(args.shift());\n        // Must reject if op rejects.\n        // If it's a write operation, must reject if tx.done rejects.\n        // Must reject with op rejection first.\n        // Must resolve with op value.\n        // Must handle both promises (no unhandled rejections)\n        return (await Promise.all([\n            target[targetFuncName](...args),\n            isWrite && tx.done,\n        ]))[0];\n    };\n    cachedMethods.set(prop, method);\n    return method;\n}\nreplaceTraps((oldTraps) => ({\n    ...oldTraps,\n    get: (target, prop, receiver) => getMethod(target, prop) || oldTraps.get(target, prop, receiver),\n    has: (target, prop) => !!getMethod(target, prop) || oldTraps.has(target, prop),\n}));\n\nexport { deleteDB, openDB };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,YAAY,QAAQ,qBAAqB;AAClE,SAASC,CAAC,IAAIC,MAAM,EAAEL,CAAC,IAAIC,IAAI,QAAQ,qBAAqB;;AAE5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,MAAMA,CAACC,IAAI,EAAEC,OAAO,EAAmD;EAAA,IAAjD;IAAEC,OAAO;IAAEC,OAAO;IAAEC,QAAQ;IAAEC;EAAW,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC1E,MAAMG,OAAO,GAAGC,SAAS,CAACC,IAAI,CAACX,IAAI,EAAEC,OAAO,CAAC;EAC7C,MAAMW,WAAW,GAAGlB,IAAI,CAACe,OAAO,CAAC;EACjC,IAAIN,OAAO,EAAE;IACTM,OAAO,CAACI,gBAAgB,CAAC,eAAe,EAAGC,KAAK,IAAK;MACjDX,OAAO,CAACT,IAAI,CAACe,OAAO,CAACM,MAAM,CAAC,EAAED,KAAK,CAACE,UAAU,EAAEF,KAAK,CAACG,UAAU,EAAEvB,IAAI,CAACe,OAAO,CAACS,WAAW,CAAC,EAAEJ,KAAK,CAAC;IACvG,CAAC,CAAC;EACN;EACA,IAAIZ,OAAO,EAAE;IACTO,OAAO,CAACI,gBAAgB,CAAC,SAAS,EAAGC,KAAK,IAAKZ,OAAO;IACtD;IACAY,KAAK,CAACE,UAAU,EAAEF,KAAK,CAACG,UAAU,EAAEH,KAAK,CAAC,CAAC;EAC/C;EACAF,WAAW,CACNO,IAAI,CAAEC,EAAE,IAAK;IACd,IAAIf,UAAU,EACVe,EAAE,CAACP,gBAAgB,CAAC,OAAO,EAAE,MAAMR,UAAU,CAAC,CAAC,CAAC;IACpD,IAAID,QAAQ,EAAE;MACVgB,EAAE,CAACP,gBAAgB,CAAC,eAAe,EAAGC,KAAK,IAAKV,QAAQ,CAACU,KAAK,CAACE,UAAU,EAAEF,KAAK,CAACG,UAAU,EAAEH,KAAK,CAAC,CAAC;IACxG;EACJ,CAAC,CAAC,CACGO,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;EACrB,OAAOT,WAAW;AACtB;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,QAAQA,CAACtB,IAAI,EAAoB;EAAA,IAAlB;IAAEE;EAAQ,CAAC,GAAAI,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACpC,MAAMG,OAAO,GAAGC,SAAS,CAACa,cAAc,CAACvB,IAAI,CAAC;EAC9C,IAAIE,OAAO,EAAE;IACTO,OAAO,CAACI,gBAAgB,CAAC,SAAS,EAAGC,KAAK,IAAKZ,OAAO;IACtD;IACAY,KAAK,CAACE,UAAU,EAAEF,KAAK,CAAC,CAAC;EAC7B;EACA,OAAOpB,IAAI,CAACe,OAAO,CAAC,CAACU,IAAI,CAAC,MAAMX,SAAS,CAAC;AAC9C;AAEA,MAAMgB,WAAW,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,OAAO,CAAC;AACtE,MAAMC,YAAY,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC;AACtD,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC/B,SAASC,SAASA,CAACC,MAAM,EAAEC,IAAI,EAAE;EAC7B,IAAI,EAAED,MAAM,YAAYE,WAAW,IAC/B,EAAED,IAAI,IAAID,MAAM,CAAC,IACjB,OAAOC,IAAI,KAAK,QAAQ,CAAC,EAAE;IAC3B;EACJ;EACA,IAAIJ,aAAa,CAACM,GAAG,CAACF,IAAI,CAAC,EACvB,OAAOJ,aAAa,CAACM,GAAG,CAACF,IAAI,CAAC;EAClC,MAAMG,cAAc,GAAGH,IAAI,CAACI,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;EACrD,MAAMC,QAAQ,GAAGL,IAAI,KAAKG,cAAc;EACxC,MAAMG,OAAO,GAAGX,YAAY,CAACY,QAAQ,CAACJ,cAAc,CAAC;EACrD;EACA;EACA,EAAEA,cAAc,IAAI,CAACE,QAAQ,GAAGG,QAAQ,GAAGC,cAAc,EAAEC,SAAS,CAAC,IACjE,EAAEJ,OAAO,IAAIZ,WAAW,CAACa,QAAQ,CAACJ,cAAc,CAAC,CAAC,EAAE;IACpD;EACJ;EACA,MAAMQ,MAAM,GAAG,eAAAA,CAAgBC,SAAS,EAAW;IAC/C;IACA,MAAMC,EAAE,GAAG,IAAI,CAACzB,WAAW,CAACwB,SAAS,EAAEN,OAAO,GAAG,WAAW,GAAG,UAAU,CAAC;IAC1E,IAAIP,MAAM,GAAGc,EAAE,CAACC,KAAK;IAAC,SAAAC,IAAA,GAAAvC,SAAA,CAAAC,MAAA,EAHmBuC,IAAI,OAAAC,KAAA,CAAAF,IAAA,OAAAA,IAAA,WAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;MAAJF,IAAI,CAAAE,IAAA,QAAA1C,SAAA,CAAA0C,IAAA;IAAA;IAI7C,IAAIb,QAAQ,EACRN,MAAM,GAAGA,MAAM,CAACoB,KAAK,CAACH,IAAI,CAACI,KAAK,CAAC,CAAC,CAAC;IACvC;IACA;IACA;IACA;IACA;IACA,OAAO,CAAC,MAAMC,OAAO,CAACC,GAAG,CAAC,CACtBvB,MAAM,CAACI,cAAc,CAAC,CAAC,GAAGa,IAAI,CAAC,EAC/BV,OAAO,IAAIO,EAAE,CAACU,IAAI,CACrB,CAAC,EAAE,CAAC,CAAC;EACV,CAAC;EACD3B,aAAa,CAAC4B,GAAG,CAACxB,IAAI,EAAEW,MAAM,CAAC;EAC/B,OAAOA,MAAM;AACjB;AACA7C,YAAY,CAAE2D,QAAQ,KAAM;EACxB,GAAGA,QAAQ;EACXvB,GAAG,EAAEA,CAACH,MAAM,EAAEC,IAAI,EAAE0B,QAAQ,KAAK5B,SAAS,CAACC,MAAM,EAAEC,IAAI,CAAC,IAAIyB,QAAQ,CAACvB,GAAG,CAACH,MAAM,EAAEC,IAAI,EAAE0B,QAAQ,CAAC;EAChGC,GAAG,EAAEA,CAAC5B,MAAM,EAAEC,IAAI,KAAK,CAAC,CAACF,SAAS,CAACC,MAAM,EAAEC,IAAI,CAAC,IAAIyB,QAAQ,CAACE,GAAG,CAAC5B,MAAM,EAAEC,IAAI;AACjF,CAAC,CAAC,CAAC;AAEH,SAASR,QAAQ,EAAEvB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}