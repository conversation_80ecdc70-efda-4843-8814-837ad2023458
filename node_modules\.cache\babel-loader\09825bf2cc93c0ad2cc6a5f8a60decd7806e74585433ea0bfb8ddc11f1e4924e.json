{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0634\\u0631\\u0648\\u0639\\\\src\\\\pages\\\\admin\\\\AdminDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\n\n// Components\nimport AdminSidebar from '../../components/Admin/AdminSidebar';\nimport AdminHeader from '../../components/Admin/AdminHeader';\nimport DashboardOverview from '../../components/Admin/DashboardOverview';\nimport CategoriesManagement from '../../components/Admin/CategoriesManagement';\nimport CoursesManagement from '../../components/Admin/CoursesManagement';\nimport StudentsManagement from '../../components/Admin/StudentsManagement';\nimport QuizzesManagement from '../../components/Admin/QuizzesManagement';\nimport CertificatesManagement from '../../components/Admin/CertificatesManagement';\nimport AnalyticsPage from '../../components/Admin/AnalyticsPage';\nimport SettingsPage from '../../components/Admin/SettingsPage';\n\n// Types\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminDashboard = ({\n  user,\n  onLogout\n}) => {\n  _s();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    dir: \"rtl\",\n    children: [/*#__PURE__*/_jsxDEV(AdminSidebar, {\n      isOpen: sidebarOpen,\n      onClose: () => setSidebarOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(AdminHeader, {\n        user: user,\n        onMenuClick: () => setSidebarOpen(true),\n        onLogout: onLogout\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-6\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.3\n          },\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(DashboardOverview, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/categories\",\n              element: /*#__PURE__*/_jsxDEV(CategoriesManagement, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 50\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/courses\",\n              element: /*#__PURE__*/_jsxDEV(CoursesManagement, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/students\",\n              element: /*#__PURE__*/_jsxDEV(StudentsManagement, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/quizzes\",\n              element: /*#__PURE__*/_jsxDEV(QuizzesManagement, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/certificates\",\n              element: /*#__PURE__*/_jsxDEV(CertificatesManagement, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 52\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/analytics\",\n              element: /*#__PURE__*/_jsxDEV(AnalyticsPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 49\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/settings\",\n              element: /*#__PURE__*/_jsxDEV(SettingsPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"*\",\n              element: /*#__PURE__*/_jsxDEV(Navigate, {\n                to: \"/admin\",\n                replace: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this), sidebarOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\",\n      onClick: () => setSidebarOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboard, \"5rGDkYpGQ8fHM9RkMWnKOwsxadk=\");\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useState", "Routes", "Route", "Navigate", "motion", "AdminSidebar", "Ad<PERSON><PERSON><PERSON><PERSON>", "DashboardOverview", "CategoriesManagement", "CoursesManagement", "StudentsManagement", "QuizzesManagement", "CertificatesManagement", "AnalyticsPage", "SettingsPage", "jsxDEV", "_jsxDEV", "AdminDashboard", "user", "onLogout", "_s", "sidebarOpen", "setSidebarOpen", "className", "dir", "children", "isOpen", "onClose", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onMenuClick", "div", "initial", "opacity", "y", "animate", "transition", "duration", "path", "element", "to", "replace", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/pages/admin/AdminDashboard.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\n\n// Components\nimport AdminSidebar from '../../components/Admin/AdminSidebar';\nimport AdminHeader from '../../components/Admin/AdminHeader';\nimport DashboardOverview from '../../components/Admin/DashboardOverview';\nimport CategoriesManagement from '../../components/Admin/CategoriesManagement';\nimport CoursesManagement from '../../components/Admin/CoursesManagement';\nimport StudentsManagement from '../../components/Admin/StudentsManagement';\nimport QuizzesManagement from '../../components/Admin/QuizzesManagement';\nimport CertificatesManagement from '../../components/Admin/CertificatesManagement';\nimport AnalyticsPage from '../../components/Admin/AnalyticsPage';\nimport SettingsPage from '../../components/Admin/SettingsPage';\n\n// Types\nimport { Admin } from '../../types';\n\ninterface AdminDashboardProps {\n  user: Admin;\n  onLogout: () => void;\n}\n\nconst AdminDashboard: React.FC<AdminDashboardProps> = ({ user, onLogout }) => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  return (\n    <div className=\"flex h-screen bg-gray-50\" dir=\"rtl\">\n      {/* Sidebar */}\n      <AdminSidebar \n        isOpen={sidebarOpen} \n        onClose={() => setSidebarOpen(false)} \n      />\n      \n      {/* Main Content */}\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        {/* Header */}\n        <AdminHeader \n          user={user}\n          onMenuClick={() => setSidebarOpen(true)}\n          onLogout={onLogout}\n        />\n        \n        {/* Page Content */}\n        <main className=\"flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-6\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.3 }}\n          >\n            <Routes>\n              <Route path=\"/\" element={<DashboardOverview />} />\n              <Route path=\"/categories\" element={<CategoriesManagement />} />\n              <Route path=\"/courses\" element={<CoursesManagement />} />\n              <Route path=\"/students\" element={<StudentsManagement />} />\n              <Route path=\"/quizzes\" element={<QuizzesManagement />} />\n              <Route path=\"/certificates\" element={<CertificatesManagement />} />\n              <Route path=\"/analytics\" element={<AnalyticsPage />} />\n              <Route path=\"/settings\" element={<SettingsPage />} />\n              <Route path=\"*\" element={<Navigate to=\"/admin\" replace />} />\n            </Routes>\n          </motion.div>\n        </main>\n      </div>\n      \n      {/* Mobile Sidebar Overlay */}\n      {sidebarOpen && (\n        <div \n          className=\"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\"\n          onClick={() => setSidebarOpen(false)}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default AdminDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AAC1D,SAASC,MAAM,QAAQ,eAAe;;AAEtC;AACA,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,OAAOC,iBAAiB,MAAM,0CAA0C;AACxE,OAAOC,oBAAoB,MAAM,6CAA6C;AAC9E,OAAOC,iBAAiB,MAAM,0CAA0C;AACxE,OAAOC,kBAAkB,MAAM,2CAA2C;AAC1E,OAAOC,iBAAiB,MAAM,0CAA0C;AACxE,OAAOC,sBAAsB,MAAM,+CAA+C;AAClF,OAAOC,aAAa,MAAM,sCAAsC;AAChE,OAAOC,YAAY,MAAM,qCAAqC;;AAE9D;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAQA,MAAMC,cAA6C,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5E,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAErD,oBACEgB,OAAA;IAAKO,SAAS,EAAC,0BAA0B;IAACC,GAAG,EAAC,KAAK;IAAAC,QAAA,gBAEjDT,OAAA,CAACX,YAAY;MACXqB,MAAM,EAAEL,WAAY;MACpBM,OAAO,EAAEA,CAAA,KAAML,cAAc,CAAC,KAAK;IAAE;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,eAGFf,OAAA;MAAKO,SAAS,EAAC,sCAAsC;MAAAE,QAAA,gBAEnDT,OAAA,CAACV,WAAW;QACVY,IAAI,EAAEA,IAAK;QACXc,WAAW,EAAEA,CAAA,KAAMV,cAAc,CAAC,IAAI,CAAE;QACxCH,QAAQ,EAAEA;MAAS;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,eAGFf,OAAA;QAAMO,SAAS,EAAC,yDAAyD;QAAAE,QAAA,eACvET,OAAA,CAACZ,MAAM,CAAC6B,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAAd,QAAA,eAE9BT,OAAA,CAACf,MAAM;YAAAwB,QAAA,gBACLT,OAAA,CAACd,KAAK;cAACsC,IAAI,EAAC,GAAG;cAACC,OAAO,eAAEzB,OAAA,CAACT,iBAAiB;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClDf,OAAA,CAACd,KAAK;cAACsC,IAAI,EAAC,aAAa;cAACC,OAAO,eAAEzB,OAAA,CAACR,oBAAoB;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/Df,OAAA,CAACd,KAAK;cAACsC,IAAI,EAAC,UAAU;cAACC,OAAO,eAAEzB,OAAA,CAACP,iBAAiB;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzDf,OAAA,CAACd,KAAK;cAACsC,IAAI,EAAC,WAAW;cAACC,OAAO,eAAEzB,OAAA,CAACN,kBAAkB;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3Df,OAAA,CAACd,KAAK;cAACsC,IAAI,EAAC,UAAU;cAACC,OAAO,eAAEzB,OAAA,CAACL,iBAAiB;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzDf,OAAA,CAACd,KAAK;cAACsC,IAAI,EAAC,eAAe;cAACC,OAAO,eAAEzB,OAAA,CAACJ,sBAAsB;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnEf,OAAA,CAACd,KAAK;cAACsC,IAAI,EAAC,YAAY;cAACC,OAAO,eAAEzB,OAAA,CAACH,aAAa;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvDf,OAAA,CAACd,KAAK;cAACsC,IAAI,EAAC,WAAW;cAACC,OAAO,eAAEzB,OAAA,CAACF,YAAY;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrDf,OAAA,CAACd,KAAK;cAACsC,IAAI,EAAC,GAAG;cAACC,OAAO,eAAEzB,OAAA,CAACb,QAAQ;gBAACuC,EAAE,EAAC,QAAQ;gBAACC,OAAO;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAGLV,WAAW,iBACVL,OAAA;MACEO,SAAS,EAAC,qDAAqD;MAC/DqB,OAAO,EAAEA,CAAA,KAAMtB,cAAc,CAAC,KAAK;IAAE;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACX,EAAA,CAnDIH,cAA6C;AAAA4B,EAAA,GAA7C5B,cAA6C;AAqDnD,eAAeA,cAAc;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}