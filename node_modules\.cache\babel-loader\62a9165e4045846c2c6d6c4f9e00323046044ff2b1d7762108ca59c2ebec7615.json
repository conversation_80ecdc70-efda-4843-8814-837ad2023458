{"ast": null, "code": "import { createContext } from 'react';\n\n/**\n * Internal, exported only for usage in Framer\n */\nconst SwitchLayoutGroupContext = createContext({});\nexport { SwitchLayoutGroupContext };", "map": {"version": 3, "names": ["createContext", "SwitchLayoutGroupContext"], "sources": ["C:/Users/<USER>/Desktop/مشروع/node_modules/framer-motion/dist/es/context/SwitchLayoutGroupContext.mjs"], "sourcesContent": ["import { createContext } from 'react';\n\n/**\n * Internal, exported only for usage in Framer\n */\nconst SwitchLayoutGroupContext = createContext({});\n\nexport { SwitchLayoutGroupContext };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;;AAErC;AACA;AACA;AACA,MAAMC,wBAAwB,GAAGD,aAAa,CAAC,CAAC,CAAC,CAAC;AAElD,SAASC,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}