{"ast": null, "code": "export const mockCourses=[{id:'1',title:'أساسيات البرمجة',description:'تعلم أساسيات البرمجة من الصفر حتى الاحتراف',categoryId:'programming',instructorId:'admin-001',videos:[],pdfs:[],quizzes:[],isActive:true,createdAt:new Date('2024-01-01'),updatedAt:new Date('2024-01-15')},{id:'2',title:'تطوير المواقع الحديثة',description:'تعلم تطوير المواقع باستخدام أحدث التقنيات',categoryId:'web',instructorId:'admin-001',videos:[],pdfs:[],quizzes:[],isActive:true,createdAt:new Date('2024-01-10'),updatedAt:new Date('2024-01-20')},{id:'3',title:'الذكاء الاصطناعي للمبتدئين',description:'مقدمة شاملة في عالم الذكاء الاصطناعي',categoryId:'ai',instructorId:'admin-001',videos:[],pdfs:[],quizzes:[],isActive:true,createdAt:new Date('2024-01-15'),updatedAt:new Date('2024-01-25')}];export const mockVideos=[{id:'1',title:'مقدمة في البرمجة',description:'تعرف على أساسيات البرمجة',url:'https://www.youtube.com/watch?v=dQw4w9WgXcQ',duration:'15:30',courseId:'1',order:1,isWatched:false},{id:'2',title:'المتغيرات والثوابت',description:'تعلم كيفية استخدام المتغيرات',url:'https://www.youtube.com/watch?v=dQw4w9WgXcQ',duration:'20:45',courseId:'1',order:2,isWatched:false}];export const mockQuizzes=[{id:'1',title:'اختبار أساسيات البرمجة',description:'اختبر معرفتك في أساسيات البرمجة',courseId:'1',questions:[{id:'1',question:'ما هو المتغير في البرمجة؟',options:['مكان لتخزين البيانات','نوع من الدوال','أمر للطباعة','لا شيء مما سبق'],correctAnswer:0,explanation:'المتغير هو مكان في الذاكرة لتخزين البيانات'}],timeLimit:30,passingScore:70,createdAt:new Date('2024-01-01')}];export const mockCertificates=[{id:'cert-001',studentId:'student-001',courseId:'1',courseName:'أساسيات البرمجة',studentName:'أحمد محمد',issueDate:new Date('2024-02-01'),certificateUrl:'https://example.com/certificate/cert-001.pdf'}];", "map": {"version": 3, "names": ["mockCourses", "id", "title", "description", "categoryId", "instructorId", "videos", "pdfs", "quizzes", "isActive", "createdAt", "Date", "updatedAt", "mockVideos", "url", "duration", "courseId", "order", "isWatched", "mockQuizzes", "questions", "question", "options", "<PERSON><PERSON><PERSON><PERSON>", "explanation", "timeLimit", "passingScore", "mockCertificates", "studentId", "courseName", "studentName", "issueDate", "certificateUrl"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/data/mockCourses.ts"], "sourcesContent": ["import { Course, Video, Quiz, Certificate } from '../types';\n\nexport const mockCourses: Course[] = [\n  {\n    id: '1',\n    title: 'أساسيات البرمجة',\n    description: 'تعلم أساسيات البرمجة من الصفر حتى الاحتراف',\n    categoryId: 'programming',\n    instructorId: 'admin-001',\n    videos: [],\n    pdfs: [],\n    quizzes: [],\n    isActive: true,\n    createdAt: new Date('2024-01-01'),\n    updatedAt: new Date('2024-01-15')\n  },\n  {\n    id: '2',\n    title: 'تطوير المواقع الحديثة',\n    description: 'تعلم تطوير المواقع باستخدام أحدث التقنيات',\n    categoryId: 'web',\n    instructorId: 'admin-001',\n    videos: [],\n    pdfs: [],\n    quizzes: [],\n    isActive: true,\n    createdAt: new Date('2024-01-10'),\n    updatedAt: new Date('2024-01-20')\n  },\n  {\n    id: '3',\n    title: 'الذكاء الاصطناعي للمبتدئين',\n    description: 'مقدمة شاملة في عالم الذكاء الاصطناعي',\n    categoryId: 'ai',\n    instructorId: 'admin-001',\n    videos: [],\n    pdfs: [],\n    quizzes: [],\n    isActive: true,\n    createdAt: new Date('2024-01-15'),\n    updatedAt: new Date('2024-01-25')\n  }\n];\n\nexport const mockVideos: Video[] = [\n  {\n    id: '1',\n    title: 'مقدمة في البرمجة',\n    description: 'تعرف على أساسيات البرمجة',\n    url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',\n    duration: '15:30',\n    courseId: '1',\n    order: 1,\n    isWatched: false\n  },\n  {\n    id: '2',\n    title: 'المتغيرات والثوابت',\n    description: 'تعلم كيفية استخدام المتغيرات',\n    url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',\n    duration: '20:45',\n    courseId: '1',\n    order: 2,\n    isWatched: false\n  }\n];\n\nexport const mockQuizzes: Quiz[] = [\n  {\n    id: '1',\n    title: 'اختبار أساسيات البرمجة',\n    description: 'اختبر معرفتك في أساسيات البرمجة',\n    courseId: '1',\n    questions: [\n      {\n        id: '1',\n        question: 'ما هو المتغير في البرمجة؟',\n        options: [\n          'مكان لتخزين البيانات',\n          'نوع من الدوال',\n          'أمر للطباعة',\n          'لا شيء مما سبق'\n        ],\n        correctAnswer: 0,\n        explanation: 'المتغير هو مكان في الذاكرة لتخزين البيانات'\n      }\n    ],\n    timeLimit: 30,\n    passingScore: 70,\n    createdAt: new Date('2024-01-01')\n  }\n];\n\nexport const mockCertificates: Certificate[] = [\n  {\n    id: 'cert-001',\n    studentId: 'student-001',\n    courseId: '1',\n    courseName: 'أساسيات البرمجة',\n    studentName: 'أحمد محمد',\n    issueDate: new Date('2024-02-01'),\n    certificateUrl: 'https://example.com/certificate/cert-001.pdf'\n  }\n];\n"], "mappings": "AAEA,MAAO,MAAM,CAAAA,WAAqB,CAAG,CACnC,CACEC,EAAE,CAAE,GAAG,CACPC,KAAK,CAAE,iBAAiB,CACxBC,WAAW,CAAE,4CAA4C,CACzDC,UAAU,CAAE,aAAa,CACzBC,YAAY,CAAE,WAAW,CACzBC,MAAM,CAAE,EAAE,CACVC,IAAI,CAAE,EAAE,CACRC,OAAO,CAAE,EAAE,CACXC,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAAC,CACjCC,SAAS,CAAE,GAAI,CAAAD,IAAI,CAAC,YAAY,CAClC,CAAC,CACD,CACEV,EAAE,CAAE,GAAG,CACPC,KAAK,CAAE,uBAAuB,CAC9BC,WAAW,CAAE,2CAA2C,CACxDC,UAAU,CAAE,KAAK,CACjBC,YAAY,CAAE,WAAW,CACzBC,MAAM,CAAE,EAAE,CACVC,IAAI,CAAE,EAAE,CACRC,OAAO,CAAE,EAAE,CACXC,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAAC,CACjCC,SAAS,CAAE,GAAI,CAAAD,IAAI,CAAC,YAAY,CAClC,CAAC,CACD,CACEV,EAAE,CAAE,GAAG,CACPC,KAAK,CAAE,4BAA4B,CACnCC,WAAW,CAAE,sCAAsC,CACnDC,UAAU,CAAE,IAAI,CAChBC,YAAY,CAAE,WAAW,CACzBC,MAAM,CAAE,EAAE,CACVC,IAAI,CAAE,EAAE,CACRC,OAAO,CAAE,EAAE,CACXC,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAAC,CACjCC,SAAS,CAAE,GAAI,CAAAD,IAAI,CAAC,YAAY,CAClC,CAAC,CACF,CAED,MAAO,MAAM,CAAAE,UAAmB,CAAG,CACjC,CACEZ,EAAE,CAAE,GAAG,CACPC,KAAK,CAAE,kBAAkB,CACzBC,WAAW,CAAE,0BAA0B,CACvCW,GAAG,CAAE,6CAA6C,CAClDC,QAAQ,CAAE,OAAO,CACjBC,QAAQ,CAAE,GAAG,CACbC,KAAK,CAAE,CAAC,CACRC,SAAS,CAAE,KACb,CAAC,CACD,CACEjB,EAAE,CAAE,GAAG,CACPC,KAAK,CAAE,oBAAoB,CAC3BC,WAAW,CAAE,8BAA8B,CAC3CW,GAAG,CAAE,6CAA6C,CAClDC,QAAQ,CAAE,OAAO,CACjBC,QAAQ,CAAE,GAAG,CACbC,KAAK,CAAE,CAAC,CACRC,SAAS,CAAE,KACb,CAAC,CACF,CAED,MAAO,MAAM,CAAAC,WAAmB,CAAG,CACjC,CACElB,EAAE,CAAE,GAAG,CACPC,KAAK,CAAE,wBAAwB,CAC/BC,WAAW,CAAE,iCAAiC,CAC9Ca,QAAQ,CAAE,GAAG,CACbI,SAAS,CAAE,CACT,CACEnB,EAAE,CAAE,GAAG,CACPoB,QAAQ,CAAE,2BAA2B,CACrCC,OAAO,CAAE,CACP,sBAAsB,CACtB,eAAe,CACf,aAAa,CACb,gBAAgB,CACjB,CACDC,aAAa,CAAE,CAAC,CAChBC,WAAW,CAAE,4CACf,CAAC,CACF,CACDC,SAAS,CAAE,EAAE,CACbC,YAAY,CAAE,EAAE,CAChBhB,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAClC,CAAC,CACF,CAED,MAAO,MAAM,CAAAgB,gBAA+B,CAAG,CAC7C,CACE1B,EAAE,CAAE,UAAU,CACd2B,SAAS,CAAE,aAAa,CACxBZ,QAAQ,CAAE,GAAG,CACba,UAAU,CAAE,iBAAiB,CAC7BC,WAAW,CAAE,WAAW,CACxBC,SAAS,CAAE,GAAI,CAAApB,IAAI,CAAC,YAAY,CAAC,CACjCqB,cAAc,CAAE,8CAClB,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}