{"ast": null, "code": "import { useRef, useState, useCallback } from 'react';\nimport { wrap } from './wrap.mjs';\n\n/**\n * Cycles through a series of visual properties. Can be used to toggle between or cycle through animations. It works similar to `useState` in React. It is provided an initial array of possible states, and returns an array of two arguments.\n *\n * An index value can be passed to the returned `cycle` function to cycle to a specific index.\n *\n * ```jsx\n * import * as React from \"react\"\n * import { motion, useCycle } from \"framer-motion\"\n *\n * export const MyComponent = () => {\n *   const [x, cycleX] = useCycle(0, 50, 100)\n *\n *   return (\n *     <motion.div\n *       animate={{ x: x }}\n *       onTap={() => cycleX()}\n *      />\n *    )\n * }\n * ```\n *\n * @param items - items to cycle through\n * @returns [currentState, cycleState]\n *\n * @public\n */\nfunction useCycle() {\n  for (var _len = arguments.length, items = new Array(_len), _key = 0; _key < _len; _key++) {\n    items[_key] = arguments[_key];\n  }\n  const index = useRef(0);\n  const [item, setItem] = useState(items[index.current]);\n  const runCycle = useCallback(next => {\n    index.current = typeof next !== \"number\" ? wrap(0, items.length, index.current + 1) : next;\n    setItem(items[index.current]);\n  },\n  // The array will change on each call, but by putting items.length at\n  // the front of this array, we guarantee the dependency comparison will match up\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [items.length, ...items]);\n  return [item, runCycle];\n}\nexport { useCycle };", "map": {"version": 3, "names": ["useRef", "useState", "useCallback", "wrap", "useCycle", "_len", "arguments", "length", "items", "Array", "_key", "index", "item", "setItem", "current", "runCycle", "next"], "sources": ["C:/Users/<USER>/Desktop/مشروع/node_modules/framer-motion/dist/es/utils/use-cycle.mjs"], "sourcesContent": ["import { useRef, useState, useCallback } from 'react';\nimport { wrap } from './wrap.mjs';\n\n/**\n * Cycles through a series of visual properties. Can be used to toggle between or cycle through animations. It works similar to `useState` in React. It is provided an initial array of possible states, and returns an array of two arguments.\n *\n * An index value can be passed to the returned `cycle` function to cycle to a specific index.\n *\n * ```jsx\n * import * as React from \"react\"\n * import { motion, useCycle } from \"framer-motion\"\n *\n * export const MyComponent = () => {\n *   const [x, cycleX] = useCycle(0, 50, 100)\n *\n *   return (\n *     <motion.div\n *       animate={{ x: x }}\n *       onTap={() => cycleX()}\n *      />\n *    )\n * }\n * ```\n *\n * @param items - items to cycle through\n * @returns [currentState, cycleState]\n *\n * @public\n */\nfunction useCycle(...items) {\n    const index = useRef(0);\n    const [item, setItem] = useState(items[index.current]);\n    const runCycle = useCallback((next) => {\n        index.current =\n            typeof next !== \"number\"\n                ? wrap(0, items.length, index.current + 1)\n                : next;\n        setItem(items[index.current]);\n    }, \n    // The array will change on each call, but by putting items.length at\n    // the front of this array, we guarantee the dependency comparison will match up\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [items.length, ...items]);\n    return [item, runCycle];\n}\n\nexport { useCycle };\n"], "mappings": "AAAA,SAASA,MAAM,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACrD,SAASC,IAAI,QAAQ,YAAY;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAAA,EAAW;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAPC,KAAK,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAALF,KAAK,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;EAAA;EACtB,MAAMC,KAAK,GAAGX,MAAM,CAAC,CAAC,CAAC;EACvB,MAAM,CAACY,IAAI,EAAEC,OAAO,CAAC,GAAGZ,QAAQ,CAACO,KAAK,CAACG,KAAK,CAACG,OAAO,CAAC,CAAC;EACtD,MAAMC,QAAQ,GAAGb,WAAW,CAAEc,IAAI,IAAK;IACnCL,KAAK,CAACG,OAAO,GACT,OAAOE,IAAI,KAAK,QAAQ,GAClBb,IAAI,CAAC,CAAC,EAAEK,KAAK,CAACD,MAAM,EAAEI,KAAK,CAACG,OAAO,GAAG,CAAC,CAAC,GACxCE,IAAI;IACdH,OAAO,CAACL,KAAK,CAACG,KAAK,CAACG,OAAO,CAAC,CAAC;EACjC,CAAC;EACD;EACA;EACA;EACA,CAACN,KAAK,CAACD,MAAM,EAAE,GAAGC,KAAK,CAAC,CAAC;EACzB,OAAO,CAACI,IAAI,EAAEG,QAAQ,CAAC;AAC3B;AAEA,SAASX,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}