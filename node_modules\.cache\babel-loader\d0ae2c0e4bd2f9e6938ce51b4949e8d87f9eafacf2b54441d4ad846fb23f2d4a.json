{"ast": null, "code": "import { Component, ComponentContainer } from '@firebase/component';\nimport { <PERSON><PERSON>, setUser<PERSON>og<PERSON><PERSON><PERSON>, setLogLevel as setLogLevel$1 } from '@firebase/logger';\nimport { ErrorFactory, getDefaultAppConfig, deepEqual, isBrowser, isWebWorker, FirebaseError, base64urlEncodeWithoutPadding, isIndexedDBAvailable, validateIndexedDBOpenable } from '@firebase/util';\nexport { FirebaseError } from '@firebase/util';\nimport { openDB } from 'idb';\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nclass PlatformLoggerServiceImpl {\n  constructor(container) {\n    this.container = container;\n  }\n  // In initial implementation, this will be called by installations on\n  // auth token refresh, and installations will send this string.\n  getPlatformInfoString() {\n    const providers = this.container.getProviders();\n    // Loop through providers and get library/version pairs from any that are\n    // version components.\n    return providers.map(provider => {\n      if (isVersionServiceProvider(provider)) {\n        const service = provider.getImmediate();\n        return `${service.library}/${service.version}`;\n      } else {\n        return null;\n      }\n    }).filter(logString => logString).join(' ');\n  }\n}\n/**\r\n *\r\n * @param provider check if this provider provides a VersionService\r\n *\r\n * NOTE: Using Provider<'app-version'> is a hack to indicate that the provider\r\n * provides VersionService. The provider is not necessarily a 'app-version'\r\n * provider.\r\n */\nfunction isVersionServiceProvider(provider) {\n  const component = provider.getComponent();\n  return (component === null || component === void 0 ? void 0 : component.type) === \"VERSION\" /* ComponentType.VERSION */;\n}\nconst name$q = \"@firebase/app\";\nconst version$1 = \"0.10.13\";\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst logger = new Logger('@firebase/app');\nconst name$p = \"@firebase/app-compat\";\nconst name$o = \"@firebase/analytics-compat\";\nconst name$n = \"@firebase/analytics\";\nconst name$m = \"@firebase/app-check-compat\";\nconst name$l = \"@firebase/app-check\";\nconst name$k = \"@firebase/auth\";\nconst name$j = \"@firebase/auth-compat\";\nconst name$i = \"@firebase/database\";\nconst name$h = \"@firebase/data-connect\";\nconst name$g = \"@firebase/database-compat\";\nconst name$f = \"@firebase/functions\";\nconst name$e = \"@firebase/functions-compat\";\nconst name$d = \"@firebase/installations\";\nconst name$c = \"@firebase/installations-compat\";\nconst name$b = \"@firebase/messaging\";\nconst name$a = \"@firebase/messaging-compat\";\nconst name$9 = \"@firebase/performance\";\nconst name$8 = \"@firebase/performance-compat\";\nconst name$7 = \"@firebase/remote-config\";\nconst name$6 = \"@firebase/remote-config-compat\";\nconst name$5 = \"@firebase/storage\";\nconst name$4 = \"@firebase/storage-compat\";\nconst name$3 = \"@firebase/firestore\";\nconst name$2 = \"@firebase/vertexai-preview\";\nconst name$1 = \"@firebase/firestore-compat\";\nconst name = \"firebase\";\nconst version = \"10.14.1\";\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * The default app name\r\n *\r\n * @internal\r\n */\nconst DEFAULT_ENTRY_NAME = '[DEFAULT]';\nconst PLATFORM_LOG_STRING = {\n  [name$q]: 'fire-core',\n  [name$p]: 'fire-core-compat',\n  [name$n]: 'fire-analytics',\n  [name$o]: 'fire-analytics-compat',\n  [name$l]: 'fire-app-check',\n  [name$m]: 'fire-app-check-compat',\n  [name$k]: 'fire-auth',\n  [name$j]: 'fire-auth-compat',\n  [name$i]: 'fire-rtdb',\n  [name$h]: 'fire-data-connect',\n  [name$g]: 'fire-rtdb-compat',\n  [name$f]: 'fire-fn',\n  [name$e]: 'fire-fn-compat',\n  [name$d]: 'fire-iid',\n  [name$c]: 'fire-iid-compat',\n  [name$b]: 'fire-fcm',\n  [name$a]: 'fire-fcm-compat',\n  [name$9]: 'fire-perf',\n  [name$8]: 'fire-perf-compat',\n  [name$7]: 'fire-rc',\n  [name$6]: 'fire-rc-compat',\n  [name$5]: 'fire-gcs',\n  [name$4]: 'fire-gcs-compat',\n  [name$3]: 'fire-fst',\n  [name$1]: 'fire-fst-compat',\n  [name$2]: 'fire-vertex',\n  'fire-js': 'fire-js',\n  [name]: 'fire-js-all'\n};\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * @internal\r\n */\nconst _apps = new Map();\n/**\r\n * @internal\r\n */\nconst _serverApps = new Map();\n/**\r\n * Registered components.\r\n *\r\n * @internal\r\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst _components = new Map();\n/**\r\n * @param component - the component being added to this app's container\r\n *\r\n * @internal\r\n */\nfunction _addComponent(app, component) {\n  try {\n    app.container.addComponent(component);\n  } catch (e) {\n    logger.debug(`Component ${component.name} failed to register with FirebaseApp ${app.name}`, e);\n  }\n}\n/**\r\n *\r\n * @internal\r\n */\nfunction _addOrOverwriteComponent(app, component) {\n  app.container.addOrOverwriteComponent(component);\n}\n/**\r\n *\r\n * @param component - the component to register\r\n * @returns whether or not the component is registered successfully\r\n *\r\n * @internal\r\n */\nfunction _registerComponent(component) {\n  const componentName = component.name;\n  if (_components.has(componentName)) {\n    logger.debug(`There were multiple attempts to register component ${componentName}.`);\n    return false;\n  }\n  _components.set(componentName, component);\n  // add the component to existing app instances\n  for (const app of _apps.values()) {\n    _addComponent(app, component);\n  }\n  for (const serverApp of _serverApps.values()) {\n    _addComponent(serverApp, component);\n  }\n  return true;\n}\n/**\r\n *\r\n * @param app - FirebaseApp instance\r\n * @param name - service name\r\n *\r\n * @returns the provider for the service with the matching name\r\n *\r\n * @internal\r\n */\nfunction _getProvider(app, name) {\n  const heartbeatController = app.container.getProvider('heartbeat').getImmediate({\n    optional: true\n  });\n  if (heartbeatController) {\n    void heartbeatController.triggerHeartbeat();\n  }\n  return app.container.getProvider(name);\n}\n/**\r\n *\r\n * @param app - FirebaseApp instance\r\n * @param name - service name\r\n * @param instanceIdentifier - service instance identifier in case the service supports multiple instances\r\n *\r\n * @internal\r\n */\nfunction _removeServiceInstance(app, name, instanceIdentifier = DEFAULT_ENTRY_NAME) {\n  _getProvider(app, name).clearInstance(instanceIdentifier);\n}\n/**\r\n *\r\n * @param obj - an object of type FirebaseApp or FirebaseOptions.\r\n *\r\n * @returns true if the provide object is of type FirebaseApp.\r\n *\r\n * @internal\r\n */\nfunction _isFirebaseApp(obj) {\n  return obj.options !== undefined;\n}\n/**\r\n *\r\n * @param obj - an object of type FirebaseApp.\r\n *\r\n * @returns true if the provided object is of type FirebaseServerAppImpl.\r\n *\r\n * @internal\r\n */\nfunction _isFirebaseServerApp(obj) {\n  return obj.settings !== undefined;\n}\n/**\r\n * Test only\r\n *\r\n * @internal\r\n */\nfunction _clearComponents() {\n  _components.clear();\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst ERRORS = {\n  [\"no-app\" /* AppError.NO_APP */]: \"No Firebase App '{$appName}' has been created - \" + 'call initializeApp() first',\n  [\"bad-app-name\" /* AppError.BAD_APP_NAME */]: \"Illegal App name: '{$appName}'\",\n  [\"duplicate-app\" /* AppError.DUPLICATE_APP */]: \"Firebase App named '{$appName}' already exists with different options or config\",\n  [\"app-deleted\" /* AppError.APP_DELETED */]: \"Firebase App named '{$appName}' already deleted\",\n  [\"server-app-deleted\" /* AppError.SERVER_APP_DELETED */]: 'Firebase Server App has been deleted',\n  [\"no-options\" /* AppError.NO_OPTIONS */]: 'Need to provide options, when not being deployed to hosting via source.',\n  [\"invalid-app-argument\" /* AppError.INVALID_APP_ARGUMENT */]: 'firebase.{$appName}() takes either no argument or a ' + 'Firebase App instance.',\n  [\"invalid-log-argument\" /* AppError.INVALID_LOG_ARGUMENT */]: 'First argument to `onLog` must be null or a function.',\n  [\"idb-open\" /* AppError.IDB_OPEN */]: 'Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.',\n  [\"idb-get\" /* AppError.IDB_GET */]: 'Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.',\n  [\"idb-set\" /* AppError.IDB_WRITE */]: 'Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.',\n  [\"idb-delete\" /* AppError.IDB_DELETE */]: 'Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.',\n  [\"finalization-registry-not-supported\" /* AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED */]: 'FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.',\n  [\"invalid-server-app-environment\" /* AppError.INVALID_SERVER_APP_ENVIRONMENT */]: 'FirebaseServerApp is not for use in browser environments.'\n};\nconst ERROR_FACTORY = new ErrorFactory('app', 'Firebase', ERRORS);\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nclass FirebaseAppImpl {\n  constructor(options, config, container) {\n    this._isDeleted = false;\n    this._options = Object.assign({}, options);\n    this._config = Object.assign({}, config);\n    this._name = config.name;\n    this._automaticDataCollectionEnabled = config.automaticDataCollectionEnabled;\n    this._container = container;\n    this.container.addComponent(new Component('app', () => this, \"PUBLIC\" /* ComponentType.PUBLIC */));\n  }\n  get automaticDataCollectionEnabled() {\n    this.checkDestroyed();\n    return this._automaticDataCollectionEnabled;\n  }\n  set automaticDataCollectionEnabled(val) {\n    this.checkDestroyed();\n    this._automaticDataCollectionEnabled = val;\n  }\n  get name() {\n    this.checkDestroyed();\n    return this._name;\n  }\n  get options() {\n    this.checkDestroyed();\n    return this._options;\n  }\n  get config() {\n    this.checkDestroyed();\n    return this._config;\n  }\n  get container() {\n    return this._container;\n  }\n  get isDeleted() {\n    return this._isDeleted;\n  }\n  set isDeleted(val) {\n    this._isDeleted = val;\n  }\n  /**\r\n   * This function will throw an Error if the App has already been deleted -\r\n   * use before performing API actions on the App.\r\n   */\n  checkDestroyed() {\n    if (this.isDeleted) {\n      throw ERROR_FACTORY.create(\"app-deleted\" /* AppError.APP_DELETED */, {\n        appName: this._name\n      });\n    }\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2023 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nclass FirebaseServerAppImpl extends FirebaseAppImpl {\n  constructor(options, serverConfig, name, container) {\n    // Build configuration parameters for the FirebaseAppImpl base class.\n    const automaticDataCollectionEnabled = serverConfig.automaticDataCollectionEnabled !== undefined ? serverConfig.automaticDataCollectionEnabled : false;\n    // Create the FirebaseAppSettings object for the FirebaseAppImp constructor.\n    const config = {\n      name,\n      automaticDataCollectionEnabled\n    };\n    if (options.apiKey !== undefined) {\n      // Construct the parent FirebaseAppImp object.\n      super(options, config, container);\n    } else {\n      const appImpl = options;\n      super(appImpl.options, config, container);\n    }\n    // Now construct the data for the FirebaseServerAppImpl.\n    this._serverConfig = Object.assign({\n      automaticDataCollectionEnabled\n    }, serverConfig);\n    this._finalizationRegistry = null;\n    if (typeof FinalizationRegistry !== 'undefined') {\n      this._finalizationRegistry = new FinalizationRegistry(() => {\n        this.automaticCleanup();\n      });\n    }\n    this._refCount = 0;\n    this.incRefCount(this._serverConfig.releaseOnDeref);\n    // Do not retain a hard reference to the dref object, otherwise the FinalizationRegistry\n    // will never trigger.\n    this._serverConfig.releaseOnDeref = undefined;\n    serverConfig.releaseOnDeref = undefined;\n    registerVersion(name$q, version$1, 'serverapp');\n  }\n  toJSON() {\n    return undefined;\n  }\n  get refCount() {\n    return this._refCount;\n  }\n  // Increment the reference count of this server app. If an object is provided, register it\n  // with the finalization registry.\n  incRefCount(obj) {\n    if (this.isDeleted) {\n      return;\n    }\n    this._refCount++;\n    if (obj !== undefined && this._finalizationRegistry !== null) {\n      this._finalizationRegistry.register(obj, this);\n    }\n  }\n  // Decrement the reference count.\n  decRefCount() {\n    if (this.isDeleted) {\n      return 0;\n    }\n    return --this._refCount;\n  }\n  // Invoked by the FinalizationRegistry callback to note that this app should go through its\n  // reference counts and delete itself if no reference count remain. The coordinating logic that\n  // handles this is in deleteApp(...).\n  automaticCleanup() {\n    void deleteApp(this);\n  }\n  get settings() {\n    this.checkDestroyed();\n    return this._serverConfig;\n  }\n  /**\r\n   * This function will throw an Error if the App has already been deleted -\r\n   * use before performing API actions on the App.\r\n   */\n  checkDestroyed() {\n    if (this.isDeleted) {\n      throw ERROR_FACTORY.create(\"server-app-deleted\" /* AppError.SERVER_APP_DELETED */);\n    }\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * The current SDK version.\r\n *\r\n * @public\r\n */\nconst SDK_VERSION = version;\nfunction initializeApp(_options, rawConfig = {}) {\n  let options = _options;\n  if (typeof rawConfig !== 'object') {\n    const name = rawConfig;\n    rawConfig = {\n      name\n    };\n  }\n  const config = Object.assign({\n    name: DEFAULT_ENTRY_NAME,\n    automaticDataCollectionEnabled: false\n  }, rawConfig);\n  const name = config.name;\n  if (typeof name !== 'string' || !name) {\n    throw ERROR_FACTORY.create(\"bad-app-name\" /* AppError.BAD_APP_NAME */, {\n      appName: String(name)\n    });\n  }\n  options || (options = getDefaultAppConfig());\n  if (!options) {\n    throw ERROR_FACTORY.create(\"no-options\" /* AppError.NO_OPTIONS */);\n  }\n  const existingApp = _apps.get(name);\n  if (existingApp) {\n    // return the existing app if options and config deep equal the ones in the existing app.\n    if (deepEqual(options, existingApp.options) && deepEqual(config, existingApp.config)) {\n      return existingApp;\n    } else {\n      throw ERROR_FACTORY.create(\"duplicate-app\" /* AppError.DUPLICATE_APP */, {\n        appName: name\n      });\n    }\n  }\n  const container = new ComponentContainer(name);\n  for (const component of _components.values()) {\n    container.addComponent(component);\n  }\n  const newApp = new FirebaseAppImpl(options, config, container);\n  _apps.set(name, newApp);\n  return newApp;\n}\nfunction initializeServerApp(_options, _serverAppConfig) {\n  if (isBrowser() && !isWebWorker()) {\n    // FirebaseServerApp isn't designed to be run in browsers.\n    throw ERROR_FACTORY.create(\"invalid-server-app-environment\" /* AppError.INVALID_SERVER_APP_ENVIRONMENT */);\n  }\n  if (_serverAppConfig.automaticDataCollectionEnabled === undefined) {\n    _serverAppConfig.automaticDataCollectionEnabled = false;\n  }\n  let appOptions;\n  if (_isFirebaseApp(_options)) {\n    appOptions = _options.options;\n  } else {\n    appOptions = _options;\n  }\n  // Build an app name based on a hash of the configuration options.\n  const nameObj = Object.assign(Object.assign({}, _serverAppConfig), appOptions);\n  // However, Do not mangle the name based on releaseOnDeref, since it will vary between the\n  // construction of FirebaseServerApp instances. For example, if the object is the request headers.\n  if (nameObj.releaseOnDeref !== undefined) {\n    delete nameObj.releaseOnDeref;\n  }\n  const hashCode = s => {\n    return [...s].reduce((hash, c) => Math.imul(31, hash) + c.charCodeAt(0) | 0, 0);\n  };\n  if (_serverAppConfig.releaseOnDeref !== undefined) {\n    if (typeof FinalizationRegistry === 'undefined') {\n      throw ERROR_FACTORY.create(\"finalization-registry-not-supported\" /* AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED */, {});\n    }\n  }\n  const nameString = '' + hashCode(JSON.stringify(nameObj));\n  const existingApp = _serverApps.get(nameString);\n  if (existingApp) {\n    existingApp.incRefCount(_serverAppConfig.releaseOnDeref);\n    return existingApp;\n  }\n  const container = new ComponentContainer(nameString);\n  for (const component of _components.values()) {\n    container.addComponent(component);\n  }\n  const newApp = new FirebaseServerAppImpl(appOptions, _serverAppConfig, nameString, container);\n  _serverApps.set(nameString, newApp);\n  return newApp;\n}\n/**\r\n * Retrieves a {@link @firebase/app#FirebaseApp} instance.\r\n *\r\n * When called with no arguments, the default app is returned. When an app name\r\n * is provided, the app corresponding to that name is returned.\r\n *\r\n * An exception is thrown if the app being retrieved has not yet been\r\n * initialized.\r\n *\r\n * @example\r\n * ```javascript\r\n * // Return the default app\r\n * const app = getApp();\r\n * ```\r\n *\r\n * @example\r\n * ```javascript\r\n * // Return a named app\r\n * const otherApp = getApp(\"otherApp\");\r\n * ```\r\n *\r\n * @param name - Optional name of the app to return. If no name is\r\n *   provided, the default is `\"[DEFAULT]\"`.\r\n *\r\n * @returns The app corresponding to the provided app name.\r\n *   If no app name is provided, the default app is returned.\r\n *\r\n * @public\r\n */\nfunction getApp(name = DEFAULT_ENTRY_NAME) {\n  const app = _apps.get(name);\n  if (!app && name === DEFAULT_ENTRY_NAME && getDefaultAppConfig()) {\n    return initializeApp();\n  }\n  if (!app) {\n    throw ERROR_FACTORY.create(\"no-app\" /* AppError.NO_APP */, {\n      appName: name\n    });\n  }\n  return app;\n}\n/**\r\n * A (read-only) array of all initialized apps.\r\n * @public\r\n */\nfunction getApps() {\n  return Array.from(_apps.values());\n}\n/**\r\n * Renders this app unusable and frees the resources of all associated\r\n * services.\r\n *\r\n * @example\r\n * ```javascript\r\n * deleteApp(app)\r\n *   .then(function() {\r\n *     console.log(\"App deleted successfully\");\r\n *   })\r\n *   .catch(function(error) {\r\n *     console.log(\"Error deleting app:\", error);\r\n *   });\r\n * ```\r\n *\r\n * @public\r\n */\nasync function deleteApp(app) {\n  let cleanupProviders = false;\n  const name = app.name;\n  if (_apps.has(name)) {\n    cleanupProviders = true;\n    _apps.delete(name);\n  } else if (_serverApps.has(name)) {\n    const firebaseServerApp = app;\n    if (firebaseServerApp.decRefCount() <= 0) {\n      _serverApps.delete(name);\n      cleanupProviders = true;\n    }\n  }\n  if (cleanupProviders) {\n    await Promise.all(app.container.getProviders().map(provider => provider.delete()));\n    app.isDeleted = true;\n  }\n}\n/**\r\n * Registers a library's name and version for platform logging purposes.\r\n * @param library - Name of 1p or 3p library (e.g. firestore, angularfire)\r\n * @param version - Current version of that library.\r\n * @param variant - Bundle variant, e.g., node, rn, etc.\r\n *\r\n * @public\r\n */\nfunction registerVersion(libraryKeyOrName, version, variant) {\n  var _a;\n  // TODO: We can use this check to whitelist strings when/if we set up\n  // a good whitelist system.\n  let library = (_a = PLATFORM_LOG_STRING[libraryKeyOrName]) !== null && _a !== void 0 ? _a : libraryKeyOrName;\n  if (variant) {\n    library += `-${variant}`;\n  }\n  const libraryMismatch = library.match(/\\s|\\//);\n  const versionMismatch = version.match(/\\s|\\//);\n  if (libraryMismatch || versionMismatch) {\n    const warning = [`Unable to register library \"${library}\" with version \"${version}\":`];\n    if (libraryMismatch) {\n      warning.push(`library name \"${library}\" contains illegal characters (whitespace or \"/\")`);\n    }\n    if (libraryMismatch && versionMismatch) {\n      warning.push('and');\n    }\n    if (versionMismatch) {\n      warning.push(`version name \"${version}\" contains illegal characters (whitespace or \"/\")`);\n    }\n    logger.warn(warning.join(' '));\n    return;\n  }\n  _registerComponent(new Component(`${library}-version`, () => ({\n    library,\n    version\n  }), \"VERSION\" /* ComponentType.VERSION */));\n}\n/**\r\n * Sets log handler for all Firebase SDKs.\r\n * @param logCallback - An optional custom log handler that executes user code whenever\r\n * the Firebase SDK makes a logging call.\r\n *\r\n * @public\r\n */\nfunction onLog(logCallback, options) {\n  if (logCallback !== null && typeof logCallback !== 'function') {\n    throw ERROR_FACTORY.create(\"invalid-log-argument\" /* AppError.INVALID_LOG_ARGUMENT */);\n  }\n  setUserLogHandler(logCallback, options);\n}\n/**\r\n * Sets log level for all Firebase SDKs.\r\n *\r\n * All of the log types above the current log level are captured (i.e. if\r\n * you set the log level to `info`, errors are logged, but `debug` and\r\n * `verbose` logs are not).\r\n *\r\n * @public\r\n */\nfunction setLogLevel(logLevel) {\n  setLogLevel$1(logLevel);\n}\n\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst DB_NAME = 'firebase-heartbeat-database';\nconst DB_VERSION = 1;\nconst STORE_NAME = 'firebase-heartbeat-store';\nlet dbPromise = null;\nfunction getDbPromise() {\n  if (!dbPromise) {\n    dbPromise = openDB(DB_NAME, DB_VERSION, {\n      upgrade: (db, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            try {\n              db.createObjectStore(STORE_NAME);\n            } catch (e) {\n              // Safari/iOS browsers throw occasional exceptions on\n              // db.createObjectStore() that may be a bug. Avoid blocking\n              // the rest of the app functionality.\n              console.warn(e);\n            }\n        }\n      }\n    }).catch(e => {\n      throw ERROR_FACTORY.create(\"idb-open\" /* AppError.IDB_OPEN */, {\n        originalErrorMessage: e.message\n      });\n    });\n  }\n  return dbPromise;\n}\nasync function readHeartbeatsFromIndexedDB(app) {\n  try {\n    const db = await getDbPromise();\n    const tx = db.transaction(STORE_NAME);\n    const result = await tx.objectStore(STORE_NAME).get(computeKey(app));\n    // We already have the value but tx.done can throw,\n    // so we need to await it here to catch errors\n    await tx.done;\n    return result;\n  } catch (e) {\n    if (e instanceof FirebaseError) {\n      logger.warn(e.message);\n    } else {\n      const idbGetError = ERROR_FACTORY.create(\"idb-get\" /* AppError.IDB_GET */, {\n        originalErrorMessage: e === null || e === void 0 ? void 0 : e.message\n      });\n      logger.warn(idbGetError.message);\n    }\n  }\n}\nasync function writeHeartbeatsToIndexedDB(app, heartbeatObject) {\n  try {\n    const db = await getDbPromise();\n    const tx = db.transaction(STORE_NAME, 'readwrite');\n    const objectStore = tx.objectStore(STORE_NAME);\n    await objectStore.put(heartbeatObject, computeKey(app));\n    await tx.done;\n  } catch (e) {\n    if (e instanceof FirebaseError) {\n      logger.warn(e.message);\n    } else {\n      const idbGetError = ERROR_FACTORY.create(\"idb-set\" /* AppError.IDB_WRITE */, {\n        originalErrorMessage: e === null || e === void 0 ? void 0 : e.message\n      });\n      logger.warn(idbGetError.message);\n    }\n  }\n}\nfunction computeKey(app) {\n  return `${app.name}!${app.options.appId}`;\n}\n\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst MAX_HEADER_BYTES = 1024;\n// 30 days\nconst STORED_HEARTBEAT_RETENTION_MAX_MILLIS = 30 * 24 * 60 * 60 * 1000;\nclass HeartbeatServiceImpl {\n  constructor(container) {\n    this.container = container;\n    /**\r\n     * In-memory cache for heartbeats, used by getHeartbeatsHeader() to generate\r\n     * the header string.\r\n     * Stores one record per date. This will be consolidated into the standard\r\n     * format of one record per user agent string before being sent as a header.\r\n     * Populated from indexedDB when the controller is instantiated and should\r\n     * be kept in sync with indexedDB.\r\n     * Leave public for easier testing.\r\n     */\n    this._heartbeatsCache = null;\n    const app = this.container.getProvider('app').getImmediate();\n    this._storage = new HeartbeatStorageImpl(app);\n    this._heartbeatsCachePromise = this._storage.read().then(result => {\n      this._heartbeatsCache = result;\n      return result;\n    });\n  }\n  /**\r\n   * Called to report a heartbeat. The function will generate\r\n   * a HeartbeatsByUserAgent object, update heartbeatsCache, and persist it\r\n   * to IndexedDB.\r\n   * Note that we only store one heartbeat per day. So if a heartbeat for today is\r\n   * already logged, subsequent calls to this function in the same day will be ignored.\r\n   */\n  async triggerHeartbeat() {\n    var _a, _b;\n    try {\n      const platformLogger = this.container.getProvider('platform-logger').getImmediate();\n      // This is the \"Firebase user agent\" string from the platform logger\n      // service, not the browser user agent.\n      const agent = platformLogger.getPlatformInfoString();\n      const date = getUTCDateString();\n      if (((_a = this._heartbeatsCache) === null || _a === void 0 ? void 0 : _a.heartbeats) == null) {\n        this._heartbeatsCache = await this._heartbeatsCachePromise;\n        // If we failed to construct a heartbeats cache, then return immediately.\n        if (((_b = this._heartbeatsCache) === null || _b === void 0 ? void 0 : _b.heartbeats) == null) {\n          return;\n        }\n      }\n      // Do not store a heartbeat if one is already stored for this day\n      // or if a header has already been sent today.\n      if (this._heartbeatsCache.lastSentHeartbeatDate === date || this._heartbeatsCache.heartbeats.some(singleDateHeartbeat => singleDateHeartbeat.date === date)) {\n        return;\n      } else {\n        // There is no entry for this date. Create one.\n        this._heartbeatsCache.heartbeats.push({\n          date,\n          agent\n        });\n      }\n      // Remove entries older than 30 days.\n      this._heartbeatsCache.heartbeats = this._heartbeatsCache.heartbeats.filter(singleDateHeartbeat => {\n        const hbTimestamp = new Date(singleDateHeartbeat.date).valueOf();\n        const now = Date.now();\n        return now - hbTimestamp <= STORED_HEARTBEAT_RETENTION_MAX_MILLIS;\n      });\n      return this._storage.overwrite(this._heartbeatsCache);\n    } catch (e) {\n      logger.warn(e);\n    }\n  }\n  /**\r\n   * Returns a base64 encoded string which can be attached to the heartbeat-specific header directly.\r\n   * It also clears all heartbeats from memory as well as in IndexedDB.\r\n   *\r\n   * NOTE: Consuming product SDKs should not send the header if this method\r\n   * returns an empty string.\r\n   */\n  async getHeartbeatsHeader() {\n    var _a;\n    try {\n      if (this._heartbeatsCache === null) {\n        await this._heartbeatsCachePromise;\n      }\n      // If it's still null or the array is empty, there is no data to send.\n      if (((_a = this._heartbeatsCache) === null || _a === void 0 ? void 0 : _a.heartbeats) == null || this._heartbeatsCache.heartbeats.length === 0) {\n        return '';\n      }\n      const date = getUTCDateString();\n      // Extract as many heartbeats from the cache as will fit under the size limit.\n      const {\n        heartbeatsToSend,\n        unsentEntries\n      } = extractHeartbeatsForHeader(this._heartbeatsCache.heartbeats);\n      const headerString = base64urlEncodeWithoutPadding(JSON.stringify({\n        version: 2,\n        heartbeats: heartbeatsToSend\n      }));\n      // Store last sent date to prevent another being logged/sent for the same day.\n      this._heartbeatsCache.lastSentHeartbeatDate = date;\n      if (unsentEntries.length > 0) {\n        // Store any unsent entries if they exist.\n        this._heartbeatsCache.heartbeats = unsentEntries;\n        // This seems more likely than emptying the array (below) to lead to some odd state\n        // since the cache isn't empty and this will be called again on the next request,\n        // and is probably safest if we await it.\n        await this._storage.overwrite(this._heartbeatsCache);\n      } else {\n        this._heartbeatsCache.heartbeats = [];\n        // Do not wait for this, to reduce latency.\n        void this._storage.overwrite(this._heartbeatsCache);\n      }\n      return headerString;\n    } catch (e) {\n      logger.warn(e);\n      return '';\n    }\n  }\n}\nfunction getUTCDateString() {\n  const today = new Date();\n  // Returns date format 'YYYY-MM-DD'\n  return today.toISOString().substring(0, 10);\n}\nfunction extractHeartbeatsForHeader(heartbeatsCache, maxSize = MAX_HEADER_BYTES) {\n  // Heartbeats grouped by user agent in the standard format to be sent in\n  // the header.\n  const heartbeatsToSend = [];\n  // Single date format heartbeats that are not sent.\n  let unsentEntries = heartbeatsCache.slice();\n  for (const singleDateHeartbeat of heartbeatsCache) {\n    // Look for an existing entry with the same user agent.\n    const heartbeatEntry = heartbeatsToSend.find(hb => hb.agent === singleDateHeartbeat.agent);\n    if (!heartbeatEntry) {\n      // If no entry for this user agent exists, create one.\n      heartbeatsToSend.push({\n        agent: singleDateHeartbeat.agent,\n        dates: [singleDateHeartbeat.date]\n      });\n      if (countBytes(heartbeatsToSend) > maxSize) {\n        // If the header would exceed max size, remove the added heartbeat\n        // entry and stop adding to the header.\n        heartbeatsToSend.pop();\n        break;\n      }\n    } else {\n      heartbeatEntry.dates.push(singleDateHeartbeat.date);\n      // If the header would exceed max size, remove the added date\n      // and stop adding to the header.\n      if (countBytes(heartbeatsToSend) > maxSize) {\n        heartbeatEntry.dates.pop();\n        break;\n      }\n    }\n    // Pop unsent entry from queue. (Skipped if adding the entry exceeded\n    // quota and the loop breaks early.)\n    unsentEntries = unsentEntries.slice(1);\n  }\n  return {\n    heartbeatsToSend,\n    unsentEntries\n  };\n}\nclass HeartbeatStorageImpl {\n  constructor(app) {\n    this.app = app;\n    this._canUseIndexedDBPromise = this.runIndexedDBEnvironmentCheck();\n  }\n  async runIndexedDBEnvironmentCheck() {\n    if (!isIndexedDBAvailable()) {\n      return false;\n    } else {\n      return validateIndexedDBOpenable().then(() => true).catch(() => false);\n    }\n  }\n  /**\r\n   * Read all heartbeats.\r\n   */\n  async read() {\n    const canUseIndexedDB = await this._canUseIndexedDBPromise;\n    if (!canUseIndexedDB) {\n      return {\n        heartbeats: []\n      };\n    } else {\n      const idbHeartbeatObject = await readHeartbeatsFromIndexedDB(this.app);\n      if (idbHeartbeatObject === null || idbHeartbeatObject === void 0 ? void 0 : idbHeartbeatObject.heartbeats) {\n        return idbHeartbeatObject;\n      } else {\n        return {\n          heartbeats: []\n        };\n      }\n    }\n  }\n  // overwrite the storage with the provided heartbeats\n  async overwrite(heartbeatsObject) {\n    var _a;\n    const canUseIndexedDB = await this._canUseIndexedDBPromise;\n    if (!canUseIndexedDB) {\n      return;\n    } else {\n      const existingHeartbeatsObject = await this.read();\n      return writeHeartbeatsToIndexedDB(this.app, {\n        lastSentHeartbeatDate: (_a = heartbeatsObject.lastSentHeartbeatDate) !== null && _a !== void 0 ? _a : existingHeartbeatsObject.lastSentHeartbeatDate,\n        heartbeats: heartbeatsObject.heartbeats\n      });\n    }\n  }\n  // add heartbeats\n  async add(heartbeatsObject) {\n    var _a;\n    const canUseIndexedDB = await this._canUseIndexedDBPromise;\n    if (!canUseIndexedDB) {\n      return;\n    } else {\n      const existingHeartbeatsObject = await this.read();\n      return writeHeartbeatsToIndexedDB(this.app, {\n        lastSentHeartbeatDate: (_a = heartbeatsObject.lastSentHeartbeatDate) !== null && _a !== void 0 ? _a : existingHeartbeatsObject.lastSentHeartbeatDate,\n        heartbeats: [...existingHeartbeatsObject.heartbeats, ...heartbeatsObject.heartbeats]\n      });\n    }\n  }\n}\n/**\r\n * Calculate bytes of a HeartbeatsByUserAgent array after being wrapped\r\n * in a platform logging header JSON object, stringified, and converted\r\n * to base 64.\r\n */\nfunction countBytes(heartbeatsCache) {\n  // base64 has a restricted set of characters, all of which should be 1 byte.\n  return base64urlEncodeWithoutPadding(\n  // heartbeatsCache wrapper properties\n  JSON.stringify({\n    version: 2,\n    heartbeats: heartbeatsCache\n  })).length;\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction registerCoreComponents(variant) {\n  _registerComponent(new Component('platform-logger', container => new PlatformLoggerServiceImpl(container), \"PRIVATE\" /* ComponentType.PRIVATE */));\n  _registerComponent(new Component('heartbeat', container => new HeartbeatServiceImpl(container), \"PRIVATE\" /* ComponentType.PRIVATE */));\n  // Register `app` package.\n  registerVersion(name$q, version$1, variant);\n  // BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\n  registerVersion(name$q, version$1, 'esm2017');\n  // Register platform SDK identifier (no version).\n  registerVersion('fire-js', '');\n}\n\n/**\r\n * Firebase App\r\n *\r\n * @remarks This package coordinates the communication between the different Firebase components\r\n * @packageDocumentation\r\n */\nregisterCoreComponents('');\nexport { SDK_VERSION, DEFAULT_ENTRY_NAME as _DEFAULT_ENTRY_NAME, _addComponent, _addOrOverwriteComponent, _apps, _clearComponents, _components, _getProvider, _isFirebaseApp, _isFirebaseServerApp, _registerComponent, _removeServiceInstance, _serverApps, deleteApp, getApp, getApps, initializeApp, initializeServerApp, onLog, registerVersion, setLogLevel };", "map": {"version": 3, "names": ["PlatformLoggerServiceImpl", "constructor", "container", "getPlatformInfoString", "providers", "getProviders", "map", "provider", "isVersionServiceProvider", "service", "getImmediate", "library", "version", "filter", "logString", "join", "component", "getComponent", "type", "logger", "<PERSON><PERSON>", "DEFAULT_ENTRY_NAME", "PLATFORM_LOG_STRING", "name$q", "name$p", "name$n", "name$o", "name$l", "name$m", "name$k", "name$j", "name$i", "name$h", "name$g", "name$f", "name$e", "name$d", "name$c", "name$b", "name$a", "name$9", "name$8", "name$7", "name$6", "name$5", "name$4", "name$3", "name$1", "name$2", "name", "_apps", "Map", "_serverApps", "_components", "_addComponent", "app", "addComponent", "e", "debug", "_addOrOverwriteComponent", "addOrOverwriteComponent", "_registerComponent", "componentName", "has", "set", "values", "serverApp", "_get<PERSON><PERSON><PERSON>", "heartbeatController", "get<PERSON><PERSON><PERSON>", "optional", "triggerHeartbeat", "_removeServiceInstance", "instanceIdentifier", "clearInstance", "_isFirebaseApp", "obj", "options", "undefined", "_isFirebaseServerApp", "settings", "_clearComponents", "clear", "ERRORS", "ERROR_FACTORY", "ErrorFactory", "FirebaseAppImpl", "config", "_isDeleted", "_options", "Object", "assign", "_config", "_name", "_automaticDataCollectionEnabled", "automaticDataCollectionEnabled", "_container", "Component", "checkDestroyed", "val", "isDeleted", "create", "appName", "FirebaseServerAppImpl", "serverConfig", "<PERSON><PERSON><PERSON><PERSON>", "appImpl", "_serverConfig", "_finalizationRegistry", "FinalizationRegistry", "automaticCleanup", "_refCount", "incRefCount", "releaseOnDeref", "registerVersion", "version$1", "toJSON", "refCount", "register", "decRefCount", "deleteApp", "SDK_VERSION", "initializeApp", "rawConfig", "String", "getDefaultAppConfig", "existingApp", "get", "deepEqual", "ComponentContainer", "newApp", "initializeServerApp", "_serverAppConfig", "<PERSON><PERSON><PERSON><PERSON>", "isWebWorker", "appOptions", "nameObj", "hashCode", "s", "reduce", "hash", "c", "Math", "imul", "charCodeAt", "nameString", "JSON", "stringify", "getApp", "getApps", "Array", "from", "cleanupProviders", "delete", "firebaseServerApp", "Promise", "all", "libraryKeyOrName", "variant", "_a", "libraryMismatch", "match", "versionMismatch", "warning", "push", "warn", "onLog", "logCallback", "setUserLogHandler", "setLogLevel", "logLevel", "setLogLevel$1", "DB_NAME", "DB_VERSION", "STORE_NAME", "db<PERSON><PERSON><PERSON>", "getDbPromise", "openDB", "upgrade", "db", "oldVersion", "createObjectStore", "console", "catch", "originalErrorMessage", "message", "readHeartbeatsFromIndexedDB", "tx", "transaction", "result", "objectStore", "computeKey", "done", "FirebaseError", "idbGetError", "writeHeartbeatsToIndexedDB", "heartbeatObject", "put", "appId", "MAX_HEADER_BYTES", "STORED_HEARTBEAT_RETENTION_MAX_MILLIS", "HeartbeatServiceImpl", "_heartbeatsCache", "_storage", "HeartbeatStorageImpl", "_heartbeatsCachePromise", "read", "then", "platformLogger", "agent", "date", "getUTCDateString", "heartbeats", "_b", "lastSentHeartbeatDate", "some", "singleDateHeartbeat", "hbTimestamp", "Date", "valueOf", "now", "overwrite", "getHeartbeatsHeader", "length", "heartbeatsToSend", "unsentEntries", "extractHeartbeatsForHeader", "headerString", "base64urlEncodeWithoutPadding", "today", "toISOString", "substring", "heartbeatsCache", "maxSize", "slice", "heartbeatEntry", "find", "hb", "dates", "countBytes", "pop", "_canUseIndexedDBPromise", "runIndexedDBEnvironmentCheck", "isIndexedDBAvailable", "validateIndexedDBOpenable", "canUseIndexedDB", "idbHeartbeatObject", "heartbeatsObject", "existingHeartbeatsObject", "add", "registerCoreComponents"], "sources": ["C:\\Users\\<USER>\\Desktop\\مشروع\\node_modules\\@firebase\\app\\src\\platformLoggerService.ts", "C:\\Users\\<USER>\\Desktop\\مشروع\\node_modules\\@firebase\\app\\src\\logger.ts", "C:\\Users\\<USER>\\Desktop\\مشروع\\node_modules\\@firebase\\app\\src\\constants.ts", "C:\\Users\\<USER>\\Desktop\\مشروع\\node_modules\\@firebase\\app\\src\\internal.ts", "C:\\Users\\<USER>\\Desktop\\مشروع\\node_modules\\@firebase\\app\\src\\errors.ts", "C:\\Users\\<USER>\\Desktop\\مشروع\\node_modules\\@firebase\\app\\src\\firebaseApp.ts", "C:\\Users\\<USER>\\Desktop\\مشروع\\node_modules\\@firebase\\app\\src\\firebaseServerApp.ts", "C:\\Users\\<USER>\\Desktop\\مشروع\\node_modules\\@firebase\\app\\src\\api.ts", "C:\\Users\\<USER>\\Desktop\\مشروع\\node_modules\\@firebase\\app\\src\\indexeddb.ts", "C:\\Users\\<USER>\\Desktop\\مشروع\\node_modules\\@firebase\\app\\src\\heartbeatService.ts", "C:\\Users\\<USER>\\Desktop\\مشروع\\node_modules\\@firebase\\app\\src\\registerCoreComponents.ts", "C:\\Users\\<USER>\\Desktop\\مشروع\\node_modules\\@firebase\\app\\src\\index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  ComponentContainer,\n  ComponentType,\n  Provider,\n  Name\n} from '@firebase/component';\nimport { PlatformLoggerService, VersionService } from './types';\n\nexport class PlatformLoggerServiceImpl implements PlatformLoggerService {\n  constructor(private readonly container: ComponentContainer) {}\n  // In initial implementation, this will be called by installations on\n  // auth token refresh, and installations will send this string.\n  getPlatformInfoString(): string {\n    const providers = this.container.getProviders();\n    // Loop through providers and get library/version pairs from any that are\n    // version components.\n    return providers\n      .map(provider => {\n        if (isVersionServiceProvider(provider)) {\n          const service = provider.getImmediate() as VersionService;\n          return `${service.library}/${service.version}`;\n        } else {\n          return null;\n        }\n      })\n      .filter(logString => logString)\n      .join(' ');\n  }\n}\n/**\n *\n * @param provider check if this provider provides a VersionService\n *\n * NOTE: Using Provider<'app-version'> is a hack to indicate that the provider\n * provides VersionService. The provider is not necessarily a 'app-version'\n * provider.\n */\nfunction isVersionServiceProvider(provider: Provider<Name>): boolean {\n  const component = provider.getComponent();\n  return component?.type === ComponentType.VERSION;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from '@firebase/logger';\n\nexport const logger = new Logger('@firebase/app');\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { name as appName } from '../package.json';\nimport { name as appCompatName } from '../../app-compat/package.json';\nimport { name as analyticsCompatName } from '../../../packages/analytics-compat/package.json';\nimport { name as analyticsName } from '../../../packages/analytics/package.json';\nimport { name as appCheckCompatName } from '../../../packages/app-check-compat/package.json';\nimport { name as appCheckName } from '../../../packages/app-check/package.json';\nimport { name as authName } from '../../../packages/auth/package.json';\nimport { name as authCompatName } from '../../../packages/auth-compat/package.json';\nimport { name as databaseName } from '../../../packages/database/package.json';\nimport { name as dataconnectName } from '../../../packages/data-connect/package.json';\nimport { name as databaseCompatName } from '../../../packages/database-compat/package.json';\nimport { name as functionsName } from '../../../packages/functions/package.json';\nimport { name as functionsCompatName } from '../../../packages/functions-compat/package.json';\nimport { name as installationsName } from '../../../packages/installations/package.json';\nimport { name as installationsCompatName } from '../../../packages/installations-compat/package.json';\nimport { name as messagingName } from '../../../packages/messaging/package.json';\nimport { name as messagingCompatName } from '../../../packages/messaging-compat/package.json';\nimport { name as performanceName } from '../../../packages/performance/package.json';\nimport { name as performanceCompatName } from '../../../packages/performance-compat/package.json';\nimport { name as remoteConfigName } from '../../../packages/remote-config/package.json';\nimport { name as remoteConfigCompatName } from '../../../packages/remote-config-compat/package.json';\nimport { name as storageName } from '../../../packages/storage/package.json';\nimport { name as storageCompatName } from '../../../packages/storage-compat/package.json';\nimport { name as firestoreName } from '../../../packages/firestore/package.json';\nimport { name as vertexName } from '../../../packages/vertexai/package.json';\nimport { name as firestoreCompatName } from '../../../packages/firestore-compat/package.json';\nimport { name as packageName } from '../../../packages/firebase/package.json';\n\n/**\n * The default app name\n *\n * @internal\n */\nexport const DEFAULT_ENTRY_NAME = '[DEFAULT]';\n\nexport const PLATFORM_LOG_STRING = {\n  [appName]: 'fire-core',\n  [appCompatName]: 'fire-core-compat',\n  [analyticsName]: 'fire-analytics',\n  [analyticsCompatName]: 'fire-analytics-compat',\n  [appCheckName]: 'fire-app-check',\n  [appCheckCompatName]: 'fire-app-check-compat',\n  [authName]: 'fire-auth',\n  [authCompatName]: 'fire-auth-compat',\n  [databaseName]: 'fire-rtdb',\n  [dataconnectName]: 'fire-data-connect',\n  [databaseCompatName]: 'fire-rtdb-compat',\n  [functionsName]: 'fire-fn',\n  [functionsCompatName]: 'fire-fn-compat',\n  [installationsName]: 'fire-iid',\n  [installationsCompatName]: 'fire-iid-compat',\n  [messagingName]: 'fire-fcm',\n  [messagingCompatName]: 'fire-fcm-compat',\n  [performanceName]: 'fire-perf',\n  [performanceCompatName]: 'fire-perf-compat',\n  [remoteConfigName]: 'fire-rc',\n  [remoteConfigCompatName]: 'fire-rc-compat',\n  [storageName]: 'fire-gcs',\n  [storageCompatName]: 'fire-gcs-compat',\n  [firestoreName]: 'fire-fst',\n  [firestoreCompatName]: 'fire-fst-compat',\n  [vertexName]: 'fire-vertex',\n  'fire-js': 'fire-js', // Platform identifier for JS SDK.\n  [packageName]: 'fire-js-all'\n} as const;\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseApp,\n  FirebaseOptions,\n  FirebaseServerApp\n} from './public-types';\nimport { Component, Provider, Name } from '@firebase/component';\nimport { logger } from './logger';\nimport { DEFAULT_ENTRY_NAME } from './constants';\nimport { FirebaseAppImpl } from './firebaseApp';\nimport { FirebaseServerAppImpl } from './firebaseServerApp';\n\n/**\n * @internal\n */\nexport const _apps = new Map<string, FirebaseApp>();\n\n/**\n * @internal\n */\nexport const _serverApps = new Map<string, FirebaseServerApp>();\n\n/**\n * Registered components.\n *\n * @internal\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport const _components = new Map<string, Component<any>>();\n\n/**\n * @param component - the component being added to this app's container\n *\n * @internal\n */\nexport function _addComponent<T extends Name>(\n  app: FirebaseApp,\n  component: Component<T>\n): void {\n  try {\n    (app as FirebaseAppImpl).container.addComponent(component);\n  } catch (e) {\n    logger.debug(\n      `Component ${component.name} failed to register with FirebaseApp ${app.name}`,\n      e\n    );\n  }\n}\n\n/**\n *\n * @internal\n */\nexport function _addOrOverwriteComponent(\n  app: FirebaseApp,\n  component: Component\n): void {\n  (app as FirebaseAppImpl).container.addOrOverwriteComponent(component);\n}\n\n/**\n *\n * @param component - the component to register\n * @returns whether or not the component is registered successfully\n *\n * @internal\n */\nexport function _registerComponent<T extends Name>(\n  component: Component<T>\n): boolean {\n  const componentName = component.name;\n  if (_components.has(componentName)) {\n    logger.debug(\n      `There were multiple attempts to register component ${componentName}.`\n    );\n\n    return false;\n  }\n\n  _components.set(componentName, component);\n\n  // add the component to existing app instances\n  for (const app of _apps.values()) {\n    _addComponent(app as FirebaseAppImpl, component);\n  }\n\n  for (const serverApp of _serverApps.values()) {\n    _addComponent(serverApp as FirebaseServerAppImpl, component);\n  }\n\n  return true;\n}\n\n/**\n *\n * @param app - FirebaseApp instance\n * @param name - service name\n *\n * @returns the provider for the service with the matching name\n *\n * @internal\n */\nexport function _getProvider<T extends Name>(\n  app: FirebaseApp,\n  name: T\n): Provider<T> {\n  const heartbeatController = (app as FirebaseAppImpl).container\n    .getProvider('heartbeat')\n    .getImmediate({ optional: true });\n  if (heartbeatController) {\n    void heartbeatController.triggerHeartbeat();\n  }\n  return (app as FirebaseAppImpl).container.getProvider(name);\n}\n\n/**\n *\n * @param app - FirebaseApp instance\n * @param name - service name\n * @param instanceIdentifier - service instance identifier in case the service supports multiple instances\n *\n * @internal\n */\nexport function _removeServiceInstance<T extends Name>(\n  app: FirebaseApp,\n  name: T,\n  instanceIdentifier: string = DEFAULT_ENTRY_NAME\n): void {\n  _getProvider(app, name).clearInstance(instanceIdentifier);\n}\n\n/**\n *\n * @param obj - an object of type FirebaseApp or FirebaseOptions.\n *\n * @returns true if the provide object is of type FirebaseApp.\n *\n * @internal\n */\nexport function _isFirebaseApp(\n  obj: FirebaseApp | FirebaseOptions\n): obj is FirebaseApp {\n  return (obj as FirebaseApp).options !== undefined;\n}\n\n/**\n *\n * @param obj - an object of type FirebaseApp.\n *\n * @returns true if the provided object is of type FirebaseServerAppImpl.\n *\n * @internal\n */\nexport function _isFirebaseServerApp(\n  obj: FirebaseApp | FirebaseServerApp\n): obj is FirebaseServerApp {\n  return (obj as FirebaseServerApp).settings !== undefined;\n}\n\n/**\n * Test only\n *\n * @internal\n */\nexport function _clearComponents(): void {\n  _components.clear();\n}\n\n/**\n * Exported in order to be used in app-compat package\n */\nexport { DEFAULT_ENTRY_NAME as _DEFAULT_ENTRY_NAME };\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, ErrorMap } from '@firebase/util';\n\nexport const enum AppError {\n  NO_APP = 'no-app',\n  BAD_APP_NAME = 'bad-app-name',\n  DUPLICATE_APP = 'duplicate-app',\n  APP_DELETED = 'app-deleted',\n  SERVER_APP_DELETED = 'server-app-deleted',\n  NO_OPTIONS = 'no-options',\n  INVALID_APP_ARGUMENT = 'invalid-app-argument',\n  INVALID_LOG_ARGUMENT = 'invalid-log-argument',\n  IDB_OPEN = 'idb-open',\n  IDB_GET = 'idb-get',\n  IDB_WRITE = 'idb-set',\n  IDB_DELETE = 'idb-delete',\n  FINALIZATION_REGISTRY_NOT_SUPPORTED = 'finalization-registry-not-supported',\n  INVALID_SERVER_APP_ENVIRONMENT = 'invalid-server-app-environment'\n}\n\nconst ERRORS: ErrorMap<AppError> = {\n  [AppError.NO_APP]:\n    \"No Firebase App '{$appName}' has been created - \" +\n    'call initializeApp() first',\n  [AppError.BAD_APP_NAME]: \"Illegal App name: '{$appName}'\",\n  [AppError.DUPLICATE_APP]:\n    \"Firebase App named '{$appName}' already exists with different options or config\",\n  [AppError.APP_DELETED]: \"Firebase App named '{$appName}' already deleted\",\n  [AppError.SERVER_APP_DELETED]: 'Firebase Server App has been deleted',\n  [AppError.NO_OPTIONS]:\n    'Need to provide options, when not being deployed to hosting via source.',\n  [AppError.INVALID_APP_ARGUMENT]:\n    'firebase.{$appName}() takes either no argument or a ' +\n    'Firebase App instance.',\n  [AppError.INVALID_LOG_ARGUMENT]:\n    'First argument to `onLog` must be null or a function.',\n  [AppError.IDB_OPEN]:\n    'Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.IDB_GET]:\n    'Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.IDB_WRITE]:\n    'Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.IDB_DELETE]:\n    'Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED]:\n    'FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.',\n  [AppError.INVALID_SERVER_APP_ENVIRONMENT]:\n    'FirebaseServerApp is not for use in browser environments.'\n};\n\ninterface ErrorParams {\n  [AppError.NO_APP]: { appName: string };\n  [AppError.BAD_APP_NAME]: { appName: string };\n  [AppError.DUPLICATE_APP]: { appName: string };\n  [AppError.APP_DELETED]: { appName: string };\n  [AppError.INVALID_APP_ARGUMENT]: { appName: string };\n  [AppError.IDB_OPEN]: { originalErrorMessage?: string };\n  [AppError.IDB_GET]: { originalErrorMessage?: string };\n  [AppError.IDB_WRITE]: { originalErrorMessage?: string };\n  [AppError.IDB_DELETE]: { originalErrorMessage?: string };\n  [AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED]: { appName?: string };\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<AppError, ErrorParams>(\n  'app',\n  'Firebase',\n  ERRORS\n);\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseApp,\n  FirebaseOptions,\n  FirebaseAppSettings\n} from './public-types';\nimport {\n  ComponentContainer,\n  Component,\n  ComponentType\n} from '@firebase/component';\nimport { ERROR_FACTORY, AppError } from './errors';\n\nexport class FirebaseAppImpl implements FirebaseApp {\n  protected readonly _options: FirebaseOptions;\n  protected readonly _name: string;\n  /**\n   * Original config values passed in as a constructor parameter.\n   * It is only used to compare with another config object to support idempotent initializeApp().\n   *\n   * Updating automaticDataCollectionEnabled on the App instance will not change its value in _config.\n   */\n  private readonly _config: Required<FirebaseAppSettings>;\n  private _automaticDataCollectionEnabled: boolean;\n  protected _isDeleted = false;\n  private readonly _container: ComponentContainer;\n\n  constructor(\n    options: FirebaseOptions,\n    config: Required<FirebaseAppSettings>,\n    container: ComponentContainer\n  ) {\n    this._options = { ...options };\n    this._config = { ...config };\n    this._name = config.name;\n    this._automaticDataCollectionEnabled =\n      config.automaticDataCollectionEnabled;\n    this._container = container;\n    this.container.addComponent(\n      new Component('app', () => this, ComponentType.PUBLIC)\n    );\n  }\n\n  get automaticDataCollectionEnabled(): boolean {\n    this.checkDestroyed();\n    return this._automaticDataCollectionEnabled;\n  }\n\n  set automaticDataCollectionEnabled(val: boolean) {\n    this.checkDestroyed();\n    this._automaticDataCollectionEnabled = val;\n  }\n\n  get name(): string {\n    this.checkDestroyed();\n    return this._name;\n  }\n\n  get options(): FirebaseOptions {\n    this.checkDestroyed();\n    return this._options;\n  }\n\n  get config(): Required<FirebaseAppSettings> {\n    this.checkDestroyed();\n    return this._config;\n  }\n\n  get container(): ComponentContainer {\n    return this._container;\n  }\n\n  get isDeleted(): boolean {\n    return this._isDeleted;\n  }\n\n  set isDeleted(val: boolean) {\n    this._isDeleted = val;\n  }\n\n  /**\n   * This function will throw an Error if the App has already been deleted -\n   * use before performing API actions on the App.\n   */\n  protected checkDestroyed(): void {\n    if (this.isDeleted) {\n      throw ERROR_FACTORY.create(AppError.APP_DELETED, { appName: this._name });\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseAppSettings,\n  FirebaseServerApp,\n  FirebaseServerAppSettings,\n  FirebaseOptions\n} from './public-types';\nimport { deleteApp, registerVersion } from './api';\nimport { ComponentContainer } from '@firebase/component';\nimport { FirebaseAppImpl } from './firebaseApp';\nimport { ERROR_FACTORY, AppError } from './errors';\nimport { name as packageName, version } from '../package.json';\n\nexport class FirebaseServerAppImpl\n  extends FirebaseAppImpl\n  implements FirebaseServerApp\n{\n  private readonly _serverConfig: FirebaseServerAppSettings;\n  private _finalizationRegistry: FinalizationRegistry<object> | null;\n  private _refCount: number;\n\n  constructor(\n    options: FirebaseOptions | FirebaseAppImpl,\n    serverConfig: FirebaseServerAppSettings,\n    name: string,\n    container: ComponentContainer\n  ) {\n    // Build configuration parameters for the FirebaseAppImpl base class.\n    const automaticDataCollectionEnabled =\n      serverConfig.automaticDataCollectionEnabled !== undefined\n        ? serverConfig.automaticDataCollectionEnabled\n        : false;\n\n    // Create the FirebaseAppSettings object for the FirebaseAppImp constructor.\n    const config: Required<FirebaseAppSettings> = {\n      name,\n      automaticDataCollectionEnabled\n    };\n\n    if ((options as FirebaseOptions).apiKey !== undefined) {\n      // Construct the parent FirebaseAppImp object.\n      super(options as FirebaseOptions, config, container);\n    } else {\n      const appImpl: FirebaseAppImpl = options as FirebaseAppImpl;\n      super(appImpl.options, config, container);\n    }\n\n    // Now construct the data for the FirebaseServerAppImpl.\n    this._serverConfig = {\n      automaticDataCollectionEnabled,\n      ...serverConfig\n    };\n\n    this._finalizationRegistry = null;\n    if (typeof FinalizationRegistry !== 'undefined') {\n      this._finalizationRegistry = new FinalizationRegistry(() => {\n        this.automaticCleanup();\n      });\n    }\n\n    this._refCount = 0;\n    this.incRefCount(this._serverConfig.releaseOnDeref);\n\n    // Do not retain a hard reference to the dref object, otherwise the FinalizationRegistry\n    // will never trigger.\n    this._serverConfig.releaseOnDeref = undefined;\n    serverConfig.releaseOnDeref = undefined;\n\n    registerVersion(packageName, version, 'serverapp');\n  }\n\n  toJSON(): undefined {\n    return undefined;\n  }\n\n  get refCount(): number {\n    return this._refCount;\n  }\n\n  // Increment the reference count of this server app. If an object is provided, register it\n  // with the finalization registry.\n  incRefCount(obj: object | undefined): void {\n    if (this.isDeleted) {\n      return;\n    }\n    this._refCount++;\n    if (obj !== undefined && this._finalizationRegistry !== null) {\n      this._finalizationRegistry.register(obj, this);\n    }\n  }\n\n  // Decrement the reference count.\n  decRefCount(): number {\n    if (this.isDeleted) {\n      return 0;\n    }\n    return --this._refCount;\n  }\n\n  // Invoked by the FinalizationRegistry callback to note that this app should go through its\n  // reference counts and delete itself if no reference count remain. The coordinating logic that\n  // handles this is in deleteApp(...).\n  private automaticCleanup(): void {\n    void deleteApp(this);\n  }\n\n  get settings(): FirebaseServerAppSettings {\n    this.checkDestroyed();\n    return this._serverConfig;\n  }\n\n  /**\n   * This function will throw an Error if the App has already been deleted -\n   * use before performing API actions on the App.\n   */\n  protected checkDestroyed(): void {\n    if (this.isDeleted) {\n      throw ERROR_FACTORY.create(AppError.SERVER_APP_DELETED);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseApp,\n  FirebaseServerApp,\n  FirebaseOptions,\n  FirebaseAppSettings,\n  FirebaseServerAppSettings\n} from './public-types';\nimport { DEFAULT_ENTRY_NAME, PLATFORM_LOG_STRING } from './constants';\nimport { ERROR_FACTORY, AppError } from './errors';\nimport {\n  ComponentContainer,\n  Component,\n  Name,\n  ComponentType\n} from '@firebase/component';\nimport { version } from '../../firebase/package.json';\nimport { FirebaseAppImpl } from './firebaseApp';\nimport { FirebaseServerAppImpl } from './firebaseServerApp';\nimport {\n  _apps,\n  _components,\n  _isFirebaseApp,\n  _registerComponent,\n  _serverApps\n} from './internal';\nimport { logger } from './logger';\nimport {\n  LogLevelString,\n  setLogLevel as setLogLevelImpl,\n  LogCallback,\n  LogOptions,\n  setUserLogHandler\n} from '@firebase/logger';\nimport {\n  deepEqual,\n  getDefaultAppConfig,\n  isBrowser,\n  isWebWorker\n} from '@firebase/util';\n\nexport { FirebaseError } from '@firebase/util';\n\n/**\n * The current SDK version.\n *\n * @public\n */\nexport const SDK_VERSION = version;\n\n/**\n * Creates and initializes a {@link @firebase/app#FirebaseApp} instance.\n *\n * See\n * {@link\n *   https://firebase.google.com/docs/web/setup#add_firebase_to_your_app\n *   | Add Firebase to your app} and\n * {@link\n *   https://firebase.google.com/docs/web/setup#multiple-projects\n *   | Initialize multiple projects} for detailed documentation.\n *\n * @example\n * ```javascript\n *\n * // Initialize default app\n * // Retrieve your own options values by adding a web app on\n * // https://console.firebase.google.com\n * initializeApp({\n *   apiKey: \"AIza....\",                             // Auth / General Use\n *   authDomain: \"YOUR_APP.firebaseapp.com\",         // Auth with popup/redirect\n *   databaseURL: \"https://YOUR_APP.firebaseio.com\", // Realtime Database\n *   storageBucket: \"YOUR_APP.appspot.com\",          // Storage\n *   messagingSenderId: \"123456789\"                  // Cloud Messaging\n * });\n * ```\n *\n * @example\n * ```javascript\n *\n * // Initialize another app\n * const otherApp = initializeApp({\n *   databaseURL: \"https://<OTHER_DATABASE_NAME>.firebaseio.com\",\n *   storageBucket: \"<OTHER_STORAGE_BUCKET>.appspot.com\"\n * }, \"otherApp\");\n * ```\n *\n * @param options - Options to configure the app's services.\n * @param name - Optional name of the app to initialize. If no name\n *   is provided, the default is `\"[DEFAULT]\"`.\n *\n * @returns The initialized app.\n *\n * @public\n */\nexport function initializeApp(\n  options: FirebaseOptions,\n  name?: string\n): FirebaseApp;\n/**\n * Creates and initializes a FirebaseApp instance.\n *\n * @param options - Options to configure the app's services.\n * @param config - FirebaseApp Configuration\n *\n * @public\n */\nexport function initializeApp(\n  options: FirebaseOptions,\n  config?: FirebaseAppSettings\n): FirebaseApp;\n/**\n * Creates and initializes a FirebaseApp instance.\n *\n * @public\n */\nexport function initializeApp(): FirebaseApp;\nexport function initializeApp(\n  _options?: FirebaseOptions,\n  rawConfig = {}\n): FirebaseApp {\n  let options = _options;\n\n  if (typeof rawConfig !== 'object') {\n    const name = rawConfig;\n    rawConfig = { name };\n  }\n\n  const config: Required<FirebaseAppSettings> = {\n    name: DEFAULT_ENTRY_NAME,\n    automaticDataCollectionEnabled: false,\n    ...rawConfig\n  };\n  const name = config.name;\n\n  if (typeof name !== 'string' || !name) {\n    throw ERROR_FACTORY.create(AppError.BAD_APP_NAME, {\n      appName: String(name)\n    });\n  }\n\n  options ||= getDefaultAppConfig();\n\n  if (!options) {\n    throw ERROR_FACTORY.create(AppError.NO_OPTIONS);\n  }\n\n  const existingApp = _apps.get(name) as FirebaseAppImpl;\n  if (existingApp) {\n    // return the existing app if options and config deep equal the ones in the existing app.\n    if (\n      deepEqual(options, existingApp.options) &&\n      deepEqual(config, existingApp.config)\n    ) {\n      return existingApp;\n    } else {\n      throw ERROR_FACTORY.create(AppError.DUPLICATE_APP, { appName: name });\n    }\n  }\n\n  const container = new ComponentContainer(name);\n  for (const component of _components.values()) {\n    container.addComponent(component);\n  }\n\n  const newApp = new FirebaseAppImpl(options, config, container);\n\n  _apps.set(name, newApp);\n\n  return newApp;\n}\n\n/**\n * Creates and initializes a {@link @firebase/app#FirebaseServerApp} instance.\n *\n * The `FirebaseServerApp` is similar to `FirebaseApp`, but is intended for execution in\n * server side rendering environments only. Initialization will fail if invoked from a\n * browser environment.\n *\n * See\n * {@link\n *   https://firebase.google.com/docs/web/setup#add_firebase_to_your_app\n *   | Add Firebase to your app} and\n * {@link\n *   https://firebase.google.com/docs/web/setup#multiple-projects\n *   | Initialize multiple projects} for detailed documentation.\n *\n * @example\n * ```javascript\n *\n * // Initialize an instance of `FirebaseServerApp`.\n * // Retrieve your own options values by adding a web app on\n * // https://console.firebase.google.com\n * initializeServerApp({\n *     apiKey: \"AIza....\",                             // Auth / General Use\n *     authDomain: \"YOUR_APP.firebaseapp.com\",         // Auth with popup/redirect\n *     databaseURL: \"https://YOUR_APP.firebaseio.com\", // Realtime Database\n *     storageBucket: \"YOUR_APP.appspot.com\",          // Storage\n *     messagingSenderId: \"123456789\"                  // Cloud Messaging\n *   },\n *   {\n *    authIdToken: \"Your Auth ID Token\"\n *   });\n * ```\n *\n * @param options - `Firebase.AppOptions` to configure the app's services, or a\n *   a `FirebaseApp` instance which contains the `AppOptions` within.\n * @param config - `FirebaseServerApp` configuration.\n *\n * @returns The initialized `FirebaseServerApp`.\n *\n * @public\n */\nexport function initializeServerApp(\n  options: FirebaseOptions | FirebaseApp,\n  config: FirebaseServerAppSettings\n): FirebaseServerApp;\n\nexport function initializeServerApp(\n  _options: FirebaseOptions | FirebaseApp,\n  _serverAppConfig: FirebaseServerAppSettings\n): FirebaseServerApp {\n  if (isBrowser() && !isWebWorker()) {\n    // FirebaseServerApp isn't designed to be run in browsers.\n    throw ERROR_FACTORY.create(AppError.INVALID_SERVER_APP_ENVIRONMENT);\n  }\n\n  if (_serverAppConfig.automaticDataCollectionEnabled === undefined) {\n    _serverAppConfig.automaticDataCollectionEnabled = false;\n  }\n\n  let appOptions: FirebaseOptions;\n  if (_isFirebaseApp(_options)) {\n    appOptions = _options.options;\n  } else {\n    appOptions = _options;\n  }\n\n  // Build an app name based on a hash of the configuration options.\n  const nameObj = {\n    ..._serverAppConfig,\n    ...appOptions\n  };\n\n  // However, Do not mangle the name based on releaseOnDeref, since it will vary between the\n  // construction of FirebaseServerApp instances. For example, if the object is the request headers.\n  if (nameObj.releaseOnDeref !== undefined) {\n    delete nameObj.releaseOnDeref;\n  }\n\n  const hashCode = (s: string): number => {\n    return [...s].reduce(\n      (hash, c) => (Math.imul(31, hash) + c.charCodeAt(0)) | 0,\n      0\n    );\n  };\n\n  if (_serverAppConfig.releaseOnDeref !== undefined) {\n    if (typeof FinalizationRegistry === 'undefined') {\n      throw ERROR_FACTORY.create(\n        AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED,\n        {}\n      );\n    }\n  }\n\n  const nameString = '' + hashCode(JSON.stringify(nameObj));\n  const existingApp = _serverApps.get(nameString) as FirebaseServerApp;\n  if (existingApp) {\n    (existingApp as FirebaseServerAppImpl).incRefCount(\n      _serverAppConfig.releaseOnDeref\n    );\n    return existingApp;\n  }\n\n  const container = new ComponentContainer(nameString);\n  for (const component of _components.values()) {\n    container.addComponent(component);\n  }\n\n  const newApp = new FirebaseServerAppImpl(\n    appOptions,\n    _serverAppConfig,\n    nameString,\n    container\n  );\n\n  _serverApps.set(nameString, newApp);\n\n  return newApp;\n}\n\n/**\n * Retrieves a {@link @firebase/app#FirebaseApp} instance.\n *\n * When called with no arguments, the default app is returned. When an app name\n * is provided, the app corresponding to that name is returned.\n *\n * An exception is thrown if the app being retrieved has not yet been\n * initialized.\n *\n * @example\n * ```javascript\n * // Return the default app\n * const app = getApp();\n * ```\n *\n * @example\n * ```javascript\n * // Return a named app\n * const otherApp = getApp(\"otherApp\");\n * ```\n *\n * @param name - Optional name of the app to return. If no name is\n *   provided, the default is `\"[DEFAULT]\"`.\n *\n * @returns The app corresponding to the provided app name.\n *   If no app name is provided, the default app is returned.\n *\n * @public\n */\nexport function getApp(name: string = DEFAULT_ENTRY_NAME): FirebaseApp {\n  const app = _apps.get(name);\n  if (!app && name === DEFAULT_ENTRY_NAME && getDefaultAppConfig()) {\n    return initializeApp();\n  }\n  if (!app) {\n    throw ERROR_FACTORY.create(AppError.NO_APP, { appName: name });\n  }\n\n  return app;\n}\n\n/**\n * A (read-only) array of all initialized apps.\n * @public\n */\nexport function getApps(): FirebaseApp[] {\n  return Array.from(_apps.values());\n}\n\n/**\n * Renders this app unusable and frees the resources of all associated\n * services.\n *\n * @example\n * ```javascript\n * deleteApp(app)\n *   .then(function() {\n *     console.log(\"App deleted successfully\");\n *   })\n *   .catch(function(error) {\n *     console.log(\"Error deleting app:\", error);\n *   });\n * ```\n *\n * @public\n */\nexport async function deleteApp(app: FirebaseApp): Promise<void> {\n  let cleanupProviders = false;\n  const name = app.name;\n  if (_apps.has(name)) {\n    cleanupProviders = true;\n    _apps.delete(name);\n  } else if (_serverApps.has(name)) {\n    const firebaseServerApp = app as FirebaseServerAppImpl;\n    if (firebaseServerApp.decRefCount() <= 0) {\n      _serverApps.delete(name);\n      cleanupProviders = true;\n    }\n  }\n\n  if (cleanupProviders) {\n    await Promise.all(\n      (app as FirebaseAppImpl).container\n        .getProviders()\n        .map(provider => provider.delete())\n    );\n    (app as FirebaseAppImpl).isDeleted = true;\n  }\n}\n\n/**\n * Registers a library's name and version for platform logging purposes.\n * @param library - Name of 1p or 3p library (e.g. firestore, angularfire)\n * @param version - Current version of that library.\n * @param variant - Bundle variant, e.g., node, rn, etc.\n *\n * @public\n */\nexport function registerVersion(\n  libraryKeyOrName: string,\n  version: string,\n  variant?: string\n): void {\n  // TODO: We can use this check to whitelist strings when/if we set up\n  // a good whitelist system.\n  let library = PLATFORM_LOG_STRING[libraryKeyOrName] ?? libraryKeyOrName;\n  if (variant) {\n    library += `-${variant}`;\n  }\n  const libraryMismatch = library.match(/\\s|\\//);\n  const versionMismatch = version.match(/\\s|\\//);\n  if (libraryMismatch || versionMismatch) {\n    const warning = [\n      `Unable to register library \"${library}\" with version \"${version}\":`\n    ];\n    if (libraryMismatch) {\n      warning.push(\n        `library name \"${library}\" contains illegal characters (whitespace or \"/\")`\n      );\n    }\n    if (libraryMismatch && versionMismatch) {\n      warning.push('and');\n    }\n    if (versionMismatch) {\n      warning.push(\n        `version name \"${version}\" contains illegal characters (whitespace or \"/\")`\n      );\n    }\n    logger.warn(warning.join(' '));\n    return;\n  }\n  _registerComponent(\n    new Component(\n      `${library}-version` as Name,\n      () => ({ library, version }),\n      ComponentType.VERSION\n    )\n  );\n}\n\n/**\n * Sets log handler for all Firebase SDKs.\n * @param logCallback - An optional custom log handler that executes user code whenever\n * the Firebase SDK makes a logging call.\n *\n * @public\n */\nexport function onLog(\n  logCallback: LogCallback | null,\n  options?: LogOptions\n): void {\n  if (logCallback !== null && typeof logCallback !== 'function') {\n    throw ERROR_FACTORY.create(AppError.INVALID_LOG_ARGUMENT);\n  }\n  setUserLogHandler(logCallback, options);\n}\n\n/**\n * Sets log level for all Firebase SDKs.\n *\n * All of the log types above the current log level are captured (i.e. if\n * you set the log level to `info`, errors are logged, but `debug` and\n * `verbose` logs are not).\n *\n * @public\n */\nexport function setLogLevel(logLevel: LogLevelString): void {\n  setLogLevelImpl(logLevel);\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\nimport { DBSchema, openDB, IDBPDatabase } from 'idb';\nimport { AppError, ERROR_FACTORY } from './errors';\nimport { FirebaseApp } from './public-types';\nimport { HeartbeatsInIndexedDB } from './types';\nimport { logger } from './logger';\n\nconst DB_NAME = 'firebase-heartbeat-database';\nconst DB_VERSION = 1;\nconst STORE_NAME = 'firebase-heartbeat-store';\n\ninterface AppDB extends DBSchema {\n  'firebase-heartbeat-store': {\n    key: string;\n    value: HeartbeatsInIndexedDB;\n  };\n}\n\nlet dbPromise: Promise<IDBPDatabase<AppDB>> | null = null;\nfunction getDbPromise(): Promise<IDBPDatabase<AppDB>> {\n  if (!dbPromise) {\n    dbPromise = openDB<AppDB>(DB_NAME, DB_VERSION, {\n      upgrade: (db, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            try {\n              db.createObjectStore(STORE_NAME);\n            } catch (e) {\n              // Safari/iOS browsers throw occasional exceptions on\n              // db.createObjectStore() that may be a bug. Avoid blocking\n              // the rest of the app functionality.\n              console.warn(e);\n            }\n        }\n      }\n    }).catch(e => {\n      throw ERROR_FACTORY.create(AppError.IDB_OPEN, {\n        originalErrorMessage: e.message\n      });\n    });\n  }\n  return dbPromise;\n}\n\nexport async function readHeartbeatsFromIndexedDB(\n  app: FirebaseApp\n): Promise<HeartbeatsInIndexedDB | undefined> {\n  try {\n    const db = await getDbPromise();\n    const tx = db.transaction(STORE_NAME);\n    const result = await tx.objectStore(STORE_NAME).get(computeKey(app));\n    // We already have the value but tx.done can throw,\n    // so we need to await it here to catch errors\n    await tx.done;\n    return result;\n  } catch (e) {\n    if (e instanceof FirebaseError) {\n      logger.warn(e.message);\n    } else {\n      const idbGetError = ERROR_FACTORY.create(AppError.IDB_GET, {\n        originalErrorMessage: (e as Error)?.message\n      });\n      logger.warn(idbGetError.message);\n    }\n  }\n}\n\nexport async function writeHeartbeatsToIndexedDB(\n  app: FirebaseApp,\n  heartbeatObject: HeartbeatsInIndexedDB\n): Promise<void> {\n  try {\n    const db = await getDbPromise();\n    const tx = db.transaction(STORE_NAME, 'readwrite');\n    const objectStore = tx.objectStore(STORE_NAME);\n    await objectStore.put(heartbeatObject, computeKey(app));\n    await tx.done;\n  } catch (e) {\n    if (e instanceof FirebaseError) {\n      logger.warn(e.message);\n    } else {\n      const idbGetError = ERROR_FACTORY.create(AppError.IDB_WRITE, {\n        originalErrorMessage: (e as Error)?.message\n      });\n      logger.warn(idbGetError.message);\n    }\n  }\n}\n\nfunction computeKey(app: FirebaseApp): string {\n  return `${app.name}!${app.options.appId}`;\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ComponentContainer } from '@firebase/component';\nimport {\n  base64urlEncodeWithoutPadding,\n  isIndexedDBAvailable,\n  validateIndexedDBOpenable\n} from '@firebase/util';\nimport {\n  readHeartbeatsFromIndexedDB,\n  writeHeartbeatsToIndexedDB\n} from './indexeddb';\nimport { FirebaseApp } from './public-types';\nimport {\n  HeartbeatsByUserAgent,\n  HeartbeatService,\n  HeartbeatsInIndexedDB,\n  HeartbeatStorage,\n  SingleDateHeartbeat\n} from './types';\nimport { logger } from './logger';\n\nconst MAX_HEADER_BYTES = 1024;\n// 30 days\nconst STORED_HEARTBEAT_RETENTION_MAX_MILLIS = 30 * 24 * 60 * 60 * 1000;\n\nexport class HeartbeatServiceImpl implements HeartbeatService {\n  /**\n   * The persistence layer for heartbeats\n   * Leave public for easier testing.\n   */\n  _storage: HeartbeatStorageImpl;\n\n  /**\n   * In-memory cache for heartbeats, used by getHeartbeatsHeader() to generate\n   * the header string.\n   * Stores one record per date. This will be consolidated into the standard\n   * format of one record per user agent string before being sent as a header.\n   * Populated from indexedDB when the controller is instantiated and should\n   * be kept in sync with indexedDB.\n   * Leave public for easier testing.\n   */\n  _heartbeatsCache: HeartbeatsInIndexedDB | null = null;\n\n  /**\n   * the initialization promise for populating heartbeatCache.\n   * If getHeartbeatsHeader() is called before the promise resolves\n   * (heartbeatsCache == null), it should wait for this promise\n   * Leave public for easier testing.\n   */\n  _heartbeatsCachePromise: Promise<HeartbeatsInIndexedDB>;\n  constructor(private readonly container: ComponentContainer) {\n    const app = this.container.getProvider('app').getImmediate();\n    this._storage = new HeartbeatStorageImpl(app);\n    this._heartbeatsCachePromise = this._storage.read().then(result => {\n      this._heartbeatsCache = result;\n      return result;\n    });\n  }\n\n  /**\n   * Called to report a heartbeat. The function will generate\n   * a HeartbeatsByUserAgent object, update heartbeatsCache, and persist it\n   * to IndexedDB.\n   * Note that we only store one heartbeat per day. So if a heartbeat for today is\n   * already logged, subsequent calls to this function in the same day will be ignored.\n   */\n  async triggerHeartbeat(): Promise<void> {\n    try {\n      const platformLogger = this.container\n        .getProvider('platform-logger')\n        .getImmediate();\n\n      // This is the \"Firebase user agent\" string from the platform logger\n      // service, not the browser user agent.\n      const agent = platformLogger.getPlatformInfoString();\n      const date = getUTCDateString();\n      if (this._heartbeatsCache?.heartbeats == null) {\n        this._heartbeatsCache = await this._heartbeatsCachePromise;\n        // If we failed to construct a heartbeats cache, then return immediately.\n        if (this._heartbeatsCache?.heartbeats == null) {\n          return;\n        }\n      }\n      // Do not store a heartbeat if one is already stored for this day\n      // or if a header has already been sent today.\n      if (\n        this._heartbeatsCache.lastSentHeartbeatDate === date ||\n        this._heartbeatsCache.heartbeats.some(\n          singleDateHeartbeat => singleDateHeartbeat.date === date\n        )\n      ) {\n        return;\n      } else {\n        // There is no entry for this date. Create one.\n        this._heartbeatsCache.heartbeats.push({ date, agent });\n      }\n      // Remove entries older than 30 days.\n      this._heartbeatsCache.heartbeats =\n        this._heartbeatsCache.heartbeats.filter(singleDateHeartbeat => {\n          const hbTimestamp = new Date(singleDateHeartbeat.date).valueOf();\n          const now = Date.now();\n          return now - hbTimestamp <= STORED_HEARTBEAT_RETENTION_MAX_MILLIS;\n        });\n      return this._storage.overwrite(this._heartbeatsCache);\n    } catch (e) {\n      logger.warn(e);\n    }\n  }\n\n  /**\n   * Returns a base64 encoded string which can be attached to the heartbeat-specific header directly.\n   * It also clears all heartbeats from memory as well as in IndexedDB.\n   *\n   * NOTE: Consuming product SDKs should not send the header if this method\n   * returns an empty string.\n   */\n  async getHeartbeatsHeader(): Promise<string> {\n    try {\n      if (this._heartbeatsCache === null) {\n        await this._heartbeatsCachePromise;\n      }\n      // If it's still null or the array is empty, there is no data to send.\n      if (\n        this._heartbeatsCache?.heartbeats == null ||\n        this._heartbeatsCache.heartbeats.length === 0\n      ) {\n        return '';\n      }\n      const date = getUTCDateString();\n      // Extract as many heartbeats from the cache as will fit under the size limit.\n      const { heartbeatsToSend, unsentEntries } = extractHeartbeatsForHeader(\n        this._heartbeatsCache.heartbeats\n      );\n      const headerString = base64urlEncodeWithoutPadding(\n        JSON.stringify({ version: 2, heartbeats: heartbeatsToSend })\n      );\n      // Store last sent date to prevent another being logged/sent for the same day.\n      this._heartbeatsCache.lastSentHeartbeatDate = date;\n      if (unsentEntries.length > 0) {\n        // Store any unsent entries if they exist.\n        this._heartbeatsCache.heartbeats = unsentEntries;\n        // This seems more likely than emptying the array (below) to lead to some odd state\n        // since the cache isn't empty and this will be called again on the next request,\n        // and is probably safest if we await it.\n        await this._storage.overwrite(this._heartbeatsCache);\n      } else {\n        this._heartbeatsCache.heartbeats = [];\n        // Do not wait for this, to reduce latency.\n        void this._storage.overwrite(this._heartbeatsCache);\n      }\n      return headerString;\n    } catch (e) {\n      logger.warn(e);\n      return '';\n    }\n  }\n}\n\nfunction getUTCDateString(): string {\n  const today = new Date();\n  // Returns date format 'YYYY-MM-DD'\n  return today.toISOString().substring(0, 10);\n}\n\nexport function extractHeartbeatsForHeader(\n  heartbeatsCache: SingleDateHeartbeat[],\n  maxSize = MAX_HEADER_BYTES\n): {\n  heartbeatsToSend: HeartbeatsByUserAgent[];\n  unsentEntries: SingleDateHeartbeat[];\n} {\n  // Heartbeats grouped by user agent in the standard format to be sent in\n  // the header.\n  const heartbeatsToSend: HeartbeatsByUserAgent[] = [];\n  // Single date format heartbeats that are not sent.\n  let unsentEntries = heartbeatsCache.slice();\n  for (const singleDateHeartbeat of heartbeatsCache) {\n    // Look for an existing entry with the same user agent.\n    const heartbeatEntry = heartbeatsToSend.find(\n      hb => hb.agent === singleDateHeartbeat.agent\n    );\n    if (!heartbeatEntry) {\n      // If no entry for this user agent exists, create one.\n      heartbeatsToSend.push({\n        agent: singleDateHeartbeat.agent,\n        dates: [singleDateHeartbeat.date]\n      });\n      if (countBytes(heartbeatsToSend) > maxSize) {\n        // If the header would exceed max size, remove the added heartbeat\n        // entry and stop adding to the header.\n        heartbeatsToSend.pop();\n        break;\n      }\n    } else {\n      heartbeatEntry.dates.push(singleDateHeartbeat.date);\n      // If the header would exceed max size, remove the added date\n      // and stop adding to the header.\n      if (countBytes(heartbeatsToSend) > maxSize) {\n        heartbeatEntry.dates.pop();\n        break;\n      }\n    }\n    // Pop unsent entry from queue. (Skipped if adding the entry exceeded\n    // quota and the loop breaks early.)\n    unsentEntries = unsentEntries.slice(1);\n  }\n  return {\n    heartbeatsToSend,\n    unsentEntries\n  };\n}\n\nexport class HeartbeatStorageImpl implements HeartbeatStorage {\n  private _canUseIndexedDBPromise: Promise<boolean>;\n  constructor(public app: FirebaseApp) {\n    this._canUseIndexedDBPromise = this.runIndexedDBEnvironmentCheck();\n  }\n  async runIndexedDBEnvironmentCheck(): Promise<boolean> {\n    if (!isIndexedDBAvailable()) {\n      return false;\n    } else {\n      return validateIndexedDBOpenable()\n        .then(() => true)\n        .catch(() => false);\n    }\n  }\n  /**\n   * Read all heartbeats.\n   */\n  async read(): Promise<HeartbeatsInIndexedDB> {\n    const canUseIndexedDB = await this._canUseIndexedDBPromise;\n    if (!canUseIndexedDB) {\n      return { heartbeats: [] };\n    } else {\n      const idbHeartbeatObject = await readHeartbeatsFromIndexedDB(this.app);\n      if (idbHeartbeatObject?.heartbeats) {\n        return idbHeartbeatObject;\n      } else {\n        return { heartbeats: [] };\n      }\n    }\n  }\n  // overwrite the storage with the provided heartbeats\n  async overwrite(heartbeatsObject: HeartbeatsInIndexedDB): Promise<void> {\n    const canUseIndexedDB = await this._canUseIndexedDBPromise;\n    if (!canUseIndexedDB) {\n      return;\n    } else {\n      const existingHeartbeatsObject = await this.read();\n      return writeHeartbeatsToIndexedDB(this.app, {\n        lastSentHeartbeatDate:\n          heartbeatsObject.lastSentHeartbeatDate ??\n          existingHeartbeatsObject.lastSentHeartbeatDate,\n        heartbeats: heartbeatsObject.heartbeats\n      });\n    }\n  }\n  // add heartbeats\n  async add(heartbeatsObject: HeartbeatsInIndexedDB): Promise<void> {\n    const canUseIndexedDB = await this._canUseIndexedDBPromise;\n    if (!canUseIndexedDB) {\n      return;\n    } else {\n      const existingHeartbeatsObject = await this.read();\n      return writeHeartbeatsToIndexedDB(this.app, {\n        lastSentHeartbeatDate:\n          heartbeatsObject.lastSentHeartbeatDate ??\n          existingHeartbeatsObject.lastSentHeartbeatDate,\n        heartbeats: [\n          ...existingHeartbeatsObject.heartbeats,\n          ...heartbeatsObject.heartbeats\n        ]\n      });\n    }\n  }\n}\n\n/**\n * Calculate bytes of a HeartbeatsByUserAgent array after being wrapped\n * in a platform logging header JSON object, stringified, and converted\n * to base 64.\n */\nexport function countBytes(heartbeatsCache: HeartbeatsByUserAgent[]): number {\n  // base64 has a restricted set of characters, all of which should be 1 byte.\n  return base64urlEncodeWithoutPadding(\n    // heartbeatsCache wrapper properties\n    JSON.stringify({ version: 2, heartbeats: heartbeatsCache })\n  ).length;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Component, ComponentType } from '@firebase/component';\nimport { PlatformLoggerServiceImpl } from './platformLoggerService';\nimport { name, version } from '../package.json';\nimport { _registerComponent } from './internal';\nimport { registerVersion } from './api';\nimport { HeartbeatServiceImpl } from './heartbeatService';\n\nexport function registerCoreComponents(variant?: string): void {\n  _registerComponent(\n    new Component(\n      'platform-logger',\n      container => new PlatformLoggerServiceImpl(container),\n      ComponentType.PRIVATE\n    )\n  );\n  _registerComponent(\n    new Component(\n      'heartbeat',\n      container => new HeartbeatServiceImpl(container),\n      ComponentType.PRIVATE\n    )\n  );\n\n  // Register `app` package.\n  registerVersion(name, version, variant);\n  // BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n  // Register platform SDK identifier (no version).\n  registerVersion('fire-js', '');\n}\n", "/**\n * Firebase App\n *\n * @remarks This package coordinates the communication between the different Firebase components\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerCoreComponents } from './registerCoreComponents';\n\nexport * from './api';\nexport * from './internal';\nexport * from './public-types';\n\nregisterCoreComponents('__RUNTIME_ENV__');\n"], "mappings": ";;;;;;AAAA;;;;;;;;;;;;;;;AAeG;MAUUA,yBAAyB;EACpCC,YAA6BC,SAA6B;IAA7B,IAAS,CAAAA,SAAA,GAATA,SAAS;;;;EAGtCC,qBAAqBA,CAAA;IACnB,MAAMC,SAAS,GAAG,IAAI,CAACF,SAAS,CAACG,YAAY,EAAE;;;IAG/C,OAAOD,SAAS,CACbE,GAAG,CAACC,QAAQ,IAAG;MACd,IAAIC,wBAAwB,CAACD,QAAQ,CAAC,EAAE;QACtC,MAAME,OAAO,GAAGF,QAAQ,CAACG,YAAY,EAAoB;QACzD,OAAO,GAAGD,OAAO,CAACE,OAAO,IAAIF,OAAO,CAACG,OAAO,EAAE;MAC/C,OAAM;QACL,OAAO,IAAI;MACZ;IACH,CAAC,CAAC,CACDC,MAAM,CAACC,SAAS,IAAIA,SAAS,CAAC,CAC9BC,IAAI,CAAC,GAAG,CAAC;;AAEf;AACD;;;;;;;AAOG;AACH,SAASP,wBAAwBA,CAACD,QAAwB;EACxD,MAAMS,SAAS,GAAGT,QAAQ,CAACU,YAAY,EAAE;EACzC,OAAO,CAAAD,SAAS,KAAT,QAAAA,SAAS,uBAATA,SAAS,CAAEE,IAAI;AACxB;;;;ACzDA;;;;;;;;;;;;;;;AAeG;AAII,MAAMC,MAAM,GAAG,IAAIC,MAAM,CAAC,eAAe,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnBjD;;;;;;;;;;;;;;;AAeG;AA8BH;;;;AAIG;AACI,MAAMC,kBAAkB,GAAG;AAE3B,MAAMC,mBAAmB,GAAG;EACjC,CAACC,MAAO,GAAG,WAAW;EACtB,CAACC,MAAa,GAAG,kBAAkB;EACnC,CAACC,MAAa,GAAG,gBAAgB;EACjC,CAACC,MAAmB,GAAG,uBAAuB;EAC9C,CAACC,MAAY,GAAG,gBAAgB;EAChC,CAACC,MAAkB,GAAG,uBAAuB;EAC7C,CAACC,MAAQ,GAAG,WAAW;EACvB,CAACC,MAAc,GAAG,kBAAkB;EACpC,CAACC,MAAY,GAAG,WAAW;EAC3B,CAACC,MAAe,GAAG,mBAAmB;EACtC,CAACC,MAAkB,GAAG,kBAAkB;EACxC,CAACC,MAAa,GAAG,SAAS;EAC1B,CAACC,MAAmB,GAAG,gBAAgB;EACvC,CAACC,MAAiB,GAAG,UAAU;EAC/B,CAACC,MAAuB,GAAG,iBAAiB;EAC5C,CAACC,MAAa,GAAG,UAAU;EAC3B,CAACC,MAAmB,GAAG,iBAAiB;EACxC,CAACC,MAAe,GAAG,WAAW;EAC9B,CAACC,MAAqB,GAAG,kBAAkB;EAC3C,CAACC,MAAgB,GAAG,SAAS;EAC7B,CAACC,MAAsB,GAAG,gBAAgB;EAC1C,CAACC,MAAW,GAAG,UAAU;EACzB,CAACC,MAAiB,GAAG,iBAAiB;EACtC,CAACC,MAAa,GAAG,UAAU;EAC3B,CAACC,MAAmB,GAAG,iBAAiB;EACxC,CAACC,MAAU,GAAG,aAAa;EAC3B,SAAS,EAAE,SAAS;EACpB,CAACC,IAAW,GAAG;CACP;;ACjFV;;;;;;;;;;;;;;;AAeG;AAaH;;AAEG;AACU,MAAAC,KAAK,GAAG,IAAIC,GAAG;AAE5B;;AAEG;AACU,MAAAC,WAAW,GAAG,IAAID,GAAG;AAElC;;;;AAIG;AACH;AACa,MAAAE,WAAW,GAAG,IAAIF,GAAG;AAElC;;;;AAIG;AACa,SAAAG,aAAaA,CAC3BC,GAAgB,EAChBvC,SAAuB;EAEvB,IAAI;IACDuC,GAAuB,CAACrD,SAAS,CAACsD,YAAY,CAACxC,SAAS,CAAC;EAC3D,EAAC,OAAOyC,CAAC,EAAE;IACVtC,MAAM,CAACuC,KAAK,CACV,aAAa1C,SAAS,CAACiC,IAAI,wCAAwCM,GAAG,CAACN,IAAI,EAAE,EAC7EQ,CAAC,CACF;EACF;AACH;AAEA;;;AAGG;AACa,SAAAE,wBAAwBA,CACtCJ,GAAgB,EAChBvC,SAAoB;EAEnBuC,GAAuB,CAACrD,SAAS,CAAC0D,uBAAuB,CAAC5C,SAAS,CAAC;AACvE;AAEA;;;;;;AAMG;AACG,SAAU6C,kBAAkBA,CAChC7C,SAAuB;EAEvB,MAAM8C,aAAa,GAAG9C,SAAS,CAACiC,IAAI;EACpC,IAAII,WAAW,CAACU,GAAG,CAACD,aAAa,CAAC,EAAE;IAClC3C,MAAM,CAACuC,KAAK,CACV,sDAAsDI,aAAa,GAAG,CACvE;IAED,OAAO,KAAK;EACb;EAEDT,WAAW,CAACW,GAAG,CAACF,aAAa,EAAE9C,SAAS,CAAC;;EAGzC,KAAK,MAAMuC,GAAG,IAAIL,KAAK,CAACe,MAAM,EAAE,EAAE;IAChCX,aAAa,CAACC,GAAsB,EAAEvC,SAAS,CAAC;EACjD;EAED,KAAK,MAAMkD,SAAS,IAAId,WAAW,CAACa,MAAM,EAAE,EAAE;IAC5CX,aAAa,CAACY,SAAkC,EAAElD,SAAS,CAAC;EAC7D;EAED,OAAO,IAAI;AACb;AAEA;;;;;;;;AAQG;AACa,SAAAmD,YAAYA,CAC1BZ,GAAgB,EAChBN,IAAO;EAEP,MAAMmB,mBAAmB,GAAIb,GAAuB,CAACrD,SAAS,CAC3DmE,WAAW,CAAC,WAAW,CAAC,CACxB3D,YAAY,CAAC;IAAE4D,QAAQ,EAAE;EAAI,CAAE,CAAC;EACnC,IAAIF,mBAAmB,EAAE;IACvB,KAAKA,mBAAmB,CAACG,gBAAgB,EAAE;EAC5C;EACD,OAAQhB,GAAuB,CAACrD,SAAS,CAACmE,WAAW,CAACpB,IAAI,CAAC;AAC7D;AAEA;;;;;;;AAOG;AACG,SAAUuB,sBAAsBA,CACpCjB,GAAgB,EAChBN,IAAO,EACPwB,kBAAA,GAA6BpD,kBAAkB;EAE/C8C,YAAY,CAACZ,GAAG,EAAEN,IAAI,CAAC,CAACyB,aAAa,CAACD,kBAAkB,CAAC;AAC3D;AAEA;;;;;;;AAOG;AACG,SAAUE,cAAcA,CAC5BC,GAAkC;EAElC,OAAQA,GAAmB,CAACC,OAAO,KAAKC,SAAS;AACnD;AAEA;;;;;;;AAOG;AACG,SAAUC,oBAAoBA,CAClCH,GAAoC;EAEpC,OAAQA,GAAyB,CAACI,QAAQ,KAAKF,SAAS;AAC1D;AAEA;;;;AAIG;SACaG,gBAAgBA,CAAA;EAC9B5B,WAAW,CAAC6B,KAAK,EAAE;AACrB;;ACtLA;;;;;;;;;;;;;;;AAeG;AAqBH,MAAMC,MAAM,GAAuB;EACjC,kCACE,kDAAkD,GAClD,4BAA4B;EAC9B,8CAAyB,gCAAgC;EACzD,gDACE,iFAAiF;EACnF,4CAAwB,iDAAiD;EACzE,0DAA+B,sCAAsC;EACrE,0CACE,yEAAyE;EAC3E,8DACE,sDAAsD,GACtD,wBAAwB;EAC1B,8DACE,uDAAuD;EACzD,sCACE,+EAA+E;EACjF,oCACE,oFAAoF;EACtF,sCACE,kFAAkF;EACpF,0CACE,qFAAqF;EACvF,4FACE,yGAAyG;EAC3G,kFACE;CACH;AAeM,MAAMC,aAAa,GAAG,IAAIC,YAAY,CAC3C,KAAK,EACL,UAAU,EACVF,MAAM,CACP;;ACnFD;;;;;;;;;;;;;;;AAeG;MAcUG,eAAe;EAc1BrF,YACE4E,OAAwB,EACxBU,MAAqC,EACrCrF,SAA6B;IANrB,IAAU,CAAAsF,UAAA,GAAG,KAAK;IAQ1B,IAAI,CAACC,QAAQ,GAAQC,MAAA,CAAAC,MAAA,KAAAd,OAAO,CAAE;IAC9B,IAAI,CAACe,OAAO,GAAQF,MAAA,CAAAC,MAAA,KAAAJ,MAAM,CAAE;IAC5B,IAAI,CAACM,KAAK,GAAGN,MAAM,CAACtC,IAAI;IACxB,IAAI,CAAC6C,+BAA+B,GAClCP,MAAM,CAACQ,8BAA8B;IACvC,IAAI,CAACC,UAAU,GAAG9F,SAAS;IAC3B,IAAI,CAACA,SAAS,CAACsD,YAAY,CACzB,IAAIyC,SAAS,CAAC,KAAK,EAAE,MAAM,IAAI,sCAAuB,CACvD;;EAGH,IAAIF,8BAA8BA,CAAA;IAChC,IAAI,CAACG,cAAc,EAAE;IACrB,OAAO,IAAI,CAACJ,+BAA+B;;EAG7C,IAAIC,8BAA8BA,CAACI,GAAY;IAC7C,IAAI,CAACD,cAAc,EAAE;IACrB,IAAI,CAACJ,+BAA+B,GAAGK,GAAG;;EAG5C,IAAIlD,IAAIA,CAAA;IACN,IAAI,CAACiD,cAAc,EAAE;IACrB,OAAO,IAAI,CAACL,KAAK;;EAGnB,IAAIhB,OAAOA,CAAA;IACT,IAAI,CAACqB,cAAc,EAAE;IACrB,OAAO,IAAI,CAACT,QAAQ;;EAGtB,IAAIF,MAAMA,CAAA;IACR,IAAI,CAACW,cAAc,EAAE;IACrB,OAAO,IAAI,CAACN,OAAO;;EAGrB,IAAI1F,SAASA,CAAA;IACX,OAAO,IAAI,CAAC8F,UAAU;;EAGxB,IAAII,SAASA,CAAA;IACX,OAAO,IAAI,CAACZ,UAAU;;EAGxB,IAAIY,SAASA,CAACD,GAAY;IACxB,IAAI,CAACX,UAAU,GAAGW,GAAG;;EAGvB;;;AAGG;EACOD,cAAcA,CAAA;IACtB,IAAI,IAAI,CAACE,SAAS,EAAE;MAClB,MAAMhB,aAAa,CAACiB,MAAM,2CAAuB;QAAEC,OAAO,EAAE,IAAI,CAACT;MAAK,CAAE,CAAC;IAC1E;;AAEJ;;ACzGD;;;;;;;;;;;;;;;AAeG;AAcG,MAAOU,qBACX,SAAQjB,eAAe;EAOvBrF,YACE4E,OAA0C,EAC1C2B,YAAuC,EACvCvD,IAAY,EACZ/C,SAA6B;;IAG7B,MAAM6F,8BAA8B,GAClCS,YAAY,CAACT,8BAA8B,KAAKjB,SAAS,GACrD0B,YAAY,CAACT,8BAA8B,GAC3C,KAAK;;IAGX,MAAMR,MAAM,GAAkC;MAC5CtC,IAAI;MACJ8C;KACD;IAED,IAAKlB,OAA2B,CAAC4B,MAAM,KAAK3B,SAAS,EAAE;;MAErD,KAAK,CAACD,OAA0B,EAAEU,MAAM,EAAErF,SAAS,CAAC;IACrD,OAAM;MACL,MAAMwG,OAAO,GAAoB7B,OAA0B;MAC3D,KAAK,CAAC6B,OAAO,CAAC7B,OAAO,EAAEU,MAAM,EAAErF,SAAS,CAAC;IAC1C;;IAGD,IAAI,CAACyG,aAAa,GAAAjB,MAAA,CAAAC,MAAA;MAChBI;IAA8B,CAC3B,EAAAS,YAAY,CAChB;IAED,IAAI,CAACI,qBAAqB,GAAG,IAAI;IACjC,IAAI,OAAOC,oBAAoB,KAAK,WAAW,EAAE;MAC/C,IAAI,CAACD,qBAAqB,GAAG,IAAIC,oBAAoB,CAAC,MAAK;QACzD,IAAI,CAACC,gBAAgB,EAAE;MACzB,CAAC,CAAC;IACH;IAED,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,WAAW,CAAC,IAAI,CAACL,aAAa,CAACM,cAAc,CAAC;;;IAInD,IAAI,CAACN,aAAa,CAACM,cAAc,GAAGnC,SAAS;IAC7C0B,YAAY,CAACS,cAAc,GAAGnC,SAAS;IAEvCoC,eAAe,CAAC3F,MAAW,EAAE4F,SAAO,EAAE,WAAW,CAAC;;EAGpDC,MAAMA,CAAA;IACJ,OAAOtC,SAAS;;EAGlB,IAAIuC,QAAQA,CAAA;IACV,OAAO,IAAI,CAACN,SAAS;;;;EAKvBC,WAAWA,CAACpC,GAAuB;IACjC,IAAI,IAAI,CAACwB,SAAS,EAAE;MAClB;IACD;IACD,IAAI,CAACW,SAAS,EAAE;IAChB,IAAInC,GAAG,KAAKE,SAAS,IAAI,IAAI,CAAC8B,qBAAqB,KAAK,IAAI,EAAE;MAC5D,IAAI,CAACA,qBAAqB,CAACU,QAAQ,CAAC1C,GAAG,EAAE,IAAI,CAAC;IAC/C;;;EAIH2C,WAAWA,CAAA;IACT,IAAI,IAAI,CAACnB,SAAS,EAAE;MAClB,OAAO,CAAC;IACT;IACD,OAAO,EAAE,IAAI,CAACW,SAAS;;;;;EAMjBD,gBAAgBA,CAAA;IACtB,KAAKU,SAAS,CAAC,IAAI,CAAC;;EAGtB,IAAIxC,QAAQA,CAAA;IACV,IAAI,CAACkB,cAAc,EAAE;IACrB,OAAO,IAAI,CAACS,aAAa;;EAG3B;;;AAGG;EACOT,cAAcA,CAAA;IACtB,IAAI,IAAI,CAACE,SAAS,EAAE;MAClB,MAAMhB,aAAa,CAACiB,MAAM,wDAA6B;IACxD;;AAEJ;;ACxID;;;;;;;;;;;;;;;AAeG;AA4CH;;;;AAIG;AACI,MAAMoB,WAAW,GAAG7G,OAAA;SAoEX8G,aAAaA,CAC3BjC,QAA0B,EAC1BkC,SAAS,GAAG,EAAE;EAEd,IAAI9C,OAAO,GAAGY,QAAQ;EAEtB,IAAI,OAAOkC,SAAS,KAAK,QAAQ,EAAE;IACjC,MAAM1E,IAAI,GAAG0E,SAAS;IACtBA,SAAS,GAAG;MAAE1E;IAAI,CAAE;EACrB;EAED,MAAMsC,MAAM,GAAAG,MAAA,CAAAC,MAAA;IACV1C,IAAI,EAAE5B,kBAAkB;IACxB0E,8BAA8B,EAAE;EAAK,GAClC4B,SAAS,CACb;EACD,MAAM1E,IAAI,GAAGsC,MAAM,CAACtC,IAAI;EAExB,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,CAACA,IAAI,EAAE;IACrC,MAAMmC,aAAa,CAACiB,MAAM,CAAwB;MAChDC,OAAO,EAAEsB,MAAM,CAAC3E,IAAI;IACrB,EAAC;EACH;EAED4B,OAAO,KAAPA,OAAO,GAAKgD,mBAAmB,EAAE,CAAC;EAElC,IAAI,CAAChD,OAAO,EAAE;IACZ,MAAMO,aAAa,CAACiB,MAAM,wCAAqB;EAChD;EAED,MAAMyB,WAAW,GAAG5E,KAAK,CAAC6E,GAAG,CAAC9E,IAAI,CAAoB;EACtD,IAAI6E,WAAW,EAAE;;IAEf,IACEE,SAAS,CAACnD,OAAO,EAAEiD,WAAW,CAACjD,OAAO,CAAC,IACvCmD,SAAS,CAACzC,MAAM,EAAEuC,WAAW,CAACvC,MAAM,CAAC,EACrC;MACA,OAAOuC,WAAW;IACnB,OAAM;MACL,MAAM1C,aAAa,CAACiB,MAAM,CAAyB;QAAEC,OAAO,EAAErD;MAAI,CAAE,CAAC;IACtE;EACF;EAED,MAAM/C,SAAS,GAAG,IAAI+H,kBAAkB,CAAChF,IAAI,CAAC;EAC9C,KAAK,MAAMjC,SAAS,IAAIqC,WAAW,CAACY,MAAM,EAAE,EAAE;IAC5C/D,SAAS,CAACsD,YAAY,CAACxC,SAAS,CAAC;EAClC;EAED,MAAMkH,MAAM,GAAG,IAAI5C,eAAe,CAACT,OAAO,EAAEU,MAAM,EAAErF,SAAS,CAAC;EAE9DgD,KAAK,CAACc,GAAG,CAACf,IAAI,EAAEiF,MAAM,CAAC;EAEvB,OAAOA,MAAM;AACf;AAgDgB,SAAAC,mBAAmBA,CACjC1C,QAAuC,EACvC2C,gBAA2C;EAE3C,IAAIC,SAAS,EAAE,IAAI,CAACC,WAAW,EAAE,EAAE;;IAEjC,MAAMlD,aAAa,CAACiB,MAAM,gFAAyC;EACpE;EAED,IAAI+B,gBAAgB,CAACrC,8BAA8B,KAAKjB,SAAS,EAAE;IACjEsD,gBAAgB,CAACrC,8BAA8B,GAAG,KAAK;EACxD;EAED,IAAIwC,UAA2B;EAC/B,IAAI5D,cAAc,CAACc,QAAQ,CAAC,EAAE;IAC5B8C,UAAU,GAAG9C,QAAQ,CAACZ,OAAO;EAC9B,OAAM;IACL0D,UAAU,GAAG9C,QAAQ;EACtB;;EAGD,MAAM+C,OAAO,GACR9C,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAAyC,gBAAgB,CAChB,EAAAG,UAAU,CACd;;;EAID,IAAIC,OAAO,CAACvB,cAAc,KAAKnC,SAAS,EAAE;IACxC,OAAO0D,OAAO,CAACvB,cAAc;EAC9B;EAED,MAAMwB,QAAQ,GAAIC,CAAS,IAAY;IACrC,OAAO,CAAC,GAAGA,CAAC,CAAC,CAACC,MAAM,CAClB,CAACC,IAAI,EAAEC,CAAC,KAAMC,IAAI,CAACC,IAAI,CAAC,EAAE,EAAEH,IAAI,CAAC,GAAGC,CAAC,CAACG,UAAU,CAAC,CAAC,CAAC,GAAI,CAAC,EACxD,CAAC,CACF;EACH,CAAC;EAED,IAAIZ,gBAAgB,CAACnB,cAAc,KAAKnC,SAAS,EAAE;IACjD,IAAI,OAAO+B,oBAAoB,KAAK,WAAW,EAAE;MAC/C,MAAMzB,aAAa,CAACiB,MAAM,CAExB,4FAAE,CACH;IACF;EACF;EAED,MAAM4C,UAAU,GAAG,EAAE,GAAGR,QAAQ,CAACS,IAAI,CAACC,SAAS,CAACX,OAAO,CAAC,CAAC;EACzD,MAAMV,WAAW,GAAG1E,WAAW,CAAC2E,GAAG,CAACkB,UAAU,CAAsB;EACpE,IAAInB,WAAW,EAAE;IACdA,WAAqC,CAACd,WAAW,CAChDoB,gBAAgB,CAACnB,cAAc,CAChC;IACD,OAAOa,WAAW;EACnB;EAED,MAAM5H,SAAS,GAAG,IAAI+H,kBAAkB,CAACgB,UAAU,CAAC;EACpD,KAAK,MAAMjI,SAAS,IAAIqC,WAAW,CAACY,MAAM,EAAE,EAAE;IAC5C/D,SAAS,CAACsD,YAAY,CAACxC,SAAS,CAAC;EAClC;EAED,MAAMkH,MAAM,GAAG,IAAI3B,qBAAqB,CACtCgC,UAAU,EACVH,gBAAgB,EAChBa,UAAU,EACV/I,SAAS,CACV;EAEDkD,WAAW,CAACY,GAAG,CAACiF,UAAU,EAAEf,MAAM,CAAC;EAEnC,OAAOA,MAAM;AACf;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BG;AACa,SAAAkB,MAAMA,CAACnG,IAAA,GAAe5B,kBAAkB;EACtD,MAAMkC,GAAG,GAAGL,KAAK,CAAC6E,GAAG,CAAC9E,IAAI,CAAC;EAC3B,IAAI,CAACM,GAAG,IAAIN,IAAI,KAAK5B,kBAAkB,IAAIwG,mBAAmB,EAAE,EAAE;IAChE,OAAOH,aAAa,EAAE;EACvB;EACD,IAAI,CAACnE,GAAG,EAAE;IACR,MAAM6B,aAAa,CAACiB,MAAM,CAAkB;MAAEC,OAAO,EAAErD;IAAI,CAAE,CAAC;EAC/D;EAED,OAAOM,GAAG;AACZ;AAEA;;;AAGG;SACa8F,OAAOA,CAAA;EACrB,OAAOC,KAAK,CAACC,IAAI,CAACrG,KAAK,CAACe,MAAM,EAAE,CAAC;AACnC;AAEA;;;;;;;;;;;;;;;;AAgBG;AACI,eAAeuD,SAASA,CAACjE,GAAgB;EAC9C,IAAIiG,gBAAgB,GAAG,KAAK;EAC5B,MAAMvG,IAAI,GAAGM,GAAG,CAACN,IAAI;EACrB,IAAIC,KAAK,CAACa,GAAG,CAACd,IAAI,CAAC,EAAE;IACnBuG,gBAAgB,GAAG,IAAI;IACvBtG,KAAK,CAACuG,MAAM,CAACxG,IAAI,CAAC;EACnB,OAAM,IAAIG,WAAW,CAACW,GAAG,CAACd,IAAI,CAAC,EAAE;IAChC,MAAMyG,iBAAiB,GAAGnG,GAA4B;IACtD,IAAImG,iBAAiB,CAACnC,WAAW,EAAE,IAAI,CAAC,EAAE;MACxCnE,WAAW,CAACqG,MAAM,CAACxG,IAAI,CAAC;MACxBuG,gBAAgB,GAAG,IAAI;IACxB;EACF;EAED,IAAIA,gBAAgB,EAAE;IACpB,MAAMG,OAAO,CAACC,GAAG,CACdrG,GAAuB,CAACrD,SAAS,CAC/BG,YAAY,EAAE,CACdC,GAAG,CAACC,QAAQ,IAAIA,QAAQ,CAACkJ,MAAM,EAAE,CAAC,CACtC;IACAlG,GAAuB,CAAC6C,SAAS,GAAG,IAAI;EAC1C;AACH;AAEA;;;;;;;AAOG;SACac,eAAeA,CAC7B2C,gBAAwB,EACxBjJ,OAAe,EACfkJ,OAAgB;;;;EAIhB,IAAInJ,OAAO,GAAG,CAAAoJ,EAAA,GAAAzI,mBAAmB,CAACuI,gBAAgB,CAAC,MAAI,QAAAE,EAAA,cAAAA,EAAA,GAAAF,gBAAgB;EACvE,IAAIC,OAAO,EAAE;IACXnJ,OAAO,IAAI,IAAImJ,OAAO,EAAE;EACzB;EACD,MAAME,eAAe,GAAGrJ,OAAO,CAACsJ,KAAK,CAAC,OAAO,CAAC;EAC9C,MAAMC,eAAe,GAAGtJ,OAAO,CAACqJ,KAAK,CAAC,OAAO,CAAC;EAC9C,IAAID,eAAe,IAAIE,eAAe,EAAE;IACtC,MAAMC,OAAO,GAAG,CACd,+BAA+BxJ,OAAO,mBAAmBC,OAAO,IAAI,CACrE;IACD,IAAIoJ,eAAe,EAAE;MACnBG,OAAO,CAACC,IAAI,CACV,iBAAiBzJ,OAAO,mDAAmD,CAC5E;IACF;IACD,IAAIqJ,eAAe,IAAIE,eAAe,EAAE;MACtCC,OAAO,CAACC,IAAI,CAAC,KAAK,CAAC;IACpB;IACD,IAAIF,eAAe,EAAE;MACnBC,OAAO,CAACC,IAAI,CACV,iBAAiBxJ,OAAO,mDAAmD,CAC5E;IACF;IACDO,MAAM,CAACkJ,IAAI,CAACF,OAAO,CAACpJ,IAAI,CAAC,GAAG,CAAC,CAAC;IAC9B;EACD;EACD8C,kBAAkB,CAChB,IAAIoC,SAAS,CACX,GAAGtF,OAAO,UAAkB,EAC5B,OAAO;IAAEA,OAAO;IAAEC;EAAO,CAAE,CAAC,wCAE7B,CACF;AACH;AAEA;;;;;;AAMG;AACa,SAAA0J,KAAKA,CACnBC,WAA+B,EAC/B1F,OAAoB;EAEpB,IAAI0F,WAAW,KAAK,IAAI,IAAI,OAAOA,WAAW,KAAK,UAAU,EAAE;IAC7D,MAAMnF,aAAa,CAACiB,MAAM,4DAA+B;EAC1D;EACDmE,iBAAiB,CAACD,WAAW,EAAE1F,OAAO,CAAC;AACzC;AAEA;;;;;;;;AAQG;AACG,SAAU4F,WAAWA,CAACC,QAAwB;EAClDC,aAAe,CAACD,QAAQ,CAAC;AAC3B;;AC3dA;;;;;;;;;;;;;;;AAeG;AASH,MAAME,OAAO,GAAG,6BAA6B;AAC7C,MAAMC,UAAU,GAAG,CAAC;AACpB,MAAMC,UAAU,GAAG,0BAA0B;AAS7C,IAAIC,SAAS,GAAwC,IAAI;AACzD,SAASC,YAAYA,CAAA;EACnB,IAAI,CAACD,SAAS,EAAE;IACdA,SAAS,GAAGE,MAAM,CAAQL,OAAO,EAAEC,UAAU,EAAE;MAC7CK,OAAO,EAAEA,CAACC,EAAE,EAAEC,UAAU,KAAI;;;;;;QAM1B,QAAQA,UAAU;UAChB,KAAK,CAAC;YACJ,IAAI;cACFD,EAAE,CAACE,iBAAiB,CAACP,UAAU,CAAC;YACjC,EAAC,OAAOrH,CAAC,EAAE;;;;cAIV6H,OAAO,CAACjB,IAAI,CAAC5G,CAAC,CAAC;YAChB;QACJ;;IAEJ,EAAC,CAAC8H,KAAK,CAAC9H,CAAC,IAAG;MACX,MAAM2B,aAAa,CAACiB,MAAM,CAAoB;QAC5CmF,oBAAoB,EAAE/H,CAAC,CAACgI;MACzB,EAAC;IACJ,CAAC,CAAC;EACH;EACD,OAAOV,SAAS;AAClB;AAEO,eAAeW,2BAA2BA,CAC/CnI,GAAgB;EAEhB,IAAI;IACF,MAAM4H,EAAE,GAAG,MAAMH,YAAY,EAAE;IAC/B,MAAMW,EAAE,GAAGR,EAAE,CAACS,WAAW,CAACd,UAAU,CAAC;IACrC,MAAMe,MAAM,GAAG,MAAMF,EAAE,CAACG,WAAW,CAAChB,UAAU,CAAC,CAAC/C,GAAG,CAACgE,UAAU,CAACxI,GAAG,CAAC,CAAC;;;IAGpE,MAAMoI,EAAE,CAACK,IAAI;IACb,OAAOH,MAAM;EACd,EAAC,OAAOpI,CAAC,EAAE;IACV,IAAIA,CAAC,YAAYwI,aAAa,EAAE;MAC9B9K,MAAM,CAACkJ,IAAI,CAAC5G,CAAC,CAACgI,OAAO,CAAC;IACvB,OAAM;MACL,MAAMS,WAAW,GAAG9G,aAAa,CAACiB,MAAM,CAAmB;QACzDmF,oBAAoB,EAAG/H,CAAW,aAAXA,CAAC,KAAD,kBAAAA,CAAC,CAAYgI;MACrC,EAAC;MACFtK,MAAM,CAACkJ,IAAI,CAAC6B,WAAW,CAACT,OAAO,CAAC;IACjC;EACF;AACH;AAEO,eAAeU,0BAA0BA,CAC9C5I,GAAgB,EAChB6I,eAAsC;EAEtC,IAAI;IACF,MAAMjB,EAAE,GAAG,MAAMH,YAAY,EAAE;IAC/B,MAAMW,EAAE,GAAGR,EAAE,CAACS,WAAW,CAACd,UAAU,EAAE,WAAW,CAAC;IAClD,MAAMgB,WAAW,GAAGH,EAAE,CAACG,WAAW,CAAChB,UAAU,CAAC;IAC9C,MAAMgB,WAAW,CAACO,GAAG,CAACD,eAAe,EAAEL,UAAU,CAACxI,GAAG,CAAC,CAAC;IACvD,MAAMoI,EAAE,CAACK,IAAI;EACd,EAAC,OAAOvI,CAAC,EAAE;IACV,IAAIA,CAAC,YAAYwI,aAAa,EAAE;MAC9B9K,MAAM,CAACkJ,IAAI,CAAC5G,CAAC,CAACgI,OAAO,CAAC;IACvB,OAAM;MACL,MAAMS,WAAW,GAAG9G,aAAa,CAACiB,MAAM,CAAqB;QAC3DmF,oBAAoB,EAAG/H,CAAW,aAAXA,CAAC,KAAD,kBAAAA,CAAC,CAAYgI;MACrC,EAAC;MACFtK,MAAM,CAACkJ,IAAI,CAAC6B,WAAW,CAACT,OAAO,CAAC;IACjC;EACF;AACH;AAEA,SAASM,UAAUA,CAACxI,GAAgB;EAClC,OAAO,GAAGA,GAAG,CAACN,IAAI,IAAIM,GAAG,CAACsB,OAAO,CAACyH,KAAK,EAAE;AAC3C;;ACjHA;;;;;;;;;;;;;;;AAeG;AAsBH,MAAMC,gBAAgB,GAAG,IAAI;AAC7B;AACA,MAAMC,qCAAqC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;MAEzDC,oBAAoB;EAyB/BxM,YAA6BC,SAA6B;IAA7B,IAAS,CAAAA,SAAA,GAATA,SAAS;IAlBtC;;;;;;;;AAQG;IACH,IAAgB,CAAAwM,gBAAA,GAAiC,IAAI;IAUnD,MAAMnJ,GAAG,GAAG,IAAI,CAACrD,SAAS,CAACmE,WAAW,CAAC,KAAK,CAAC,CAAC3D,YAAY,EAAE;IAC5D,IAAI,CAACiM,QAAQ,GAAG,IAAIC,oBAAoB,CAACrJ,GAAG,CAAC;IAC7C,IAAI,CAACsJ,uBAAuB,GAAG,IAAI,CAACF,QAAQ,CAACG,IAAI,EAAE,CAACC,IAAI,CAAClB,MAAM,IAAG;MAChE,IAAI,CAACa,gBAAgB,GAAGb,MAAM;MAC9B,OAAOA,MAAM;IACf,CAAC,CAAC;;EAGJ;;;;;;AAMG;EACH,MAAMtH,gBAAgBA,CAAA;;IACpB,IAAI;MACF,MAAMyI,cAAc,GAAG,IAAI,CAAC9M,SAAS,CAClCmE,WAAW,CAAC,iBAAiB,CAAC,CAC9B3D,YAAY,EAAE;;;MAIjB,MAAMuM,KAAK,GAAGD,cAAc,CAAC7M,qBAAqB,EAAE;MACpD,MAAM+M,IAAI,GAAGC,gBAAgB,EAAE;MAC/B,IAAI,EAAApD,EAAA,OAAI,CAAC2C,gBAAgB,MAAE,QAAA3C,EAAA,uBAAAA,EAAA,CAAAqD,UAAU,KAAI,IAAI,EAAE;QAC7C,IAAI,CAACV,gBAAgB,GAAG,MAAM,IAAI,CAACG,uBAAuB;;QAE1D,IAAI,EAAAQ,EAAA,OAAI,CAACX,gBAAgB,MAAE,QAAAW,EAAA,uBAAAA,EAAA,CAAAD,UAAU,KAAI,IAAI,EAAE;UAC7C;QACD;MACF;;;MAGD,IACE,IAAI,CAACV,gBAAgB,CAACY,qBAAqB,KAAKJ,IAAI,IACpD,IAAI,CAACR,gBAAgB,CAACU,UAAU,CAACG,IAAI,CACnCC,mBAAmB,IAAIA,mBAAmB,CAACN,IAAI,KAAKA,IAAI,CACzD,EACD;QACA;MACD,OAAM;;QAEL,IAAI,CAACR,gBAAgB,CAACU,UAAU,CAAChD,IAAI,CAAC;UAAE8C,IAAI;UAAED;QAAK,CAAE,CAAC;MACvD;;MAED,IAAI,CAACP,gBAAgB,CAACU,UAAU,GAC9B,IAAI,CAACV,gBAAgB,CAACU,UAAU,CAACvM,MAAM,CAAC2M,mBAAmB,IAAG;QAC5D,MAAMC,WAAW,GAAG,IAAIC,IAAI,CAACF,mBAAmB,CAACN,IAAI,CAAC,CAACS,OAAO,EAAE;QAChE,MAAMC,GAAG,GAAGF,IAAI,CAACE,GAAG,EAAE;QACtB,OAAOA,GAAG,GAAGH,WAAW,IAAIjB,qCAAqC;MACnE,CAAC,CAAC;MACJ,OAAO,IAAI,CAACG,QAAQ,CAACkB,SAAS,CAAC,IAAI,CAACnB,gBAAgB,CAAC;IACtD,EAAC,OAAOjJ,CAAC,EAAE;MACVtC,MAAM,CAACkJ,IAAI,CAAC5G,CAAC,CAAC;IACf;;EAGH;;;;;;AAMG;EACH,MAAMqK,mBAAmBA,CAAA;;IACvB,IAAI;MACF,IAAI,IAAI,CAACpB,gBAAgB,KAAK,IAAI,EAAE;QAClC,MAAM,IAAI,CAACG,uBAAuB;MACnC;;MAED,IACE,EAAA9C,EAAA,OAAI,CAAC2C,gBAAgB,MAAE,QAAA3C,EAAA,uBAAAA,EAAA,CAAAqD,UAAU,KAAI,IAAI,IACzC,IAAI,CAACV,gBAAgB,CAACU,UAAU,CAACW,MAAM,KAAK,CAAC,EAC7C;QACA,OAAO,EAAE;MACV;MACD,MAAMb,IAAI,GAAGC,gBAAgB,EAAE;;MAE/B,MAAM;QAAEa,gBAAgB;QAAEC;MAAa,CAAE,GAAGC,0BAA0B,CACpE,IAAI,CAACxB,gBAAgB,CAACU,UAAU,CACjC;MACD,MAAMe,YAAY,GAAGC,6BAA6B,CAChDlF,IAAI,CAACC,SAAS,CAAC;QAAEvI,OAAO,EAAE,CAAC;QAAEwM,UAAU,EAAEY;MAAgB,CAAE,CAAC,CAC7D;;MAED,IAAI,CAACtB,gBAAgB,CAACY,qBAAqB,GAAGJ,IAAI;MAClD,IAAIe,aAAa,CAACF,MAAM,GAAG,CAAC,EAAE;;QAE5B,IAAI,CAACrB,gBAAgB,CAACU,UAAU,GAAGa,aAAa;;;;QAIhD,MAAM,IAAI,CAACtB,QAAQ,CAACkB,SAAS,CAAC,IAAI,CAACnB,gBAAgB,CAAC;MACrD,OAAM;QACL,IAAI,CAACA,gBAAgB,CAACU,UAAU,GAAG,EAAE;;QAErC,KAAK,IAAI,CAACT,QAAQ,CAACkB,SAAS,CAAC,IAAI,CAACnB,gBAAgB,CAAC;MACpD;MACD,OAAOyB,YAAY;IACpB,EAAC,OAAO1K,CAAC,EAAE;MACVtC,MAAM,CAACkJ,IAAI,CAAC5G,CAAC,CAAC;MACd,OAAO,EAAE;IACV;;AAEJ;AAED,SAAS0J,gBAAgBA,CAAA;EACvB,MAAMkB,KAAK,GAAG,IAAIX,IAAI,EAAE;;EAExB,OAAOW,KAAK,CAACC,WAAW,EAAE,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;AAC7C;SAEgBL,0BAA0BA,CACxCM,eAAsC,EACtCC,OAAO,GAAGlC,gBAAgB;;;EAO1B,MAAMyB,gBAAgB,GAA4B,EAAE;;EAEpD,IAAIC,aAAa,GAAGO,eAAe,CAACE,KAAK,EAAE;EAC3C,KAAK,MAAMlB,mBAAmB,IAAIgB,eAAe,EAAE;;IAEjD,MAAMG,cAAc,GAAGX,gBAAgB,CAACY,IAAI,CAC1CC,EAAE,IAAIA,EAAE,CAAC5B,KAAK,KAAKO,mBAAmB,CAACP,KAAK,CAC7C;IACD,IAAI,CAAC0B,cAAc,EAAE;;MAEnBX,gBAAgB,CAAC5D,IAAI,CAAC;QACpB6C,KAAK,EAAEO,mBAAmB,CAACP,KAAK;QAChC6B,KAAK,EAAE,CAACtB,mBAAmB,CAACN,IAAI;MACjC,EAAC;MACF,IAAI6B,UAAU,CAACf,gBAAgB,CAAC,GAAGS,OAAO,EAAE;;;QAG1CT,gBAAgB,CAACgB,GAAG,EAAE;QACtB;MACD;IACF,OAAM;MACLL,cAAc,CAACG,KAAK,CAAC1E,IAAI,CAACoD,mBAAmB,CAACN,IAAI,CAAC;;;MAGnD,IAAI6B,UAAU,CAACf,gBAAgB,CAAC,GAAGS,OAAO,EAAE;QAC1CE,cAAc,CAACG,KAAK,CAACE,GAAG,EAAE;QAC1B;MACD;IACF;;;IAGDf,aAAa,GAAGA,aAAa,CAACS,KAAK,CAAC,CAAC,CAAC;EACvC;EACD,OAAO;IACLV,gBAAgB;IAChBC;GACD;AACH;MAEarB,oBAAoB;EAE/B3M,YAAmBsD,GAAgB;IAAhB,IAAG,CAAAA,GAAA,GAAHA,GAAG;IACpB,IAAI,CAAC0L,uBAAuB,GAAG,IAAI,CAACC,4BAA4B,EAAE;;EAEpE,MAAMA,4BAA4BA,CAAA;IAChC,IAAI,CAACC,oBAAoB,EAAE,EAAE;MAC3B,OAAO,KAAK;IACb,OAAM;MACL,OAAOC,yBAAyB,EAAE,CAC/BrC,IAAI,CAAC,MAAM,IAAI,CAAC,CAChBxB,KAAK,CAAC,MAAM,KAAK,CAAC;IACtB;;EAEH;;AAEG;EACH,MAAMuB,IAAIA,CAAA;IACR,MAAMuC,eAAe,GAAG,MAAM,IAAI,CAACJ,uBAAuB;IAC1D,IAAI,CAACI,eAAe,EAAE;MACpB,OAAO;QAAEjC,UAAU,EAAE;MAAE,CAAE;IAC1B,OAAM;MACL,MAAMkC,kBAAkB,GAAG,MAAM5D,2BAA2B,CAAC,IAAI,CAACnI,GAAG,CAAC;MACtE,IAAI+L,kBAAkB,KAAlB,QAAAA,kBAAkB,uBAAlBA,kBAAkB,CAAElC,UAAU,EAAE;QAClC,OAAOkC,kBAAkB;MAC1B,OAAM;QACL,OAAO;UAAElC,UAAU,EAAE;QAAE,CAAE;MAC1B;IACF;;;EAGH,MAAMS,SAASA,CAAC0B,gBAAuC;;IACrD,MAAMF,eAAe,GAAG,MAAM,IAAI,CAACJ,uBAAuB;IAC1D,IAAI,CAACI,eAAe,EAAE;MACpB;IACD,OAAM;MACL,MAAMG,wBAAwB,GAAG,MAAM,IAAI,CAAC1C,IAAI,EAAE;MAClD,OAAOX,0BAA0B,CAAC,IAAI,CAAC5I,GAAG,EAAE;QAC1C+J,qBAAqB,EACnB,CAAAvD,EAAA,GAAAwF,gBAAgB,CAACjC,qBAAqB,MACtC,QAAAvD,EAAA,cAAAA,EAAA,GAAAyF,wBAAwB,CAAClC,qBAAqB;QAChDF,UAAU,EAAEmC,gBAAgB,CAACnC;MAC9B,EAAC;IACH;;;EAGH,MAAMqC,GAAGA,CAACF,gBAAuC;;IAC/C,MAAMF,eAAe,GAAG,MAAM,IAAI,CAACJ,uBAAuB;IAC1D,IAAI,CAACI,eAAe,EAAE;MACpB;IACD,OAAM;MACL,MAAMG,wBAAwB,GAAG,MAAM,IAAI,CAAC1C,IAAI,EAAE;MAClD,OAAOX,0BAA0B,CAAC,IAAI,CAAC5I,GAAG,EAAE;QAC1C+J,qBAAqB,EACnB,CAAAvD,EAAA,GAAAwF,gBAAgB,CAACjC,qBAAqB,MACtC,QAAAvD,EAAA,cAAAA,EAAA,GAAAyF,wBAAwB,CAAClC,qBAAqB;QAChDF,UAAU,EAAE,CACV,GAAGoC,wBAAwB,CAACpC,UAAU,EACtC,GAAGmC,gBAAgB,CAACnC,UAAU;MAEjC,EAAC;IACH;;AAEJ;AAED;;;;AAIG;AACG,SAAU2B,UAAUA,CAACP,eAAwC;;EAEjE,OAAOJ,6BAA6B;;EAElClF,IAAI,CAACC,SAAS,CAAC;IAAEvI,OAAO,EAAE,CAAC;IAAEwM,UAAU,EAAEoB;EAAe,CAAE,CAAC,CAC5D,CAACT,MAAM;AACV;;AChTA;;;;;;;;;;;;;;;AAeG;AASG,SAAU2B,sBAAsBA,CAAC5F,OAAgB;EACrDjG,kBAAkB,CAChB,IAAIoC,SAAS,CACX,iBAAiB,EACjB/F,SAAS,IAAI,IAAIF,yBAAyB,CAACE,SAAS,CAAC,wCAEtD,CACF;EACD2D,kBAAkB,CAChB,IAAIoC,SAAS,CACX,WAAW,EACX/F,SAAS,IAAI,IAAIuM,oBAAoB,CAACvM,SAAS,CAAC,wCAEjD,CACF;;EAGDgH,eAAe,CAAC3F,MAAI,EAAE4F,SAAO,EAAE2C,OAAO,CAAC;;EAEvC5C,eAAe,CAAC3F,MAAI,EAAE4F,SAAO,EAAE,SAAkB,CAAC;;EAElDD,eAAe,CAAC,SAAS,EAAE,EAAE,CAAC;AAChC;;AC9CA;;;;;AAKG;AAyBHwI,sBAAsB,CAAC,EAAiB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}