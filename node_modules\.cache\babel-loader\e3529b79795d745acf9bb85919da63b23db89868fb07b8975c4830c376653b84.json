{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0634\\u0631\\u0648\\u0639\\\\src\\\\pages\\\\LoginPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { toast } from 'react-hot-toast';\nimport { UserIcon, KeyIcon, EyeIcon, EyeSlashIcon, AcademicCapIcon, ShieldCheckIcon } from '@heroicons/react/24/outline';\n\n// Services\nimport { authService } from '../services/authService';\n\n// Components\nimport AIAssistant from '../components/AIAssistant/AIAssistant';\n\n// Types\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoginPage = ({\n  onLogin\n}) => {\n  _s();\n  const [loginType, setLoginType] = useState('student');\n  const [loading, setLoading] = useState(false);\n  const [showPassword, setShowPassword] = useState(false);\n\n  // Admin login form\n  const [adminForm, setAdminForm] = useState({\n    email: '',\n    password: ''\n  });\n\n  // Student login form\n  const [studentForm, setStudentForm] = useState({\n    accessCode: ''\n  });\n  const handleAdminLogin = async e => {\n    e.preventDefault();\n    if (!adminForm.email || !adminForm.password) {\n      toast.error('يرجى ملء جميع الحقول');\n      return;\n    }\n    setLoading(true);\n    try {\n      const user = await authService.loginAdmin(adminForm.email, adminForm.password);\n      toast.success(`مرحباً ${user.name || 'المدير'}`);\n      onLogin(user);\n    } catch (error) {\n      toast.error(error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleStudentLogin = async e => {\n    e.preventDefault();\n    if (!studentForm.accessCode || studentForm.accessCode.length !== 7) {\n      toast.error('يرجى إدخال كود دخول صحيح مكون من 7 أرقام');\n      return;\n    }\n    setLoading(true);\n    try {\n      const user = await authService.loginStudent(studentForm.accessCode);\n      toast.success(`مرحباً ${user.name || 'الطالب'}`);\n      onLogin(user);\n    } catch (error) {\n      toast.error(error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen gradient-primary flex items-center justify-center p-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full max-w-md\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(AcademicCapIcon, {\n            className: \"w-10 h-10 text-primary-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-white mb-2\",\n          children: \"\\u0645\\u0646\\u0635\\u0629 ALaa Abd Hamied\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-blue-100\",\n          children: \"\\u0644\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\\u0629 \\u0648\\u0627\\u0644\\u062A\\u0639\\u0644\\u0645 \\u0627\\u0644\\u062A\\u0641\\u0627\\u0639\\u0644\\u064A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.1\n        },\n        className: \"bg-white rounded-xl shadow-xl p-6 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex rounded-lg bg-gray-100 p-1 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setLoginType('student'),\n            className: `\n                flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all duration-200\n                ${loginType === 'student' ? 'bg-primary-600 text-white shadow-sm' : 'text-gray-600 hover:text-gray-800'}\n              `,\n            children: [/*#__PURE__*/_jsxDEV(UserIcon, {\n              className: \"w-4 h-4 inline-block ml-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this), \"\\u062F\\u062E\\u0648\\u0644 \\u0627\\u0644\\u0637\\u0627\\u0644\\u0628\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setLoginType('admin'),\n            className: `\n                flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all duration-200\n                ${loginType === 'admin' ? 'bg-primary-600 text-white shadow-sm' : 'text-gray-600 hover:text-gray-800'}\n              `,\n            children: [/*#__PURE__*/_jsxDEV(ShieldCheckIcon, {\n              className: \"w-4 h-4 inline-block ml-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), \"\\u062F\\u062E\\u0648\\u0644 \\u0627\\u0644\\u0645\\u062F\\u064A\\u0631\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), loginType === 'student' && /*#__PURE__*/_jsxDEV(motion.form, {\n          initial: {\n            opacity: 0,\n            x: 20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          onSubmit: handleStudentLogin,\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0643\\u0648\\u062F \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644 (7 \\u0623\\u0631\\u0642\\u0627\\u0645)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(KeyIcon, {\n                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: studentForm.accessCode,\n                onChange: e => {\n                  const value = e.target.value.replace(/\\D/g, '').slice(0, 7);\n                  setStudentForm({\n                    accessCode: value\n                  });\n                },\n                placeholder: \"1234567\",\n                className: \"form-input pr-10 text-center text-lg font-mono tracking-wider\",\n                maxLength: 7,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500 mt-1\",\n              children: \"\\u064A\\u0645\\u0643\\u0646\\u0643 \\u0627\\u0644\\u062D\\u0635\\u0648\\u0644 \\u0639\\u0644\\u0649 \\u0643\\u0648\\u062F \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644 \\u0645\\u0646 \\u0627\\u0644\\u0645\\u062F\\u064A\\u0631\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: loading || studentForm.accessCode.length !== 7,\n            className: \"w-full btn-primary py-3 text-lg disabled:opacity-50 disabled:cursor-not-allowed\",\n            children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin ml-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 21\n              }, this), \"\\u062C\\u0627\\u0631\\u064A \\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 19\n            }, this) : 'دخول'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 13\n        }, this), loginType === 'admin' && /*#__PURE__*/_jsxDEV(motion.form, {\n          initial: {\n            opacity: 0,\n            x: -20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          onSubmit: handleAdminLogin,\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(UserIcon, {\n                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                value: adminForm.email,\n                onChange: e => setAdminForm({\n                  ...adminForm,\n                  email: e.target.value\n                }),\n                placeholder: \"<EMAIL>\",\n                className: \"form-input pr-10\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(KeyIcon, {\n                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: showPassword ? 'text' : 'password',\n                value: adminForm.password,\n                onChange: e => setAdminForm({\n                  ...adminForm,\n                  password: e.target.value\n                }),\n                placeholder: \"\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\",\n                className: \"form-input pr-10 pl-10\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => setShowPassword(!showPassword),\n                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                children: showPassword ? /*#__PURE__*/_jsxDEV(EyeSlashIcon, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(EyeIcon, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: loading || !adminForm.email || !adminForm.password,\n            className: \"w-full btn-primary py-3 text-lg disabled:opacity-50 disabled:cursor-not-allowed\",\n            children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin ml-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 21\n              }, this), \"\\u062C\\u0627\\u0631\\u064A \\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 19\n            }, this) : 'دخول'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        transition: {\n          delay: 0.3\n        },\n        className: \"text-center text-blue-100 text-sm\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\xA9 2024 \\u0645\\u0646\\u0635\\u0629 ALaa Abd Hamied. \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u062D\\u0642\\u0648\\u0642 \\u0645\\u062D\\u0641\\u0648\\u0638\\u0629.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AIAssistant, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 5\n  }, this);\n};\n_s(LoginPage, \"uv3FEJ9MtyQ4NLaHCAUBZqOB5Ak=\");\n_c = LoginPage;\nexport default LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");", "map": {"version": 3, "names": ["React", "useState", "motion", "toast", "UserIcon", "KeyIcon", "EyeIcon", "EyeSlashIcon", "AcademicCapIcon", "ShieldCheckIcon", "authService", "AIAssistant", "jsxDEV", "_jsxDEV", "LoginPage", "onLogin", "_s", "loginType", "setLoginType", "loading", "setLoading", "showPassword", "setShowPassword", "adminForm", "setAdminForm", "email", "password", "studentForm", "setStudentForm", "accessCode", "handleAdminLogin", "e", "preventDefault", "error", "user", "loginAdmin", "success", "name", "message", "handleStudentLogin", "length", "loginStudent", "className", "children", "div", "initial", "opacity", "y", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "transition", "delay", "type", "onClick", "form", "x", "onSubmit", "value", "onChange", "target", "replace", "slice", "placeholder", "max<PERSON><PERSON><PERSON>", "required", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/pages/LoginPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { toast } from 'react-hot-toast';\nimport { \n  UserIcon, \n  KeyIcon, \n  EyeIcon, \n  EyeSlashIcon,\n  AcademicCapIcon,\n  ShieldCheckIcon\n} from '@heroicons/react/24/outline';\n\n// Services\nimport { authService } from '../services/authService';\n\n// Components\nimport AIAssistant from '../components/AIAssistant/AIAssistant';\n\n// Types\nimport { User } from '../types';\n\ninterface LoginPageProps {\n  onLogin: (user: User) => void;\n}\n\nconst LoginPage: React.FC<LoginPageProps> = ({ onLogin }) => {\n  const [loginType, setLoginType] = useState<'admin' | 'student'>('student');\n  const [loading, setLoading] = useState(false);\n  const [showPassword, setShowPassword] = useState(false);\n  \n  // Admin login form\n  const [adminForm, setAdminForm] = useState({\n    email: '',\n    password: ''\n  });\n  \n  // Student login form\n  const [studentForm, setStudentForm] = useState({\n    accessCode: ''\n  });\n\n  const handleAdminLogin = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!adminForm.email || !adminForm.password) {\n      toast.error('يرجى ملء جميع الحقول');\n      return;\n    }\n\n    setLoading(true);\n    try {\n      const user = await authService.loginAdmin(adminForm.email, adminForm.password);\n      toast.success(`مرحباً ${user.name || 'المدير'}`);\n      onLogin(user);\n    } catch (error: any) {\n      toast.error(error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleStudentLogin = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!studentForm.accessCode || studentForm.accessCode.length !== 7) {\n      toast.error('يرجى إدخال كود دخول صحيح مكون من 7 أرقام');\n      return;\n    }\n\n    setLoading(true);\n    try {\n      const user = await authService.loginStudent(studentForm.accessCode);\n      toast.success(`مرحباً ${user.name || 'الطالب'}`);\n      onLogin(user);\n    } catch (error: any) {\n      toast.error(error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen gradient-primary flex items-center justify-center p-4\">\n      <div className=\"w-full max-w-md\">\n        {/* Logo and Title */}\n        <motion.div \n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"text-center mb-8\"\n        >\n          <div className=\"bg-white rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center shadow-lg\">\n            <AcademicCapIcon className=\"w-10 h-10 text-primary-600\" />\n          </div>\n          <h1 className=\"text-3xl font-bold text-white mb-2\">\n            منصة ALaa Abd Hamied\n          </h1>\n          <p className=\"text-blue-100\">\n            للكورسات الإلكترونية والتعلم التفاعلي\n          </p>\n        </motion.div>\n\n        {/* Login Type Selector */}\n        <motion.div \n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.1 }}\n          className=\"bg-white rounded-xl shadow-xl p-6 mb-6\"\n        >\n          <div className=\"flex rounded-lg bg-gray-100 p-1 mb-6\">\n            <button\n              type=\"button\"\n              onClick={() => setLoginType('student')}\n              className={`\n                flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all duration-200\n                ${loginType === 'student' \n                  ? 'bg-primary-600 text-white shadow-sm' \n                  : 'text-gray-600 hover:text-gray-800'\n                }\n              `}\n            >\n              <UserIcon className=\"w-4 h-4 inline-block ml-2\" />\n              دخول الطالب\n            </button>\n            <button\n              type=\"button\"\n              onClick={() => setLoginType('admin')}\n              className={`\n                flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all duration-200\n                ${loginType === 'admin' \n                  ? 'bg-primary-600 text-white shadow-sm' \n                  : 'text-gray-600 hover:text-gray-800'\n                }\n              `}\n            >\n              <ShieldCheckIcon className=\"w-4 h-4 inline-block ml-2\" />\n              دخول المدير\n            </button>\n          </div>\n\n          {/* Student Login Form */}\n          {loginType === 'student' && (\n            <motion.form \n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              onSubmit={handleStudentLogin}\n              className=\"space-y-4\"\n            >\n              <div>\n                <label className=\"form-label\">\n                  كود الدخول (7 أرقام)\n                </label>\n                <div className=\"relative\">\n                  <KeyIcon className=\"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" />\n                  <input\n                    type=\"text\"\n                    value={studentForm.accessCode}\n                    onChange={(e) => {\n                      const value = e.target.value.replace(/\\D/g, '').slice(0, 7);\n                      setStudentForm({ accessCode: value });\n                    }}\n                    placeholder=\"1234567\"\n                    className=\"form-input pr-10 text-center text-lg font-mono tracking-wider\"\n                    maxLength={7}\n                    required\n                  />\n                </div>\n                <p className=\"text-xs text-gray-500 mt-1\">\n                  يمكنك الحصول على كود الدخول من المدير\n                </p>\n              </div>\n\n              <button\n                type=\"submit\"\n                disabled={loading || studentForm.accessCode.length !== 7}\n                className=\"w-full btn-primary py-3 text-lg disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {loading ? (\n                  <div className=\"flex items-center justify-center\">\n                    <div className=\"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin ml-2\" />\n                    جاري تسجيل الدخول...\n                  </div>\n                ) : (\n                  'دخول'\n                )}\n              </button>\n            </motion.form>\n          )}\n\n          {/* Admin Login Form */}\n          {loginType === 'admin' && (\n            <motion.form \n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              onSubmit={handleAdminLogin}\n              className=\"space-y-4\"\n            >\n              <div>\n                <label className=\"form-label\">\n                  البريد الإلكتروني\n                </label>\n                <div className=\"relative\">\n                  <UserIcon className=\"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" />\n                  <input\n                    type=\"email\"\n                    value={adminForm.email}\n                    onChange={(e) => setAdminForm({ ...adminForm, email: e.target.value })}\n                    placeholder=\"<EMAIL>\"\n                    className=\"form-input pr-10\"\n                    required\n                  />\n                </div>\n              </div>\n\n              <div>\n                <label className=\"form-label\">\n                  كلمة المرور\n                </label>\n                <div className=\"relative\">\n                  <KeyIcon className=\"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" />\n                  <input\n                    type={showPassword ? 'text' : 'password'}\n                    value={adminForm.password}\n                    onChange={(e) => setAdminForm({ ...adminForm, password: e.target.value })}\n                    placeholder=\"••••••••\"\n                    className=\"form-input pr-10 pl-10\"\n                    required\n                  />\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowPassword(!showPassword)}\n                    className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\"\n                  >\n                    {showPassword ? (\n                      <EyeSlashIcon className=\"w-5 h-5\" />\n                    ) : (\n                      <EyeIcon className=\"w-5 h-5\" />\n                    )}\n                  </button>\n                </div>\n              </div>\n\n              <button\n                type=\"submit\"\n                disabled={loading || !adminForm.email || !adminForm.password}\n                className=\"w-full btn-primary py-3 text-lg disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {loading ? (\n                  <div className=\"flex items-center justify-center\">\n                    <div className=\"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin ml-2\" />\n                    جاري تسجيل الدخول...\n                  </div>\n                ) : (\n                  'دخول'\n                )}\n              </button>\n            </motion.form>\n          )}\n        </motion.div>\n\n        {/* Footer */}\n        <motion.div \n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.3 }}\n          className=\"text-center text-blue-100 text-sm\"\n        >\n          <p>© 2024 منصة ALaa Abd Hamied. جميع الحقوق محفوظة.</p>\n        </motion.div>\n      </div>\n\n      {/* AI Assistant */}\n      <AIAssistant />\n    </div>\n  );\n};\n\nexport default LoginPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,KAAK,QAAQ,iBAAiB;AACvC,SACEC,QAAQ,EACRC,OAAO,EACPC,OAAO,EACPC,YAAY,EACZC,eAAe,EACfC,eAAe,QACV,6BAA6B;;AAEpC;AACA,SAASC,WAAW,QAAQ,yBAAyB;;AAErD;AACA,OAAOC,WAAW,MAAM,uCAAuC;;AAE/D;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAOA,MAAMC,SAAmC,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC3D,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAsB,SAAS,CAAC;EAC1E,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM,CAACsB,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAC;IACzCwB,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC;IAC7C4B,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,MAAMC,gBAAgB,GAAG,MAAOC,CAAkB,IAAK;IACrDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACT,SAAS,CAACE,KAAK,IAAI,CAACF,SAAS,CAACG,QAAQ,EAAE;MAC3CvB,KAAK,CAAC8B,KAAK,CAAC,sBAAsB,CAAC;MACnC;IACF;IAEAb,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMc,IAAI,GAAG,MAAMxB,WAAW,CAACyB,UAAU,CAACZ,SAAS,CAACE,KAAK,EAAEF,SAAS,CAACG,QAAQ,CAAC;MAC9EvB,KAAK,CAACiC,OAAO,CAAC,UAAUF,IAAI,CAACG,IAAI,IAAI,QAAQ,EAAE,CAAC;MAChDtB,OAAO,CAACmB,IAAI,CAAC;IACf,CAAC,CAAC,OAAOD,KAAU,EAAE;MACnB9B,KAAK,CAAC8B,KAAK,CAACA,KAAK,CAACK,OAAO,CAAC;IAC5B,CAAC,SAAS;MACRlB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmB,kBAAkB,GAAG,MAAOR,CAAkB,IAAK;IACvDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACL,WAAW,CAACE,UAAU,IAAIF,WAAW,CAACE,UAAU,CAACW,MAAM,KAAK,CAAC,EAAE;MAClErC,KAAK,CAAC8B,KAAK,CAAC,0CAA0C,CAAC;MACvD;IACF;IAEAb,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMc,IAAI,GAAG,MAAMxB,WAAW,CAAC+B,YAAY,CAACd,WAAW,CAACE,UAAU,CAAC;MACnE1B,KAAK,CAACiC,OAAO,CAAC,UAAUF,IAAI,CAACG,IAAI,IAAI,QAAQ,EAAE,CAAC;MAChDtB,OAAO,CAACmB,IAAI,CAAC;IACf,CAAC,CAAC,OAAOD,KAAU,EAAE;MACnB9B,KAAK,CAAC8B,KAAK,CAACA,KAAK,CAACK,OAAO,CAAC;IAC5B,CAAC,SAAS;MACRlB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEP,OAAA;IAAK6B,SAAS,EAAC,oEAAoE;IAAAC,QAAA,gBACjF9B,OAAA;MAAK6B,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAE9B9B,OAAA,CAACX,MAAM,CAAC0C,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BL,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAE5B9B,OAAA;UAAK6B,SAAS,EAAC,yFAAyF;UAAAC,QAAA,eACtG9B,OAAA,CAACL,eAAe;YAACkC,SAAS,EAAC;UAA4B;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,eACNvC,OAAA;UAAI6B,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAEnD;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLvC,OAAA;UAAG6B,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE7B;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGbvC,OAAA,CAACX,MAAM,CAAC0C,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BM,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BZ,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBAElD9B,OAAA;UAAK6B,SAAS,EAAC,sCAAsC;UAAAC,QAAA,gBACnD9B,OAAA;YACE0C,IAAI,EAAC,QAAQ;YACbC,OAAO,EAAEA,CAAA,KAAMtC,YAAY,CAAC,SAAS,CAAE;YACvCwB,SAAS,EAAE;AACzB;AACA,kBAAkBzB,SAAS,KAAK,SAAS,GACrB,qCAAqC,GACrC,mCAAmC;AACvD,eACgB;YAAA0B,QAAA,gBAEF9B,OAAA,CAACT,QAAQ;cAACsC,SAAS,EAAC;YAA2B;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iEAEpD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTvC,OAAA;YACE0C,IAAI,EAAC,QAAQ;YACbC,OAAO,EAAEA,CAAA,KAAMtC,YAAY,CAAC,OAAO,CAAE;YACrCwB,SAAS,EAAE;AACzB;AACA,kBAAkBzB,SAAS,KAAK,OAAO,GACnB,qCAAqC,GACrC,mCAAmC;AACvD,eACgB;YAAA0B,QAAA,gBAEF9B,OAAA,CAACJ,eAAe;cAACiC,SAAS,EAAC;YAA2B;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iEAE3D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAGLnC,SAAS,KAAK,SAAS,iBACtBJ,OAAA,CAACX,MAAM,CAACuD,IAAI;UACVZ,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEY,CAAC,EAAE;UAAG,CAAE;UAC/BV,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEY,CAAC,EAAE;UAAE,CAAE;UAC9BC,QAAQ,EAAEpB,kBAAmB;UAC7BG,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAErB9B,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAO6B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAE9B;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRvC,OAAA;cAAK6B,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB9B,OAAA,CAACR,OAAO;gBAACqC,SAAS,EAAC;cAA2E;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjGvC,OAAA;gBACE0C,IAAI,EAAC,MAAM;gBACXK,KAAK,EAAEjC,WAAW,CAACE,UAAW;gBAC9BgC,QAAQ,EAAG9B,CAAC,IAAK;kBACf,MAAM6B,KAAK,GAAG7B,CAAC,CAAC+B,MAAM,CAACF,KAAK,CAACG,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;kBAC3DpC,cAAc,CAAC;oBAAEC,UAAU,EAAE+B;kBAAM,CAAC,CAAC;gBACvC,CAAE;gBACFK,WAAW,EAAC,SAAS;gBACrBvB,SAAS,EAAC,+DAA+D;gBACzEwB,SAAS,EAAE,CAAE;gBACbC,QAAQ;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNvC,OAAA;cAAG6B,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENvC,OAAA;YACE0C,IAAI,EAAC,QAAQ;YACba,QAAQ,EAAEjD,OAAO,IAAIQ,WAAW,CAACE,UAAU,CAACW,MAAM,KAAK,CAAE;YACzDE,SAAS,EAAC,iFAAiF;YAAAC,QAAA,EAE1FxB,OAAO,gBACNN,OAAA;cAAK6B,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/C9B,OAAA;gBAAK6B,SAAS,EAAC;cAAmF;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mGAEvG;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GAEN;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACd,EAGAnC,SAAS,KAAK,OAAO,iBACpBJ,OAAA,CAACX,MAAM,CAACuD,IAAI;UACVZ,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEY,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCV,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEY,CAAC,EAAE;UAAE,CAAE;UAC9BC,QAAQ,EAAE7B,gBAAiB;UAC3BY,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAErB9B,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAO6B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAE9B;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRvC,OAAA;cAAK6B,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB9B,OAAA,CAACT,QAAQ;gBAACsC,SAAS,EAAC;cAA2E;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClGvC,OAAA;gBACE0C,IAAI,EAAC,OAAO;gBACZK,KAAK,EAAErC,SAAS,CAACE,KAAM;gBACvBoC,QAAQ,EAAG9B,CAAC,IAAKP,YAAY,CAAC;kBAAE,GAAGD,SAAS;kBAAEE,KAAK,EAAEM,CAAC,CAAC+B,MAAM,CAACF;gBAAM,CAAC,CAAE;gBACvEK,WAAW,EAAC,mBAAmB;gBAC/BvB,SAAS,EAAC,kBAAkB;gBAC5ByB,QAAQ;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENvC,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAO6B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAE9B;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRvC,OAAA;cAAK6B,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB9B,OAAA,CAACR,OAAO;gBAACqC,SAAS,EAAC;cAA2E;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjGvC,OAAA;gBACE0C,IAAI,EAAElC,YAAY,GAAG,MAAM,GAAG,UAAW;gBACzCuC,KAAK,EAAErC,SAAS,CAACG,QAAS;gBAC1BmC,QAAQ,EAAG9B,CAAC,IAAKP,YAAY,CAAC;kBAAE,GAAGD,SAAS;kBAAEG,QAAQ,EAAEK,CAAC,CAAC+B,MAAM,CAACF;gBAAM,CAAC,CAAE;gBAC1EK,WAAW,EAAC,kDAAU;gBACtBvB,SAAS,EAAC,wBAAwB;gBAClCyB,QAAQ;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACFvC,OAAA;gBACE0C,IAAI,EAAC,QAAQ;gBACbC,OAAO,EAAEA,CAAA,KAAMlC,eAAe,CAAC,CAACD,YAAY,CAAE;gBAC9CqB,SAAS,EAAC,sFAAsF;gBAAAC,QAAA,EAE/FtB,YAAY,gBACXR,OAAA,CAACN,YAAY;kBAACmC,SAAS,EAAC;gBAAS;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAEpCvC,OAAA,CAACP,OAAO;kBAACoC,SAAS,EAAC;gBAAS;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAC/B;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENvC,OAAA;YACE0C,IAAI,EAAC,QAAQ;YACba,QAAQ,EAAEjD,OAAO,IAAI,CAACI,SAAS,CAACE,KAAK,IAAI,CAACF,SAAS,CAACG,QAAS;YAC7DgB,SAAS,EAAC,iFAAiF;YAAAC,QAAA,EAE1FxB,OAAO,gBACNN,OAAA;cAAK6B,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/C9B,OAAA;gBAAK6B,SAAS,EAAC;cAAmF;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mGAEvG;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GAEN;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACd;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eAGbvC,OAAA,CAACX,MAAM,CAAC0C,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBE,OAAO,EAAE;UAAEF,OAAO,EAAE;QAAE,CAAE;QACxBO,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BZ,SAAS,EAAC,mCAAmC;QAAAC,QAAA,eAE7C9B,OAAA;UAAA8B,QAAA,EAAG;QAAgD;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGNvC,OAAA,CAACF,WAAW;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV,CAAC;AAACpC,EAAA,CAvPIF,SAAmC;AAAAuD,EAAA,GAAnCvD,SAAmC;AAyPzC,eAAeA,SAAS;AAAC,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}