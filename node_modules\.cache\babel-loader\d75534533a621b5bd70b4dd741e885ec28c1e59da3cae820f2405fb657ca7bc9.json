{"ast": null, "code": "import React,{useState}from'react';import{motion}from'framer-motion';import{UserIcon,CameraIcon,CheckCircleIcon,AcademicCapIcon,TrophyIcon}from'@heroicons/react/24/outline';// Types\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const StudentProfile=_ref=>{let{user,onBack}=_ref;const[activeTab,setActiveTab]=useState('profile');const[isEditing,setIsEditing]=useState(false);// Mock student data\nconst[studentData,setStudentData]=useState({name:'أحمد محمد',email:'<EMAIL>',accessCode:'STU001',joinDate:new Date('2024-01-15'),avatar:null,bio:'طالب مهتم بتعلم البرمجة وتطوير المواقع',phone:'+966501234567',city:'الرياض'});const[passwordData,setPasswordData]=useState({currentPassword:'',newPassword:'',confirmPassword:''});// Mock stats\nconst stats={enrolledCourses:5,completedCourses:3,certificates:2,totalWatchTime:45,// hours\naverageScore:87};const handleSaveProfile=()=>{// TODO: Implement save profile functionality\nconsole.log('Save profile:',studentData);setIsEditing(false);};const handleChangePassword=()=>{// TODO: Implement change password functionality\nconsole.log('Change password');setPasswordData({currentPassword:'',newPassword:'',confirmPassword:''});};const handleAvatarChange=event=>{// TODO: Implement avatar upload\nconsole.log('Avatar change:',event.target.files);};return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4 space-x-reverse\",children:[onBack&&/*#__PURE__*/_jsx(\"button\",{onClick:onBack,className:\"p-2 text-gray-600 hover:text-gray-900 transition-colors\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-6 h-6\",fill:\"none\",stroke:\"currentColor\",viewBox:\"0 0 24 24\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M15 19l-7-7 7-7\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:\"\\u0627\\u0644\\u0645\\u0644\\u0641 \\u0627\\u0644\\u0634\\u062E\\u0635\\u064A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A\\u0643 \\u0627\\u0644\\u0634\\u062E\\u0635\\u064A\\u0629 \\u0648\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A\\u0643\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 lg:grid-cols-3 gap-6\",children:[/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:\"lg:col-span-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-sm p-6 text-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative inline-block mb-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto\",children:studentData.avatar?/*#__PURE__*/_jsx(\"img\",{src:studentData.avatar,alt:\"Profile\",className:\"w-24 h-24 rounded-full object-cover\"}):/*#__PURE__*/_jsx(UserIcon,{className:\"w-12 h-12 text-blue-600\"})}),/*#__PURE__*/_jsxs(\"label\",{className:\"absolute bottom-0 right-0 bg-blue-600 text-white p-2 rounded-full cursor-pointer hover:bg-blue-700 transition-colors\",children:[/*#__PURE__*/_jsx(CameraIcon,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsx(\"input\",{type:\"file\",accept:\"image/*\",onChange:handleAvatarChange,className:\"hidden\"})]})]}),/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-bold text-gray-900 mb-1\",children:studentData.name}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mb-2\",children:studentData.email}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-500 mb-4\",children:[\"\\u0631\\u0645\\u0632 \\u0627\\u0644\\u0648\\u0635\\u0648\\u0644: \",/*#__PURE__*/_jsx(\"span\",{className:\"font-mono bg-gray-100 px-2 py-1 rounded\",children:studentData.accessCode})]}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-500\",children:[\"\\u0639\\u0636\\u0648 \\u0645\\u0646\\u0630 \",studentData.joinDate.toLocaleDateString('ar-SA')]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-sm p-6 mt-6\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900 mb-4\",children:\"\\u0625\\u062D\\u0635\\u0627\\u0626\\u064A\\u0627\\u062A\\u064A\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 space-x-reverse\",children:[/*#__PURE__*/_jsx(AcademicCapIcon,{className:\"w-5 h-5 text-blue-600\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"\\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A \\u0627\\u0644\\u0645\\u0633\\u062C\\u0644\\u0629\"})]}),/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold text-gray-900\",children:stats.enrolledCourses})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 space-x-reverse\",children:[/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"w-5 h-5 text-green-600\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"\\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A \\u0627\\u0644\\u0645\\u0643\\u062A\\u0645\\u0644\\u0629\"})]}),/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold text-gray-900\",children:stats.completedCourses})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 space-x-reverse\",children:[/*#__PURE__*/_jsx(TrophyIcon,{className:\"w-5 h-5 text-yellow-600\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"\\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0627\\u062A\"})]}),/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold text-gray-900\",children:stats.certificates})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"\\u0633\\u0627\\u0639\\u0627\\u062A \\u0627\\u0644\\u0645\\u0634\\u0627\\u0647\\u062F\\u0629\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"font-semibold text-gray-900\",children:[stats.totalWatchTime,\"h\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"\\u0645\\u062A\\u0648\\u0633\\u0637 \\u0627\\u0644\\u0646\\u062A\\u0627\\u0626\\u062C\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"font-semibold text-green-600\",children:[stats.averageScore,\"%\"]})]})]})]})]}),/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0.1},className:\"lg:col-span-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow-sm p-6 mb-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-4 space-x-reverse border-b border-gray-200\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setActiveTab('profile'),className:`pb-2 px-1 border-b-2 transition-colors ${activeTab==='profile'?'border-blue-600 text-blue-600':'border-transparent text-gray-600 hover:text-gray-900'}`,children:\"\\u0627\\u0644\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0634\\u062E\\u0635\\u064A\\u0629\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setActiveTab('security'),className:`pb-2 px-1 border-b-2 transition-colors ${activeTab==='security'?'border-blue-600 text-blue-600':'border-transparent text-gray-600 hover:text-gray-900'}`,children:\"\\u0627\\u0644\\u0623\\u0645\\u0627\\u0646\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-sm p-6\",children:[activeTab==='profile'&&/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900\",children:\"\\u0627\\u0644\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0634\\u062E\\u0635\\u064A\\u0629\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setIsEditing(!isEditing),className:\"text-blue-600 hover:text-blue-700 transition-colors\",children:isEditing?'إلغاء':'تعديل'})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0627\\u0644\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0643\\u0627\\u0645\\u0644\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:studentData.name,onChange:e=>setStudentData(prev=>({...prev,name:e.target.value})),disabled:!isEditing,className:\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",value:studentData.email,onChange:e=>setStudentData(prev=>({...prev,email:e.target.value})),disabled:!isEditing,className:\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0647\\u0627\\u062A\\u0641\"}),/*#__PURE__*/_jsx(\"input\",{type:\"tel\",value:studentData.phone,onChange:e=>setStudentData(prev=>({...prev,phone:e.target.value})),disabled:!isEditing,className:\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0627\\u0644\\u0645\\u062F\\u064A\\u0646\\u0629\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:studentData.city,onChange:e=>setStudentData(prev=>({...prev,city:e.target.value})),disabled:!isEditing,className:\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0646\\u0628\\u0630\\u0629 \\u0634\\u062E\\u0635\\u064A\\u0629\"}),/*#__PURE__*/_jsx(\"textarea\",{value:studentData.bio,onChange:e=>setStudentData(prev=>({...prev,bio:e.target.value})),disabled:!isEditing,rows:3,className:\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50\"})]}),isEditing&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-end space-x-3 space-x-reverse\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setIsEditing(false),className:\"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors\",children:\"\\u0625\\u0644\\u063A\\u0627\\u0621\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleSaveProfile,className:\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",children:\"\\u062D\\u0641\\u0638 \\u0627\\u0644\\u062A\\u063A\\u064A\\u064A\\u0631\\u0627\\u062A\"})]})]}),activeTab==='security'&&/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900\",children:\"\\u062A\\u063A\\u064A\\u064A\\u0631 \\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631 \\u0627\\u0644\\u062D\\u0627\\u0644\\u064A\\u0629\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",value:passwordData.currentPassword,onChange:e=>setPasswordData(prev=>({...prev,currentPassword:e.target.value})),className:\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631 \\u0627\\u0644\\u062C\\u062F\\u064A\\u062F\\u0629\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",value:passwordData.newPassword,onChange:e=>setPasswordData(prev=>({...prev,newPassword:e.target.value})),className:\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u062A\\u0623\\u0643\\u064A\\u062F \\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631 \\u0627\\u0644\\u062C\\u062F\\u064A\\u062F\\u0629\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",value:passwordData.confirmPassword,onChange:e=>setPasswordData(prev=>({...prev,confirmPassword:e.target.value})),className:\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"})]}),/*#__PURE__*/_jsx(\"button\",{onClick:handleChangePassword,className:\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",children:\"\\u062A\\u063A\\u064A\\u064A\\u0631 \\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631\"})]})]})]})]})]})]});};export default StudentProfile;", "map": {"version": 3, "names": ["React", "useState", "motion", "UserIcon", "CameraIcon", "CheckCircleIcon", "AcademicCapIcon", "TrophyIcon", "jsx", "_jsx", "jsxs", "_jsxs", "StudentProfile", "_ref", "user", "onBack", "activeTab", "setActiveTab", "isEditing", "setIsEditing", "studentData", "setStudentData", "name", "email", "accessCode", "joinDate", "Date", "avatar", "bio", "phone", "city", "passwordData", "setPasswordData", "currentPassword", "newPassword", "confirmPassword", "stats", "enrolledCourses", "completedCourses", "certificates", "totalWatchTime", "averageScore", "handleSaveProfile", "console", "log", "handleChangePassword", "handleAvatarChange", "event", "target", "files", "className", "children", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "div", "initial", "opacity", "y", "animate", "src", "alt", "type", "accept", "onChange", "toLocaleDateString", "transition", "delay", "value", "e", "prev", "disabled", "rows"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Student/StudentProfile.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  UserIcon,\n  EnvelopeIcon,\n  KeyIcon,\n  CameraIcon,\n  CheckCircleIcon,\n  AcademicCapIcon,\n  TrophyIcon\n} from '@heroicons/react/24/outline';\n\n// Types\nimport { Student } from '../../types';\n\ninterface StudentProfileProps {\n  user?: Student;\n  onBack?: () => void;\n}\n\nconst StudentProfile: React.FC<StudentProfileProps> = ({ user, onBack }) => {\n  const [activeTab, setActiveTab] = useState('profile');\n  const [isEditing, setIsEditing] = useState(false);\n  \n  // Mock student data\n  const [studentData, setStudentData] = useState({\n    name: 'أحمد محمد',\n    email: '<EMAIL>',\n    accessCode: 'STU001',\n    joinDate: new Date('2024-01-15'),\n    avatar: null,\n    bio: 'طالب مهتم بتعلم البرمجة وتطوير المواقع',\n    phone: '+966501234567',\n    city: 'الرياض'\n  });\n\n  const [passwordData, setPasswordData] = useState({\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: ''\n  });\n\n  // Mock stats\n  const stats = {\n    enrolledCourses: 5,\n    completedCourses: 3,\n    certificates: 2,\n    totalWatchTime: 45, // hours\n    averageScore: 87\n  };\n\n  const handleSaveProfile = () => {\n    // TODO: Implement save profile functionality\n    console.log('Save profile:', studentData);\n    setIsEditing(false);\n  };\n\n  const handleChangePassword = () => {\n    // TODO: Implement change password functionality\n    console.log('Change password');\n    setPasswordData({\n      currentPassword: '',\n      newPassword: '',\n      confirmPassword: ''\n    });\n  };\n\n  const handleAvatarChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n    // TODO: Implement avatar upload\n    console.log('Avatar change:', event.target.files);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center space-x-4 space-x-reverse\">\n        {onBack && (\n          <button\n            onClick={onBack}\n            className=\"p-2 text-gray-600 hover:text-gray-900 transition-colors\"\n          >\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n            </svg>\n          </button>\n        )}\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">الملف الشخصي</h1>\n          <p className=\"text-gray-600\">إدارة معلوماتك الشخصية وإعداداتك</p>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Profile Card */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"lg:col-span-1\"\n        >\n          <div className=\"bg-white rounded-lg shadow-sm p-6 text-center\">\n            <div className=\"relative inline-block mb-4\">\n              <div className=\"w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto\">\n                {studentData.avatar ? (\n                  <img\n                    src={studentData.avatar}\n                    alt=\"Profile\"\n                    className=\"w-24 h-24 rounded-full object-cover\"\n                  />\n                ) : (\n                  <UserIcon className=\"w-12 h-12 text-blue-600\" />\n                )}\n              </div>\n              <label className=\"absolute bottom-0 right-0 bg-blue-600 text-white p-2 rounded-full cursor-pointer hover:bg-blue-700 transition-colors\">\n                <CameraIcon className=\"w-4 h-4\" />\n                <input\n                  type=\"file\"\n                  accept=\"image/*\"\n                  onChange={handleAvatarChange}\n                  className=\"hidden\"\n                />\n              </label>\n            </div>\n            \n            <h2 className=\"text-xl font-bold text-gray-900 mb-1\">{studentData.name}</h2>\n            <p className=\"text-gray-600 mb-2\">{studentData.email}</p>\n            <p className=\"text-sm text-gray-500 mb-4\">\n              رمز الوصول: <span className=\"font-mono bg-gray-100 px-2 py-1 rounded\">{studentData.accessCode}</span>\n            </p>\n            <p className=\"text-sm text-gray-500\">\n              عضو منذ {studentData.joinDate.toLocaleDateString('ar-SA')}\n            </p>\n          </div>\n\n          {/* Stats */}\n          <div className=\"bg-white rounded-lg shadow-sm p-6 mt-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">إحصائياتي</h3>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <AcademicCapIcon className=\"w-5 h-5 text-blue-600\" />\n                  <span className=\"text-sm text-gray-600\">الكورسات المسجلة</span>\n                </div>\n                <span className=\"font-semibold text-gray-900\">{stats.enrolledCourses}</span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <CheckCircleIcon className=\"w-5 h-5 text-green-600\" />\n                  <span className=\"text-sm text-gray-600\">الكورسات المكتملة</span>\n                </div>\n                <span className=\"font-semibold text-gray-900\">{stats.completedCourses}</span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <TrophyIcon className=\"w-5 h-5 text-yellow-600\" />\n                  <span className=\"text-sm text-gray-600\">الشهادات</span>\n                </div>\n                <span className=\"font-semibold text-gray-900\">{stats.certificates}</span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">ساعات المشاهدة</span>\n                <span className=\"font-semibold text-gray-900\">{stats.totalWatchTime}h</span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">متوسط النتائج</span>\n                <span className=\"font-semibold text-green-600\">{stats.averageScore}%</span>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Main Content */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.1 }}\n          className=\"lg:col-span-2\"\n        >\n          {/* Tabs */}\n          <div className=\"bg-white rounded-lg shadow-sm p-6 mb-6\">\n            <div className=\"flex space-x-4 space-x-reverse border-b border-gray-200\">\n              <button\n                onClick={() => setActiveTab('profile')}\n                className={`pb-2 px-1 border-b-2 transition-colors ${\n                  activeTab === 'profile'\n                    ? 'border-blue-600 text-blue-600'\n                    : 'border-transparent text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                المعلومات الشخصية\n              </button>\n              <button\n                onClick={() => setActiveTab('security')}\n                className={`pb-2 px-1 border-b-2 transition-colors ${\n                  activeTab === 'security'\n                    ? 'border-blue-600 text-blue-600'\n                    : 'border-transparent text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                الأمان\n              </button>\n            </div>\n          </div>\n\n          {/* Tab Content */}\n          <div className=\"bg-white rounded-lg shadow-sm p-6\">\n            {activeTab === 'profile' && (\n              <div className=\"space-y-6\">\n                <div className=\"flex items-center justify-between\">\n                  <h3 className=\"text-lg font-semibold text-gray-900\">المعلومات الشخصية</h3>\n                  <button\n                    onClick={() => setIsEditing(!isEditing)}\n                    className=\"text-blue-600 hover:text-blue-700 transition-colors\"\n                  >\n                    {isEditing ? 'إلغاء' : 'تعديل'}\n                  </button>\n                </div>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      الاسم الكامل\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={studentData.name}\n                      onChange={(e) => setStudentData(prev => ({ ...prev, name: e.target.value }))}\n                      disabled={!isEditing}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      البريد الإلكتروني\n                    </label>\n                    <input\n                      type=\"email\"\n                      value={studentData.email}\n                      onChange={(e) => setStudentData(prev => ({ ...prev, email: e.target.value }))}\n                      disabled={!isEditing}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      رقم الهاتف\n                    </label>\n                    <input\n                      type=\"tel\"\n                      value={studentData.phone}\n                      onChange={(e) => setStudentData(prev => ({ ...prev, phone: e.target.value }))}\n                      disabled={!isEditing}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      المدينة\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={studentData.city}\n                      onChange={(e) => setStudentData(prev => ({ ...prev, city: e.target.value }))}\n                      disabled={!isEditing}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50\"\n                    />\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    نبذة شخصية\n                  </label>\n                  <textarea\n                    value={studentData.bio}\n                    onChange={(e) => setStudentData(prev => ({ ...prev, bio: e.target.value }))}\n                    disabled={!isEditing}\n                    rows={3}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50\"\n                  />\n                </div>\n\n                {isEditing && (\n                  <div className=\"flex justify-end space-x-3 space-x-reverse\">\n                    <button\n                      onClick={() => setIsEditing(false)}\n                      className=\"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors\"\n                    >\n                      إلغاء\n                    </button>\n                    <button\n                      onClick={handleSaveProfile}\n                      className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n                    >\n                      حفظ التغييرات\n                    </button>\n                  </div>\n                )}\n              </div>\n            )}\n\n            {activeTab === 'security' && (\n              <div className=\"space-y-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900\">تغيير كلمة المرور</h3>\n\n                <div className=\"space-y-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      كلمة المرور الحالية\n                    </label>\n                    <input\n                      type=\"password\"\n                      value={passwordData.currentPassword}\n                      onChange={(e) => setPasswordData(prev => ({ ...prev, currentPassword: e.target.value }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      كلمة المرور الجديدة\n                    </label>\n                    <input\n                      type=\"password\"\n                      value={passwordData.newPassword}\n                      onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      تأكيد كلمة المرور الجديدة\n                    </label>\n                    <input\n                      type=\"password\"\n                      value={passwordData.confirmPassword}\n                      onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n\n                  <button\n                    onClick={handleChangePassword}\n                    className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n                  >\n                    تغيير كلمة المرور\n                  </button>\n                </div>\n              </div>\n            )}\n          </div>\n        </motion.div>\n      </div>\n    </div>\n  );\n};\n\nexport default StudentProfile;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,MAAM,KAAQ,eAAe,CACtC,OACEC,QAAQ,CAGRC,UAAU,CACVC,eAAe,CACfC,eAAe,CACfC,UAAU,KACL,6BAA6B,CAEpC;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAQA,KAAM,CAAAC,cAA6C,CAAGC,IAAA,EAAsB,IAArB,CAAEC,IAAI,CAAEC,MAAO,CAAC,CAAAF,IAAA,CACrE,KAAM,CAACG,SAAS,CAAEC,YAAY,CAAC,CAAGhB,QAAQ,CAAC,SAAS,CAAC,CACrD,KAAM,CAACiB,SAAS,CAAEC,YAAY,CAAC,CAAGlB,QAAQ,CAAC,KAAK,CAAC,CAEjD;AACA,KAAM,CAACmB,WAAW,CAAEC,cAAc,CAAC,CAAGpB,QAAQ,CAAC,CAC7CqB,IAAI,CAAE,WAAW,CACjBC,KAAK,CAAE,mBAAmB,CAC1BC,UAAU,CAAE,QAAQ,CACpBC,QAAQ,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAAC,CAChCC,MAAM,CAAE,IAAI,CACZC,GAAG,CAAE,wCAAwC,CAC7CC,KAAK,CAAE,eAAe,CACtBC,IAAI,CAAE,QACR,CAAC,CAAC,CAEF,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAG/B,QAAQ,CAAC,CAC/CgC,eAAe,CAAE,EAAE,CACnBC,WAAW,CAAE,EAAE,CACfC,eAAe,CAAE,EACnB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,KAAK,CAAG,CACZC,eAAe,CAAE,CAAC,CAClBC,gBAAgB,CAAE,CAAC,CACnBC,YAAY,CAAE,CAAC,CACfC,cAAc,CAAE,EAAE,CAAE;AACpBC,YAAY,CAAE,EAChB,CAAC,CAED,KAAM,CAAAC,iBAAiB,CAAGA,CAAA,GAAM,CAC9B;AACAC,OAAO,CAACC,GAAG,CAAC,eAAe,CAAExB,WAAW,CAAC,CACzCD,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAED,KAAM,CAAA0B,oBAAoB,CAAGA,CAAA,GAAM,CACjC;AACAF,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC,CAC9BZ,eAAe,CAAC,CACdC,eAAe,CAAE,EAAE,CACnBC,WAAW,CAAE,EAAE,CACfC,eAAe,CAAE,EACnB,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAW,kBAAkB,CAAIC,KAA0C,EAAK,CACzE;AACAJ,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAEG,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC,CACnD,CAAC,CAED,mBACEtC,KAAA,QAAKuC,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBxC,KAAA,QAAKuC,SAAS,CAAC,6CAA6C,CAAAC,QAAA,EACzDpC,MAAM,eACLN,IAAA,WACE2C,OAAO,CAAErC,MAAO,CAChBmC,SAAS,CAAC,yDAAyD,CAAAC,QAAA,cAEnE1C,IAAA,QAAKyC,SAAS,CAAC,SAAS,CAACG,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAJ,QAAA,cAC5E1C,IAAA,SAAM+C,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,WAAW,CAAE,CAAE,CAACC,CAAC,CAAC,iBAAiB,CAAE,CAAC,CACtF,CAAC,CACA,CACT,cACDhD,KAAA,QAAAwC,QAAA,eACE1C,IAAA,OAAIyC,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,qEAAY,CAAI,CAAC,cAClE1C,IAAA,MAAGyC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,mLAAgC,CAAG,CAAC,EAC9D,CAAC,EACH,CAAC,cAENxC,KAAA,QAAKuC,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eAEpDxC,KAAA,CAACT,MAAM,CAAC0D,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9Bb,SAAS,CAAC,eAAe,CAAAC,QAAA,eAEzBxC,KAAA,QAAKuC,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eAC5DxC,KAAA,QAAKuC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC1C,IAAA,QAAKyC,SAAS,CAAC,6EAA6E,CAAAC,QAAA,CACzF/B,WAAW,CAACO,MAAM,cACjBlB,IAAA,QACEwD,GAAG,CAAE7C,WAAW,CAACO,MAAO,CACxBuC,GAAG,CAAC,SAAS,CACbhB,SAAS,CAAC,qCAAqC,CAChD,CAAC,cAEFzC,IAAA,CAACN,QAAQ,EAAC+C,SAAS,CAAC,yBAAyB,CAAE,CAChD,CACE,CAAC,cACNvC,KAAA,UAAOuC,SAAS,CAAC,sHAAsH,CAAAC,QAAA,eACrI1C,IAAA,CAACL,UAAU,EAAC8C,SAAS,CAAC,SAAS,CAAE,CAAC,cAClCzC,IAAA,UACE0D,IAAI,CAAC,MAAM,CACXC,MAAM,CAAC,SAAS,CAChBC,QAAQ,CAAEvB,kBAAmB,CAC7BI,SAAS,CAAC,QAAQ,CACnB,CAAC,EACG,CAAC,EACL,CAAC,cAENzC,IAAA,OAAIyC,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAE/B,WAAW,CAACE,IAAI,CAAK,CAAC,cAC5Eb,IAAA,MAAGyC,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAE/B,WAAW,CAACG,KAAK,CAAI,CAAC,cACzDZ,KAAA,MAAGuC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,EAAC,2DAC5B,cAAA1C,IAAA,SAAMyC,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAE/B,WAAW,CAACI,UAAU,CAAO,CAAC,EACpG,CAAC,cACJb,KAAA,MAAGuC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,wCAC3B,CAAC/B,WAAW,CAACK,QAAQ,CAAC6C,kBAAkB,CAAC,OAAO,CAAC,EACxD,CAAC,EACD,CAAC,cAGN3D,KAAA,QAAKuC,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrD1C,IAAA,OAAIyC,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,wDAAS,CAAI,CAAC,cACvExC,KAAA,QAAKuC,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBxC,KAAA,QAAKuC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDxC,KAAA,QAAKuC,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1D1C,IAAA,CAACH,eAAe,EAAC4C,SAAS,CAAC,uBAAuB,CAAE,CAAC,cACrDzC,IAAA,SAAMyC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,6FAAgB,CAAM,CAAC,EAC5D,CAAC,cACN1C,IAAA,SAAMyC,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAEf,KAAK,CAACC,eAAe,CAAO,CAAC,EACzE,CAAC,cACN1B,KAAA,QAAKuC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDxC,KAAA,QAAKuC,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1D1C,IAAA,CAACJ,eAAe,EAAC6C,SAAS,CAAC,wBAAwB,CAAE,CAAC,cACtDzC,IAAA,SAAMyC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,mGAAiB,CAAM,CAAC,EAC7D,CAAC,cACN1C,IAAA,SAAMyC,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAEf,KAAK,CAACE,gBAAgB,CAAO,CAAC,EAC1E,CAAC,cACN3B,KAAA,QAAKuC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDxC,KAAA,QAAKuC,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1D1C,IAAA,CAACF,UAAU,EAAC2C,SAAS,CAAC,yBAAyB,CAAE,CAAC,cAClDzC,IAAA,SAAMyC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,kDAAQ,CAAM,CAAC,EACpD,CAAC,cACN1C,IAAA,SAAMyC,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAEf,KAAK,CAACG,YAAY,CAAO,CAAC,EACtE,CAAC,cACN5B,KAAA,QAAKuC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD1C,IAAA,SAAMyC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,iFAAc,CAAM,CAAC,cAC7DxC,KAAA,SAAMuC,SAAS,CAAC,6BAA6B,CAAAC,QAAA,EAAEf,KAAK,CAACI,cAAc,CAAC,GAAC,EAAM,CAAC,EACzE,CAAC,cACN7B,KAAA,QAAKuC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD1C,IAAA,SAAMyC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,2EAAa,CAAM,CAAC,cAC5DxC,KAAA,SAAMuC,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAEf,KAAK,CAACK,YAAY,CAAC,GAAC,EAAM,CAAC,EACxE,CAAC,EACH,CAAC,EACH,CAAC,EACI,CAAC,cAGb9B,KAAA,CAACT,MAAM,CAAC0D,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BQ,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3BtB,SAAS,CAAC,eAAe,CAAAC,QAAA,eAGzB1C,IAAA,QAAKyC,SAAS,CAAC,wCAAwC,CAAAC,QAAA,cACrDxC,KAAA,QAAKuC,SAAS,CAAC,yDAAyD,CAAAC,QAAA,eACtE1C,IAAA,WACE2C,OAAO,CAAEA,CAAA,GAAMnC,YAAY,CAAC,SAAS,CAAE,CACvCiC,SAAS,CAAE,0CACTlC,SAAS,GAAK,SAAS,CACnB,+BAA+B,CAC/B,sDAAsD,EACzD,CAAAmC,QAAA,CACJ,mGAED,CAAQ,CAAC,cACT1C,IAAA,WACE2C,OAAO,CAAEA,CAAA,GAAMnC,YAAY,CAAC,UAAU,CAAE,CACxCiC,SAAS,CAAE,0CACTlC,SAAS,GAAK,UAAU,CACpB,+BAA+B,CAC/B,sDAAsD,EACzD,CAAAmC,QAAA,CACJ,sCAED,CAAQ,CAAC,EACN,CAAC,CACH,CAAC,cAGNxC,KAAA,QAAKuC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,EAC/CnC,SAAS,GAAK,SAAS,eACtBL,KAAA,QAAKuC,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBxC,KAAA,QAAKuC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD1C,IAAA,OAAIyC,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,mGAAiB,CAAI,CAAC,cAC1E1C,IAAA,WACE2C,OAAO,CAAEA,CAAA,GAAMjC,YAAY,CAAC,CAACD,SAAS,CAAE,CACxCgC,SAAS,CAAC,qDAAqD,CAAAC,QAAA,CAE9DjC,SAAS,CAAG,OAAO,CAAG,OAAO,CACxB,CAAC,EACN,CAAC,cAENP,KAAA,QAAKuC,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDxC,KAAA,QAAAwC,QAAA,eACE1C,IAAA,UAAOyC,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,qEAEhE,CAAO,CAAC,cACR1C,IAAA,UACE0D,IAAI,CAAC,MAAM,CACXM,KAAK,CAAErD,WAAW,CAACE,IAAK,CACxB+C,QAAQ,CAAGK,CAAC,EAAKrD,cAAc,CAACsD,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAErD,IAAI,CAAEoD,CAAC,CAAC1B,MAAM,CAACyB,KAAM,CAAC,CAAC,CAAE,CAC7EG,QAAQ,CAAE,CAAC1D,SAAU,CACrBgC,SAAS,CAAC,4HAA4H,CACvI,CAAC,EACC,CAAC,cAENvC,KAAA,QAAAwC,QAAA,eACE1C,IAAA,UAAOyC,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,mGAEhE,CAAO,CAAC,cACR1C,IAAA,UACE0D,IAAI,CAAC,OAAO,CACZM,KAAK,CAAErD,WAAW,CAACG,KAAM,CACzB8C,QAAQ,CAAGK,CAAC,EAAKrD,cAAc,CAACsD,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAEpD,KAAK,CAAEmD,CAAC,CAAC1B,MAAM,CAACyB,KAAM,CAAC,CAAC,CAAE,CAC9EG,QAAQ,CAAE,CAAC1D,SAAU,CACrBgC,SAAS,CAAC,4HAA4H,CACvI,CAAC,EACC,CAAC,cAENvC,KAAA,QAAAwC,QAAA,eACE1C,IAAA,UAAOyC,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,yDAEhE,CAAO,CAAC,cACR1C,IAAA,UACE0D,IAAI,CAAC,KAAK,CACVM,KAAK,CAAErD,WAAW,CAACS,KAAM,CACzBwC,QAAQ,CAAGK,CAAC,EAAKrD,cAAc,CAACsD,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE9C,KAAK,CAAE6C,CAAC,CAAC1B,MAAM,CAACyB,KAAM,CAAC,CAAC,CAAE,CAC9EG,QAAQ,CAAE,CAAC1D,SAAU,CACrBgC,SAAS,CAAC,4HAA4H,CACvI,CAAC,EACC,CAAC,cAENvC,KAAA,QAAAwC,QAAA,eACE1C,IAAA,UAAOyC,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,4CAEhE,CAAO,CAAC,cACR1C,IAAA,UACE0D,IAAI,CAAC,MAAM,CACXM,KAAK,CAAErD,WAAW,CAACU,IAAK,CACxBuC,QAAQ,CAAGK,CAAC,EAAKrD,cAAc,CAACsD,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE7C,IAAI,CAAE4C,CAAC,CAAC1B,MAAM,CAACyB,KAAM,CAAC,CAAC,CAAE,CAC7EG,QAAQ,CAAE,CAAC1D,SAAU,CACrBgC,SAAS,CAAC,4HAA4H,CACvI,CAAC,EACC,CAAC,EACH,CAAC,cAENvC,KAAA,QAAAwC,QAAA,eACE1C,IAAA,UAAOyC,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,yDAEhE,CAAO,CAAC,cACR1C,IAAA,aACEgE,KAAK,CAAErD,WAAW,CAACQ,GAAI,CACvByC,QAAQ,CAAGK,CAAC,EAAKrD,cAAc,CAACsD,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE/C,GAAG,CAAE8C,CAAC,CAAC1B,MAAM,CAACyB,KAAM,CAAC,CAAC,CAAE,CAC5EG,QAAQ,CAAE,CAAC1D,SAAU,CACrB2D,IAAI,CAAE,CAAE,CACR3B,SAAS,CAAC,4HAA4H,CACvI,CAAC,EACC,CAAC,CAELhC,SAAS,eACRP,KAAA,QAAKuC,SAAS,CAAC,4CAA4C,CAAAC,QAAA,eACzD1C,IAAA,WACE2C,OAAO,CAAEA,CAAA,GAAMjC,YAAY,CAAC,KAAK,CAAE,CACnC+B,SAAS,CAAC,oFAAoF,CAAAC,QAAA,CAC/F,gCAED,CAAQ,CAAC,cACT1C,IAAA,WACE2C,OAAO,CAAEV,iBAAkB,CAC3BQ,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAC5F,2EAED,CAAQ,CAAC,EACN,CACN,EACE,CACN,CAEAnC,SAAS,GAAK,UAAU,eACvBL,KAAA,QAAKuC,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB1C,IAAA,OAAIyC,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,8FAAiB,CAAI,CAAC,cAE1ExC,KAAA,QAAKuC,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBxC,KAAA,QAAAwC,QAAA,eACE1C,IAAA,UAAOyC,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,0GAEhE,CAAO,CAAC,cACR1C,IAAA,UACE0D,IAAI,CAAC,UAAU,CACfM,KAAK,CAAE1C,YAAY,CAACE,eAAgB,CACpCoC,QAAQ,CAAGK,CAAC,EAAK1C,eAAe,CAAC2C,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE1C,eAAe,CAAEyC,CAAC,CAAC1B,MAAM,CAACyB,KAAM,CAAC,CAAC,CAAE,CACzFvB,SAAS,CAAC,wGAAwG,CACnH,CAAC,EACC,CAAC,cAENvC,KAAA,QAAAwC,QAAA,eACE1C,IAAA,UAAOyC,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,0GAEhE,CAAO,CAAC,cACR1C,IAAA,UACE0D,IAAI,CAAC,UAAU,CACfM,KAAK,CAAE1C,YAAY,CAACG,WAAY,CAChCmC,QAAQ,CAAGK,CAAC,EAAK1C,eAAe,CAAC2C,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAEzC,WAAW,CAAEwC,CAAC,CAAC1B,MAAM,CAACyB,KAAM,CAAC,CAAC,CAAE,CACrFvB,SAAS,CAAC,wGAAwG,CACnH,CAAC,EACC,CAAC,cAENvC,KAAA,QAAAwC,QAAA,eACE1C,IAAA,UAAOyC,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,yIAEhE,CAAO,CAAC,cACR1C,IAAA,UACE0D,IAAI,CAAC,UAAU,CACfM,KAAK,CAAE1C,YAAY,CAACI,eAAgB,CACpCkC,QAAQ,CAAGK,CAAC,EAAK1C,eAAe,CAAC2C,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAExC,eAAe,CAAEuC,CAAC,CAAC1B,MAAM,CAACyB,KAAM,CAAC,CAAC,CAAE,CACzFvB,SAAS,CAAC,wGAAwG,CACnH,CAAC,EACC,CAAC,cAENzC,IAAA,WACE2C,OAAO,CAAEP,oBAAqB,CAC9BK,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAC5F,8FAED,CAAQ,CAAC,EACN,CAAC,EACH,CACN,EACE,CAAC,EACI,CAAC,EACV,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAvC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}