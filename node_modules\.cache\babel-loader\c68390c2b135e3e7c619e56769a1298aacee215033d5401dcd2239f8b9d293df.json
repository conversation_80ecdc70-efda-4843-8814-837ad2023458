{"ast": null, "code": "import { useContext, useMemo } from 'react';\nimport { MotionContext } from './index.mjs';\nimport { getCurrentTreeVariants } from './utils.mjs';\nfunction useCreateMotionContext(props) {\n  const {\n    initial,\n    animate\n  } = getCurrentTreeVariants(props, useContext(MotionContext));\n  return useMemo(() => ({\n    initial,\n    animate\n  }), [variantLabelsAsDependency(initial), variantLabelsAsDependency(animate)]);\n}\nfunction variantLabelsAsDependency(prop) {\n  return Array.isArray(prop) ? prop.join(\" \") : prop;\n}\nexport { useCreateMotionContext };", "map": {"version": 3, "names": ["useContext", "useMemo", "MotionContext", "getCurrentTreeVariants", "useCreateMotionContext", "props", "initial", "animate", "variantLabelsAsDependency", "prop", "Array", "isArray", "join"], "sources": ["C:/Users/<USER>/Desktop/مشروع/node_modules/framer-motion/dist/es/context/MotionContext/create.mjs"], "sourcesContent": ["import { useContext, useMemo } from 'react';\nimport { MotionContext } from './index.mjs';\nimport { getCurrentTreeVariants } from './utils.mjs';\n\nfunction useCreateMotionContext(props) {\n    const { initial, animate } = getCurrentTreeVariants(props, useContext(MotionContext));\n    return useMemo(() => ({ initial, animate }), [variantLabelsAsDependency(initial), variantLabelsAsDependency(animate)]);\n}\nfunction variantLabelsAsDependency(prop) {\n    return Array.isArray(prop) ? prop.join(\" \") : prop;\n}\n\nexport { useCreateMotionContext };\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,OAAO,QAAQ,OAAO;AAC3C,SAASC,aAAa,QAAQ,aAAa;AAC3C,SAASC,sBAAsB,QAAQ,aAAa;AAEpD,SAASC,sBAAsBA,CAACC,KAAK,EAAE;EACnC,MAAM;IAAEC,OAAO;IAAEC;EAAQ,CAAC,GAAGJ,sBAAsB,CAACE,KAAK,EAAEL,UAAU,CAACE,aAAa,CAAC,CAAC;EACrF,OAAOD,OAAO,CAAC,OAAO;IAAEK,OAAO;IAAEC;EAAQ,CAAC,CAAC,EAAE,CAACC,yBAAyB,CAACF,OAAO,CAAC,EAAEE,yBAAyB,CAACD,OAAO,CAAC,CAAC,CAAC;AAC1H;AACA,SAASC,yBAAyBA,CAACC,IAAI,EAAE;EACrC,OAAOC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,GAAGA,IAAI,CAACG,IAAI,CAAC,GAAG,CAAC,GAAGH,IAAI;AACtD;AAEA,SAASL,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}