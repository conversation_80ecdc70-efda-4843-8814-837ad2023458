import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  ClipboardDocumentListIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';

interface QuizPageProps {
  quizId: string;
  onBack: () => void;
}

const QuizPage: React.FC<QuizPageProps> = ({ quizId, onBack }) => {
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<{ [key: number]: any }>({});
  const [timeLeft, setTimeLeft] = useState(1800); // 30 minutes in seconds
  const [quizCompleted, setQuizCompleted] = useState(false);
  const [score, setScore] = useState<number | null>(null);

  // Mock quiz data
  const quiz = {
    id: quizId,
    title: 'اختبار أساسيات البرمجة',
    description: 'اختبار شامل لأساسيات البرمجة',
    timeLimit: 30,
    passingScore: 70,
    questions: [
      {
        id: 1,
        question: 'ما هو المتغير في البرمجة؟',
        type: 'multiple-choice',
        options: [
          'مكان لتخزين البيانات',
          'نوع من الدوال',
          'أمر برمجي',
          'لا شيء مما سبق'
        ],
        correctAnswer: 0
      },
      {
        id: 2,
        question: 'أي من التالي يُستخدم لإنشاء حلقة تكرارية؟',
        type: 'multiple-choice',
        options: [
          'if',
          'for',
          'function',
          'variable'
        ],
        correctAnswer: 1
      },
      {
        id: 3,
        question: 'البرمجة الكائنية تعتمد على مفهوم الكلاسات',
        type: 'true-false',
        options: ['صحيح', 'خطأ'],
        correctAnswer: 0
      }
    ]
  };

  // Format time
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleAnswerSelect = (answerIndex: number) => {
    setAnswers(prev => ({
      ...prev,
      [currentQuestion]: answerIndex
    }));
  };

  const handleNextQuestion = () => {
    if (currentQuestion < quiz.questions.length - 1) {
      setCurrentQuestion(prev => prev + 1);
    }
  };

  const handlePrevQuestion = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(prev => prev - 1);
    }
  };

  const handleSubmitQuiz = () => {
    // Calculate score
    let correctAnswers = 0;
    quiz.questions.forEach((question, index) => {
      if (answers[index] === question.correctAnswer) {
        correctAnswers++;
      }
    });
    
    const finalScore = Math.round((correctAnswers / quiz.questions.length) * 100);
    setScore(finalScore);
    setQuizCompleted(true);
  };

  const currentQuestionData = quiz.questions[currentQuestion];
  const progress = ((currentQuestion + 1) / quiz.questions.length) * 100;

  if (quizCompleted) {
    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4 space-x-reverse">
          <button
            onClick={onBack}
            className="p-2 text-gray-600 hover:text-gray-900 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">نتيجة الاختبار</h1>
            <p className="text-gray-600">{quiz.title}</p>
          </div>
        </div>

        {/* Results */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-sm p-8 text-center"
        >
          <div className="mb-6">
            {score! >= quiz.passingScore ? (
              <CheckCircleIcon className="w-16 h-16 text-green-600 mx-auto mb-4" />
            ) : (
              <XCircleIcon className="w-16 h-16 text-red-600 mx-auto mb-4" />
            )}
            
            <h2 className="text-3xl font-bold text-gray-900 mb-2">{score}%</h2>
            <p className={`text-lg ${score! >= quiz.passingScore ? 'text-green-600' : 'text-red-600'}`}>
              {score! >= quiz.passingScore ? 'مبروك! لقد نجحت في الاختبار' : 'للأسف، لم تحقق الدرجة المطلوبة'}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-sm text-gray-600">النتيجة</p>
              <p className="text-2xl font-bold text-gray-900">{score}%</p>
            </div>
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-sm text-gray-600">الدرجة المطلوبة</p>
              <p className="text-2xl font-bold text-gray-900">{quiz.passingScore}%</p>
            </div>
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-sm text-gray-600">الأسئلة الصحيحة</p>
              <p className="text-2xl font-bold text-gray-900">
                {Math.round((score! / 100) * quiz.questions.length)}/{quiz.questions.length}
              </p>
            </div>
          </div>

          <div className="flex justify-center space-x-4 space-x-reverse">
            <button
              onClick={onBack}
              className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              العودة للكورس
            </button>
            {score! < quiz.passingScore && (
              <button
                onClick={() => {
                  setQuizCompleted(false);
                  setCurrentQuestion(0);
                  setAnswers({});
                  setScore(null);
                }}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                إعادة المحاولة
              </button>
            )}
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 space-x-reverse">
          <button
            onClick={onBack}
            className="p-2 text-gray-600 hover:text-gray-900 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{quiz.title}</h1>
            <p className="text-gray-600">{quiz.description}</p>
          </div>
        </div>
        
        {/* Timer */}
        <div className="flex items-center space-x-2 space-x-reverse bg-red-50 px-4 py-2 rounded-lg">
          <ClockIcon className="w-5 h-5 text-red-600" />
          <span className="text-red-600 font-medium">{formatTime(timeLeft)}</span>
        </div>
      </div>

      {/* Progress */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-gray-600">السؤال {currentQuestion + 1} من {quiz.questions.length}</span>
          <span className="text-sm text-gray-600">{Math.round(progress)}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
      </div>

      {/* Question */}
      <motion.div
        key={currentQuestion}
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        className="bg-white rounded-lg shadow-sm p-6"
      >
        <div className="mb-6">
          <div className="flex items-center space-x-3 space-x-reverse mb-4">
            <ClipboardDocumentListIcon className="w-6 h-6 text-blue-600" />
            <h2 className="text-lg font-semibold text-gray-900">
              السؤال {currentQuestion + 1}
            </h2>
          </div>
          <p className="text-gray-900 text-lg leading-relaxed">
            {currentQuestionData.question}
          </p>
        </div>

        {/* Answer Options */}
        <div className="space-y-3 mb-6">
          {currentQuestionData.options.map((option, index) => (
            <label
              key={index}
              className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-colors ${
                answers[currentQuestion] === index
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <input
                type="radio"
                name={`question-${currentQuestion}`}
                value={index}
                checked={answers[currentQuestion] === index}
                onChange={() => handleAnswerSelect(index)}
                className="sr-only"
              />
              <div className={`w-4 h-4 rounded-full border-2 mr-3 ${
                answers[currentQuestion] === index
                  ? 'border-blue-500 bg-blue-500'
                  : 'border-gray-300'
              }`}>
                {answers[currentQuestion] === index && (
                  <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>
                )}
              </div>
              <span className="text-gray-900">{option}</span>
            </label>
          ))}
        </div>

        {/* Navigation */}
        <div className="flex items-center justify-between">
          <button
            onClick={handlePrevQuestion}
            disabled={currentQuestion === 0}
            className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            السؤال السابق
          </button>

          <div className="flex space-x-2 space-x-reverse">
            {currentQuestion === quiz.questions.length - 1 ? (
              <button
                onClick={handleSubmitQuiz}
                className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                إنهاء الاختبار
              </button>
            ) : (
              <button
                onClick={handleNextQuestion}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                السؤال التالي
              </button>
            )}
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default QuizPage;
