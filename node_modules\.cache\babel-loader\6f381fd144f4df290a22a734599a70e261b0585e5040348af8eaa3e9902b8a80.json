{"ast": null, "code": "import { initializeApp } from 'firebase/app';\nimport { getAuth } from 'firebase/auth';\nimport { getFirestore } from 'firebase/firestore';\nimport { getStorage } from 'firebase/storage';\nimport { getFunctions } from 'firebase/functions';\nconst firebaseConfig = {\n  apiKey: \"AIzaSyDXQJ8QJ8QJ8QJ8QJ8QJ8QJ8QJ8QJ8QJ8Q\",\n  authDomain: \"alaa-courses-platform.firebaseapp.com\",\n  projectId: \"alaa-courses-platform\",\n  storageBucket: \"alaa-courses-platform.appspot.com\",\n  messagingSenderId: \"341945258779\",\n  appId: \"1:341945258779:web:a1b2c3d4e5f6g7h8i9j0k1l2\"\n};\n\n// Initialize Firebase\nconst app = initializeApp(firebaseConfig);\n\n// Initialize Firebase services\nexport const auth = getAuth(app);\nexport const db = getFirestore(app);\nexport const storage = getStorage(app);\nexport const functions = getFunctions(app);\nexport default app;", "map": {"version": 3, "names": ["initializeApp", "getAuth", "getFirestore", "getStorage", "getFunctions", "firebaseConfig", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "projectId", "storageBucket", "messagingSenderId", "appId", "app", "auth", "db", "storage", "functions"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/config/firebase.ts"], "sourcesContent": ["import { initializeApp } from 'firebase/app';\nimport { getAuth } from 'firebase/auth';\nimport { getFirestore } from 'firebase/firestore';\nimport { getStorage } from 'firebase/storage';\nimport { getFunctions } from 'firebase/functions';\n\nconst firebaseConfig = {\n  apiKey: \"AIzaSyDXQJ8QJ8QJ8QJ8QJ8QJ8QJ8QJ8QJ8QJ8Q\",\n  authDomain: \"alaa-courses-platform.firebaseapp.com\",\n  projectId: \"alaa-courses-platform\",\n  storageBucket: \"alaa-courses-platform.appspot.com\",\n  messagingSenderId: \"341945258779\",\n  appId: \"1:341945258779:web:a1b2c3d4e5f6g7h8i9j0k1l2\"\n};\n\n// Initialize Firebase\nconst app = initializeApp(firebaseConfig);\n\n// Initialize Firebase services\nexport const auth = getAuth(app);\nexport const db = getFirestore(app);\nexport const storage = getStorage(app);\nexport const functions = getFunctions(app);\n\nexport default app;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,cAAc;AAC5C,SAASC,OAAO,QAAQ,eAAe;AACvC,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,YAAY,QAAQ,oBAAoB;AAEjD,MAAMC,cAAc,GAAG;EACrBC,MAAM,EAAE,yCAAyC;EACjDC,UAAU,EAAE,uCAAuC;EACnDC,SAAS,EAAE,uBAAuB;EAClCC,aAAa,EAAE,mCAAmC;EAClDC,iBAAiB,EAAE,cAAc;EACjCC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,MAAMC,GAAG,GAAGZ,aAAa,CAACK,cAAc,CAAC;;AAEzC;AACA,OAAO,MAAMQ,IAAI,GAAGZ,OAAO,CAACW,GAAG,CAAC;AAChC,OAAO,MAAME,EAAE,GAAGZ,YAAY,CAACU,GAAG,CAAC;AACnC,OAAO,MAAMG,OAAO,GAAGZ,UAAU,CAACS,GAAG,CAAC;AACtC,OAAO,MAAMI,SAAS,GAAGZ,YAAY,CAACQ,GAAG,CAAC;AAE1C,eAAeA,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}