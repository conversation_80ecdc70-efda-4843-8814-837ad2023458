{"ast": null, "code": "import { interpolate } from './interpolate.mjs';\nconst isCustomValueType = v => {\n  return v && typeof v === \"object\" && v.mix;\n};\nconst getMixer = v => isCustomValueType(v) ? v.mix : undefined;\nfunction transform(...args) {\n  const useImmediate = !Array.isArray(args[0]);\n  const argOffset = useImmediate ? 0 : -1;\n  const inputValue = args[0 + argOffset];\n  const inputRange = args[1 + argOffset];\n  const outputRange = args[2 + argOffset];\n  const options = args[3 + argOffset];\n  const interpolator = interpolate(inputRange, outputRange, {\n    mixer: getMixer(outputRange[0]),\n    ...options\n  });\n  return useImmediate ? interpolator(inputValue) : interpolator;\n}\nexport { transform };", "map": {"version": 3, "names": ["interpolate", "isCustomValueType", "v", "mix", "getMixer", "undefined", "transform", "args", "useImmediate", "Array", "isArray", "argOffset", "inputValue", "inputRange", "outputRange", "options", "interpolator", "mixer"], "sources": ["C:/Users/<USER>/Desktop/مشروع/node_modules/framer-motion/dist/es/utils/transform.mjs"], "sourcesContent": ["import { interpolate } from './interpolate.mjs';\n\nconst isCustomValueType = (v) => {\n    return v && typeof v === \"object\" && v.mix;\n};\nconst getMixer = (v) => (isCustomValueType(v) ? v.mix : undefined);\nfunction transform(...args) {\n    const useImmediate = !Array.isArray(args[0]);\n    const argOffset = useImmediate ? 0 : -1;\n    const inputValue = args[0 + argOffset];\n    const inputRange = args[1 + argOffset];\n    const outputRange = args[2 + argOffset];\n    const options = args[3 + argOffset];\n    const interpolator = interpolate(inputRange, outputRange, {\n        mixer: getMixer(outputRange[0]),\n        ...options,\n    });\n    return useImmediate ? interpolator(inputValue) : interpolator;\n}\n\nexport { transform };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,mBAAmB;AAE/C,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;EAC7B,OAAOA,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,CAACC,GAAG;AAC9C,CAAC;AACD,MAAMC,QAAQ,GAAIF,CAAC,IAAMD,iBAAiB,CAACC,CAAC,CAAC,GAAGA,CAAC,CAACC,GAAG,GAAGE,SAAU;AAClE,SAASC,SAASA,CAAC,GAAGC,IAAI,EAAE;EACxB,MAAMC,YAAY,GAAG,CAACC,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAMI,SAAS,GAAGH,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC;EACvC,MAAMI,UAAU,GAAGL,IAAI,CAAC,CAAC,GAAGI,SAAS,CAAC;EACtC,MAAME,UAAU,GAAGN,IAAI,CAAC,CAAC,GAAGI,SAAS,CAAC;EACtC,MAAMG,WAAW,GAAGP,IAAI,CAAC,CAAC,GAAGI,SAAS,CAAC;EACvC,MAAMI,OAAO,GAAGR,IAAI,CAAC,CAAC,GAAGI,SAAS,CAAC;EACnC,MAAMK,YAAY,GAAGhB,WAAW,CAACa,UAAU,EAAEC,WAAW,EAAE;IACtDG,KAAK,EAAEb,QAAQ,CAACU,WAAW,CAAC,CAAC,CAAC,CAAC;IAC/B,GAAGC;EACP,CAAC,CAAC;EACF,OAAOP,YAAY,GAAGQ,YAAY,CAACJ,UAAU,CAAC,GAAGI,YAAY;AACjE;AAEA,SAASV,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}