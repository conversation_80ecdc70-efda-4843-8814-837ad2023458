import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  PlayIcon,
  DocumentIcon,
  ClipboardDocumentListIcon,
  CheckCircleIcon,
  ArrowLeftIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';

// Types
import { Student } from '../../types';

interface CourseViewerProps {
  user?: Student;
  courseId?: string;
  onBack?: () => void;
}

const CourseViewer: React.FC<CourseViewerProps> = ({ user, courseId, onBack }) => {
  const [activeTab, setActiveTab] = useState('videos');
  const [selectedVideo, setSelectedVideo] = useState('1');

  // Mock course data
  const course = {
    id: courseId,
    title: 'أساسيات البرمجة',
    description: 'تعلم أساسيات البرمجة من الصفر',
    videos: [
      { id: '1', title: 'مقدمة في البرمجة', duration: '15:30', completed: true },
      { id: '2', title: 'المتغيرات والثوابت', duration: '22:45', completed: true },
      { id: '3', title: 'الحلقات التكرارية', duration: '18:20', completed: false },
      { id: '4', title: 'الدوال', duration: '25:10', completed: false }
    ],
    pdfs: [
      { id: '1', title: 'مرجع البرمجة الأساسي', size: '2.5 MB' },
      { id: '2', title: 'تمارين عملية', size: '1.8 MB' }
    ],
    quizzes: [
      { id: '1', title: 'اختبار المتغيرات', questions: 10, completed: true, score: 85 },
      { id: '2', title: 'اختبار الحلقات', questions: 8, completed: false, score: null }
    ]
  };

  const currentVideo = course.videos.find(v => v.id === selectedVideo);

  const handleNextVideo = () => {
    const currentIndex = course.videos.findIndex(v => v.id === selectedVideo);
    if (currentIndex < course.videos.length - 1) {
      setSelectedVideo(course.videos[currentIndex + 1].id);
    }
  };

  const handlePrevVideo = () => {
    const currentIndex = course.videos.findIndex(v => v.id === selectedVideo);
    if (currentIndex > 0) {
      setSelectedVideo(course.videos[currentIndex - 1].id);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4 space-x-reverse">
        {onBack && (
          <button
            onClick={onBack}
            className="p-2 text-gray-600 hover:text-gray-900 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
        )}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{course.title}</h1>
          <p className="text-gray-600">{course.description}</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Video Player */}
        <div className="lg:col-span-2 space-y-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow-sm overflow-hidden"
          >
            {/* Video Area */}
            <div className="aspect-video bg-gray-900 flex items-center justify-center">
              <div className="text-center text-white">
                <PlayIcon className="w-16 h-16 mx-auto mb-4 opacity-50" />
                <h3 className="text-xl font-semibold mb-2">{currentVideo?.title}</h3>
                <p className="text-gray-300">مدة الفيديو: {currentVideo?.duration}</p>
              </div>
            </div>

            {/* Video Controls */}
            <div className="p-4 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <button
                    onClick={handlePrevVideo}
                    disabled={course.videos.findIndex(v => v.id === selectedVideo) === 0}
                    className="flex items-center space-x-2 space-x-reverse px-3 py-2 text-sm bg-gray-100 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    <ArrowRightIcon className="w-4 h-4" />
                    <span>السابق</span>
                  </button>
                  <button
                    onClick={handleNextVideo}
                    disabled={course.videos.findIndex(v => v.id === selectedVideo) === course.videos.length - 1}
                    className="flex items-center space-x-2 space-x-reverse px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    <span>التالي</span>
                    <ArrowLeftIcon className="w-4 h-4" />
                  </button>
                </div>
                <button className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                  تم المشاهدة
                </button>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Course Content Sidebar */}
        <div className="space-y-4">
          {/* Tabs */}
          <div className="bg-white rounded-lg shadow-sm p-4">
            <div className="flex space-x-2 space-x-reverse">
              <button
                onClick={() => setActiveTab('videos')}
                className={`flex-1 py-2 px-3 text-sm rounded-lg transition-colors ${
                  activeTab === 'videos'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                الفيديوهات
              </button>
              <button
                onClick={() => setActiveTab('pdfs')}
                className={`flex-1 py-2 px-3 text-sm rounded-lg transition-colors ${
                  activeTab === 'pdfs'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                الملفات
              </button>
              <button
                onClick={() => setActiveTab('quizzes')}
                className={`flex-1 py-2 px-3 text-sm rounded-lg transition-colors ${
                  activeTab === 'quizzes'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                الاختبارات
              </button>
            </div>
          </div>

          {/* Content */}
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow-sm p-4"
          >
            {activeTab === 'videos' && (
              <div className="space-y-3">
                <h3 className="font-semibold text-gray-900 mb-4">قائمة الفيديوهات</h3>
                {course.videos.map((video, index) => (
                  <div
                    key={video.id}
                    onClick={() => setSelectedVideo(video.id)}
                    className={`p-3 rounded-lg cursor-pointer transition-colors ${
                      selectedVideo === video.id
                        ? 'bg-blue-50 border border-blue-200'
                        : 'bg-gray-50 hover:bg-gray-100'
                    }`}
                  >
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <div className="flex-shrink-0">
                        {video.completed ? (
                          <CheckCircleIcon className="w-5 h-5 text-green-600" />
                        ) : (
                          <PlayIcon className="w-5 h-5 text-gray-400" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {index + 1}. {video.title}
                        </p>
                        <p className="text-xs text-gray-500">{video.duration}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {activeTab === 'pdfs' && (
              <div className="space-y-3">
                <h3 className="font-semibold text-gray-900 mb-4">الملفات والمراجع</h3>
                {course.pdfs.map((pdf) => (
                  <div
                    key={pdf.id}
                    className="p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors"
                  >
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <DocumentIcon className="w-5 h-5 text-red-600" />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {pdf.title}
                        </p>
                        <p className="text-xs text-gray-500">{pdf.size}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {activeTab === 'quizzes' && (
              <div className="space-y-3">
                <h3 className="font-semibold text-gray-900 mb-4">الاختبارات</h3>
                {course.quizzes.map((quiz) => (
                  <div
                    key={quiz.id}
                    className="p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors"
                  >
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <ClipboardDocumentListIcon className="w-5 h-5 text-purple-600" />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {quiz.title}
                        </p>
                        <p className="text-xs text-gray-500">
                          {quiz.questions} سؤال
                          {quiz.completed && quiz.score && (
                            <span className="text-green-600 mr-2">• النتيجة: {quiz.score}%</span>
                          )}
                        </p>
                      </div>
                      {quiz.completed && (
                        <CheckCircleIcon className="w-5 h-5 text-green-600" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default CourseViewer;
