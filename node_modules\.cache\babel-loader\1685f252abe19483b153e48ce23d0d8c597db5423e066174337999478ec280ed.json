{"ast": null, "code": "import React from'react';import{motion}from'framer-motion';import{useNavigate}from'react-router-dom';import{PlusIcon,FolderPlusIcon,UserPlusIcon,ClipboardDocumentListIcon,DocumentTextIcon,ChartBarIcon}from'@heroicons/react/24/outline';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const QuickActions=()=>{const navigate=useNavigate();const actions=[{title:'إضافة كورس جديد',description:'إنشاء كورس تعليمي جديد',icon:PlusIcon,color:'blue',onClick:()=>navigate('/admin/courses?action=create')},{title:'إضافة قسم',description:'إنشاء قسم جديد للكورسات',icon:FolderPlusIcon,color:'green',onClick:()=>navigate('/admin/categories?action=create')},{title:'إضافة طالب',description:'إنشاء حساب طالب جديد',icon:UserPlusIcon,color:'purple',onClick:()=>navigate('/admin/students?action=create')},{title:'إنشاء اختبار',description:'إضافة اختبار جديد',icon:ClipboardDocumentListIcon,color:'orange',onClick:()=>navigate('/admin/quizzes?action=create')},{title:'إصدار شهادة',description:'إنشاء شهادة جديدة',icon:DocumentTextIcon,color:'red',onClick:()=>navigate('/admin/certificates?action=create')},{title:'عرض التحليلات',description:'مراجعة إحصائيات المنصة',icon:ChartBarIcon,color:'indigo',onClick:()=>navigate('/admin/analytics')}];const colorClasses={blue:'from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700',green:'from-green-500 to-green-600 hover:from-green-600 hover:to-green-700',purple:'from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700',orange:'from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700',red:'from-red-500 to-red-600 hover:from-red-600 hover:to-red-700',indigo:'from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700'};return/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-xl p-6 shadow-sm\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-bold text-gray-900 mb-4\",children:\"\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A \\u0633\\u0631\\u064A\\u0639\\u0629\"}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 sm:grid-cols-2 gap-4\",children:actions.map((action,index)=>/*#__PURE__*/_jsx(motion.button,{initial:{opacity:0,scale:0.9},animate:{opacity:1,scale:1},transition:{delay:index*0.1},whileHover:{scale:1.02},whileTap:{scale:0.98},onClick:action.onClick,className:`\n              p-4 rounded-lg text-white text-right transition-all duration-200\n              bg-gradient-to-br ${colorClasses[action.color]}\n              hover:shadow-lg transform hover:-translate-y-1\n            `,children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-semibold text-sm mb-1\",children:action.title}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs opacity-90\",children:action.description})]}),/*#__PURE__*/_jsx(action.icon,{className:\"w-6 h-6 opacity-80\"})]})},action.title))}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-6 pt-4 border-t border-gray-200\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between text-sm\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"\\u0622\\u062E\\u0631 \\u062A\\u062D\\u062F\\u064A\\u062B\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-900 font-medium\",children:new Date().toLocaleDateString('ar-SA')})]})})]});};export default QuickActions;", "map": {"version": 3, "names": ["React", "motion", "useNavigate", "PlusIcon", "FolderPlusIcon", "UserPlusIcon", "ClipboardDocumentListIcon", "DocumentTextIcon", "ChartBarIcon", "jsx", "_jsx", "jsxs", "_jsxs", "QuickActions", "navigate", "actions", "title", "description", "icon", "color", "onClick", "colorClasses", "blue", "green", "purple", "orange", "red", "indigo", "className", "children", "map", "action", "index", "button", "initial", "opacity", "scale", "animate", "transition", "delay", "whileHover", "whileTap", "Date", "toLocaleDateString"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Admin/QuickActions.tsx"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  PlusIcon,\n  FolderPlusIcon,\n  UserPlusIcon,\n  ClipboardDocumentListIcon,\n  DocumentTextIcon,\n  ChartBarIcon\n} from '@heroicons/react/24/outline';\n\nconst QuickActions: React.FC = () => {\n  const navigate = useNavigate();\n\n  const actions = [\n    {\n      title: 'إضافة كورس جديد',\n      description: 'إنشاء كورس تعليمي جديد',\n      icon: PlusIcon,\n      color: 'blue',\n      onClick: () => navigate('/admin/courses?action=create')\n    },\n    {\n      title: 'إضافة قسم',\n      description: 'إنشاء قسم جديد للكورسات',\n      icon: FolderPlusIcon,\n      color: 'green',\n      onClick: () => navigate('/admin/categories?action=create')\n    },\n    {\n      title: 'إضافة طالب',\n      description: 'إنشاء حساب طالب جديد',\n      icon: UserPlusIcon,\n      color: 'purple',\n      onClick: () => navigate('/admin/students?action=create')\n    },\n    {\n      title: 'إنشاء اختبار',\n      description: 'إضافة اختبار جديد',\n      icon: ClipboardDocumentListIcon,\n      color: 'orange',\n      onClick: () => navigate('/admin/quizzes?action=create')\n    },\n    {\n      title: 'إصدار شهادة',\n      description: 'إنشاء شهادة جديدة',\n      icon: DocumentTextIcon,\n      color: 'red',\n      onClick: () => navigate('/admin/certificates?action=create')\n    },\n    {\n      title: 'عرض التحليلات',\n      description: 'مراجعة إحصائيات المنصة',\n      icon: ChartBarIcon,\n      color: 'indigo',\n      onClick: () => navigate('/admin/analytics')\n    }\n  ];\n\n  const colorClasses = {\n    blue: 'from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700',\n    green: 'from-green-500 to-green-600 hover:from-green-600 hover:to-green-700',\n    purple: 'from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700',\n    orange: 'from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700',\n    red: 'from-red-500 to-red-600 hover:from-red-600 hover:to-red-700',\n    indigo: 'from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700'\n  };\n\n  return (\n    <div className=\"bg-white rounded-xl p-6 shadow-sm\">\n      <h3 className=\"text-lg font-bold text-gray-900 mb-4\">إجراءات سريعة</h3>\n      \n      <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\n        {actions.map((action, index) => (\n          <motion.button\n            key={action.title}\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ delay: index * 0.1 }}\n            whileHover={{ scale: 1.02 }}\n            whileTap={{ scale: 0.98 }}\n            onClick={action.onClick}\n            className={`\n              p-4 rounded-lg text-white text-right transition-all duration-200\n              bg-gradient-to-br ${colorClasses[action.color as keyof typeof colorClasses]}\n              hover:shadow-lg transform hover:-translate-y-1\n            `}\n          >\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex-1\">\n                <h4 className=\"font-semibold text-sm mb-1\">{action.title}</h4>\n                <p className=\"text-xs opacity-90\">{action.description}</p>\n              </div>\n              <action.icon className=\"w-6 h-6 opacity-80\" />\n            </div>\n          </motion.button>\n        ))}\n      </div>\n      \n      <div className=\"mt-6 pt-4 border-t border-gray-200\">\n        <div className=\"flex items-center justify-between text-sm\">\n          <span className=\"text-gray-600\">آخر تحديث</span>\n          <span className=\"text-gray-900 font-medium\">\n            {new Date().toLocaleDateString('ar-SA')}\n          </span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuickActions;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,KAAQ,eAAe,CACtC,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OACEC,QAAQ,CACRC,cAAc,CACdC,YAAY,CACZC,yBAAyB,CACzBC,gBAAgB,CAChBC,YAAY,KACP,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErC,KAAM,CAAAC,YAAsB,CAAGA,CAAA,GAAM,CACnC,KAAM,CAAAC,QAAQ,CAAGZ,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAa,OAAO,CAAG,CACd,CACEC,KAAK,CAAE,iBAAiB,CACxBC,WAAW,CAAE,wBAAwB,CACrCC,IAAI,CAAEf,QAAQ,CACdgB,KAAK,CAAE,MAAM,CACbC,OAAO,CAAEA,CAAA,GAAMN,QAAQ,CAAC,8BAA8B,CACxD,CAAC,CACD,CACEE,KAAK,CAAE,WAAW,CAClBC,WAAW,CAAE,yBAAyB,CACtCC,IAAI,CAAEd,cAAc,CACpBe,KAAK,CAAE,OAAO,CACdC,OAAO,CAAEA,CAAA,GAAMN,QAAQ,CAAC,iCAAiC,CAC3D,CAAC,CACD,CACEE,KAAK,CAAE,YAAY,CACnBC,WAAW,CAAE,sBAAsB,CACnCC,IAAI,CAAEb,YAAY,CAClBc,KAAK,CAAE,QAAQ,CACfC,OAAO,CAAEA,CAAA,GAAMN,QAAQ,CAAC,+BAA+B,CACzD,CAAC,CACD,CACEE,KAAK,CAAE,cAAc,CACrBC,WAAW,CAAE,mBAAmB,CAChCC,IAAI,CAAEZ,yBAAyB,CAC/Ba,KAAK,CAAE,QAAQ,CACfC,OAAO,CAAEA,CAAA,GAAMN,QAAQ,CAAC,8BAA8B,CACxD,CAAC,CACD,CACEE,KAAK,CAAE,aAAa,CACpBC,WAAW,CAAE,mBAAmB,CAChCC,IAAI,CAAEX,gBAAgB,CACtBY,KAAK,CAAE,KAAK,CACZC,OAAO,CAAEA,CAAA,GAAMN,QAAQ,CAAC,mCAAmC,CAC7D,CAAC,CACD,CACEE,KAAK,CAAE,eAAe,CACtBC,WAAW,CAAE,wBAAwB,CACrCC,IAAI,CAAEV,YAAY,CAClBW,KAAK,CAAE,QAAQ,CACfC,OAAO,CAAEA,CAAA,GAAMN,QAAQ,CAAC,kBAAkB,CAC5C,CAAC,CACF,CAED,KAAM,CAAAO,YAAY,CAAG,CACnBC,IAAI,CAAE,iEAAiE,CACvEC,KAAK,CAAE,qEAAqE,CAC5EC,MAAM,CAAE,yEAAyE,CACjFC,MAAM,CAAE,yEAAyE,CACjFC,GAAG,CAAE,6DAA6D,CAClEC,MAAM,CAAE,yEACV,CAAC,CAED,mBACEf,KAAA,QAAKgB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDnB,IAAA,OAAIkB,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,2EAAa,CAAI,CAAC,cAEvEnB,IAAA,QAAKkB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CACnDd,OAAO,CAACe,GAAG,CAAC,CAACC,MAAM,CAAEC,KAAK,gBACzBtB,IAAA,CAACT,MAAM,CAACgC,MAAM,EAEZC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,KAAK,CAAE,GAAI,CAAE,CACpCC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,KAAK,CAAE,CAAE,CAAE,CAClCE,UAAU,CAAE,CAAEC,KAAK,CAAEP,KAAK,CAAG,GAAI,CAAE,CACnCQ,UAAU,CAAE,CAAEJ,KAAK,CAAE,IAAK,CAAE,CAC5BK,QAAQ,CAAE,CAAEL,KAAK,CAAE,IAAK,CAAE,CAC1BhB,OAAO,CAAEW,MAAM,CAACX,OAAQ,CACxBQ,SAAS,CAAE;AACvB;AACA,kCAAkCP,YAAY,CAACU,MAAM,CAACZ,KAAK,CAA8B;AACzF;AACA,aAAc,CAAAU,QAAA,cAEFjB,KAAA,QAAKgB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDjB,KAAA,QAAKgB,SAAS,CAAC,QAAQ,CAAAC,QAAA,eACrBnB,IAAA,OAAIkB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAEE,MAAM,CAACf,KAAK,CAAK,CAAC,cAC9DN,IAAA,MAAGkB,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAEE,MAAM,CAACd,WAAW,CAAI,CAAC,EACvD,CAAC,cACNP,IAAA,CAACqB,MAAM,CAACb,IAAI,EAACU,SAAS,CAAC,oBAAoB,CAAE,CAAC,EAC3C,CAAC,EAnBDG,MAAM,CAACf,KAoBC,CAChB,CAAC,CACC,CAAC,cAENN,IAAA,QAAKkB,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjDjB,KAAA,QAAKgB,SAAS,CAAC,2CAA2C,CAAAC,QAAA,eACxDnB,IAAA,SAAMkB,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,mDAAS,CAAM,CAAC,cAChDnB,IAAA,SAAMkB,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CACxC,GAAI,CAAAa,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC,CACnC,CAAC,EACJ,CAAC,CACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA9B,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}