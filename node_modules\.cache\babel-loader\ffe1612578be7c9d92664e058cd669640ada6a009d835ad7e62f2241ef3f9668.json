{"ast": null, "code": "import { PanSession } from './PanSession.mjs';\nimport { addPointerEvent } from '../../events/add-pointer-event.mjs';\nimport { Feature } from '../../motion/features/Feature.mjs';\nimport { noop } from '../../utils/noop.mjs';\nimport { getContextWindow } from '../../utils/get-context-window.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\nconst asyncHandler = handler => (event, info) => {\n  if (handler) {\n    frame.update(() => handler(event, info));\n  }\n};\nclass PanGesture extends Feature {\n  constructor() {\n    super(...arguments);\n    this.removePointerDownListener = noop;\n  }\n  onPointerDown(pointerDownEvent) {\n    this.session = new PanSession(pointerDownEvent, this.createPanHandlers(), {\n      transformPagePoint: this.node.getTransformPagePoint(),\n      contextWindow: getContextWindow(this.node)\n    });\n  }\n  createPanHandlers() {\n    const {\n      onPanSessionStart,\n      onPanStart,\n      onPan,\n      onPanEnd\n    } = this.node.getProps();\n    return {\n      onSessionStart: asyncHandler(onPanSessionStart),\n      onStart: asyncHandler(onPanStart),\n      onMove: onPan,\n      onEnd: (event, info) => {\n        delete this.session;\n        if (onPanEnd) {\n          frame.update(() => onPanEnd(event, info));\n        }\n      }\n    };\n  }\n  mount() {\n    this.removePointerDownListener = addPointerEvent(this.node.current, \"pointerdown\", event => this.onPointerDown(event));\n  }\n  update() {\n    this.session && this.session.updateHandlers(this.createPanHandlers());\n  }\n  unmount() {\n    this.removePointerDownListener();\n    this.session && this.session.end();\n  }\n}\nexport { PanGesture };", "map": {"version": 3, "names": ["PanSession", "addPointerEvent", "Feature", "noop", "getContextWindow", "frame", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "handler", "event", "info", "update", "PanGesture", "constructor", "arguments", "removePointerDownListener", "onPointerDown", "pointerDownEvent", "session", "createPanHandlers", "transformPagePoint", "node", "getTransformPagePoint", "contextWindow", "onPanSessionStart", "onPanStart", "onPan", "onPanEnd", "getProps", "onSessionStart", "onStart", "onMove", "onEnd", "mount", "current", "updateHandlers", "unmount", "end"], "sources": ["C:/Users/<USER>/Desktop/مشروع/node_modules/framer-motion/dist/es/gestures/pan/index.mjs"], "sourcesContent": ["import { PanSession } from './PanSession.mjs';\nimport { addPointerEvent } from '../../events/add-pointer-event.mjs';\nimport { Feature } from '../../motion/features/Feature.mjs';\nimport { noop } from '../../utils/noop.mjs';\nimport { getContextWindow } from '../../utils/get-context-window.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\n\nconst asyncHandler = (handler) => (event, info) => {\n    if (handler) {\n        frame.update(() => handler(event, info));\n    }\n};\nclass PanGesture extends Feature {\n    constructor() {\n        super(...arguments);\n        this.removePointerDownListener = noop;\n    }\n    onPointerDown(pointerDownEvent) {\n        this.session = new PanSession(pointerDownEvent, this.createPanHandlers(), {\n            transformPagePoint: this.node.getTransformPagePoint(),\n            contextWindow: getContextWindow(this.node),\n        });\n    }\n    createPanHandlers() {\n        const { onPanSessionStart, onPanStart, onPan, onPanEnd } = this.node.getProps();\n        return {\n            onSessionStart: asyncHandler(onPanSessionStart),\n            onStart: asyncHandler(onPanStart),\n            onMove: onPan,\n            onEnd: (event, info) => {\n                delete this.session;\n                if (onPanEnd) {\n                    frame.update(() => onPanEnd(event, info));\n                }\n            },\n        };\n    }\n    mount() {\n        this.removePointerDownListener = addPointerEvent(this.node.current, \"pointerdown\", (event) => this.onPointerDown(event));\n    }\n    update() {\n        this.session && this.session.updateHandlers(this.createPanHandlers());\n    }\n    unmount() {\n        this.removePointerDownListener();\n        this.session && this.session.end();\n    }\n}\n\nexport { PanGesture };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,eAAe,QAAQ,oCAAoC;AACpE,SAASC,OAAO,QAAQ,mCAAmC;AAC3D,SAASC,IAAI,QAAQ,sBAAsB;AAC3C,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,KAAK,QAAQ,2BAA2B;AAEjD,MAAMC,YAAY,GAAIC,OAAO,IAAK,CAACC,KAAK,EAAEC,IAAI,KAAK;EAC/C,IAAIF,OAAO,EAAE;IACTF,KAAK,CAACK,MAAM,CAAC,MAAMH,OAAO,CAACC,KAAK,EAAEC,IAAI,CAAC,CAAC;EAC5C;AACJ,CAAC;AACD,MAAME,UAAU,SAAST,OAAO,CAAC;EAC7BU,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,yBAAyB,GAAGX,IAAI;EACzC;EACAY,aAAaA,CAACC,gBAAgB,EAAE;IAC5B,IAAI,CAACC,OAAO,GAAG,IAAIjB,UAAU,CAACgB,gBAAgB,EAAE,IAAI,CAACE,iBAAiB,CAAC,CAAC,EAAE;MACtEC,kBAAkB,EAAE,IAAI,CAACC,IAAI,CAACC,qBAAqB,CAAC,CAAC;MACrDC,aAAa,EAAElB,gBAAgB,CAAC,IAAI,CAACgB,IAAI;IAC7C,CAAC,CAAC;EACN;EACAF,iBAAiBA,CAAA,EAAG;IAChB,MAAM;MAAEK,iBAAiB;MAAEC,UAAU;MAAEC,KAAK;MAAEC;IAAS,CAAC,GAAG,IAAI,CAACN,IAAI,CAACO,QAAQ,CAAC,CAAC;IAC/E,OAAO;MACHC,cAAc,EAAEtB,YAAY,CAACiB,iBAAiB,CAAC;MAC/CM,OAAO,EAAEvB,YAAY,CAACkB,UAAU,CAAC;MACjCM,MAAM,EAAEL,KAAK;MACbM,KAAK,EAAEA,CAACvB,KAAK,EAAEC,IAAI,KAAK;QACpB,OAAO,IAAI,CAACQ,OAAO;QACnB,IAAIS,QAAQ,EAAE;UACVrB,KAAK,CAACK,MAAM,CAAC,MAAMgB,QAAQ,CAAClB,KAAK,EAAEC,IAAI,CAAC,CAAC;QAC7C;MACJ;IACJ,CAAC;EACL;EACAuB,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAClB,yBAAyB,GAAGb,eAAe,CAAC,IAAI,CAACmB,IAAI,CAACa,OAAO,EAAE,aAAa,EAAGzB,KAAK,IAAK,IAAI,CAACO,aAAa,CAACP,KAAK,CAAC,CAAC;EAC5H;EACAE,MAAMA,CAAA,EAAG;IACL,IAAI,CAACO,OAAO,IAAI,IAAI,CAACA,OAAO,CAACiB,cAAc,CAAC,IAAI,CAAChB,iBAAiB,CAAC,CAAC,CAAC;EACzE;EACAiB,OAAOA,CAAA,EAAG;IACN,IAAI,CAACrB,yBAAyB,CAAC,CAAC;IAChC,IAAI,CAACG,OAAO,IAAI,IAAI,CAACA,OAAO,CAACmB,GAAG,CAAC,CAAC;EACtC;AACJ;AAEA,SAASzB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}