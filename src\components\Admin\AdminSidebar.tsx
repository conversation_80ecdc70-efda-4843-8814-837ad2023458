import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  HomeIcon,
  FolderIcon,
  AcademicCapIcon,
  UsersIcon,
  ClipboardDocumentListIcon,
  DocumentTextIcon,
  ChartBarIcon,
  CogIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

interface AdminSidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const AdminSidebar: React.FC<AdminSidebarProps> = ({ isOpen, onClose }) => {
  const location = useLocation();

  const menuItems = [
    {
      name: 'لوحة التحكم',
      href: '/admin',
      icon: HomeIcon,
      exact: true
    },
    {
      name: 'الأقسام',
      href: '/admin/categories',
      icon: FolderIcon
    },
    {
      name: 'الكورسات',
      href: '/admin/courses',
      icon: AcademicCapIcon
    },
    {
      name: 'الطلاب',
      href: '/admin/students',
      icon: UsersIcon
    },
    {
      name: 'الاختبارات',
      href: '/admin/quizzes',
      icon: ClipboardDocumentListIcon
    },
    {
      name: 'الشهادات',
      href: '/admin/certificates',
      icon: DocumentTextIcon
    },
    {
      name: 'التحليلات',
      href: '/admin/analytics',
      icon: ChartBarIcon
    },
    {
      name: 'الإعدادات',
      href: '/admin/settings',
      icon: CogIcon
    }
  ];

  const isActive = (href: string, exact?: boolean) => {
    if (exact) {
      return location.pathname === href;
    }
    return location.pathname.startsWith(href);
  };

  return (
    <>
      {/* Desktop Sidebar */}
      <div className="hidden lg:flex lg:flex-shrink-0">
        <div className="flex flex-col w-64">
          <div className="flex flex-col flex-grow bg-white border-l border-gray-200 pt-5 pb-4 overflow-y-auto">
            {/* Logo */}
            <div className="flex items-center flex-shrink-0 px-4 mb-8">
              <div className="bg-primary-600 rounded-lg p-2">
                <AcademicCapIcon className="w-8 h-8 text-white" />
              </div>
              <div className="mr-3">
                <h2 className="text-lg font-bold text-gray-900">ALaa Abd Hamied</h2>
                <p className="text-sm text-gray-500">لوحة المدير</p>
              </div>
            </div>

            {/* Navigation */}
            <nav className="mt-5 flex-1 px-2 space-y-1">
              {menuItems.map((item) => {
                const Icon = item.icon;
                const active = isActive(item.href, item.exact);
                
                return (
                  <NavLink
                    key={item.name}
                    to={item.href}
                    className={`
                      group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200
                      ${active
                        ? 'bg-primary-100 text-primary-900 border-l-4 border-primary-600'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                      }
                    `}
                  >
                    <Icon
                      className={`
                        ml-3 flex-shrink-0 h-6 w-6 transition-colors duration-200
                        ${active ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-500'}
                      `}
                    />
                    {item.name}
                  </NavLink>
                );
              })}
            </nav>
          </div>
        </div>
      </div>

      {/* Mobile Sidebar */}
      <motion.div
        initial={{ x: -300 }}
        animate={{ x: isOpen ? 0 : -300 }}
        transition={{ type: 'tween', duration: 0.3 }}
        className="fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-xl lg:hidden"
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <div className="flex items-center">
              <div className="bg-primary-600 rounded-lg p-2">
                <AcademicCapIcon className="w-6 h-6 text-white" />
              </div>
              <div className="mr-3">
                <h2 className="text-lg font-bold text-gray-900">ALaa Abd Hamied</h2>
                <p className="text-sm text-gray-500">لوحة المدير</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-2 py-4 space-y-1 overflow-y-auto">
            {menuItems.map((item) => {
              const Icon = item.icon;
              const active = isActive(item.href, item.exact);
              
              return (
                <NavLink
                  key={item.name}
                  to={item.href}
                  onClick={onClose}
                  className={`
                    group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200
                    ${active
                      ? 'bg-primary-100 text-primary-900 border-l-4 border-primary-600'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }
                  `}
                >
                  <Icon
                    className={`
                      ml-3 flex-shrink-0 h-6 w-6 transition-colors duration-200
                      ${active ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-500'}
                    `}
                  />
                  {item.name}
                </NavLink>
              );
            })}
          </nav>
        </div>
      </motion.div>
    </>
  );
};

export default AdminSidebar;
