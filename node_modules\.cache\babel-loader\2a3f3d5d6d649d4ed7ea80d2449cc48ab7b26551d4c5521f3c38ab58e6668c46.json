{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0634\\u0631\\u0648\\u0639\\\\src\\\\components\\\\AIAssistant\\\\AIAssistant.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { XMarkIcon, PaperAirplaneIcon, MinusIcon, StarIcon } from '@heroicons/react/24/outline';\n\n// Types\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n// Custom AI Assistant Icon Component with Animation\nconst AIAssistantIcon = ({\n  className = \"w-7 h-7\",\n  animated = false\n}) => /*#__PURE__*/_jsxDEV(motion.svg, {\n  className: className,\n  viewBox: \"0 0 64 64\",\n  fill: \"none\",\n  xmlns: \"http://www.w3.org/2000/svg\",\n  animate: animated ? {\n    scale: [1, 1.05, 1]\n  } : {},\n  transition: animated ? {\n    duration: 2,\n    repeat: Infinity,\n    ease: \"easeInOut\"\n  } : {},\n  children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n    x: \"18\",\n    y: \"16\",\n    width: \"28\",\n    height: \"24\",\n    rx: \"8\",\n    fill: \"currentColor\",\n    stroke: \"rgba(255,255,255,0.4)\",\n    strokeWidth: \"1.5\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n    x: \"20\",\n    y: \"18\",\n    width: \"24\",\n    height: \"20\",\n    rx: \"6\",\n    fill: \"rgba(255,255,255,0.1)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n    cx: \"26\",\n    cy: \"26\",\n    r: \"4\",\n    fill: \"rgba(255,255,255,0.3)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n    cx: \"38\",\n    cy: \"26\",\n    r: \"4\",\n    fill: \"rgba(255,255,255,0.3)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n    cx: \"26\",\n    cy: \"26\",\n    r: \"3\",\n    fill: \"rgba(255,255,255,0.95)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 62,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n    cx: \"38\",\n    cy: \"26\",\n    r: \"3\",\n    fill: \"rgba(255,255,255,0.95)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 63,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(motion.circle, {\n    cx: \"26\",\n    cy: \"26\",\n    r: \"1.5\",\n    fill: \"rgba(59, 130, 246, 0.9)\",\n    animate: animated ? {\n      fill: [\"rgba(59, 130, 246, 0.9)\", \"rgba(34, 197, 94, 0.9)\", \"rgba(59, 130, 246, 0.9)\"]\n    } : {},\n    transition: animated ? {\n      duration: 3,\n      repeat: Infinity,\n      ease: \"easeInOut\"\n    } : {}\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(motion.circle, {\n    cx: \"38\",\n    cy: \"26\",\n    r: \"1.5\",\n    fill: \"rgba(59, 130, 246, 0.9)\",\n    animate: animated ? {\n      fill: [\"rgba(59, 130, 246, 0.9)\", \"rgba(34, 197, 94, 0.9)\", \"rgba(59, 130, 246, 0.9)\"]\n    } : {},\n    transition: animated ? {\n      duration: 3,\n      repeat: Infinity,\n      ease: \"easeInOut\"\n    } : {}\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 80,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n    cx: \"26\",\n    cy: \"25\",\n    r: \"0.5\",\n    fill: \"rgba(255,255,255,0.8)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 94,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n    cx: \"38\",\n    cy: \"25\",\n    r: \"0.5\",\n    fill: \"rgba(255,255,255,0.8)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M 28 32 Q 32 35 36 32\",\n    stroke: \"rgba(255,255,255,0.8)\",\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    fill: \"none\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 98,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n    x1: \"28\",\n    y1: \"16\",\n    x2: \"28\",\n    y2: \"10\",\n    stroke: \"rgba(255,255,255,0.7)\",\n    strokeWidth: \"2.5\",\n    strokeLinecap: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 107,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n    x1: \"36\",\n    y1: \"16\",\n    x2: \"36\",\n    y2: \"10\",\n    stroke: \"rgba(255,255,255,0.7)\",\n    strokeWidth: \"2.5\",\n    strokeLinecap: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 108,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n    cx: \"28\",\n    cy: \"8\",\n    r: \"2.5\",\n    fill: \"rgba(255,255,255,0.9)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 111,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n    cx: \"36\",\n    cy: \"8\",\n    r: \"2.5\",\n    fill: \"rgba(255,255,255,0.9)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 112,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(motion.circle, {\n    cx: \"28\",\n    cy: \"8\",\n    r: \"1.5\",\n    fill: \"rgba(34, 197, 94, 0.8)\",\n    animate: animated ? {\n      opacity: [0.8, 1, 0.8],\n      scale: [1, 1.1, 1]\n    } : {},\n    transition: animated ? {\n      duration: 1.5,\n      repeat: Infinity,\n      ease: \"easeInOut\"\n    } : {}\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 113,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(motion.circle, {\n    cx: \"36\",\n    cy: \"8\",\n    r: \"1.5\",\n    fill: \"rgba(34, 197, 94, 0.8)\",\n    animate: animated ? {\n      opacity: [0.8, 1, 0.8],\n      scale: [1, 1.1, 1]\n    } : {},\n    transition: animated ? {\n      duration: 1.5,\n      repeat: Infinity,\n      ease: \"easeInOut\",\n      delay: 0.3\n    } : {}\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 128,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n    x: \"22\",\n    y: \"40\",\n    width: \"20\",\n    height: \"16\",\n    rx: \"4\",\n    fill: \"currentColor\",\n    stroke: \"rgba(255,255,255,0.3)\",\n    strokeWidth: \"1.5\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 146,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n    x: \"24\",\n    y: \"42\",\n    width: \"16\",\n    height: \"12\",\n    rx: \"3\",\n    fill: \"rgba(255,255,255,0.1)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 158,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n    x: \"14\",\n    y: \"44\",\n    width: \"8\",\n    height: \"6\",\n    rx: \"3\",\n    fill: \"currentColor\",\n    stroke: \"rgba(255,255,255,0.2)\",\n    strokeWidth: \"1\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 168,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n    x: \"42\",\n    y: \"44\",\n    width: \"8\",\n    height: \"6\",\n    rx: \"3\",\n    fill: \"currentColor\",\n    stroke: \"rgba(255,255,255,0.2)\",\n    strokeWidth: \"1\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 169,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n    cx: \"32\",\n    cy: \"46\",\n    r: \"1.5\",\n    fill: \"rgba(255,255,255,0.6)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 172,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n    x: \"29\",\n    y: \"50\",\n    width: \"6\",\n    height: \"1\",\n    rx: \"0.5\",\n    fill: \"rgba(255,255,255,0.5)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 173,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n    x: \"30\",\n    y: \"52\",\n    width: \"4\",\n    height: \"1\",\n    rx: \"0.5\",\n    fill: \"rgba(255,255,255,0.4)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 174,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M 24 20 L 26 20 L 26 22\",\n    stroke: \"rgba(255,255,255,0.3)\",\n    strokeWidth: \"1\",\n    fill: \"none\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 177,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M 40 20 L 38 20 L 38 22\",\n    stroke: \"rgba(255,255,255,0.3)\",\n    strokeWidth: \"1\",\n    fill: \"none\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 178,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 21,\n  columnNumber: 3\n}, this);\n_c = AIAssistantIcon;\nconst AIAssistant = ({\n  context = 'student'\n}) => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const [isMinimized, setIsMinimized] = useState(false);\n  const getWelcomeMessage = () => {\n    switch (context) {\n      case 'login':\n        return 'مرحباً! أنا المساعد الذكي لمنصة ALaa Abd Hamied. هل تحتاج مساعدة في تسجيل الدخول أو لديك أسئلة حول المنصة؟';\n      case 'admin':\n        return 'مرحباً أيها المدير! أنا هنا لمساعدتك في إدارة المنصة والإجابة على أي استفسارات تقنية.';\n      default:\n        return 'مرحباً عزيزي الطالب! أنا المساعد الذكي لمنصة ALaa Abd Hamied. كيف يمكنني مساعدتك في رحلتك التعليمية اليوم؟';\n    }\n  };\n  const [messages, setMessages] = useState([{\n    id: '1',\n    type: 'assistant',\n    content: getWelcomeMessage(),\n    timestamp: new Date()\n  }]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const messagesEndRef = useRef(null);\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  const getQuickReplies = () => {\n    switch (context) {\n      case 'login':\n        return ['كيف أسجل الدخول كطالب؟', 'نسيت كود الوصول', 'مشكلة في تسجيل الدخول', 'ما هي متطلبات النظام؟', 'كيف أحصل على حساب؟', 'التواصل مع الدعم'];\n      case 'admin':\n        return ['كيف أضيف كورس جديد؟', 'إدارة الطلاب', 'إنشاء اختبارات', 'تقارير الأداء', 'إعدادات النظام', 'النسخ الاحتياطي'];\n      default:\n        return ['كيف أشاهد الكورسات؟', 'كيف أؤدي الاختبارات؟', 'كيف أحصل على الشهادة؟', 'مشكلة في تشغيل الفيديو', 'عرض تقدمي', 'التواصل مع الدعم'];\n    }\n  };\n  const quickReplies = getQuickReplies();\n  const getAIResponse = userMessage => {\n    const message = userMessage.toLowerCase();\n\n    // Login context responses\n    if (context === 'login') {\n      if (message.includes('دخول') || message.includes('تسجيل')) {\n        return 'للطلاب: استخدم كود الوصول الخاص بك في خانة \"كود الوصول\". للمدراء: استخدم البريد الإلكتروني وكلمة المرور. تأكد من اختيار النوع الصحيح من أعلى الصفحة.';\n      }\n      if (message.includes('كود') || message.includes('نسيت')) {\n        return 'إذا نسيت كود الوصول، تواصل مع المدير أو فريق الدعم للحصول على كود جديد. كود الوصول يتكون من أرقام وحروف ويُعطى لك عند التسجيل.';\n      }\n      if (message.includes('حساب') || message.includes('تسجيل جديد')) {\n        return 'للحصول على حساب جديد، تواصل مع إدارة المنصة. سيتم إنشاء حساب لك وإرسال كود الوصول الخاص بك.';\n      }\n      if (message.includes('متطلبات') || message.includes('نظام')) {\n        return 'المنصة تعمل على جميع المتصفحات الحديثة (Chrome, Firefox, Safari, Edge). تحتاج اتصال إنترنت مستقر لمشاهدة الفيديوهات.';\n      }\n    }\n    if (message.includes('كورس') || message.includes('مشاهدة')) {\n      return context === 'login' ? 'بعد تسجيل الدخول، ستجد جميع الكورسات المتاحة لك في لوحة التحكم الرئيسية.' : 'لمشاهدة الكورسات، اذهب إلى قسم \"كورساتي\" من القائمة الرئيسية. ستجد جميع الكورسات المتاحة لك مع إمكانية متابعة التقدم.';\n    }\n    if (message.includes('اختبار') || message.includes('امتحان')) {\n      return 'يمكنك الوصول للاختبارات من داخل كل كورس. بعد مشاهدة الفيديوهات المطلوبة، ستظهر لك الاختبارات المتاحة. تأكد من قراءة التعليمات قبل البدء.';\n    }\n    if (message.includes('شهادة')) {\n      return 'للحصول على الشهادة، يجب إكمال جميع فيديوهات الكورس واجتياز الاختبارات بنجاح. ستظهر الشهادة تلقائياً في قسم \"شهاداتي\".';\n    }\n    if (message.includes('فيديو') || message.includes('تشغيل')) {\n      return 'إذا واجهت مشكلة في تشغيل الفيديو، تأكد من اتصالك بالإنترنت وحاول تحديث الصفحة. إذا استمرت المشكلة، جرب متصفح آخر.';\n    }\n    if (message.includes('مرور') || message.includes('كلمة')) {\n      return 'لا يمكن تغيير كلمة المرور للطلاب حيث يتم الدخول بكود الوصول. إذا نسيت الكود، تواصل مع المدير للحصول على كود جديد.';\n    }\n    if (message.includes('دعم') || message.includes('مساعدة') || message.includes('مشكلة')) {\n      return 'يمكنك التواصل مع فريق الدعم من خلال النقر على \"اتصل بنا\" في أسفل الصفحة، أو إرسال رسالة مباشرة للمدير.';\n    }\n    if (message.includes('مرحبا') || message.includes('السلام') || message.includes('أهلا')) {\n      return 'أهلاً وسهلاً بك! أنا هنا لمساعدتك في أي استفسار حول المنصة. ما الذي تحتاج مساعدة فيه؟';\n    }\n    if (message.includes('شكرا') || message.includes('شكراً')) {\n      return 'العفو! أتمنى أن أكون قد ساعدتك. لا تتردد في سؤالي عن أي شيء آخر.';\n    }\n    return 'أعتذر، لم أفهم سؤالك بوضوح. يمكنك تجربة إحدى الأسئلة الشائعة أدناه، أو إعادة صياغة سؤالك بطريقة أخرى.';\n  };\n  const handleSendMessage = async () => {\n    if (!inputMessage.trim()) return;\n    const userMessage = {\n      id: Date.now().toString(),\n      type: 'user',\n      content: inputMessage,\n      timestamp: new Date()\n    };\n    setMessages(prev => [...prev, userMessage]);\n    setInputMessage('');\n    setIsTyping(true);\n\n    // Simulate AI thinking time\n    setTimeout(() => {\n      const aiResponse = {\n        id: (Date.now() + 1).toString(),\n        type: 'assistant',\n        content: getAIResponse(inputMessage),\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, aiResponse]);\n      setIsTyping(false);\n    }, 1000 + Math.random() * 1000);\n  };\n  const handleQuickReply = reply => {\n    setInputMessage(reply);\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: !isOpen && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          scale: 0,\n          opacity: 0\n        },\n        animate: {\n          scale: 1,\n          opacity: 1\n        },\n        exit: {\n          scale: 0,\n          opacity: 0\n        },\n        className: \"fixed bottom-6 left-6 z-50\",\n        children: [/*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.1,\n            rotate: 5\n          },\n          whileTap: {\n            scale: 0.9\n          },\n          onClick: () => setIsOpen(true),\n          className: \"relative w-16 h-16 bg-gradient-to-br from-yellow-400 via-yellow-500 to-amber-600 text-white rounded-full shadow-2xl hover:shadow-yellow-500/25 transition-all duration-300 flex items-center justify-center group overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-gradient-to-br from-yellow-300 to-amber-500 rounded-full opacity-0 group-hover:opacity-20 transition-opacity duration-300\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 rounded-full\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              animate: {\n                rotate: 360\n              },\n              transition: {\n                duration: 8,\n                repeat: Infinity,\n                ease: \"linear\"\n              },\n              className: \"absolute inset-2 border-2 border-yellow-300/30 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              animate: {\n                rotate: -360\n              },\n              transition: {\n                duration: 12,\n                repeat: Infinity,\n                ease: \"linear\"\n              },\n              className: \"absolute inset-1 border border-yellow-200/20 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative z-10 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(AIAssistantIcon, {\n              className: \"w-8 h-8 text-white drop-shadow-lg\",\n              animated: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            animate: {\n              scale: [1, 1.2, 1],\n              opacity: [0.7, 1, 0.7]\n            },\n            transition: {\n              duration: 2,\n              repeat: Infinity,\n              ease: \"easeInOut\"\n            },\n            className: \"absolute -top-1 -right-1 w-3 h-3 bg-yellow-300 rounded-full shadow-lg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            animate: {\n              scale: [1, 1.3, 1],\n              opacity: [0.5, 1, 0.5]\n            },\n            transition: {\n              duration: 2.5,\n              repeat: Infinity,\n              ease: \"easeInOut\",\n              delay: 0.5\n            },\n            className: \"absolute -bottom-1 -left-1 w-2 h-2 bg-amber-300 rounded-full shadow-lg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            animate: {\n              scale: [1, 1.1, 1],\n              opacity: [0.6, 1, 0.6]\n            },\n            transition: {\n              duration: 3,\n              repeat: Infinity,\n              ease: \"easeInOut\",\n              delay: 1\n            },\n            className: \"absolute top-1 -left-2 w-1.5 h-1.5 bg-yellow-200 rounded-full shadow-lg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full animate-pulse border-2 border-white shadow-lg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: -10\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            delay: 2\n          },\n          className: \"absolute right-20 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-gray-900 to-gray-800 text-white px-4 py-2 rounded-lg text-sm whitespace-nowrap shadow-xl border border-gray-700\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 space-x-reverse\",\n            children: [/*#__PURE__*/_jsxDEV(AIAssistantIcon, {\n              className: \"w-4 h-4 text-yellow-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u0627\\u0644\\u0645\\u0633\\u0627\\u0639\\u062F \\u0627\\u0644\\u0630\\u0643\\u064A - \\u0627\\u0636\\u063A\\u0637 \\u0644\\u0644\\u0645\\u0633\\u0627\\u0639\\u062F\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute left-0 top-1/2 transform -translate-y-1/2 translate-x-1 w-2 h-2 bg-gray-900 rotate-45\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 355,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: isOpen && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 100,\n          scale: 0.9\n        },\n        animate: {\n          opacity: 1,\n          y: 0,\n          scale: 1,\n          height: isMinimized ? 60 : 500\n        },\n        exit: {\n          opacity: 0,\n          y: 100,\n          scale: 0.9\n        },\n        className: \"fixed bottom-6 left-6 w-80 bg-white rounded-xl shadow-2xl border border-gray-200 z-50 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-yellow-500 via-amber-500 to-yellow-600 text-white p-4 flex items-center justify-between relative overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 opacity-10\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute top-2 left-4 w-2 h-2 bg-white rounded-full animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute top-6 right-8 w-1 h-1 bg-white rounded-full animate-pulse\",\n              style: {\n                animationDelay: '0.5s'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute bottom-3 left-12 w-1.5 h-1.5 bg-white rounded-full animate-pulse\",\n              style: {\n                animationDelay: '1s'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center relative z-10\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-10 h-10 bg-gradient-to-br from-white/30 to-white/10 rounded-full flex items-center justify-center ml-3 backdrop-blur-sm border border-white/20\",\n              children: /*#__PURE__*/_jsxDEV(AIAssistantIcon, {\n                className: \"w-6 h-6 text-white drop-shadow-lg\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-bold text-sm flex items-center\",\n                children: [\"\\u0627\\u0644\\u0645\\u0633\\u0627\\u0639\\u062F \\u0627\\u0644\\u0630\\u0643\\u064A\", /*#__PURE__*/_jsxDEV(StarIcon, {\n                  className: \"w-3 h-3 mr-1 text-yellow-200\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs opacity-90 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-2 h-2 bg-green-400 rounded-full mr-1 animate-pulse\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 21\n                }, this), \"\\u0645\\u062A\\u0627\\u062D \\u0627\\u0644\\u0622\\u0646 \\u0644\\u0644\\u0645\\u0633\\u0627\\u0639\\u062F\\u0629\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 space-x-reverse\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsMinimized(!isMinimized),\n              className: \"p-1 hover:bg-white hover:bg-opacity-20 rounded\",\n              children: /*#__PURE__*/_jsxDEV(MinusIcon, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsOpen(false),\n              className: \"p-1 hover:bg-white hover:bg-opacity-20 rounded\",\n              children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 13\n        }, this), !isMinimized && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-80 overflow-y-auto p-4 space-y-4\",\n            children: [messages.map(message => /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 10\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              className: `flex ${message.type === 'user' ? 'justify-start' : 'justify-end'}`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `\n                        max-w-xs px-4 py-3 rounded-lg text-sm shadow-sm\n                        ${message.type === 'user' ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-br-sm' : 'bg-gradient-to-r from-yellow-50 to-amber-50 text-gray-800 rounded-bl-sm border border-yellow-200/50'}\n                      `,\n                children: message.content\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 23\n              }, this)\n            }, message.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 21\n            }, this)), isTyping && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0\n              },\n              animate: {\n                opacity: 1\n              },\n              className: \"flex justify-end\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-yellow-50 to-amber-50 px-4 py-3 rounded-lg rounded-bl-sm border border-yellow-200/50 shadow-sm\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-1 items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(AIAssistantIcon, {\n                    className: \"w-4 h-4 text-yellow-600 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 541,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-yellow-500 rounded-full animate-bounce\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 542,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-amber-500 rounded-full animate-bounce\",\n                    style: {\n                      animationDelay: '0.1s'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 543,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-yellow-600 rounded-full animate-bounce\",\n                    style: {\n                      animationDelay: '0.2s'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 544,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-xs text-gray-600 mr-2\",\n                    children: \"\\u064A\\u0643\\u062A\\u0628...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 545,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 540,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 539,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              ref: messagesEndRef\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 17\n          }, this), messages.length <= 2 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-4 pb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500 mb-2\",\n              children: \"\\u0623\\u0633\\u0626\\u0644\\u0629 \\u0634\\u0627\\u0626\\u0639\\u0629:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-wrap gap-1\",\n              children: quickReplies.slice(0, 3).map(reply => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleQuickReply(reply),\n                className: \"text-xs bg-gradient-to-r from-yellow-100 to-amber-100 hover:from-yellow-200 hover:to-amber-200 text-gray-700 px-3 py-1.5 rounded-full transition-all duration-200 border border-yellow-200/50 hover:border-yellow-300 shadow-sm hover:shadow-md\",\n                children: reply\n              }, reply, false, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 25\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 556,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 border-t border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 space-x-reverse\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: inputMessage,\n                onChange: e => setInputMessage(e.target.value),\n                onKeyPress: handleKeyPress,\n                placeholder: \"\\u0627\\u0643\\u062A\\u0628 \\u0631\\u0633\\u0627\\u0644\\u062A\\u0643...\",\n                className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm\",\n                disabled: isTyping\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 575,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                onClick: handleSendMessage,\n                disabled: !inputMessage.trim() || isTyping,\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                className: \"p-2.5 bg-gradient-to-r from-yellow-500 to-amber-600 text-white rounded-lg hover:from-yellow-600 hover:to-amber-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl disabled:hover:scale-100\",\n                children: /*#__PURE__*/_jsxDEV(PaperAirplaneIcon, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 591,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 584,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 574,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 573,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 457,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 455,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(AIAssistant, \"czjJEpDcIy5UgZYwWPaaoufKVMM=\");\n_c2 = AIAssistant;\nexport default AIAssistant;\nvar _c, _c2;\n$RefreshReg$(_c, \"AIAssistantIcon\");\n$RefreshReg$(_c2, \"AIAssistant\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "motion", "AnimatePresence", "XMarkIcon", "PaperAirplaneIcon", "MinusIcon", "StarIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AIAssistantIcon", "className", "animated", "svg", "viewBox", "fill", "xmlns", "animate", "scale", "transition", "duration", "repeat", "Infinity", "ease", "children", "x", "y", "width", "height", "rx", "stroke", "strokeWidth", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "cx", "cy", "r", "circle", "d", "strokeLinecap", "x1", "y1", "x2", "y2", "opacity", "delay", "_c", "AIAssistant", "context", "_s", "isOpen", "setIsOpen", "isMinimized", "setIsMinimized", "getWelcomeMessage", "messages", "setMessages", "id", "type", "content", "timestamp", "Date", "inputMessage", "setInputMessage", "isTyping", "setIsTyping", "messagesEndRef", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "getQuickReplies", "quickReplies", "getAIResponse", "userMessage", "message", "toLowerCase", "includes", "handleSendMessage", "trim", "now", "toString", "prev", "setTimeout", "aiResponse", "Math", "random", "handleQuickReply", "reply", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "div", "initial", "exit", "button", "whileHover", "rotate", "whileTap", "onClick", "style", "animationDelay", "map", "ref", "length", "slice", "value", "onChange", "target", "onKeyPress", "placeholder", "disabled", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/AIAssistant/AIAssistant.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  ChatBubbleLeftRightIcon,\n  XMarkIcon,\n  PaperAirplaneIcon,\n  SparklesIcon,\n  MinusIcon,\n  StarIcon\n} from '@heroicons/react/24/outline';\nimport { SparklesIcon as SparklesIconSolid } from '@heroicons/react/24/solid';\n\n// Types\nimport { ChatMessage } from '../../types';\n\n// Custom AI Assistant Icon Component with Animation\nconst AIAssistantIcon: React.FC<{ className?: string; animated?: boolean }> = ({\n  className = \"w-7 h-7\",\n  animated = false\n}) => (\n  <motion.svg\n    className={className}\n    viewBox=\"0 0 64 64\"\n    fill=\"none\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n    animate={animated ? {\n      scale: [1, 1.05, 1],\n    } : {}}\n    transition={animated ? {\n      duration: 2,\n      repeat: Infinity,\n      ease: \"easeInOut\"\n    } : {}}\n  >\n    {/* Robot Head - Main */}\n    <rect\n      x=\"18\"\n      y=\"16\"\n      width=\"28\"\n      height=\"24\"\n      rx=\"8\"\n      fill=\"currentColor\"\n      stroke=\"rgba(255,255,255,0.4)\"\n      strokeWidth=\"1.5\"\n    />\n\n    {/* Robot Head - Inner glow */}\n    <rect\n      x=\"20\"\n      y=\"18\"\n      width=\"24\"\n      height=\"20\"\n      rx=\"6\"\n      fill=\"rgba(255,255,255,0.1)\"\n    />\n\n    {/* Robot Eyes - Outer glow */}\n    <circle cx=\"26\" cy=\"26\" r=\"4\" fill=\"rgba(255,255,255,0.3)\" />\n    <circle cx=\"38\" cy=\"26\" r=\"4\" fill=\"rgba(255,255,255,0.3)\" />\n\n    {/* Robot Eyes - Main */}\n    <circle cx=\"26\" cy=\"26\" r=\"3\" fill=\"rgba(255,255,255,0.95)\" />\n    <circle cx=\"38\" cy=\"26\" r=\"3\" fill=\"rgba(255,255,255,0.95)\" />\n\n    {/* Robot Eyes - Pupils with glow */}\n    <motion.circle\n      cx=\"26\"\n      cy=\"26\"\n      r=\"1.5\"\n      fill=\"rgba(59, 130, 246, 0.9)\"\n      animate={animated ? {\n        fill: [\"rgba(59, 130, 246, 0.9)\", \"rgba(34, 197, 94, 0.9)\", \"rgba(59, 130, 246, 0.9)\"]\n      } : {}}\n      transition={animated ? {\n        duration: 3,\n        repeat: Infinity,\n        ease: \"easeInOut\"\n      } : {}}\n    />\n    <motion.circle\n      cx=\"38\"\n      cy=\"26\"\n      r=\"1.5\"\n      fill=\"rgba(59, 130, 246, 0.9)\"\n      animate={animated ? {\n        fill: [\"rgba(59, 130, 246, 0.9)\", \"rgba(34, 197, 94, 0.9)\", \"rgba(59, 130, 246, 0.9)\"]\n      } : {}}\n      transition={animated ? {\n        duration: 3,\n        repeat: Infinity,\n        ease: \"easeInOut\"\n      } : {}}\n    />\n    <circle cx=\"26\" cy=\"25\" r=\"0.5\" fill=\"rgba(255,255,255,0.8)\" />\n    <circle cx=\"38\" cy=\"25\" r=\"0.5\" fill=\"rgba(255,255,255,0.8)\" />\n\n    {/* Robot Mouth - Smile */}\n    <path\n      d=\"M 28 32 Q 32 35 36 32\"\n      stroke=\"rgba(255,255,255,0.8)\"\n      strokeWidth=\"2\"\n      strokeLinecap=\"round\"\n      fill=\"none\"\n    />\n\n    {/* Robot Antennas */}\n    <line x1=\"28\" y1=\"16\" x2=\"28\" y2=\"10\" stroke=\"rgba(255,255,255,0.7)\" strokeWidth=\"2.5\" strokeLinecap=\"round\" />\n    <line x1=\"36\" y1=\"16\" x2=\"36\" y2=\"10\" stroke=\"rgba(255,255,255,0.7)\" strokeWidth=\"2.5\" strokeLinecap=\"round\" />\n\n    {/* Antenna Tips with glow */}\n    <circle cx=\"28\" cy=\"8\" r=\"2.5\" fill=\"rgba(255,255,255,0.9)\" />\n    <circle cx=\"36\" cy=\"8\" r=\"2.5\" fill=\"rgba(255,255,255,0.9)\" />\n    <motion.circle\n      cx=\"28\"\n      cy=\"8\"\n      r=\"1.5\"\n      fill=\"rgba(34, 197, 94, 0.8)\"\n      animate={animated ? {\n        opacity: [0.8, 1, 0.8],\n        scale: [1, 1.1, 1]\n      } : {}}\n      transition={animated ? {\n        duration: 1.5,\n        repeat: Infinity,\n        ease: \"easeInOut\"\n      } : {}}\n    />\n    <motion.circle\n      cx=\"36\"\n      cy=\"8\"\n      r=\"1.5\"\n      fill=\"rgba(34, 197, 94, 0.8)\"\n      animate={animated ? {\n        opacity: [0.8, 1, 0.8],\n        scale: [1, 1.1, 1]\n      } : {}}\n      transition={animated ? {\n        duration: 1.5,\n        repeat: Infinity,\n        ease: \"easeInOut\",\n        delay: 0.3\n      } : {}}\n    />\n\n    {/* Robot Body */}\n    <rect\n      x=\"22\"\n      y=\"40\"\n      width=\"20\"\n      height=\"16\"\n      rx=\"4\"\n      fill=\"currentColor\"\n      stroke=\"rgba(255,255,255,0.3)\"\n      strokeWidth=\"1.5\"\n    />\n\n    {/* Body Inner glow */}\n    <rect\n      x=\"24\"\n      y=\"42\"\n      width=\"16\"\n      height=\"12\"\n      rx=\"3\"\n      fill=\"rgba(255,255,255,0.1)\"\n    />\n\n    {/* Robot Arms */}\n    <rect x=\"14\" y=\"44\" width=\"8\" height=\"6\" rx=\"3\" fill=\"currentColor\" stroke=\"rgba(255,255,255,0.2)\" strokeWidth=\"1\" />\n    <rect x=\"42\" y=\"44\" width=\"8\" height=\"6\" rx=\"3\" fill=\"currentColor\" stroke=\"rgba(255,255,255,0.2)\" strokeWidth=\"1\" />\n\n    {/* Body Details */}\n    <circle cx=\"32\" cy=\"46\" r=\"1.5\" fill=\"rgba(255,255,255,0.6)\" />\n    <rect x=\"29\" y=\"50\" width=\"6\" height=\"1\" rx=\"0.5\" fill=\"rgba(255,255,255,0.5)\" />\n    <rect x=\"30\" y=\"52\" width=\"4\" height=\"1\" rx=\"0.5\" fill=\"rgba(255,255,255,0.4)\" />\n\n    {/* Decorative circuits */}\n    <path d=\"M 24 20 L 26 20 L 26 22\" stroke=\"rgba(255,255,255,0.3)\" strokeWidth=\"1\" fill=\"none\" />\n    <path d=\"M 40 20 L 38 20 L 38 22\" stroke=\"rgba(255,255,255,0.3)\" strokeWidth=\"1\" fill=\"none\" />\n  </motion.svg>\n);\n\n\n\ninterface AIAssistantProps {\n  context?: 'login' | 'student' | 'admin';\n}\n\nconst AIAssistant: React.FC<AIAssistantProps> = ({ context = 'student' }) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [isMinimized, setIsMinimized] = useState(false);\n\n  const getWelcomeMessage = () => {\n    switch (context) {\n      case 'login':\n        return 'مرحباً! أنا المساعد الذكي لمنصة ALaa Abd Hamied. هل تحتاج مساعدة في تسجيل الدخول أو لديك أسئلة حول المنصة؟';\n      case 'admin':\n        return 'مرحباً أيها المدير! أنا هنا لمساعدتك في إدارة المنصة والإجابة على أي استفسارات تقنية.';\n      default:\n        return 'مرحباً عزيزي الطالب! أنا المساعد الذكي لمنصة ALaa Abd Hamied. كيف يمكنني مساعدتك في رحلتك التعليمية اليوم؟';\n    }\n  };\n\n  const [messages, setMessages] = useState<ChatMessage[]>([\n    {\n      id: '1',\n      type: 'assistant',\n      content: getWelcomeMessage(),\n      timestamp: new Date()\n    }\n  ]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const getQuickReplies = () => {\n    switch (context) {\n      case 'login':\n        return [\n          'كيف أسجل الدخول كطالب؟',\n          'نسيت كود الوصول',\n          'مشكلة في تسجيل الدخول',\n          'ما هي متطلبات النظام؟',\n          'كيف أحصل على حساب؟',\n          'التواصل مع الدعم'\n        ];\n      case 'admin':\n        return [\n          'كيف أضيف كورس جديد؟',\n          'إدارة الطلاب',\n          'إنشاء اختبارات',\n          'تقارير الأداء',\n          'إعدادات النظام',\n          'النسخ الاحتياطي'\n        ];\n      default:\n        return [\n          'كيف أشاهد الكورسات؟',\n          'كيف أؤدي الاختبارات؟',\n          'كيف أحصل على الشهادة؟',\n          'مشكلة في تشغيل الفيديو',\n          'عرض تقدمي',\n          'التواصل مع الدعم'\n        ];\n    }\n  };\n\n  const quickReplies = getQuickReplies();\n\n  const getAIResponse = (userMessage: string): string => {\n    const message = userMessage.toLowerCase();\n\n    // Login context responses\n    if (context === 'login') {\n      if (message.includes('دخول') || message.includes('تسجيل')) {\n        return 'للطلاب: استخدم كود الوصول الخاص بك في خانة \"كود الوصول\". للمدراء: استخدم البريد الإلكتروني وكلمة المرور. تأكد من اختيار النوع الصحيح من أعلى الصفحة.';\n      }\n      if (message.includes('كود') || message.includes('نسيت')) {\n        return 'إذا نسيت كود الوصول، تواصل مع المدير أو فريق الدعم للحصول على كود جديد. كود الوصول يتكون من أرقام وحروف ويُعطى لك عند التسجيل.';\n      }\n      if (message.includes('حساب') || message.includes('تسجيل جديد')) {\n        return 'للحصول على حساب جديد، تواصل مع إدارة المنصة. سيتم إنشاء حساب لك وإرسال كود الوصول الخاص بك.';\n      }\n      if (message.includes('متطلبات') || message.includes('نظام')) {\n        return 'المنصة تعمل على جميع المتصفحات الحديثة (Chrome, Firefox, Safari, Edge). تحتاج اتصال إنترنت مستقر لمشاهدة الفيديوهات.';\n      }\n    }\n\n    if (message.includes('كورس') || message.includes('مشاهدة')) {\n      return context === 'login'\n        ? 'بعد تسجيل الدخول، ستجد جميع الكورسات المتاحة لك في لوحة التحكم الرئيسية.'\n        : 'لمشاهدة الكورسات، اذهب إلى قسم \"كورساتي\" من القائمة الرئيسية. ستجد جميع الكورسات المتاحة لك مع إمكانية متابعة التقدم.';\n    }\n    \n    if (message.includes('اختبار') || message.includes('امتحان')) {\n      return 'يمكنك الوصول للاختبارات من داخل كل كورس. بعد مشاهدة الفيديوهات المطلوبة، ستظهر لك الاختبارات المتاحة. تأكد من قراءة التعليمات قبل البدء.';\n    }\n    \n    if (message.includes('شهادة')) {\n      return 'للحصول على الشهادة، يجب إكمال جميع فيديوهات الكورس واجتياز الاختبارات بنجاح. ستظهر الشهادة تلقائياً في قسم \"شهاداتي\".';\n    }\n    \n    if (message.includes('فيديو') || message.includes('تشغيل')) {\n      return 'إذا واجهت مشكلة في تشغيل الفيديو، تأكد من اتصالك بالإنترنت وحاول تحديث الصفحة. إذا استمرت المشكلة، جرب متصفح آخر.';\n    }\n    \n    if (message.includes('مرور') || message.includes('كلمة')) {\n      return 'لا يمكن تغيير كلمة المرور للطلاب حيث يتم الدخول بكود الوصول. إذا نسيت الكود، تواصل مع المدير للحصول على كود جديد.';\n    }\n    \n    if (message.includes('دعم') || message.includes('مساعدة') || message.includes('مشكلة')) {\n      return 'يمكنك التواصل مع فريق الدعم من خلال النقر على \"اتصل بنا\" في أسفل الصفحة، أو إرسال رسالة مباشرة للمدير.';\n    }\n    \n    if (message.includes('مرحبا') || message.includes('السلام') || message.includes('أهلا')) {\n      return 'أهلاً وسهلاً بك! أنا هنا لمساعدتك في أي استفسار حول المنصة. ما الذي تحتاج مساعدة فيه؟';\n    }\n    \n    if (message.includes('شكرا') || message.includes('شكراً')) {\n      return 'العفو! أتمنى أن أكون قد ساعدتك. لا تتردد في سؤالي عن أي شيء آخر.';\n    }\n    \n    return 'أعتذر، لم أفهم سؤالك بوضوح. يمكنك تجربة إحدى الأسئلة الشائعة أدناه، أو إعادة صياغة سؤالك بطريقة أخرى.';\n  };\n\n  const handleSendMessage = async () => {\n    if (!inputMessage.trim()) return;\n\n    const userMessage: ChatMessage = {\n      id: Date.now().toString(),\n      type: 'user',\n      content: inputMessage,\n      timestamp: new Date()\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setInputMessage('');\n    setIsTyping(true);\n\n    // Simulate AI thinking time\n    setTimeout(() => {\n      const aiResponse: ChatMessage = {\n        id: (Date.now() + 1).toString(),\n        type: 'assistant',\n        content: getAIResponse(inputMessage),\n        timestamp: new Date()\n      };\n\n      setMessages(prev => [...prev, aiResponse]);\n      setIsTyping(false);\n    }, 1000 + Math.random() * 1000);\n  };\n\n  const handleQuickReply = (reply: string) => {\n    setInputMessage(reply);\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  return (\n    <>\n      {/* Chat Button */}\n      <AnimatePresence>\n        {!isOpen && (\n          <motion.div\n            initial={{ scale: 0, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            exit={{ scale: 0, opacity: 0 }}\n            className=\"fixed bottom-6 left-6 z-50\"\n          >\n            <motion.button\n              whileHover={{ scale: 1.1, rotate: 5 }}\n              whileTap={{ scale: 0.9 }}\n              onClick={() => setIsOpen(true)}\n              className=\"relative w-16 h-16 bg-gradient-to-br from-yellow-400 via-yellow-500 to-amber-600 text-white rounded-full shadow-2xl hover:shadow-yellow-500/25 transition-all duration-300 flex items-center justify-center group overflow-hidden\"\n            >\n              {/* Background glow effect */}\n              <div className=\"absolute inset-0 bg-gradient-to-br from-yellow-300 to-amber-500 rounded-full opacity-0 group-hover:opacity-20 transition-opacity duration-300\" />\n\n              {/* Sparkle animation background */}\n              <div className=\"absolute inset-0 rounded-full\">\n                <motion.div\n                  animate={{ rotate: 360 }}\n                  transition={{ duration: 8, repeat: Infinity, ease: \"linear\" }}\n                  className=\"absolute inset-2 border-2 border-yellow-300/30 rounded-full\"\n                />\n                <motion.div\n                  animate={{ rotate: -360 }}\n                  transition={{ duration: 12, repeat: Infinity, ease: \"linear\" }}\n                  className=\"absolute inset-1 border border-yellow-200/20 rounded-full\"\n                />\n              </div>\n\n              {/* Main icon */}\n              <div className=\"relative z-10 flex items-center justify-center\">\n                <AIAssistantIcon className=\"w-8 h-8 text-white drop-shadow-lg\" animated={true} />\n              </div>\n\n              {/* Floating sparkles */}\n              <motion.div\n                animate={{\n                  scale: [1, 1.2, 1],\n                  opacity: [0.7, 1, 0.7]\n                }}\n                transition={{\n                  duration: 2,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                }}\n                className=\"absolute -top-1 -right-1 w-3 h-3 bg-yellow-300 rounded-full shadow-lg\"\n              />\n\n              <motion.div\n                animate={{\n                  scale: [1, 1.3, 1],\n                  opacity: [0.5, 1, 0.5]\n                }}\n                transition={{\n                  duration: 2.5,\n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                  delay: 0.5\n                }}\n                className=\"absolute -bottom-1 -left-1 w-2 h-2 bg-amber-300 rounded-full shadow-lg\"\n              />\n\n              <motion.div\n                animate={{\n                  scale: [1, 1.1, 1],\n                  opacity: [0.6, 1, 0.6]\n                }}\n                transition={{\n                  duration: 3,\n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                  delay: 1\n                }}\n                className=\"absolute top-1 -left-2 w-1.5 h-1.5 bg-yellow-200 rounded-full shadow-lg\"\n              />\n\n              {/* Status indicator */}\n              <div className=\"absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full animate-pulse border-2 border-white shadow-lg\" />\n            </motion.button>\n\n            {/* Tooltip */}\n            <motion.div\n              initial={{ opacity: 0, x: -10 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: 2 }}\n              className=\"absolute right-20 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-gray-900 to-gray-800 text-white px-4 py-2 rounded-lg text-sm whitespace-nowrap shadow-xl border border-gray-700\"\n            >\n              <div className=\"flex items-center space-x-2 space-x-reverse\">\n                <AIAssistantIcon className=\"w-4 h-4 text-yellow-400\" />\n                <span>المساعد الذكي - اضغط للمساعدة</span>\n              </div>\n              <div className=\"absolute left-0 top-1/2 transform -translate-y-1/2 translate-x-1 w-2 h-2 bg-gray-900 rotate-45\" />\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Chat Window */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ opacity: 0, y: 100, scale: 0.9 }}\n            animate={{ \n              opacity: 1, \n              y: 0, \n              scale: 1,\n              height: isMinimized ? 60 : 500\n            }}\n            exit={{ opacity: 0, y: 100, scale: 0.9 }}\n            className=\"fixed bottom-6 left-6 w-80 bg-white rounded-xl shadow-2xl border border-gray-200 z-50 overflow-hidden\"\n          >\n            {/* Header */}\n            <div className=\"bg-gradient-to-r from-yellow-500 via-amber-500 to-yellow-600 text-white p-4 flex items-center justify-between relative overflow-hidden\">\n              {/* Background pattern */}\n              <div className=\"absolute inset-0 opacity-10\">\n                <div className=\"absolute top-2 left-4 w-2 h-2 bg-white rounded-full animate-pulse\" />\n                <div className=\"absolute top-6 right-8 w-1 h-1 bg-white rounded-full animate-pulse\" style={{ animationDelay: '0.5s' }} />\n                <div className=\"absolute bottom-3 left-12 w-1.5 h-1.5 bg-white rounded-full animate-pulse\" style={{ animationDelay: '1s' }} />\n              </div>\n\n              <div className=\"flex items-center relative z-10\">\n                <div className=\"w-10 h-10 bg-gradient-to-br from-white/30 to-white/10 rounded-full flex items-center justify-center ml-3 backdrop-blur-sm border border-white/20\">\n                  <AIAssistantIcon className=\"w-6 h-6 text-white drop-shadow-lg\" />\n                </div>\n                <div>\n                  <h3 className=\"font-bold text-sm flex items-center\">\n                    المساعد الذكي\n                    <StarIcon className=\"w-3 h-3 mr-1 text-yellow-200\" />\n                  </h3>\n                  <p className=\"text-xs opacity-90 flex items-center\">\n                    <div className=\"w-2 h-2 bg-green-400 rounded-full mr-1 animate-pulse\" />\n                    متاح الآن للمساعدة\n                  </p>\n                </div>\n              </div>\n              <div className=\"flex items-center space-x-2 space-x-reverse\">\n                <button\n                  onClick={() => setIsMinimized(!isMinimized)}\n                  className=\"p-1 hover:bg-white hover:bg-opacity-20 rounded\"\n                >\n                  <MinusIcon className=\"w-4 h-4\" />\n                </button>\n                <button\n                  onClick={() => setIsOpen(false)}\n                  className=\"p-1 hover:bg-white hover:bg-opacity-20 rounded\"\n                >\n                  <XMarkIcon className=\"w-4 h-4\" />\n                </button>\n              </div>\n            </div>\n\n            {/* Chat Content */}\n            {!isMinimized && (\n              <>\n                {/* Messages */}\n                <div className=\"h-80 overflow-y-auto p-4 space-y-4\">\n                  {messages.map((message) => (\n                    <motion.div\n                      key={message.id}\n                      initial={{ opacity: 0, y: 10 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      className={`flex ${message.type === 'user' ? 'justify-start' : 'justify-end'}`}\n                    >\n                      <div className={`\n                        max-w-xs px-4 py-3 rounded-lg text-sm shadow-sm\n                        ${message.type === 'user'\n                          ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-br-sm'\n                          : 'bg-gradient-to-r from-yellow-50 to-amber-50 text-gray-800 rounded-bl-sm border border-yellow-200/50'\n                        }\n                      `}>\n                        {message.content}\n                      </div>\n                    </motion.div>\n                  ))}\n\n                  {/* Typing Indicator */}\n                  {isTyping && (\n                    <motion.div\n                      initial={{ opacity: 0 }}\n                      animate={{ opacity: 1 }}\n                      className=\"flex justify-end\"\n                    >\n                      <div className=\"bg-gradient-to-r from-yellow-50 to-amber-50 px-4 py-3 rounded-lg rounded-bl-sm border border-yellow-200/50 shadow-sm\">\n                        <div className=\"flex space-x-1 items-center\">\n                          <AIAssistantIcon className=\"w-4 h-4 text-yellow-600 mr-2\" />\n                          <div className=\"w-2 h-2 bg-yellow-500 rounded-full animate-bounce\" />\n                          <div className=\"w-2 h-2 bg-amber-500 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }} />\n                          <div className=\"w-2 h-2 bg-yellow-600 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }} />\n                          <span className=\"text-xs text-gray-600 mr-2\">يكتب...</span>\n                        </div>\n                      </div>\n                    </motion.div>\n                  )}\n\n                  <div ref={messagesEndRef} />\n                </div>\n\n                {/* Quick Replies */}\n                {messages.length <= 2 && (\n                  <div className=\"px-4 pb-2\">\n                    <p className=\"text-xs text-gray-500 mb-2\">أسئلة شائعة:</p>\n                    <div className=\"flex flex-wrap gap-1\">\n                      {quickReplies.slice(0, 3).map((reply) => (\n                        <button\n                          key={reply}\n                          onClick={() => handleQuickReply(reply)}\n                          className=\"text-xs bg-gradient-to-r from-yellow-100 to-amber-100 hover:from-yellow-200 hover:to-amber-200 text-gray-700 px-3 py-1.5 rounded-full transition-all duration-200 border border-yellow-200/50 hover:border-yellow-300 shadow-sm hover:shadow-md\"\n                        >\n                          {reply}\n                        </button>\n                      ))}\n                    </div>\n                  </div>\n                )}\n\n                {/* Input */}\n                <div className=\"p-4 border-t border-gray-200\">\n                  <div className=\"flex items-center space-x-2 space-x-reverse\">\n                    <input\n                      type=\"text\"\n                      value={inputMessage}\n                      onChange={(e) => setInputMessage(e.target.value)}\n                      onKeyPress={handleKeyPress}\n                      placeholder=\"اكتب رسالتك...\"\n                      className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm\"\n                      disabled={isTyping}\n                    />\n                    <motion.button\n                      onClick={handleSendMessage}\n                      disabled={!inputMessage.trim() || isTyping}\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      className=\"p-2.5 bg-gradient-to-r from-yellow-500 to-amber-600 text-white rounded-lg hover:from-yellow-600 hover:to-amber-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl disabled:hover:scale-100\"\n                    >\n                      <PaperAirplaneIcon className=\"w-4 h-4\" />\n                    </motion.button>\n                  </div>\n                </div>\n              </>\n            )}\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </>\n  );\n};\n\nexport default AIAssistant;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAEEC,SAAS,EACTC,iBAAiB,EAEjBC,SAAS,EACTC,QAAQ,QACH,6BAA6B;;AAGpC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGA;AACA,MAAMC,eAAqE,GAAGA,CAAC;EAC7EC,SAAS,GAAG,SAAS;EACrBC,QAAQ,GAAG;AACb,CAAC,kBACCL,OAAA,CAACP,MAAM,CAACa,GAAG;EACTF,SAAS,EAAEA,SAAU;EACrBG,OAAO,EAAC,WAAW;EACnBC,IAAI,EAAC,MAAM;EACXC,KAAK,EAAC,4BAA4B;EAClCC,OAAO,EAAEL,QAAQ,GAAG;IAClBM,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;EACpB,CAAC,GAAG,CAAC,CAAE;EACPC,UAAU,EAAEP,QAAQ,GAAG;IACrBQ,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAEC,QAAQ;IAChBC,IAAI,EAAE;EACR,CAAC,GAAG,CAAC,CAAE;EAAAC,QAAA,gBAGPjB,OAAA;IACEkB,CAAC,EAAC,IAAI;IACNC,CAAC,EAAC,IAAI;IACNC,KAAK,EAAC,IAAI;IACVC,MAAM,EAAC,IAAI;IACXC,EAAE,EAAC,GAAG;IACNd,IAAI,EAAC,cAAc;IACnBe,MAAM,EAAC,uBAAuB;IAC9BC,WAAW,EAAC;EAAK;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClB,CAAC,eAGF5B,OAAA;IACEkB,CAAC,EAAC,IAAI;IACNC,CAAC,EAAC,IAAI;IACNC,KAAK,EAAC,IAAI;IACVC,MAAM,EAAC,IAAI;IACXC,EAAE,EAAC,GAAG;IACNd,IAAI,EAAC;EAAuB;IAAAiB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC7B,CAAC,eAGF5B,OAAA;IAAQ6B,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC,GAAG;IAACvB,IAAI,EAAC;EAAuB;IAAAiB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC7D5B,OAAA;IAAQ6B,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC,GAAG;IAACvB,IAAI,EAAC;EAAuB;IAAAiB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAG7D5B,OAAA;IAAQ6B,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC,GAAG;IAACvB,IAAI,EAAC;EAAwB;IAAAiB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC9D5B,OAAA;IAAQ6B,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC,GAAG;IAACvB,IAAI,EAAC;EAAwB;IAAAiB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAG9D5B,OAAA,CAACP,MAAM,CAACuC,MAAM;IACZH,EAAE,EAAC,IAAI;IACPC,EAAE,EAAC,IAAI;IACPC,CAAC,EAAC,KAAK;IACPvB,IAAI,EAAC,yBAAyB;IAC9BE,OAAO,EAAEL,QAAQ,GAAG;MAClBG,IAAI,EAAE,CAAC,yBAAyB,EAAE,wBAAwB,EAAE,yBAAyB;IACvF,CAAC,GAAG,CAAC,CAAE;IACPI,UAAU,EAAEP,QAAQ,GAAG;MACrBQ,QAAQ,EAAE,CAAC;MACXC,MAAM,EAAEC,QAAQ;MAChBC,IAAI,EAAE;IACR,CAAC,GAAG,CAAC;EAAE;IAAAS,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC,eACF5B,OAAA,CAACP,MAAM,CAACuC,MAAM;IACZH,EAAE,EAAC,IAAI;IACPC,EAAE,EAAC,IAAI;IACPC,CAAC,EAAC,KAAK;IACPvB,IAAI,EAAC,yBAAyB;IAC9BE,OAAO,EAAEL,QAAQ,GAAG;MAClBG,IAAI,EAAE,CAAC,yBAAyB,EAAE,wBAAwB,EAAE,yBAAyB;IACvF,CAAC,GAAG,CAAC,CAAE;IACPI,UAAU,EAAEP,QAAQ,GAAG;MACrBQ,QAAQ,EAAE,CAAC;MACXC,MAAM,EAAEC,QAAQ;MAChBC,IAAI,EAAE;IACR,CAAC,GAAG,CAAC;EAAE;IAAAS,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC,eACF5B,OAAA;IAAQ6B,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC,KAAK;IAACvB,IAAI,EAAC;EAAuB;IAAAiB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC/D5B,OAAA;IAAQ6B,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC,KAAK;IAACvB,IAAI,EAAC;EAAuB;IAAAiB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAG/D5B,OAAA;IACEiC,CAAC,EAAC,uBAAuB;IACzBV,MAAM,EAAC,uBAAuB;IAC9BC,WAAW,EAAC,GAAG;IACfU,aAAa,EAAC,OAAO;IACrB1B,IAAI,EAAC;EAAM;IAAAiB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC,eAGF5B,OAAA;IAAMmC,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACf,MAAM,EAAC,uBAAuB;IAACC,WAAW,EAAC,KAAK;IAACU,aAAa,EAAC;EAAO;IAAAT,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC/G5B,OAAA;IAAMmC,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACf,MAAM,EAAC,uBAAuB;IAACC,WAAW,EAAC,KAAK;IAACU,aAAa,EAAC;EAAO;IAAAT,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAG/G5B,OAAA;IAAQ6B,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,GAAG;IAACC,CAAC,EAAC,KAAK;IAACvB,IAAI,EAAC;EAAuB;IAAAiB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC9D5B,OAAA;IAAQ6B,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,GAAG;IAACC,CAAC,EAAC,KAAK;IAACvB,IAAI,EAAC;EAAuB;IAAAiB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC9D5B,OAAA,CAACP,MAAM,CAACuC,MAAM;IACZH,EAAE,EAAC,IAAI;IACPC,EAAE,EAAC,GAAG;IACNC,CAAC,EAAC,KAAK;IACPvB,IAAI,EAAC,wBAAwB;IAC7BE,OAAO,EAAEL,QAAQ,GAAG;MAClBkC,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;MACtB5B,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;IACnB,CAAC,GAAG,CAAC,CAAE;IACPC,UAAU,EAAEP,QAAQ,GAAG;MACrBQ,QAAQ,EAAE,GAAG;MACbC,MAAM,EAAEC,QAAQ;MAChBC,IAAI,EAAE;IACR,CAAC,GAAG,CAAC;EAAE;IAAAS,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC,eACF5B,OAAA,CAACP,MAAM,CAACuC,MAAM;IACZH,EAAE,EAAC,IAAI;IACPC,EAAE,EAAC,GAAG;IACNC,CAAC,EAAC,KAAK;IACPvB,IAAI,EAAC,wBAAwB;IAC7BE,OAAO,EAAEL,QAAQ,GAAG;MAClBkC,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;MACtB5B,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;IACnB,CAAC,GAAG,CAAC,CAAE;IACPC,UAAU,EAAEP,QAAQ,GAAG;MACrBQ,QAAQ,EAAE,GAAG;MACbC,MAAM,EAAEC,QAAQ;MAChBC,IAAI,EAAE,WAAW;MACjBwB,KAAK,EAAE;IACT,CAAC,GAAG,CAAC;EAAE;IAAAf,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC,eAGF5B,OAAA;IACEkB,CAAC,EAAC,IAAI;IACNC,CAAC,EAAC,IAAI;IACNC,KAAK,EAAC,IAAI;IACVC,MAAM,EAAC,IAAI;IACXC,EAAE,EAAC,GAAG;IACNd,IAAI,EAAC,cAAc;IACnBe,MAAM,EAAC,uBAAuB;IAC9BC,WAAW,EAAC;EAAK;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClB,CAAC,eAGF5B,OAAA;IACEkB,CAAC,EAAC,IAAI;IACNC,CAAC,EAAC,IAAI;IACNC,KAAK,EAAC,IAAI;IACVC,MAAM,EAAC,IAAI;IACXC,EAAE,EAAC,GAAG;IACNd,IAAI,EAAC;EAAuB;IAAAiB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC7B,CAAC,eAGF5B,OAAA;IAAMkB,CAAC,EAAC,IAAI;IAACC,CAAC,EAAC,IAAI;IAACC,KAAK,EAAC,GAAG;IAACC,MAAM,EAAC,GAAG;IAACC,EAAE,EAAC,GAAG;IAACd,IAAI,EAAC,cAAc;IAACe,MAAM,EAAC,uBAAuB;IAACC,WAAW,EAAC;EAAG;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACrH5B,OAAA;IAAMkB,CAAC,EAAC,IAAI;IAACC,CAAC,EAAC,IAAI;IAACC,KAAK,EAAC,GAAG;IAACC,MAAM,EAAC,GAAG;IAACC,EAAE,EAAC,GAAG;IAACd,IAAI,EAAC,cAAc;IAACe,MAAM,EAAC,uBAAuB;IAACC,WAAW,EAAC;EAAG;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAGrH5B,OAAA;IAAQ6B,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC,KAAK;IAACvB,IAAI,EAAC;EAAuB;IAAAiB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC/D5B,OAAA;IAAMkB,CAAC,EAAC,IAAI;IAACC,CAAC,EAAC,IAAI;IAACC,KAAK,EAAC,GAAG;IAACC,MAAM,EAAC,GAAG;IAACC,EAAE,EAAC,KAAK;IAACd,IAAI,EAAC;EAAuB;IAAAiB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACjF5B,OAAA;IAAMkB,CAAC,EAAC,IAAI;IAACC,CAAC,EAAC,IAAI;IAACC,KAAK,EAAC,GAAG;IAACC,MAAM,EAAC,GAAG;IAACC,EAAE,EAAC,KAAK;IAACd,IAAI,EAAC;EAAuB;IAAAiB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAGjF5B,OAAA;IAAMiC,CAAC,EAAC,yBAAyB;IAACV,MAAM,EAAC,uBAAuB;IAACC,WAAW,EAAC,GAAG;IAAChB,IAAI,EAAC;EAAM;IAAAiB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC/F5B,OAAA;IAAMiC,CAAC,EAAC,yBAAyB;IAACV,MAAM,EAAC,uBAAuB;IAACC,WAAW,EAAC,GAAG;IAAChB,IAAI,EAAC;EAAM;IAAAiB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACrF,CACb;AAACa,EAAA,GAnKItC,eAAqE;AA2K3E,MAAMuC,WAAuC,GAAGA,CAAC;EAAEC,OAAO,GAAG;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC3E,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACyD,WAAW,EAAEC,cAAc,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAM2D,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQN,OAAO;MACb,KAAK,OAAO;QACV,OAAO,4GAA4G;MACrH,KAAK,OAAO;QACV,OAAO,uFAAuF;MAChG;QACE,OAAO,4GAA4G;IACvH;EACF,CAAC;EAED,MAAM,CAACO,QAAQ,EAAEC,WAAW,CAAC,GAAG7D,QAAQ,CAAgB,CACtD;IACE8D,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,WAAW;IACjBC,OAAO,EAAEL,iBAAiB,CAAC,CAAC;IAC5BM,SAAS,EAAE,IAAIC,IAAI,CAAC;EACtB,CAAC,CACF,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACqE,QAAQ,EAAEC,WAAW,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAMuE,cAAc,GAAGtE,MAAM,CAAiB,IAAI,CAAC;EAEnD,MAAMuE,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAF,cAAc,CAACG,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAED1E,SAAS,CAAC,MAAM;IACdsE,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACZ,QAAQ,CAAC,CAAC;EAEd,MAAMiB,eAAe,GAAGA,CAAA,KAAM;IAC5B,QAAQxB,OAAO;MACb,KAAK,OAAO;QACV,OAAO,CACL,wBAAwB,EACxB,iBAAiB,EACjB,uBAAuB,EACvB,uBAAuB,EACvB,oBAAoB,EACpB,kBAAkB,CACnB;MACH,KAAK,OAAO;QACV,OAAO,CACL,qBAAqB,EACrB,cAAc,EACd,gBAAgB,EAChB,eAAe,EACf,gBAAgB,EAChB,iBAAiB,CAClB;MACH;QACE,OAAO,CACL,qBAAqB,EACrB,sBAAsB,EACtB,uBAAuB,EACvB,wBAAwB,EACxB,WAAW,EACX,kBAAkB,CACnB;IACL;EACF,CAAC;EAED,MAAMyB,YAAY,GAAGD,eAAe,CAAC,CAAC;EAEtC,MAAME,aAAa,GAAIC,WAAmB,IAAa;IACrD,MAAMC,OAAO,GAAGD,WAAW,CAACE,WAAW,CAAC,CAAC;;IAEzC;IACA,IAAI7B,OAAO,KAAK,OAAO,EAAE;MACvB,IAAI4B,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;QACzD,OAAO,sJAAsJ;MAC/J;MACA,IAAIF,OAAO,CAACE,QAAQ,CAAC,KAAK,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;QACvD,OAAO,gIAAgI;MACzI;MACA,IAAIF,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,YAAY,CAAC,EAAE;QAC9D,OAAO,6FAA6F;MACtG;MACA,IAAIF,OAAO,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC3D,OAAO,sHAAsH;MAC/H;IACF;IAEA,IAAIF,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAC1D,OAAO9B,OAAO,KAAK,OAAO,GACtB,0EAA0E,GAC1E,uHAAuH;IAC7H;IAEA,IAAI4B,OAAO,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAC5D,OAAO,0IAA0I;IACnJ;IAEA,IAAIF,OAAO,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC7B,OAAO,uHAAuH;IAChI;IAEA,IAAIF,OAAO,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC1D,OAAO,mHAAmH;IAC5H;IAEA,IAAIF,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MACxD,OAAO,mHAAmH;IAC5H;IAEA,IAAIF,OAAO,CAACE,QAAQ,CAAC,KAAK,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;MACtF,OAAO,wGAAwG;IACjH;IAEA,IAAIF,OAAO,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MACvF,OAAO,uFAAuF;IAChG;IAEA,IAAIF,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,OAAO,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;MACzD,OAAO,kEAAkE;IAC3E;IAEA,OAAO,uGAAuG;EAChH,CAAC;EAED,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACjB,YAAY,CAACkB,IAAI,CAAC,CAAC,EAAE;IAE1B,MAAML,WAAwB,GAAG;MAC/BlB,EAAE,EAAEI,IAAI,CAACoB,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MACzBxB,IAAI,EAAE,MAAM;MACZC,OAAO,EAAEG,YAAY;MACrBF,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC;IAEDL,WAAW,CAAC2B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAER,WAAW,CAAC,CAAC;IAC3CZ,eAAe,CAAC,EAAE,CAAC;IACnBE,WAAW,CAAC,IAAI,CAAC;;IAEjB;IACAmB,UAAU,CAAC,MAAM;MACf,MAAMC,UAAuB,GAAG;QAC9B5B,EAAE,EAAE,CAACI,IAAI,CAACoB,GAAG,CAAC,CAAC,GAAG,CAAC,EAAEC,QAAQ,CAAC,CAAC;QAC/BxB,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAEe,aAAa,CAACZ,YAAY,CAAC;QACpCF,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC;MAEDL,WAAW,CAAC2B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEE,UAAU,CAAC,CAAC;MAC1CpB,WAAW,CAAC,KAAK,CAAC;IACpB,CAAC,EAAE,IAAI,GAAGqB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC;EACjC,CAAC;EAED,MAAMC,gBAAgB,GAAIC,KAAa,IAAK;IAC1C1B,eAAe,CAAC0B,KAAK,CAAC;EACxB,CAAC;EAED,MAAMC,cAAc,GAAIC,CAAsB,IAAK;IACjD,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBf,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAED,oBACE1E,OAAA,CAAAE,SAAA;IAAAe,QAAA,gBAEEjB,OAAA,CAACN,eAAe;MAAAuB,QAAA,EACb,CAAC4B,MAAM,iBACN7C,OAAA,CAACP,MAAM,CAACiG,GAAG;QACTC,OAAO,EAAE;UAAEhF,KAAK,EAAE,CAAC;UAAE4B,OAAO,EAAE;QAAE,CAAE;QAClC7B,OAAO,EAAE;UAAEC,KAAK,EAAE,CAAC;UAAE4B,OAAO,EAAE;QAAE,CAAE;QAClCqD,IAAI,EAAE;UAAEjF,KAAK,EAAE,CAAC;UAAE4B,OAAO,EAAE;QAAE,CAAE;QAC/BnC,SAAS,EAAC,4BAA4B;QAAAa,QAAA,gBAEtCjB,OAAA,CAACP,MAAM,CAACoG,MAAM;UACZC,UAAU,EAAE;YAAEnF,KAAK,EAAE,GAAG;YAAEoF,MAAM,EAAE;UAAE,CAAE;UACtCC,QAAQ,EAAE;YAAErF,KAAK,EAAE;UAAI,CAAE;UACzBsF,OAAO,EAAEA,CAAA,KAAMnD,SAAS,CAAC,IAAI,CAAE;UAC/B1C,SAAS,EAAC,mOAAmO;UAAAa,QAAA,gBAG7OjB,OAAA;YAAKI,SAAS,EAAC;UAA+I;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGjK5B,OAAA;YAAKI,SAAS,EAAC,+BAA+B;YAAAa,QAAA,gBAC5CjB,OAAA,CAACP,MAAM,CAACiG,GAAG;cACThF,OAAO,EAAE;gBAAEqF,MAAM,EAAE;cAAI,CAAE;cACzBnF,UAAU,EAAE;gBAAEC,QAAQ,EAAE,CAAC;gBAAEC,MAAM,EAAEC,QAAQ;gBAAEC,IAAI,EAAE;cAAS,CAAE;cAC9DZ,SAAS,EAAC;YAA6D;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC,eACF5B,OAAA,CAACP,MAAM,CAACiG,GAAG;cACThF,OAAO,EAAE;gBAAEqF,MAAM,EAAE,CAAC;cAAI,CAAE;cAC1BnF,UAAU,EAAE;gBAAEC,QAAQ,EAAE,EAAE;gBAAEC,MAAM,EAAEC,QAAQ;gBAAEC,IAAI,EAAE;cAAS,CAAE;cAC/DZ,SAAS,EAAC;YAA2D;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN5B,OAAA;YAAKI,SAAS,EAAC,gDAAgD;YAAAa,QAAA,eAC7DjB,OAAA,CAACG,eAAe;cAACC,SAAS,EAAC,mCAAmC;cAACC,QAAQ,EAAE;YAAK;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC,eAGN5B,OAAA,CAACP,MAAM,CAACiG,GAAG;YACThF,OAAO,EAAE;cACPC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;cAClB4B,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;YACvB,CAAE;YACF3B,UAAU,EAAE;cACVC,QAAQ,EAAE,CAAC;cACXC,MAAM,EAAEC,QAAQ;cAChBC,IAAI,EAAE;YACR,CAAE;YACFZ,SAAS,EAAC;UAAuE;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF,CAAC,eAEF5B,OAAA,CAACP,MAAM,CAACiG,GAAG;YACThF,OAAO,EAAE;cACPC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;cAClB4B,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;YACvB,CAAE;YACF3B,UAAU,EAAE;cACVC,QAAQ,EAAE,GAAG;cACbC,MAAM,EAAEC,QAAQ;cAChBC,IAAI,EAAE,WAAW;cACjBwB,KAAK,EAAE;YACT,CAAE;YACFpC,SAAS,EAAC;UAAwE;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF,CAAC,eAEF5B,OAAA,CAACP,MAAM,CAACiG,GAAG;YACThF,OAAO,EAAE;cACPC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;cAClB4B,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;YACvB,CAAE;YACF3B,UAAU,EAAE;cACVC,QAAQ,EAAE,CAAC;cACXC,MAAM,EAAEC,QAAQ;cAChBC,IAAI,EAAE,WAAW;cACjBwB,KAAK,EAAE;YACT,CAAE;YACFpC,SAAS,EAAC;UAAyE;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC,eAGF5B,OAAA;YAAKI,SAAS,EAAC;UAA0G;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/G,CAAC,eAGhB5B,OAAA,CAACP,MAAM,CAACiG,GAAG;UACTC,OAAO,EAAE;YAAEpD,OAAO,EAAE,CAAC;YAAErB,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCR,OAAO,EAAE;YAAE6B,OAAO,EAAE,CAAC;YAAErB,CAAC,EAAE;UAAE,CAAE;UAC9BN,UAAU,EAAE;YAAE4B,KAAK,EAAE;UAAE,CAAE;UACzBpC,SAAS,EAAC,4LAA4L;UAAAa,QAAA,gBAEtMjB,OAAA;YAAKI,SAAS,EAAC,6CAA6C;YAAAa,QAAA,gBAC1DjB,OAAA,CAACG,eAAe;cAACC,SAAS,EAAC;YAAyB;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvD5B,OAAA;cAAAiB,QAAA,EAAM;YAA6B;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACN5B,OAAA;YAAKI,SAAS,EAAC;UAAgG;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACb;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC,eAGlB5B,OAAA,CAACN,eAAe;MAAAuB,QAAA,EACb4B,MAAM,iBACL7C,OAAA,CAACP,MAAM,CAACiG,GAAG;QACTC,OAAO,EAAE;UAAEpD,OAAO,EAAE,CAAC;UAAEpB,CAAC,EAAE,GAAG;UAAER,KAAK,EAAE;QAAI,CAAE;QAC5CD,OAAO,EAAE;UACP6B,OAAO,EAAE,CAAC;UACVpB,CAAC,EAAE,CAAC;UACJR,KAAK,EAAE,CAAC;UACRU,MAAM,EAAE0B,WAAW,GAAG,EAAE,GAAG;QAC7B,CAAE;QACF6C,IAAI,EAAE;UAAErD,OAAO,EAAE,CAAC;UAAEpB,CAAC,EAAE,GAAG;UAAER,KAAK,EAAE;QAAI,CAAE;QACzCP,SAAS,EAAC,uGAAuG;QAAAa,QAAA,gBAGjHjB,OAAA;UAAKI,SAAS,EAAC,wIAAwI;UAAAa,QAAA,gBAErJjB,OAAA;YAAKI,SAAS,EAAC,6BAA6B;YAAAa,QAAA,gBAC1CjB,OAAA;cAAKI,SAAS,EAAC;YAAmE;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrF5B,OAAA;cAAKI,SAAS,EAAC,oEAAoE;cAAC8F,KAAK,EAAE;gBAAEC,cAAc,EAAE;cAAO;YAAE;cAAA1E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzH5B,OAAA;cAAKI,SAAS,EAAC,2EAA2E;cAAC8F,KAAK,EAAE;gBAAEC,cAAc,EAAE;cAAK;YAAE;cAAA1E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3H,CAAC,eAEN5B,OAAA;YAAKI,SAAS,EAAC,iCAAiC;YAAAa,QAAA,gBAC9CjB,OAAA;cAAKI,SAAS,EAAC,kJAAkJ;cAAAa,QAAA,eAC/JjB,OAAA,CAACG,eAAe;gBAACC,SAAS,EAAC;cAAmC;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC,eACN5B,OAAA;cAAAiB,QAAA,gBACEjB,OAAA;gBAAII,SAAS,EAAC,qCAAqC;gBAAAa,QAAA,GAAC,2EAElD,eAAAjB,OAAA,CAACF,QAAQ;kBAACM,SAAS,EAAC;gBAA8B;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC,eACL5B,OAAA;gBAAGI,SAAS,EAAC,sCAAsC;gBAAAa,QAAA,gBACjDjB,OAAA;kBAAKI,SAAS,EAAC;gBAAsD;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,sGAE1E;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN5B,OAAA;YAAKI,SAAS,EAAC,6CAA6C;YAAAa,QAAA,gBAC1DjB,OAAA;cACEiG,OAAO,EAAEA,CAAA,KAAMjD,cAAc,CAAC,CAACD,WAAW,CAAE;cAC5C3C,SAAS,EAAC,gDAAgD;cAAAa,QAAA,eAE1DjB,OAAA,CAACH,SAAS;gBAACO,SAAS,EAAC;cAAS;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACT5B,OAAA;cACEiG,OAAO,EAAEA,CAAA,KAAMnD,SAAS,CAAC,KAAK,CAAE;cAChC1C,SAAS,EAAC,gDAAgD;cAAAa,QAAA,eAE1DjB,OAAA,CAACL,SAAS;gBAACS,SAAS,EAAC;cAAS;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL,CAACmB,WAAW,iBACX/C,OAAA,CAAAE,SAAA;UAAAe,QAAA,gBAEEjB,OAAA;YAAKI,SAAS,EAAC,oCAAoC;YAAAa,QAAA,GAChDiC,QAAQ,CAACkD,GAAG,CAAE7B,OAAO,iBACpBvE,OAAA,CAACP,MAAM,CAACiG,GAAG;cAETC,OAAO,EAAE;gBAAEpD,OAAO,EAAE,CAAC;gBAAEpB,CAAC,EAAE;cAAG,CAAE;cAC/BT,OAAO,EAAE;gBAAE6B,OAAO,EAAE,CAAC;gBAAEpB,CAAC,EAAE;cAAE,CAAE;cAC9Bf,SAAS,EAAE,QAAQmE,OAAO,CAAClB,IAAI,KAAK,MAAM,GAAG,eAAe,GAAG,aAAa,EAAG;cAAApC,QAAA,eAE/EjB,OAAA;gBAAKI,SAAS,EAAE;AACtC;AACA,0BAA0BmE,OAAO,CAAClB,IAAI,KAAK,MAAM,GACrB,qEAAqE,GACrE,qGAAqG;AACjI,uBACwB;gBAAApC,QAAA,EACCsD,OAAO,CAACjB;cAAO;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC,GAbD2C,OAAO,CAACnB,EAAE;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAcL,CACb,CAAC,EAGD+B,QAAQ,iBACP3D,OAAA,CAACP,MAAM,CAACiG,GAAG;cACTC,OAAO,EAAE;gBAAEpD,OAAO,EAAE;cAAE,CAAE;cACxB7B,OAAO,EAAE;gBAAE6B,OAAO,EAAE;cAAE,CAAE;cACxBnC,SAAS,EAAC,kBAAkB;cAAAa,QAAA,eAE5BjB,OAAA;gBAAKI,SAAS,EAAC,sHAAsH;gBAAAa,QAAA,eACnIjB,OAAA;kBAAKI,SAAS,EAAC,6BAA6B;kBAAAa,QAAA,gBAC1CjB,OAAA,CAACG,eAAe;oBAACC,SAAS,EAAC;kBAA8B;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5D5B,OAAA;oBAAKI,SAAS,EAAC;kBAAmD;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACrE5B,OAAA;oBAAKI,SAAS,EAAC,kDAAkD;oBAAC8F,KAAK,EAAE;sBAAEC,cAAc,EAAE;oBAAO;kBAAE;oBAAA1E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvG5B,OAAA;oBAAKI,SAAS,EAAC,mDAAmD;oBAAC8F,KAAK,EAAE;sBAAEC,cAAc,EAAE;oBAAO;kBAAE;oBAAA1E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACxG5B,OAAA;oBAAMI,SAAS,EAAC,4BAA4B;oBAAAa,QAAA,EAAC;kBAAO;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,eAED5B,OAAA;cAAKqG,GAAG,EAAExC;YAAe;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,EAGLsB,QAAQ,CAACoD,MAAM,IAAI,CAAC,iBACnBtG,OAAA;YAAKI,SAAS,EAAC,WAAW;YAAAa,QAAA,gBACxBjB,OAAA;cAAGI,SAAS,EAAC,4BAA4B;cAAAa,QAAA,EAAC;YAAY;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC1D5B,OAAA;cAAKI,SAAS,EAAC,sBAAsB;cAAAa,QAAA,EAClCmD,YAAY,CAACmC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACH,GAAG,CAAEhB,KAAK,iBAClCpF,OAAA;gBAEEiG,OAAO,EAAEA,CAAA,KAAMd,gBAAgB,CAACC,KAAK,CAAE;gBACvChF,SAAS,EAAC,iPAAiP;gBAAAa,QAAA,EAE1PmE;cAAK,GAJDA,KAAK;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKJ,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGD5B,OAAA;YAAKI,SAAS,EAAC,8BAA8B;YAAAa,QAAA,eAC3CjB,OAAA;cAAKI,SAAS,EAAC,6CAA6C;cAAAa,QAAA,gBAC1DjB,OAAA;gBACEqD,IAAI,EAAC,MAAM;gBACXmD,KAAK,EAAE/C,YAAa;gBACpBgD,QAAQ,EAAGnB,CAAC,IAAK5B,eAAe,CAAC4B,CAAC,CAACoB,MAAM,CAACF,KAAK,CAAE;gBACjDG,UAAU,EAAEtB,cAAe;gBAC3BuB,WAAW,EAAC,kEAAgB;gBAC5BxG,SAAS,EAAC,yHAAyH;gBACnIyG,QAAQ,EAAElD;cAAS;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACF5B,OAAA,CAACP,MAAM,CAACoG,MAAM;gBACZI,OAAO,EAAEvB,iBAAkB;gBAC3BmC,QAAQ,EAAE,CAACpD,YAAY,CAACkB,IAAI,CAAC,CAAC,IAAIhB,QAAS;gBAC3CmC,UAAU,EAAE;kBAAEnF,KAAK,EAAE;gBAAK,CAAE;gBAC5BqF,QAAQ,EAAE;kBAAErF,KAAK,EAAE;gBAAK,CAAE;gBAC1BP,SAAS,EAAC,mPAAmP;gBAAAa,QAAA,eAE7PjB,OAAA,CAACJ,iBAAiB;kBAACQ,SAAS,EAAC;gBAAS;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,eACN,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS;IACb;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC;EAAA,eAClB,CAAC;AAEP,CAAC;AAACgB,EAAA,CA9ZIF,WAAuC;AAAAoE,GAAA,GAAvCpE,WAAuC;AAga7C,eAAeA,WAAW;AAAC,IAAAD,EAAA,EAAAqE,GAAA;AAAAC,YAAA,CAAAtE,EAAA;AAAAsE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}