{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0634\\u0631\\u0648\\u0639\\\\src\\\\components\\\\Admin\\\\CertificatesManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { PlusIcon, PencilIcon, TrashIcon, EyeIcon, DocumentIcon, CheckBadgeIcon } from '@heroicons/react/24/outline';\n\n// Types\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CertificatesManagement = ({\n  onBack\n}) => {\n  _s();\n  const [certificates, setCertificates] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // Mock data for demonstration\n  const mockCertificates = [{\n    id: '1',\n    studentId: '1',\n    courseId: '1',\n    templateUrl: '/templates/cert1.pdf',\n    certificateUrl: '/certificates/cert1.pdf',\n    issuedAt: new Date('2024-03-01'),\n    verificationCode: 'CERT-2024-001'\n  }, {\n    id: '2',\n    studentId: '3',\n    courseId: '1',\n    templateUrl: '/templates/cert1.pdf',\n    certificateUrl: '/certificates/cert2.pdf',\n    issuedAt: new Date('2024-03-05'),\n    verificationCode: 'CERT-2024-002'\n  }];\n  React.useEffect(() => {\n    setCertificates(mockCertificates);\n  }, []);\n  const filteredCertificates = certificates.filter(cert => cert.verificationCode.toLowerCase().includes(searchTerm.toLowerCase()));\n  const handleAddCertificate = () => {\n    console.log('Add certificate');\n  };\n  const handleEditCertificate = certId => {\n    console.log('Edit certificate:', certId);\n  };\n  const handleDeleteCertificate = certId => {\n    console.log('Delete certificate:', certId);\n  };\n  const handleViewCertificate = certId => {\n    console.log('View certificate:', certId);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4 space-x-reverse\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onBack,\n          className: \"p-2 text-gray-600 hover:text-gray-900 transition-colors\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M15 19l-7-7 7-7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"\\u0625\\u0635\\u062F\\u0627\\u0631 \\u0648\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0634\\u0647\\u0627\\u062F\\u0627\\u062A \\u0625\\u062A\\u0645\\u0627\\u0645 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleAddCertificate,\n        className: \"flex items-center space-x-2 space-x-reverse bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n        children: [/*#__PURE__*/_jsxDEV(PlusIcon, {\n          className: \"w-5 h-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u0625\\u0635\\u062F\\u0627\\u0631 \\u0634\\u0647\\u0627\\u062F\\u0629 \\u062C\\u062F\\u064A\\u062F\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"\\u0627\\u0644\\u0628\\u062D\\u062B \\u0641\\u064A \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          placeholder: \"\\u0627\\u0628\\u062D\\u062B \\u0628\\u0631\\u0645\\u0632 \\u0627\\u0644\\u062A\\u062D\\u0642\\u0642...\",\n          className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"min-w-full divide-y divide-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"\\u0631\\u0645\\u0632 \\u0627\\u0644\\u062A\\u062D\\u0642\\u0642\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"\\u0627\\u0644\\u0637\\u0627\\u0644\\u0628\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"\\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"\\u062A\\u0627\\u0631\\u064A\\u062E \\u0627\\u0644\\u0625\\u0635\\u062F\\u0627\\u0631\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"bg-white divide-y divide-gray-200\",\n            children: filteredCertificates.map((certificate, index) => /*#__PURE__*/_jsxDEV(motion.tr, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: index * 0.05\n              },\n              className: \"hover:bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 space-x-reverse\",\n                  children: [/*#__PURE__*/_jsxDEV(CheckBadgeIcon, {\n                    className: \"w-5 h-5 text-green-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 146,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-mono text-gray-900 bg-gray-100 px-2 py-1 rounded\",\n                    children: certificate.verificationCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 147,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-900\",\n                  children: [\"\\u0637\\u0627\\u0644\\u0628 \", certificate.studentId]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-900\",\n                  children: [\"\\u0643\\u0648\\u0631\\u0633 \", certificate.courseId]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: new Date(certificate.issuedAt).toLocaleDateString('ar-SA')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 space-x-reverse\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleViewCertificate(certificate.id),\n                    className: \"text-blue-600 hover:text-blue-900\",\n                    title: \"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0629\",\n                    children: /*#__PURE__*/_jsxDEV(EyeIcon, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 168,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 163,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleEditCertificate(certificate.id),\n                    className: \"text-green-600 hover:text-green-900\",\n                    title: \"\\u062A\\u0639\\u062F\\u064A\\u0644 \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0629\",\n                    children: /*#__PURE__*/_jsxDEV(PencilIcon, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 175,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 170,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDeleteCertificate(certificate.id),\n                    className: \"text-red-600 hover:text-red-900\",\n                    title: \"\\u062D\\u0630\\u0641 \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0629\",\n                    children: /*#__PURE__*/_jsxDEV(TrashIcon, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 182,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 19\n              }, this)]\n            }, certificate.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this), filteredCertificates.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(DocumentIcon, {\n        className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-2\",\n        children: \"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0634\\u0647\\u0627\\u062F\\u0627\\u062A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: \"\\u0644\\u0645 \\u064A\\u062A\\u0645 \\u0627\\u0644\\u0639\\u062B\\u0648\\u0631 \\u0639\\u0644\\u0649 \\u0623\\u064A \\u0634\\u0647\\u0627\\u062F\\u0627\\u062A \\u062A\\u0637\\u0627\\u0628\\u0642 \\u0627\\u0644\\u0628\\u062D\\u062B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 5\n  }, this);\n};\n_s(CertificatesManagement, \"cuSmHZcZ91rul7Fmp92IMRmHlis=\");\n_c = CertificatesManagement;\nexport default CertificatesManagement;\nvar _c;\n$RefreshReg$(_c, \"CertificatesManagement\");", "map": {"version": 3, "names": ["React", "useState", "motion", "PlusIcon", "PencilIcon", "TrashIcon", "EyeIcon", "DocumentIcon", "CheckBadgeIcon", "jsxDEV", "_jsxDEV", "CertificatesManagement", "onBack", "_s", "certificates", "setCertificates", "searchTerm", "setSearchTerm", "mockCertificates", "id", "studentId", "courseId", "templateUrl", "certificateUrl", "issuedAt", "Date", "verificationCode", "useEffect", "filteredCertificates", "filter", "cert", "toLowerCase", "includes", "handleAddCertificate", "console", "log", "handleEditCertificate", "certId", "handleDeleteCertificate", "handleViewCertificate", "className", "children", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "value", "onChange", "e", "target", "placeholder", "map", "certificate", "index", "tr", "initial", "opacity", "y", "animate", "transition", "delay", "toLocaleDateString", "title", "length", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Admin/CertificatesManagement.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  PlusIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n  DocumentIcon,\n  CheckBadgeIcon\n} from '@heroicons/react/24/outline';\n\n// Types\nimport { Certificate } from '../../types';\n\ninterface CertificatesManagementProps {\n  onBack?: () => void;\n}\n\nconst CertificatesManagement: React.FC<CertificatesManagementProps> = ({ onBack }) => {\n  const [certificates, setCertificates] = useState<Certificate[]>([]);\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // Mock data for demonstration\n  const mockCertificates: Certificate[] = [\n    {\n      id: '1',\n      studentId: '1',\n      courseId: '1',\n      templateUrl: '/templates/cert1.pdf',\n      certificateUrl: '/certificates/cert1.pdf',\n      issuedAt: new Date('2024-03-01'),\n      verificationCode: 'CERT-2024-001'\n    },\n    {\n      id: '2',\n      studentId: '3',\n      courseId: '1',\n      templateUrl: '/templates/cert1.pdf',\n      certificateUrl: '/certificates/cert2.pdf',\n      issuedAt: new Date('2024-03-05'),\n      verificationCode: 'CERT-2024-002'\n    }\n  ];\n\n  React.useEffect(() => {\n    setCertificates(mockCertificates);\n  }, []);\n\n  const filteredCertificates = certificates.filter(cert =>\n    cert.verificationCode.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  const handleAddCertificate = () => {\n    console.log('Add certificate');\n  };\n\n  const handleEditCertificate = (certId: string) => {\n    console.log('Edit certificate:', certId);\n  };\n\n  const handleDeleteCertificate = (certId: string) => {\n    console.log('Delete certificate:', certId);\n  };\n\n  const handleViewCertificate = (certId: string) => {\n    console.log('View certificate:', certId);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4 space-x-reverse\">\n          <button\n            onClick={onBack}\n            className=\"p-2 text-gray-600 hover:text-gray-900 transition-colors\"\n          >\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n            </svg>\n          </button>\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">إدارة الشهادات</h1>\n            <p className=\"text-gray-600\">إصدار وإدارة شهادات إتمام الكورسات</p>\n          </div>\n        </div>\n        <button\n          onClick={handleAddCertificate}\n          className=\"flex items-center space-x-2 space-x-reverse bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n        >\n          <PlusIcon className=\"w-5 h-5\" />\n          <span>إصدار شهادة جديدة</span>\n        </button>\n      </div>\n\n      {/* Search */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            البحث في الشهادات\n          </label>\n          <input\n            type=\"text\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            placeholder=\"ابحث برمز التحقق...\"\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          />\n        </div>\n      </div>\n\n      {/* Certificates Table */}\n      <div className=\"bg-white rounded-lg shadow-sm overflow-hidden\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  رمز التحقق\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  الطالب\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  الكورس\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  تاريخ الإصدار\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  الإجراءات\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {filteredCertificates.map((certificate, index) => (\n                <motion.tr\n                  key={certificate.id}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: index * 0.05 }}\n                  className=\"hover:bg-gray-50\"\n                >\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center space-x-2 space-x-reverse\">\n                      <CheckBadgeIcon className=\"w-5 h-5 text-green-600\" />\n                      <span className=\"text-sm font-mono text-gray-900 bg-gray-100 px-2 py-1 rounded\">\n                        {certificate.verificationCode}\n                      </span>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className=\"text-sm text-gray-900\">طالب {certificate.studentId}</span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className=\"text-sm text-gray-900\">كورس {certificate.courseId}</span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    {new Date(certificate.issuedAt).toLocaleDateString('ar-SA')}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <div className=\"flex items-center space-x-2 space-x-reverse\">\n                      <button\n                        onClick={() => handleViewCertificate(certificate.id)}\n                        className=\"text-blue-600 hover:text-blue-900\"\n                        title=\"عرض الشهادة\"\n                      >\n                        <EyeIcon className=\"w-4 h-4\" />\n                      </button>\n                      <button\n                        onClick={() => handleEditCertificate(certificate.id)}\n                        className=\"text-green-600 hover:text-green-900\"\n                        title=\"تعديل الشهادة\"\n                      >\n                        <PencilIcon className=\"w-4 h-4\" />\n                      </button>\n                      <button\n                        onClick={() => handleDeleteCertificate(certificate.id)}\n                        className=\"text-red-600 hover:text-red-900\"\n                        title=\"حذف الشهادة\"\n                      >\n                        <TrashIcon className=\"w-4 h-4\" />\n                      </button>\n                    </div>\n                  </td>\n                </motion.tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      {filteredCertificates.length === 0 && (\n        <div className=\"text-center py-12\">\n          <DocumentIcon className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">لا توجد شهادات</h3>\n          <p className=\"text-gray-600\">لم يتم العثور على أي شهادات تطابق البحث</p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default CertificatesManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,QAAQ,EACRC,UAAU,EACVC,SAAS,EACTC,OAAO,EACPC,YAAY,EACZC,cAAc,QACT,6BAA6B;;AAEpC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAOA,MAAMC,sBAA6D,GAAGA,CAAC;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EACpF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;;EAEhD;EACA,MAAMiB,gBAA+B,GAAG,CACtC;IACEC,EAAE,EAAE,GAAG;IACPC,SAAS,EAAE,GAAG;IACdC,QAAQ,EAAE,GAAG;IACbC,WAAW,EAAE,sBAAsB;IACnCC,cAAc,EAAE,yBAAyB;IACzCC,QAAQ,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;IAChCC,gBAAgB,EAAE;EACpB,CAAC,EACD;IACEP,EAAE,EAAE,GAAG;IACPC,SAAS,EAAE,GAAG;IACdC,QAAQ,EAAE,GAAG;IACbC,WAAW,EAAE,sBAAsB;IACnCC,cAAc,EAAE,yBAAyB;IACzCC,QAAQ,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;IAChCC,gBAAgB,EAAE;EACpB,CAAC,CACF;EAED1B,KAAK,CAAC2B,SAAS,CAAC,MAAM;IACpBZ,eAAe,CAACG,gBAAgB,CAAC;EACnC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,oBAAoB,GAAGd,YAAY,CAACe,MAAM,CAACC,IAAI,IACnDA,IAAI,CAACJ,gBAAgB,CAACK,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChB,UAAU,CAACe,WAAW,CAAC,CAAC,CACvE,CAAC;EAED,MAAME,oBAAoB,GAAGA,CAAA,KAAM;IACjCC,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;EAChC,CAAC;EAED,MAAMC,qBAAqB,GAAIC,MAAc,IAAK;IAChDH,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEE,MAAM,CAAC;EAC1C,CAAC;EAED,MAAMC,uBAAuB,GAAID,MAAc,IAAK;IAClDH,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEE,MAAM,CAAC;EAC5C,CAAC;EAED,MAAME,qBAAqB,GAAIF,MAAc,IAAK;IAChDH,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEE,MAAM,CAAC;EAC1C,CAAC;EAED,oBACE3B,OAAA;IAAK8B,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB/B,OAAA;MAAK8B,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD/B,OAAA;QAAK8B,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAC1D/B,OAAA;UACEgC,OAAO,EAAE9B,MAAO;UAChB4B,SAAS,EAAC,yDAAyD;UAAAC,QAAA,eAEnE/B,OAAA;YAAK8B,SAAS,EAAC,SAAS;YAACG,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAJ,QAAA,eAC5E/B,OAAA;cAAMoC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACT3C,OAAA;UAAA+B,QAAA,gBACE/B,OAAA;YAAI8B,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAc;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpE3C,OAAA;YAAG8B,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAkC;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN3C,OAAA;QACEgC,OAAO,EAAET,oBAAqB;QAC9BO,SAAS,EAAC,6HAA6H;QAAAC,QAAA,gBAEvI/B,OAAA,CAACP,QAAQ;UAACqC,SAAS,EAAC;QAAS;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChC3C,OAAA;UAAA+B,QAAA,EAAM;QAAiB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN3C,OAAA;MAAK8B,SAAS,EAAC,mCAAmC;MAAAC,QAAA,eAChD/B,OAAA;QAAA+B,QAAA,gBACE/B,OAAA;UAAO8B,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEhE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR3C,OAAA;UACE4C,IAAI,EAAC,MAAM;UACXC,KAAK,EAAEvC,UAAW;UAClBwC,QAAQ,EAAGC,CAAC,IAAKxC,aAAa,CAACwC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAC/CI,WAAW,EAAC,2FAAqB;UACjCnB,SAAS,EAAC;QAAwG;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3C,OAAA;MAAK8B,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5D/B,OAAA;QAAK8B,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9B/B,OAAA;UAAO8B,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBACpD/B,OAAA;YAAO8B,SAAS,EAAC,YAAY;YAAAC,QAAA,eAC3B/B,OAAA;cAAA+B,QAAA,gBACE/B,OAAA;gBAAI8B,SAAS,EAAC,iFAAiF;gBAAAC,QAAA,EAAC;cAEhG;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL3C,OAAA;gBAAI8B,SAAS,EAAC,iFAAiF;gBAAAC,QAAA,EAAC;cAEhG;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL3C,OAAA;gBAAI8B,SAAS,EAAC,iFAAiF;gBAAAC,QAAA,EAAC;cAEhG;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL3C,OAAA;gBAAI8B,SAAS,EAAC,iFAAiF;gBAAAC,QAAA,EAAC;cAEhG;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL3C,OAAA;gBAAI8B,SAAS,EAAC,iFAAiF;gBAAAC,QAAA,EAAC;cAEhG;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR3C,OAAA;YAAO8B,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EACjDb,oBAAoB,CAACgC,GAAG,CAAC,CAACC,WAAW,EAAEC,KAAK,kBAC3CpD,OAAA,CAACR,MAAM,CAAC6D,EAAE;cAERC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEC,KAAK,EAAEP,KAAK,GAAG;cAAK,CAAE;cACpCtB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAE5B/B,OAAA;gBAAI8B,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACzC/B,OAAA;kBAAK8B,SAAS,EAAC,6CAA6C;kBAAAC,QAAA,gBAC1D/B,OAAA,CAACF,cAAc;oBAACgC,SAAS,EAAC;kBAAwB;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACrD3C,OAAA;oBAAM8B,SAAS,EAAC,+DAA+D;oBAAAC,QAAA,EAC5EoB,WAAW,CAACnC;kBAAgB;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL3C,OAAA;gBAAI8B,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACzC/B,OAAA;kBAAM8B,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAC,2BAAK,EAACoB,WAAW,CAACzC,SAAS;gBAAA;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC,eACL3C,OAAA;gBAAI8B,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACzC/B,OAAA;kBAAM8B,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAC,2BAAK,EAACoB,WAAW,CAACxC,QAAQ;gBAAA;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE,CAAC,eACL3C,OAAA;gBAAI8B,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC9D,IAAIhB,IAAI,CAACoC,WAAW,CAACrC,QAAQ,CAAC,CAAC8C,kBAAkB,CAAC,OAAO;cAAC;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACL3C,OAAA;gBAAI8B,SAAS,EAAC,iDAAiD;gBAAAC,QAAA,eAC7D/B,OAAA;kBAAK8B,SAAS,EAAC,6CAA6C;kBAAAC,QAAA,gBAC1D/B,OAAA;oBACEgC,OAAO,EAAEA,CAAA,KAAMH,qBAAqB,CAACsB,WAAW,CAAC1C,EAAE,CAAE;oBACrDqB,SAAS,EAAC,mCAAmC;oBAC7C+B,KAAK,EAAC,+DAAa;oBAAA9B,QAAA,eAEnB/B,OAAA,CAACJ,OAAO;sBAACkC,SAAS,EAAC;oBAAS;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,eACT3C,OAAA;oBACEgC,OAAO,EAAEA,CAAA,KAAMN,qBAAqB,CAACyB,WAAW,CAAC1C,EAAE,CAAE;oBACrDqB,SAAS,EAAC,qCAAqC;oBAC/C+B,KAAK,EAAC,2EAAe;oBAAA9B,QAAA,eAErB/B,OAAA,CAACN,UAAU;sBAACoC,SAAS,EAAC;oBAAS;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACT3C,OAAA;oBACEgC,OAAO,EAAEA,CAAA,KAAMJ,uBAAuB,CAACuB,WAAW,CAAC1C,EAAE,CAAE;oBACvDqB,SAAS,EAAC,iCAAiC;oBAC3C+B,KAAK,EAAC,+DAAa;oBAAA9B,QAAA,eAEnB/B,OAAA,CAACL,SAAS;sBAACmC,SAAS,EAAC;oBAAS;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GA/CAQ,WAAW,CAAC1C,EAAE;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgDV,CACZ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELzB,oBAAoB,CAAC4C,MAAM,KAAK,CAAC,iBAChC9D,OAAA;MAAK8B,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC/B,OAAA,CAACH,YAAY;QAACiC,SAAS,EAAC;MAAsC;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjE3C,OAAA;QAAI8B,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAc;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1E3C,OAAA;QAAG8B,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAuC;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACxC,EAAA,CAvLIF,sBAA6D;AAAA8D,EAAA,GAA7D9D,sBAA6D;AAyLnE,eAAeA,sBAAsB;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}