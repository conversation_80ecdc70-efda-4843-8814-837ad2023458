{"ast": null, "code": "import * as React from 'react';\nimport { useContext, useMemo } from 'react';\nimport { MotionConfigContext } from '../../context/MotionConfigContext.mjs';\nimport { loadExternalIsValidProp } from '../../render/dom/utils/filter-props.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\n\n/**\n * `MotionConfig` is used to set configuration options for all children `motion` components.\n *\n * ```jsx\n * import { motion, MotionConfig } from \"framer-motion\"\n *\n * export function App() {\n *   return (\n *     <MotionConfig transition={{ type: \"spring\" }}>\n *       <motion.div animate={{ x: 100 }} />\n *     </MotionConfig>\n *   )\n * }\n * ```\n *\n * @public\n */\nfunction MotionConfig({\n  children,\n  isValidProp,\n  ...config\n}) {\n  isValidProp && loadExternalIsValidProp(isValidProp);\n  /**\n   * Inherit props from any parent MotionConfig components\n   */\n  config = {\n    ...useContext(MotionConfigContext),\n    ...config\n  };\n  /**\n   * Don't allow isStatic to change between renders as it affects how many hooks\n   * motion components fire.\n   */\n  config.isStatic = useConstant(() => config.isStatic);\n  /**\n   * Creating a new config context object will re-render every `motion` component\n   * every time it renders. So we only want to create a new one sparingly.\n   */\n  const context = useMemo(() => config, [JSON.stringify(config.transition), config.transformPagePoint, config.reducedMotion]);\n  return React.createElement(MotionConfigContext.Provider, {\n    value: context\n  }, children);\n}\nexport { MotionConfig };", "map": {"version": 3, "names": ["React", "useContext", "useMemo", "MotionConfigContext", "loadExternalIsValidProp", "useConstant", "MotionConfig", "children", "isValidProp", "config", "isStatic", "context", "JSON", "stringify", "transition", "transformPagePoint", "reducedMotion", "createElement", "Provider", "value"], "sources": ["C:/Users/<USER>/Desktop/مشروع/node_modules/framer-motion/dist/es/components/MotionConfig/index.mjs"], "sourcesContent": ["import * as React from 'react';\nimport { useContext, useMemo } from 'react';\nimport { MotionConfigContext } from '../../context/MotionConfigContext.mjs';\nimport { loadExternalIsValidProp } from '../../render/dom/utils/filter-props.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\n\n/**\n * `MotionConfig` is used to set configuration options for all children `motion` components.\n *\n * ```jsx\n * import { motion, MotionConfig } from \"framer-motion\"\n *\n * export function App() {\n *   return (\n *     <MotionConfig transition={{ type: \"spring\" }}>\n *       <motion.div animate={{ x: 100 }} />\n *     </MotionConfig>\n *   )\n * }\n * ```\n *\n * @public\n */\nfunction MotionConfig({ children, isValidProp, ...config }) {\n    isValidProp && loadExternalIsValidProp(isValidProp);\n    /**\n     * Inherit props from any parent MotionConfig components\n     */\n    config = { ...useContext(MotionConfigContext), ...config };\n    /**\n     * Don't allow isStatic to change between renders as it affects how many hooks\n     * motion components fire.\n     */\n    config.isStatic = useConstant(() => config.isStatic);\n    /**\n     * Creating a new config context object will re-render every `motion` component\n     * every time it renders. So we only want to create a new one sparingly.\n     */\n    const context = useMemo(() => config, [JSON.stringify(config.transition), config.transformPagePoint, config.reducedMotion]);\n    return (React.createElement(MotionConfigContext.Provider, { value: context }, children));\n}\n\nexport { MotionConfig };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,OAAO,QAAQ,OAAO;AAC3C,SAASC,mBAAmB,QAAQ,uCAAuC;AAC3E,SAASC,uBAAuB,QAAQ,yCAAyC;AACjF,SAASC,WAAW,QAAQ,8BAA8B;;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAAC;EAAEC,QAAQ;EAAEC,WAAW;EAAE,GAAGC;AAAO,CAAC,EAAE;EACxDD,WAAW,IAAIJ,uBAAuB,CAACI,WAAW,CAAC;EACnD;AACJ;AACA;EACIC,MAAM,GAAG;IAAE,GAAGR,UAAU,CAACE,mBAAmB,CAAC;IAAE,GAAGM;EAAO,CAAC;EAC1D;AACJ;AACA;AACA;EACIA,MAAM,CAACC,QAAQ,GAAGL,WAAW,CAAC,MAAMI,MAAM,CAACC,QAAQ,CAAC;EACpD;AACJ;AACA;AACA;EACI,MAAMC,OAAO,GAAGT,OAAO,CAAC,MAAMO,MAAM,EAAE,CAACG,IAAI,CAACC,SAAS,CAACJ,MAAM,CAACK,UAAU,CAAC,EAAEL,MAAM,CAACM,kBAAkB,EAAEN,MAAM,CAACO,aAAa,CAAC,CAAC;EAC3H,OAAQhB,KAAK,CAACiB,aAAa,CAACd,mBAAmB,CAACe,QAAQ,EAAE;IAAEC,KAAK,EAAER;EAAQ,CAAC,EAAEJ,QAAQ,CAAC;AAC3F;AAEA,SAASD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}