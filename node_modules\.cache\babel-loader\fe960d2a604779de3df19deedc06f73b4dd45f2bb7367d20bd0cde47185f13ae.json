{"ast": null, "code": "// بيانات المدير الافتراضية\nexport const defaultAdmin = {\n  id: 'admin-001',\n  email: '<EMAIL>',\n  name: 'مدير النظام',\n  role: 'admin',\n  avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\n  permissions: ['all'],\n  createdAt: new Date('2024-01-01'),\n  lastLogin: new Date()\n};\n\n// كلمة مرور المدير الافتراضية\nexport const defaultAdminPassword = 'Admin@123456';\n\n// بيانات تسجيل الدخول للمدير\nexport const adminCredentials = {\n  email: '<EMAIL>',\n  password: 'Admin@123456'\n};", "map": {"version": 3, "names": ["defaultAdmin", "id", "email", "name", "role", "avatar", "permissions", "createdAt", "Date", "lastLogin", "defaultAdminPassword", "adminCredentials", "password"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/data/defaultAdmin.ts"], "sourcesContent": ["import { User, Admin } from '../types';\n\n// بيانات المدير الافتراضية\nexport const defaultAdmin: Admin = {\n  id: 'admin-001',\n  email: '<EMAIL>',\n  name: 'مدير النظام',\n  role: 'admin',\n  avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\n  permissions: ['all'],\n  createdAt: new Date('2024-01-01'),\n  lastLogin: new Date()\n};\n\n// كلمة مرور المدير الافتراضية\nexport const defaultAdminPassword = 'Admin@123456';\n\n// بيانات تسجيل الدخول للمدير\nexport const adminCredentials = {\n  email: '<EMAIL>',\n  password: 'Admin@123456'\n};\n"], "mappings": "AAEA;AACA,OAAO,MAAMA,YAAmB,GAAG;EACjCC,EAAE,EAAE,WAAW;EACfC,KAAK,EAAE,wBAAwB;EAC/BC,IAAI,EAAE,aAAa;EACnBC,IAAI,EAAE,OAAO;EACbC,MAAM,EAAE,6FAA6F;EACrGC,WAAW,EAAE,CAAC,KAAK,CAAC;EACpBC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;EACjCC,SAAS,EAAE,IAAID,IAAI,CAAC;AACtB,CAAC;;AAED;AACA,OAAO,MAAME,oBAAoB,GAAG,cAAc;;AAElD;AACA,OAAO,MAAMC,gBAAgB,GAAG;EAC9BT,KAAK,EAAE,wBAAwB;EAC/BU,QAAQ,EAAE;AACZ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}