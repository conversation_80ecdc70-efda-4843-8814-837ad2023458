{"ast": null, "code": "import{db}from'../config/firebase';import{collection,doc,addDoc,updateDoc,deleteDoc}from'firebase/firestore';import{mockCourses,mockQuizzes,mockCertificates}from'../data/mockCourses';import{mockStudents}from'../data/mockStudents';class DataService{// Courses\nasync getCourses(){try{// Return mock data for now\nreturn mockCourses;}catch(error){console.error('Error fetching courses:',error);return mockCourses;// Fallback to mock data\n}}async getCourse(id){try{const course=mockCourses.find(c=>c.id===id);return course||null;}catch(error){console.error('Error fetching course:',error);return null;}}async addCourse(course){try{const docRef=await addDoc(collection(db,'courses'),course);return docRef.id;}catch(error){console.error('Error adding course:',error);throw error;}}async updateCourse(id,course){try{await updateDoc(doc(db,'courses',id),course);}catch(error){console.error('Error updating course:',error);throw error;}}async deleteCourse(id){try{await deleteDoc(doc(db,'courses',id));}catch(error){console.error('Error deleting course:',error);throw error;}}// Students\nasync getStudents(){try{return mockStudents;}catch(error){console.error('Error fetching students:',error);return mockStudents;}}async getStudent(id){try{const student=mockStudents.find(s=>s.id===id);return student||null;}catch(error){console.error('Error fetching student:',error);return null;}}// Quizzes\nasync getQuizzes(){try{return mockQuizzes;}catch(error){console.error('Error fetching quizzes:',error);return mockQuizzes;}}async getQuiz(id){try{const quiz=mockQuizzes.find(q=>q.id===id);return quiz||null;}catch(error){console.error('Error fetching quiz:',error);return null;}}// Certificates\nasync getCertificates(){try{return mockCertificates;}catch(error){console.error('Error fetching certificates:',error);return mockCertificates;}}async getStudentCertificates(studentId){try{return mockCertificates.filter(cert=>cert.studentId===studentId);}catch(error){console.error('Error fetching student certificates:',error);return[];}}// Analytics\nasync getAnalytics(){return{totalStudents:mockStudents.length,totalCourses:mockCourses.length,totalQuizzes:mockQuizzes.length,totalCertificates:mockCertificates.length,revenue:mockCourses.reduce((sum,course)=>sum+course.price,0),enrollments:mockStudents.reduce((sum,student)=>sum+student.enrolledCourses.length,0)};}}export const dataService=new DataService();", "map": {"version": 3, "names": ["db", "collection", "doc", "addDoc", "updateDoc", "deleteDoc", "mockCourses", "mockQuizzes", "mockCertificates", "mockStudents", "DataService", "getCourses", "error", "console", "getCourse", "id", "course", "find", "c", "addCourse", "doc<PERSON>ef", "updateCourse", "deleteCourse", "getStudents", "getStudent", "student", "s", "getQuizzes", "getQuiz", "quiz", "q", "getCertificates", "getStudentCertificates", "studentId", "filter", "cert", "getAnalytics", "totalStudents", "length", "totalCourses", "totalQuizzes", "totalCertificates", "revenue", "reduce", "sum", "price", "enrollments", "enrolledCourses", "dataService"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/services/dataService.ts"], "sourcesContent": ["import { db } from '../config/firebase';\nimport { collection, getDocs, doc, getDoc, addDoc, updateDoc, deleteDoc } from 'firebase/firestore';\nimport { Course, Student, Quiz, Certificate } from '../types';\nimport { mockCourses, mockVideos, mockQuizzes, mockCertificates } from '../data/mockCourses';\nimport { mockStudents } from '../data/mockStudents';\n\nclass DataService {\n  // Courses\n  async getCourses(): Promise<Course[]> {\n    try {\n      // Return mock data for now\n      return mockCourses;\n    } catch (error) {\n      console.error('Error fetching courses:', error);\n      return mockCourses; // Fallback to mock data\n    }\n  }\n\n  async getCourse(id: string): Promise<Course | null> {\n    try {\n      const course = mockCourses.find(c => c.id === id);\n      return course || null;\n    } catch (error) {\n      console.error('Error fetching course:', error);\n      return null;\n    }\n  }\n\n  async addCourse(course: Omit<Course, 'id'>): Promise<string> {\n    try {\n      const docRef = await addDoc(collection(db, 'courses'), course);\n      return docRef.id;\n    } catch (error) {\n      console.error('Error adding course:', error);\n      throw error;\n    }\n  }\n\n  async updateCourse(id: string, course: Partial<Course>): Promise<void> {\n    try {\n      await updateDoc(doc(db, 'courses', id), course);\n    } catch (error) {\n      console.error('Error updating course:', error);\n      throw error;\n    }\n  }\n\n  async deleteCourse(id: string): Promise<void> {\n    try {\n      await deleteDoc(doc(db, 'courses', id));\n    } catch (error) {\n      console.error('Error deleting course:', error);\n      throw error;\n    }\n  }\n\n  // Students\n  async getStudents(): Promise<Student[]> {\n    try {\n      return mockStudents;\n    } catch (error) {\n      console.error('Error fetching students:', error);\n      return mockStudents;\n    }\n  }\n\n  async getStudent(id: string): Promise<Student | null> {\n    try {\n      const student = mockStudents.find(s => s.id === id);\n      return student || null;\n    } catch (error) {\n      console.error('Error fetching student:', error);\n      return null;\n    }\n  }\n\n  // Quizzes\n  async getQuizzes(): Promise<Quiz[]> {\n    try {\n      return mockQuizzes;\n    } catch (error) {\n      console.error('Error fetching quizzes:', error);\n      return mockQuizzes;\n    }\n  }\n\n  async getQuiz(id: string): Promise<Quiz | null> {\n    try {\n      const quiz = mockQuizzes.find(q => q.id === id);\n      return quiz || null;\n    } catch (error) {\n      console.error('Error fetching quiz:', error);\n      return null;\n    }\n  }\n\n  // Certificates\n  async getCertificates(): Promise<Certificate[]> {\n    try {\n      return mockCertificates;\n    } catch (error) {\n      console.error('Error fetching certificates:', error);\n      return mockCertificates;\n    }\n  }\n\n  async getStudentCertificates(studentId: string): Promise<Certificate[]> {\n    try {\n      return mockCertificates.filter(cert => cert.studentId === studentId);\n    } catch (error) {\n      console.error('Error fetching student certificates:', error);\n      return [];\n    }\n  }\n\n  // Analytics\n  async getAnalytics() {\n    return {\n      totalStudents: mockStudents.length,\n      totalCourses: mockCourses.length,\n      totalQuizzes: mockQuizzes.length,\n      totalCertificates: mockCertificates.length,\n      revenue: mockCourses.reduce((sum, course) => sum + course.price, 0),\n      enrollments: mockStudents.reduce((sum, student) => sum + student.enrolledCourses.length, 0)\n    };\n  }\n}\n\nexport const dataService = new DataService();\n"], "mappings": "AAAA,OAASA,EAAE,KAAQ,oBAAoB,CACvC,OAASC,UAAU,CAAWC,GAAG,CAAUC,MAAM,CAAEC,SAAS,CAAEC,SAAS,KAAQ,oBAAoB,CAEnG,OAASC,WAAW,CAAcC,WAAW,CAAEC,gBAAgB,KAAQ,qBAAqB,CAC5F,OAASC,YAAY,KAAQ,sBAAsB,CAEnD,KAAM,CAAAC,WAAY,CAChB;AACA,KAAM,CAAAC,UAAUA,CAAA,CAAsB,CACpC,GAAI,CACF;AACA,MAAO,CAAAL,WAAW,CACpB,CAAE,MAAOM,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/C,MAAO,CAAAN,WAAW,CAAE;AACtB,CACF,CAEA,KAAM,CAAAQ,SAASA,CAACC,EAAU,CAA0B,CAClD,GAAI,CACF,KAAM,CAAAC,MAAM,CAAGV,WAAW,CAACW,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACH,EAAE,GAAKA,EAAE,CAAC,CACjD,MAAO,CAAAC,MAAM,EAAI,IAAI,CACvB,CAAE,MAAOJ,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9C,MAAO,KAAI,CACb,CACF,CAEA,KAAM,CAAAO,SAASA,CAACH,MAA0B,CAAmB,CAC3D,GAAI,CACF,KAAM,CAAAI,MAAM,CAAG,KAAM,CAAAjB,MAAM,CAACF,UAAU,CAACD,EAAE,CAAE,SAAS,CAAC,CAAEgB,MAAM,CAAC,CAC9D,MAAO,CAAAI,MAAM,CAACL,EAAE,CAClB,CAAE,MAAOH,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C,KAAM,CAAAA,KAAK,CACb,CACF,CAEA,KAAM,CAAAS,YAAYA,CAACN,EAAU,CAAEC,MAAuB,CAAiB,CACrE,GAAI,CACF,KAAM,CAAAZ,SAAS,CAACF,GAAG,CAACF,EAAE,CAAE,SAAS,CAAEe,EAAE,CAAC,CAAEC,MAAM,CAAC,CACjD,CAAE,MAAOJ,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9C,KAAM,CAAAA,KAAK,CACb,CACF,CAEA,KAAM,CAAAU,YAAYA,CAACP,EAAU,CAAiB,CAC5C,GAAI,CACF,KAAM,CAAAV,SAAS,CAACH,GAAG,CAACF,EAAE,CAAE,SAAS,CAAEe,EAAE,CAAC,CAAC,CACzC,CAAE,MAAOH,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9C,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACA,KAAM,CAAAW,WAAWA,CAAA,CAAuB,CACtC,GAAI,CACF,MAAO,CAAAd,YAAY,CACrB,CAAE,MAAOG,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD,MAAO,CAAAH,YAAY,CACrB,CACF,CAEA,KAAM,CAAAe,UAAUA,CAACT,EAAU,CAA2B,CACpD,GAAI,CACF,KAAM,CAAAU,OAAO,CAAGhB,YAAY,CAACQ,IAAI,CAACS,CAAC,EAAIA,CAAC,CAACX,EAAE,GAAKA,EAAE,CAAC,CACnD,MAAO,CAAAU,OAAO,EAAI,IAAI,CACxB,CAAE,MAAOb,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/C,MAAO,KAAI,CACb,CACF,CAEA;AACA,KAAM,CAAAe,UAAUA,CAAA,CAAoB,CAClC,GAAI,CACF,MAAO,CAAApB,WAAW,CACpB,CAAE,MAAOK,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/C,MAAO,CAAAL,WAAW,CACpB,CACF,CAEA,KAAM,CAAAqB,OAAOA,CAACb,EAAU,CAAwB,CAC9C,GAAI,CACF,KAAM,CAAAc,IAAI,CAAGtB,WAAW,CAACU,IAAI,CAACa,CAAC,EAAIA,CAAC,CAACf,EAAE,GAAKA,EAAE,CAAC,CAC/C,MAAO,CAAAc,IAAI,EAAI,IAAI,CACrB,CAAE,MAAOjB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C,MAAO,KAAI,CACb,CACF,CAEA;AACA,KAAM,CAAAmB,eAAeA,CAAA,CAA2B,CAC9C,GAAI,CACF,MAAO,CAAAvB,gBAAgB,CACzB,CAAE,MAAOI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACpD,MAAO,CAAAJ,gBAAgB,CACzB,CACF,CAEA,KAAM,CAAAwB,sBAAsBA,CAACC,SAAiB,CAA0B,CACtE,GAAI,CACF,MAAO,CAAAzB,gBAAgB,CAAC0B,MAAM,CAACC,IAAI,EAAIA,IAAI,CAACF,SAAS,GAAKA,SAAS,CAAC,CACtE,CAAE,MAAOrB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,CAAEA,KAAK,CAAC,CAC5D,MAAO,EAAE,CACX,CACF,CAEA;AACA,KAAM,CAAAwB,YAAYA,CAAA,CAAG,CACnB,MAAO,CACLC,aAAa,CAAE5B,YAAY,CAAC6B,MAAM,CAClCC,YAAY,CAAEjC,WAAW,CAACgC,MAAM,CAChCE,YAAY,CAAEjC,WAAW,CAAC+B,MAAM,CAChCG,iBAAiB,CAAEjC,gBAAgB,CAAC8B,MAAM,CAC1CI,OAAO,CAAEpC,WAAW,CAACqC,MAAM,CAAC,CAACC,GAAG,CAAE5B,MAAM,GAAK4B,GAAG,CAAG5B,MAAM,CAAC6B,KAAK,CAAE,CAAC,CAAC,CACnEC,WAAW,CAAErC,YAAY,CAACkC,MAAM,CAAC,CAACC,GAAG,CAAEnB,OAAO,GAAKmB,GAAG,CAAGnB,OAAO,CAACsB,eAAe,CAACT,MAAM,CAAE,CAAC,CAC5F,CAAC,CACH,CACF,CAEA,MAAO,MAAM,CAAAU,WAAW,CAAG,GAAI,CAAAtC,WAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}