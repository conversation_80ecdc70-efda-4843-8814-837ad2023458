{"ast": null, "code": "class Feature {\n  constructor(node) {\n    this.isMounted = false;\n    this.node = node;\n  }\n  update() {}\n}\nexport { Feature };", "map": {"version": 3, "names": ["Feature", "constructor", "node", "isMounted", "update"], "sources": ["C:/Users/<USER>/Desktop/مشروع/node_modules/framer-motion/dist/es/motion/features/Feature.mjs"], "sourcesContent": ["class Feature {\n    constructor(node) {\n        this.isMounted = false;\n        this.node = node;\n    }\n    update() { }\n}\n\nexport { Feature };\n"], "mappings": "AAAA,MAAMA,OAAO,CAAC;EACVC,WAAWA,CAACC,IAAI,EAAE;IACd,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACD,IAAI,GAAGA,IAAI;EACpB;EACAE,MAAMA,CAAA,EAAG,CAAE;AACf;AAEA,SAASJ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}