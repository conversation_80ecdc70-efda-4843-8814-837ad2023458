{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0634\\u0631\\u0648\\u0639\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { Toaster } from 'react-hot-toast';\n// Services\nimport { authService } from './services/authService';\n\n// Components\nimport LoadingSpinner from './components/common/LoadingSpinner';\nimport LoginPage from './pages/LoginPage';\nimport AdminDashboard from './pages/admin/AdminDashboard';\nimport StudentDashboard from './pages/student/StudentDashboard';\nimport AIAssistant from './components/AIAssistant/AIAssistant';\n\n// Types\n\n// Styles\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [firebaseUser, setFirebaseUser] = useState(null);\n  useEffect(() => {\n    const unsubscribe = authService.onAuthStateChange(async firebaseUser => {\n      setFirebaseUser(firebaseUser);\n      if (firebaseUser) {\n        // User is signed in, get user data\n        try {\n          // This would be implemented based on your auth logic\n          // For now, we'll handle it in the login components\n        } catch (error) {\n          console.error('Error getting user data:', error);\n        }\n      } else {\n        setUser(null);\n      }\n      setLoading(false);\n    });\n    return () => unsubscribe();\n  }, []);\n  const handleLogin = userData => {\n    setUser(userData);\n  };\n  const handleLogout = async () => {\n    try {\n      await authService.logout();\n      setUser(null);\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App min-h-screen bg-gray-50\",\n    dir: \"rtl\",\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: [/*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: user ? /*#__PURE__*/_jsxDEV(Navigate, {\n            to: user.role === 'admin' ? '/admin' : '/student',\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(LoginPage, {\n            onLogin: handleLogin\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin/*\",\n          element: user && user.role === 'admin' ? /*#__PURE__*/_jsxDEV(AdminDashboard, {\n            user: user,\n            onLogout: handleLogout\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/login\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/student/*\",\n          element: user && user.role === 'student' ? /*#__PURE__*/_jsxDEV(StudentDashboard, {\n            user: user,\n            onLogout: handleLogout\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/login\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: user ? user.role === 'admin' ? '/admin' : '/student' : '/login',\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), user && user.role === 'student' && /*#__PURE__*/_jsxDEV(AIAssistant, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 45\n      }, this), /*#__PURE__*/_jsxDEV(Toaster, {\n        position: \"top-center\",\n        toastOptions: {\n          duration: 4000,\n          style: {\n            background: '#363636',\n            color: '#fff',\n            fontFamily: 'Cairo, sans-serif',\n            direction: 'rtl'\n          },\n          success: {\n            style: {\n              background: '#10b981'\n            }\n          },\n          error: {\n            style: {\n              background: '#ef4444'\n            }\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"i0dpnYEwBVuTrzEGBDFjNyGeOGA=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "Toaster", "authService", "LoadingSpinner", "LoginPage", "AdminDashboard", "StudentDashboard", "AIAssistant", "jsxDEV", "_jsxDEV", "App", "_s", "user", "setUser", "loading", "setLoading", "firebaseUser", "setFirebaseUser", "unsubscribe", "onAuthStateChange", "error", "console", "handleLogin", "userData", "handleLogout", "logout", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "dir", "children", "path", "element", "to", "role", "replace", "onLogin", "onLogout", "position", "toastOptions", "duration", "style", "background", "color", "fontFamily", "direction", "success", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/App.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { Toaster } from 'react-hot-toast';\nimport { User as FirebaseUser } from 'firebase/auth';\n\n// Services\nimport { authService } from './services/authService';\n\n// Components\nimport LoadingSpinner from './components/common/LoadingSpinner';\nimport LoginPage from './pages/LoginPage';\nimport AdminDashboard from './pages/admin/AdminDashboard';\nimport StudentDashboard from './pages/student/StudentDashboard';\nimport AIAssistant from './components/AIAssistant/AIAssistant';\n\n// Types\nimport { User } from './types';\n\n// Styles\nimport './App.css';\n\nfunction App() {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [firebaseUser, setFirebaseUser] = useState<FirebaseUser | null>(null);\n\n  useEffect(() => {\n    const unsubscribe = authService.onAuthStateChange(async (firebaseUser) => {\n      setFirebaseUser(firebaseUser);\n      \n      if (firebaseUser) {\n        // User is signed in, get user data\n        try {\n          // This would be implemented based on your auth logic\n          // For now, we'll handle it in the login components\n        } catch (error) {\n          console.error('Error getting user data:', error);\n        }\n      } else {\n        setUser(null);\n      }\n      \n      setLoading(false);\n    });\n\n    return () => unsubscribe();\n  }, []);\n\n  const handleLogin = (userData: User) => {\n    setUser(userData);\n  };\n\n  const handleLogout = async () => {\n    try {\n      await authService.logout();\n      setUser(null);\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n\n  if (loading) {\n    return <LoadingSpinner />;\n  }\n\n  return (\n    <div className=\"App min-h-screen bg-gray-50\" dir=\"rtl\">\n      <Router>\n        <Routes>\n          {/* Public Routes */}\n          <Route \n            path=\"/login\" \n            element={\n              user ? (\n                <Navigate to={user.role === 'admin' ? '/admin' : '/student'} replace />\n              ) : (\n                <LoginPage onLogin={handleLogin} />\n              )\n            } \n          />\n          \n          {/* Protected Admin Routes */}\n          <Route \n            path=\"/admin/*\" \n            element={\n              user && user.role === 'admin' ? (\n                <AdminDashboard user={user} onLogout={handleLogout} />\n              ) : (\n                <Navigate to=\"/login\" replace />\n              )\n            } \n          />\n          \n          {/* Protected Student Routes */}\n          <Route \n            path=\"/student/*\" \n            element={\n              user && user.role === 'student' ? (\n                <StudentDashboard user={user} onLogout={handleLogout} />\n              ) : (\n                <Navigate to=\"/login\" replace />\n              )\n            } \n          />\n          \n          {/* Default Route */}\n          <Route \n            path=\"/\" \n            element={\n              <Navigate to={\n                user \n                  ? (user.role === 'admin' ? '/admin' : '/student')\n                  : '/login'\n              } replace />\n            } \n          />\n        </Routes>\n        \n        {/* AI Assistant - Available for students */}\n        {user && user.role === 'student' && <AIAssistant />}\n        \n        {/* Toast Notifications */}\n        <Toaster\n          position=\"top-center\"\n          toastOptions={{\n            duration: 4000,\n            style: {\n              background: '#363636',\n              color: '#fff',\n              fontFamily: 'Cairo, sans-serif',\n              direction: 'rtl'\n            },\n            success: {\n              style: {\n                background: '#10b981',\n              },\n            },\n            error: {\n              style: {\n                background: '#ef4444',\n              },\n            },\n          }}\n        />\n      </Router>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,OAAO,QAAQ,iBAAiB;AAGzC;AACA,SAASC,WAAW,QAAQ,wBAAwB;;AAEpD;AACA,OAAOC,cAAc,MAAM,oCAAoC;AAC/D,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,WAAW,MAAM,sCAAsC;;AAE9D;;AAGA;AACA,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGnB,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsB,YAAY,EAAEC,eAAe,CAAC,GAAGvB,QAAQ,CAAsB,IAAI,CAAC;EAE3EC,SAAS,CAAC,MAAM;IACd,MAAMuB,WAAW,GAAGhB,WAAW,CAACiB,iBAAiB,CAAC,MAAOH,YAAY,IAAK;MACxEC,eAAe,CAACD,YAAY,CAAC;MAE7B,IAAIA,YAAY,EAAE;QAChB;QACA,IAAI;UACF;UACA;QAAA,CACD,CAAC,OAAOI,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAClD;MACF,CAAC,MAAM;QACLP,OAAO,CAAC,IAAI,CAAC;MACf;MAEAE,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC;IAEF,OAAO,MAAMG,WAAW,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,WAAW,GAAIC,QAAc,IAAK;IACtCV,OAAO,CAACU,QAAQ,CAAC;EACnB,CAAC;EAED,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMtB,WAAW,CAACuB,MAAM,CAAC,CAAC;MAC1BZ,OAAO,CAAC,IAAI,CAAC;IACf,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC;EACF,CAAC;EAED,IAAIN,OAAO,EAAE;IACX,oBAAOL,OAAA,CAACN,cAAc;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC3B;EAEA,oBACEpB,OAAA;IAAKqB,SAAS,EAAC,6BAA6B;IAACC,GAAG,EAAC,KAAK;IAAAC,QAAA,eACpDvB,OAAA,CAACZ,MAAM;MAAAmC,QAAA,gBACLvB,OAAA,CAACX,MAAM;QAAAkC,QAAA,gBAELvB,OAAA,CAACV,KAAK;UACJkC,IAAI,EAAC,QAAQ;UACbC,OAAO,EACLtB,IAAI,gBACFH,OAAA,CAACT,QAAQ;YAACmC,EAAE,EAAEvB,IAAI,CAACwB,IAAI,KAAK,OAAO,GAAG,QAAQ,GAAG,UAAW;YAACC,OAAO;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEvEpB,OAAA,CAACL,SAAS;YAACkC,OAAO,EAAEhB;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAErC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGFpB,OAAA,CAACV,KAAK;UACJkC,IAAI,EAAC,UAAU;UACfC,OAAO,EACLtB,IAAI,IAAIA,IAAI,CAACwB,IAAI,KAAK,OAAO,gBAC3B3B,OAAA,CAACJ,cAAc;YAACO,IAAI,EAAEA,IAAK;YAAC2B,QAAQ,EAAEf;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEtDpB,OAAA,CAACT,QAAQ;YAACmC,EAAE,EAAC,QAAQ;YAACE,OAAO;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAElC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGFpB,OAAA,CAACV,KAAK;UACJkC,IAAI,EAAC,YAAY;UACjBC,OAAO,EACLtB,IAAI,IAAIA,IAAI,CAACwB,IAAI,KAAK,SAAS,gBAC7B3B,OAAA,CAACH,gBAAgB;YAACM,IAAI,EAAEA,IAAK;YAAC2B,QAAQ,EAAEf;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAExDpB,OAAA,CAACT,QAAQ;YAACmC,EAAE,EAAC,QAAQ;YAACE,OAAO;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAElC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGFpB,OAAA,CAACV,KAAK;UACJkC,IAAI,EAAC,GAAG;UACRC,OAAO,eACLzB,OAAA,CAACT,QAAQ;YAACmC,EAAE,EACVvB,IAAI,GACCA,IAAI,CAACwB,IAAI,KAAK,OAAO,GAAG,QAAQ,GAAG,UAAU,GAC9C,QACL;YAACC,OAAO;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QACZ;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,EAGRjB,IAAI,IAAIA,IAAI,CAACwB,IAAI,KAAK,SAAS,iBAAI3B,OAAA,CAACF,WAAW;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGnDpB,OAAA,CAACR,OAAO;QACNuC,QAAQ,EAAC,YAAY;QACrBC,YAAY,EAAE;UACZC,QAAQ,EAAE,IAAI;UACdC,KAAK,EAAE;YACLC,UAAU,EAAE,SAAS;YACrBC,KAAK,EAAE,MAAM;YACbC,UAAU,EAAE,mBAAmB;YAC/BC,SAAS,EAAE;UACb,CAAC;UACDC,OAAO,EAAE;YACPL,KAAK,EAAE;cACLC,UAAU,EAAE;YACd;UACF,CAAC;UACDxB,KAAK,EAAE;YACLuB,KAAK,EAAE;cACLC,UAAU,EAAE;YACd;UACF;QACF;MAAE;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAAClB,EAAA,CA9HQD,GAAG;AAAAuC,EAAA,GAAHvC,GAAG;AAgIZ,eAAeA,GAAG;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}