import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import {
  AcademicCapIcon,
  ClipboardDocumentListIcon,
  DocumentTextIcon,
  TrophyIcon,
  PlayIcon,
  BookOpenIcon,
  ChartBarIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

// Types
import { Student } from '../../types';

interface StudentOverviewProps {
  user: Student;
}

const StudentOverview: React.FC<StudentOverviewProps> = ({ user }) => {
  const navigate = useNavigate();
  const [stats, setStats] = useState({
    enrolledCourses: 0,
    completedCourses: 0,
    certificates: 0,
    totalWatchTime: 0,
    completedQuizzes: 0,
    averageScore: 0
  });

  useEffect(() => {
    // Simulate loading stats
    setStats({
      enrolledCourses: user.enrolledCourses?.length || 3,
      completedCourses: user.completedCourses?.length || 1,
      certificates: user.certificates?.length || 1,
      totalWatchTime: 240, // minutes
      completedQuizzes: 5,
      averageScore: 85
    });
  }, [user]);

  const statsCards = [
    {
      title: 'الكورسات المسجلة',
      value: stats.enrolledCourses,
      icon: AcademicCapIcon,
      color: 'blue',
      onClick: () => navigate('/student/courses')
    },
    {
      title: 'الكورسات المكتملة',
      value: stats.completedCourses,
      icon: BookOpenIcon,
      color: 'green',
      onClick: () => navigate('/student/courses?filter=completed')
    },
    {
      title: 'الشهادات',
      value: stats.certificates,
      icon: DocumentTextIcon,
      color: 'purple',
      onClick: () => navigate('/student/certificates')
    },
    {
      title: 'الاختبارات المكتملة',
      value: stats.completedQuizzes,
      icon: ClipboardDocumentListIcon,
      color: 'orange',
      onClick: () => navigate('/student/quizzes')
    }
  ];

  const recentCourses = [
    {
      id: '1',
      title: 'أساسيات البرمجة',
      progress: 75,
      thumbnail: '/api/placeholder/300/200',
      instructor: 'أ. محمد أحمد',
      lastAccessed: 'منذ يوم'
    },
    {
      id: '2',
      title: 'تطوير المواقع',
      progress: 45,
      thumbnail: '/api/placeholder/300/200',
      instructor: 'أ. سارة محمد',
      lastAccessed: 'منذ 3 أيام'
    },
    {
      id: '3',
      title: 'قواعد البيانات',
      progress: 20,
      thumbnail: '/api/placeholder/300/200',
      instructor: 'أ. أحمد علي',
      lastAccessed: 'منذ أسبوع'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-l from-primary-600 to-primary-700 rounded-xl p-6 text-white"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold mb-2">
              مرحباً بك، {user.name || 'الطالب'}!
            </h1>
            <p className="text-primary-100 mb-4">
              استمر في رحلة التعلم واكتشف المزيد من المعرفة
            </p>
            <div className="flex items-center space-x-4 space-x-reverse text-sm">
              <div className="flex items-center">
                <ClockIcon className="w-4 h-4 ml-1" />
                <span>{Math.floor(stats.totalWatchTime / 60)}س {stats.totalWatchTime % 60}د من المشاهدة</span>
              </div>
              <div className="flex items-center">
                <ChartBarIcon className="w-4 h-4 ml-1" />
                <span>متوسط النتائج: {stats.averageScore}%</span>
              </div>
            </div>
          </div>
          <div className="hidden md:block">
            <TrophyIcon className="w-20 h-20 text-primary-200" />
          </div>
        </div>
      </motion.div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsCards.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            whileHover={{ y: -2 }}
            onClick={stat.onClick}
            className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                <p className="text-3xl font-bold text-gray-900 mt-1">
                  {stat.value}
                </p>
              </div>
              <div className={`
                w-12 h-12 rounded-lg flex items-center justify-center
                ${stat.color === 'blue' ? 'bg-blue-100' : ''}
                ${stat.color === 'green' ? 'bg-green-100' : ''}
                ${stat.color === 'purple' ? 'bg-purple-100' : ''}
                ${stat.color === 'orange' ? 'bg-orange-100' : ''}
              `}>
                <stat.icon className={`
                  w-6 h-6
                  ${stat.color === 'blue' ? 'text-blue-600' : ''}
                  ${stat.color === 'green' ? 'text-green-600' : ''}
                  ${stat.color === 'purple' ? 'text-purple-600' : ''}
                  ${stat.color === 'orange' ? 'text-orange-600' : ''}
                `} />
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Recent Courses */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="bg-white rounded-xl p-6 shadow-sm"
      >
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-gray-900">الكورسات الأخيرة</h2>
          <button
            onClick={() => navigate('/student/courses')}
            className="text-primary-600 hover:text-primary-700 font-medium text-sm"
          >
            عرض الكل
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {recentCourses.map((course, index) => (
            <motion.div
              key={course.id}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.4 + index * 0.1 }}
              whileHover={{ y: -4 }}
              onClick={() => navigate(`/student/course/${course.id}`)}
              className="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-all duration-200 cursor-pointer"
            >
              <div className="aspect-video bg-gradient-to-br from-primary-400 to-primary-600 rounded-lg mb-4 flex items-center justify-center">
                <PlayIcon className="w-12 h-12 text-white" />
              </div>
              
              <h3 className="font-semibold text-gray-900 mb-2">{course.title}</h3>
              <p className="text-sm text-gray-600 mb-3">{course.instructor}</p>
              
              {/* Progress Bar */}
              <div className="mb-3">
                <div className="flex items-center justify-between text-sm mb-1">
                  <span className="text-gray-600">التقدم</span>
                  <span className="font-medium text-gray-900">{course.progress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${course.progress}%` }}
                  />
                </div>
              </div>
              
              <p className="text-xs text-gray-500">{course.lastAccessed}</p>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="bg-white rounded-xl p-6 shadow-sm"
      >
        <h2 className="text-xl font-bold text-gray-900 mb-4">إجراءات سريعة</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={() => navigate('/student/courses')}
            className="flex items-center p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200"
          >
            <AcademicCapIcon className="w-8 h-8 text-blue-600 ml-3" />
            <div className="text-right">
              <h3 className="font-semibold text-blue-900">تصفح الكورسات</h3>
              <p className="text-sm text-blue-700">اكتشف كورسات جديدة</p>
            </div>
          </button>
          
          <button
            onClick={() => navigate('/student/quizzes')}
            className="flex items-center p-4 bg-green-50 hover:bg-green-100 rounded-lg transition-colors duration-200"
          >
            <ClipboardDocumentListIcon className="w-8 h-8 text-green-600 ml-3" />
            <div className="text-right">
              <h3 className="font-semibold text-green-900">أداء اختبار</h3>
              <p className="text-sm text-green-700">اختبر معلوماتك</p>
            </div>
          </button>
          
          <button
            onClick={() => navigate('/student/certificates')}
            className="flex items-center p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors duration-200"
          >
            <DocumentTextIcon className="w-8 h-8 text-purple-600 ml-3" />
            <div className="text-right">
              <h3 className="font-semibold text-purple-900">شهاداتي</h3>
              <p className="text-sm text-purple-700">عرض الإنجازات</p>
            </div>
          </button>
        </div>
      </motion.div>
    </div>
  );
};

export default StudentOverview;
