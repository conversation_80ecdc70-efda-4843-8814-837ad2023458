{"ast": null, "code": "import React,{useState,useRef,useEffect}from'react';import{motion,AnimatePresence}from'framer-motion';import{XMarkIcon,PaperAirplaneIcon,MinusIcon,StarIcon}from'@heroicons/react/24/outline';// Types\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";// Custom AI Assistant Icon Component with Animation\nconst AIAssistantIcon=_ref=>{let{className=\"w-7 h-7\",animated=false}=_ref;return/*#__PURE__*/_jsxs(motion.svg,{className:className,viewBox:\"0 0 64 64\",fill:\"none\",xmlns:\"http://www.w3.org/2000/svg\",animate:animated?{scale:[1,1.05,1]}:{},transition:animated?{duration:2,repeat:Infinity,ease:\"easeInOut\"}:{},children:[/*#__PURE__*/_jsx(\"rect\",{x:\"18\",y:\"16\",width:\"28\",height:\"24\",rx:\"8\",fill:\"currentColor\",stroke:\"rgba(255,255,255,0.4)\",strokeWidth:\"1.5\"}),/*#__PURE__*/_jsx(\"rect\",{x:\"20\",y:\"18\",width:\"24\",height:\"20\",rx:\"6\",fill:\"rgba(255,255,255,0.1)\"}),/*#__PURE__*/_jsx(\"circle\",{cx:\"26\",cy:\"26\",r:\"4\",fill:\"rgba(255,255,255,0.3)\"}),/*#__PURE__*/_jsx(\"circle\",{cx:\"38\",cy:\"26\",r:\"4\",fill:\"rgba(255,255,255,0.3)\"}),/*#__PURE__*/_jsx(\"circle\",{cx:\"26\",cy:\"26\",r:\"3\",fill:\"rgba(255,255,255,0.95)\"}),/*#__PURE__*/_jsx(\"circle\",{cx:\"38\",cy:\"26\",r:\"3\",fill:\"rgba(255,255,255,0.95)\"}),/*#__PURE__*/_jsx(motion.circle,{cx:\"26\",cy:\"26\",r:\"1.5\",fill:\"rgba(59, 130, 246, 0.9)\",animate:animated?{fill:[\"rgba(59, 130, 246, 0.9)\",\"rgba(34, 197, 94, 0.9)\",\"rgba(59, 130, 246, 0.9)\"]}:{},transition:animated?{duration:3,repeat:Infinity,ease:\"easeInOut\"}:{}}),/*#__PURE__*/_jsx(motion.circle,{cx:\"38\",cy:\"26\",r:\"1.5\",fill:\"rgba(59, 130, 246, 0.9)\",animate:animated?{fill:[\"rgba(59, 130, 246, 0.9)\",\"rgba(34, 197, 94, 0.9)\",\"rgba(59, 130, 246, 0.9)\"]}:{},transition:animated?{duration:3,repeat:Infinity,ease:\"easeInOut\"}:{}}),/*#__PURE__*/_jsx(\"circle\",{cx:\"26\",cy:\"25\",r:\"0.5\",fill:\"rgba(255,255,255,0.8)\"}),/*#__PURE__*/_jsx(\"circle\",{cx:\"38\",cy:\"25\",r:\"0.5\",fill:\"rgba(255,255,255,0.8)\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M 28 32 Q 32 35 36 32\",stroke:\"rgba(255,255,255,0.8)\",strokeWidth:\"2\",strokeLinecap:\"round\",fill:\"none\"}),/*#__PURE__*/_jsx(\"line\",{x1:\"28\",y1:\"16\",x2:\"28\",y2:\"10\",stroke:\"rgba(255,255,255,0.7)\",strokeWidth:\"2.5\",strokeLinecap:\"round\"}),/*#__PURE__*/_jsx(\"line\",{x1:\"36\",y1:\"16\",x2:\"36\",y2:\"10\",stroke:\"rgba(255,255,255,0.7)\",strokeWidth:\"2.5\",strokeLinecap:\"round\"}),/*#__PURE__*/_jsx(\"circle\",{cx:\"28\",cy:\"8\",r:\"2.5\",fill:\"rgba(255,255,255,0.9)\"}),/*#__PURE__*/_jsx(\"circle\",{cx:\"36\",cy:\"8\",r:\"2.5\",fill:\"rgba(255,255,255,0.9)\"}),/*#__PURE__*/_jsx(motion.circle,{cx:\"28\",cy:\"8\",r:\"1.5\",fill:\"rgba(34, 197, 94, 0.8)\",animate:animated?{opacity:[0.8,1,0.8],scale:[1,1.1,1]}:{},transition:animated?{duration:1.5,repeat:Infinity,ease:\"easeInOut\"}:{}}),/*#__PURE__*/_jsx(motion.circle,{cx:\"36\",cy:\"8\",r:\"1.5\",fill:\"rgba(34, 197, 94, 0.8)\",animate:animated?{opacity:[0.8,1,0.8],scale:[1,1.1,1]}:{},transition:animated?{duration:1.5,repeat:Infinity,ease:\"easeInOut\",delay:0.3}:{}}),/*#__PURE__*/_jsx(\"rect\",{x:\"22\",y:\"40\",width:\"20\",height:\"16\",rx:\"4\",fill:\"currentColor\",stroke:\"rgba(255,255,255,0.3)\",strokeWidth:\"1.5\"}),/*#__PURE__*/_jsx(\"rect\",{x:\"24\",y:\"42\",width:\"16\",height:\"12\",rx:\"3\",fill:\"rgba(255,255,255,0.1)\"}),/*#__PURE__*/_jsx(\"rect\",{x:\"14\",y:\"44\",width:\"8\",height:\"6\",rx:\"3\",fill:\"currentColor\",stroke:\"rgba(255,255,255,0.2)\",strokeWidth:\"1\"}),/*#__PURE__*/_jsx(\"rect\",{x:\"42\",y:\"44\",width:\"8\",height:\"6\",rx:\"3\",fill:\"currentColor\",stroke:\"rgba(255,255,255,0.2)\",strokeWidth:\"1\"}),/*#__PURE__*/_jsx(\"circle\",{cx:\"32\",cy:\"46\",r:\"1.5\",fill:\"rgba(255,255,255,0.6)\"}),/*#__PURE__*/_jsx(\"rect\",{x:\"29\",y:\"50\",width:\"6\",height:\"1\",rx:\"0.5\",fill:\"rgba(255,255,255,0.5)\"}),/*#__PURE__*/_jsx(\"rect\",{x:\"30\",y:\"52\",width:\"4\",height:\"1\",rx:\"0.5\",fill:\"rgba(255,255,255,0.4)\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M 24 20 L 26 20 L 26 22\",stroke:\"rgba(255,255,255,0.3)\",strokeWidth:\"1\",fill:\"none\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M 40 20 L 38 20 L 38 22\",stroke:\"rgba(255,255,255,0.3)\",strokeWidth:\"1\",fill:\"none\"})]});};const AIAssistant=_ref2=>{let{context='student'}=_ref2;const[isOpen,setIsOpen]=useState(false);const[isMinimized,setIsMinimized]=useState(false);const getWelcomeMessage=()=>{switch(context){case'login':return'مرحباً! أنا المساعد الذكي لمنصة ALaa Abd Hamied. هل تحتاج مساعدة في تسجيل الدخول أو لديك أسئلة حول المنصة؟';case'admin':return'مرحباً أيها المدير! أنا هنا لمساعدتك في إدارة المنصة والإجابة على أي استفسارات تقنية.';default:return'مرحباً عزيزي الطالب! أنا المساعد الذكي لمنصة ALaa Abd Hamied. كيف يمكنني مساعدتك في رحلتك التعليمية اليوم؟';}};const[messages,setMessages]=useState([{id:'1',type:'assistant',content:getWelcomeMessage(),timestamp:new Date()}]);const[inputMessage,setInputMessage]=useState('');const[isTyping,setIsTyping]=useState(false);const messagesEndRef=useRef(null);const scrollToBottom=()=>{var _messagesEndRef$curre;(_messagesEndRef$curre=messagesEndRef.current)===null||_messagesEndRef$curre===void 0?void 0:_messagesEndRef$curre.scrollIntoView({behavior:'smooth'});};useEffect(()=>{scrollToBottom();},[messages]);const getQuickReplies=()=>{switch(context){case'login':return['كيف أسجل الدخول كطالب؟','نسيت كود الوصول','مشكلة في تسجيل الدخول','ما هي متطلبات النظام؟','كيف أحصل على حساب؟','التواصل مع الدعم'];case'admin':return['كيف أضيف كورس جديد؟','إدارة الطلاب','إنشاء اختبارات','تقارير الأداء','إعدادات النظام','النسخ الاحتياطي'];default:return['كيف أشاهد الكورسات؟','كيف أؤدي الاختبارات؟','كيف أحصل على الشهادة؟','مشكلة في تشغيل الفيديو','عرض تقدمي','التواصل مع الدعم'];}};const quickReplies=getQuickReplies();const getAIResponse=userMessage=>{const message=userMessage.toLowerCase();// Login context responses\nif(context==='login'){if(message.includes('دخول')||message.includes('تسجيل')){return'للطلاب: استخدم كود الوصول الخاص بك في خانة \"كود الوصول\". للمدراء: استخدم البريد الإلكتروني وكلمة المرور. تأكد من اختيار النوع الصحيح من أعلى الصفحة.';}if(message.includes('كود')||message.includes('نسيت')){return'إذا نسيت كود الوصول، تواصل مع المدير أو فريق الدعم للحصول على كود جديد. كود الوصول يتكون من أرقام وحروف ويُعطى لك عند التسجيل.';}if(message.includes('حساب')||message.includes('تسجيل جديد')){return'للحصول على حساب جديد، تواصل مع إدارة المنصة. سيتم إنشاء حساب لك وإرسال كود الوصول الخاص بك.';}if(message.includes('متطلبات')||message.includes('نظام')){return'المنصة تعمل على جميع المتصفحات الحديثة (Chrome, Firefox, Safari, Edge). تحتاج اتصال إنترنت مستقر لمشاهدة الفيديوهات.';}}if(message.includes('كورس')||message.includes('مشاهدة')){return context==='login'?'بعد تسجيل الدخول، ستجد جميع الكورسات المتاحة لك في لوحة التحكم الرئيسية.':'لمشاهدة الكورسات، اذهب إلى قسم \"كورساتي\" من القائمة الرئيسية. ستجد جميع الكورسات المتاحة لك مع إمكانية متابعة التقدم.';}if(message.includes('اختبار')||message.includes('امتحان')){return'يمكنك الوصول للاختبارات من داخل كل كورس. بعد مشاهدة الفيديوهات المطلوبة، ستظهر لك الاختبارات المتاحة. تأكد من قراءة التعليمات قبل البدء.';}if(message.includes('شهادة')){return'للحصول على الشهادة، يجب إكمال جميع فيديوهات الكورس واجتياز الاختبارات بنجاح. ستظهر الشهادة تلقائياً في قسم \"شهاداتي\".';}if(message.includes('فيديو')||message.includes('تشغيل')){return'إذا واجهت مشكلة في تشغيل الفيديو، تأكد من اتصالك بالإنترنت وحاول تحديث الصفحة. إذا استمرت المشكلة، جرب متصفح آخر.';}if(message.includes('مرور')||message.includes('كلمة')){return'لا يمكن تغيير كلمة المرور للطلاب حيث يتم الدخول بكود الوصول. إذا نسيت الكود، تواصل مع المدير للحصول على كود جديد.';}if(message.includes('دعم')||message.includes('مساعدة')||message.includes('مشكلة')){return'يمكنك التواصل مع فريق الدعم من خلال النقر على \"اتصل بنا\" في أسفل الصفحة، أو إرسال رسالة مباشرة للمدير.';}if(message.includes('مرحبا')||message.includes('السلام')||message.includes('أهلا')){return'أهلاً وسهلاً بك! أنا هنا لمساعدتك في أي استفسار حول المنصة. ما الذي تحتاج مساعدة فيه؟';}if(message.includes('شكرا')||message.includes('شكراً')){return'العفو! أتمنى أن أكون قد ساعدتك. لا تتردد في سؤالي عن أي شيء آخر.';}return'أعتذر، لم أفهم سؤالك بوضوح. يمكنك تجربة إحدى الأسئلة الشائعة أدناه، أو إعادة صياغة سؤالك بطريقة أخرى.';};const handleSendMessage=async()=>{if(!inputMessage.trim())return;const userMessage={id:Date.now().toString(),type:'user',content:inputMessage,timestamp:new Date()};setMessages(prev=>[...prev,userMessage]);setInputMessage('');setIsTyping(true);// Simulate AI thinking time\nsetTimeout(()=>{const aiResponse={id:(Date.now()+1).toString(),type:'assistant',content:getAIResponse(inputMessage),timestamp:new Date()};setMessages(prev=>[...prev,aiResponse]);setIsTyping(false);},1000+Math.random()*1000);};const handleQuickReply=reply=>{setInputMessage(reply);};const handleKeyPress=e=>{if(e.key==='Enter'&&!e.shiftKey){e.preventDefault();handleSendMessage();}};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(AnimatePresence,{children:!isOpen&&/*#__PURE__*/_jsxs(motion.div,{initial:{scale:0,opacity:0},animate:{scale:1,opacity:1},exit:{scale:0,opacity:0},className:\"fixed bottom-6 left-6 z-50\",children:[/*#__PURE__*/_jsxs(motion.button,{whileHover:{scale:1.1,rotate:5},whileTap:{scale:0.9},onClick:()=>setIsOpen(true),className:\"relative w-16 h-16 bg-gradient-to-br from-yellow-400 via-yellow-500 to-amber-600 text-white rounded-full shadow-2xl hover:shadow-yellow-500/25 transition-all duration-300 flex items-center justify-center group overflow-hidden\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 bg-gradient-to-br from-yellow-300 to-amber-500 rounded-full opacity-0 group-hover:opacity-20 transition-opacity duration-300\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"absolute inset-0 rounded-full\",children:[/*#__PURE__*/_jsx(motion.div,{animate:{rotate:360},transition:{duration:8,repeat:Infinity,ease:\"linear\"},className:\"absolute inset-2 border-2 border-yellow-300/30 rounded-full\"}),/*#__PURE__*/_jsx(motion.div,{animate:{rotate:-360},transition:{duration:12,repeat:Infinity,ease:\"linear\"},className:\"absolute inset-1 border border-yellow-200/20 rounded-full\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"relative z-10 flex items-center justify-center\",children:/*#__PURE__*/_jsx(AIAssistantIcon,{className:\"w-8 h-8 text-white drop-shadow-lg\",animated:true})}),/*#__PURE__*/_jsx(motion.div,{animate:{scale:[1,1.2,1],opacity:[0.7,1,0.7]},transition:{duration:2,repeat:Infinity,ease:\"easeInOut\"},className:\"absolute -top-1 -right-1 w-3 h-3 bg-yellow-300 rounded-full shadow-lg\"}),/*#__PURE__*/_jsx(motion.div,{animate:{scale:[1,1.3,1],opacity:[0.5,1,0.5]},transition:{duration:2.5,repeat:Infinity,ease:\"easeInOut\",delay:0.5},className:\"absolute -bottom-1 -left-1 w-2 h-2 bg-amber-300 rounded-full shadow-lg\"}),/*#__PURE__*/_jsx(motion.div,{animate:{scale:[1,1.1,1],opacity:[0.6,1,0.6]},transition:{duration:3,repeat:Infinity,ease:\"easeInOut\",delay:1},className:\"absolute top-1 -left-2 w-1.5 h-1.5 bg-yellow-200 rounded-full shadow-lg\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full animate-pulse border-2 border-white shadow-lg\"})]}),/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,x:-10},animate:{opacity:1,x:0},transition:{delay:2},className:\"absolute right-20 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-gray-900 to-gray-800 text-white px-4 py-2 rounded-lg text-sm whitespace-nowrap shadow-xl border border-gray-700\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 space-x-reverse\",children:[/*#__PURE__*/_jsx(AIAssistantIcon,{className:\"w-4 h-4 text-yellow-400\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u0627\\u0644\\u0645\\u0633\\u0627\\u0639\\u062F \\u0627\\u0644\\u0630\\u0643\\u064A - \\u0627\\u0636\\u063A\\u0637 \\u0644\\u0644\\u0645\\u0633\\u0627\\u0639\\u062F\\u0629\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute left-0 top-1/2 transform -translate-y-1/2 translate-x-1 w-2 h-2 bg-gray-900 rotate-45\"})]})]})}),/*#__PURE__*/_jsx(AnimatePresence,{children:isOpen&&/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:100,scale:0.9},animate:{opacity:1,y:0,scale:1,height:isMinimized?60:500},exit:{opacity:0,y:100,scale:0.9},className:\"fixed bottom-6 left-6 w-80 bg-white rounded-xl shadow-2xl border border-gray-200 z-50 overflow-hidden\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gradient-to-r from-yellow-500 via-amber-500 to-yellow-600 text-white p-4 flex items-center justify-between relative overflow-hidden\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"absolute inset-0 opacity-10\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-2 left-4 w-2 h-2 bg-white rounded-full animate-pulse\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-6 right-8 w-1 h-1 bg-white rounded-full animate-pulse\",style:{animationDelay:'0.5s'}}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute bottom-3 left-12 w-1.5 h-1.5 bg-white rounded-full animate-pulse\",style:{animationDelay:'1s'}})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center relative z-10\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-10 h-10 bg-gradient-to-br from-white/30 to-white/10 rounded-full flex items-center justify-center ml-3 backdrop-blur-sm border border-white/20\",children:/*#__PURE__*/_jsx(AIAssistantIcon,{className:\"w-6 h-6 text-white drop-shadow-lg\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"font-bold text-sm flex items-center\",children:[\"\\u0627\\u0644\\u0645\\u0633\\u0627\\u0639\\u062F \\u0627\\u0644\\u0630\\u0643\\u064A\",/*#__PURE__*/_jsx(StarIcon,{className:\"w-3 h-3 mr-1 text-yellow-200\"})]}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-xs opacity-90 flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-2 h-2 bg-green-400 rounded-full mr-1 animate-pulse\"}),\"\\u0645\\u062A\\u0627\\u062D \\u0627\\u0644\\u0622\\u0646 \\u0644\\u0644\\u0645\\u0633\\u0627\\u0639\\u062F\\u0629\"]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 space-x-reverse\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setIsMinimized(!isMinimized),className:\"p-1 hover:bg-white hover:bg-opacity-20 rounded\",children:/*#__PURE__*/_jsx(MinusIcon,{className:\"w-4 h-4\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setIsOpen(false),className:\"p-1 hover:bg-white hover:bg-opacity-20 rounded\",children:/*#__PURE__*/_jsx(XMarkIcon,{className:\"w-4 h-4\"})})]})]}),!isMinimized&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"h-80 overflow-y-auto p-4 space-y-4\",children:[messages.map(message=>/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:`flex ${message.type==='user'?'justify-start':'justify-end'}`,children:/*#__PURE__*/_jsx(\"div\",{className:`\n                        max-w-xs px-4 py-3 rounded-lg text-sm shadow-sm\n                        ${message.type==='user'?'bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-br-sm':'bg-gradient-to-r from-yellow-50 to-amber-50 text-gray-800 rounded-bl-sm border border-yellow-200/50'}\n                      `,children:message.content})},message.id)),isTyping&&/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0},animate:{opacity:1},className:\"flex justify-end\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-r from-yellow-50 to-amber-50 px-4 py-3 rounded-lg rounded-bl-sm border border-yellow-200/50 shadow-sm\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-1 items-center\",children:[/*#__PURE__*/_jsx(AIAssistantIcon,{className:\"w-4 h-4 text-yellow-600 mr-2\"}),/*#__PURE__*/_jsx(\"div\",{className:\"w-2 h-2 bg-yellow-500 rounded-full animate-bounce\"}),/*#__PURE__*/_jsx(\"div\",{className:\"w-2 h-2 bg-amber-500 rounded-full animate-bounce\",style:{animationDelay:'0.1s'}}),/*#__PURE__*/_jsx(\"div\",{className:\"w-2 h-2 bg-yellow-600 rounded-full animate-bounce\",style:{animationDelay:'0.2s'}}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-600 mr-2\",children:\"\\u064A\\u0643\\u062A\\u0628...\"})]})})}),/*#__PURE__*/_jsx(\"div\",{ref:messagesEndRef})]}),messages.length<=2&&/*#__PURE__*/_jsxs(\"div\",{className:\"px-4 pb-2\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500 mb-2\",children:\"\\u0623\\u0633\\u0626\\u0644\\u0629 \\u0634\\u0627\\u0626\\u0639\\u0629:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap gap-1\",children:quickReplies.slice(0,3).map(reply=>/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleQuickReply(reply),className:\"text-xs bg-gradient-to-r from-yellow-100 to-amber-100 hover:from-yellow-200 hover:to-amber-200 text-gray-700 px-3 py-1.5 rounded-full transition-all duration-200 border border-yellow-200/50 hover:border-yellow-300 shadow-sm hover:shadow-md\",children:reply},reply))})]}),/*#__PURE__*/_jsx(\"div\",{className:\"p-4 border-t border-gray-200\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 space-x-reverse\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:inputMessage,onChange:e=>setInputMessage(e.target.value),onKeyPress:handleKeyPress,placeholder:\"\\u0627\\u0643\\u062A\\u0628 \\u0631\\u0633\\u0627\\u0644\\u062A\\u0643...\",className:\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm\",disabled:isTyping}),/*#__PURE__*/_jsx(motion.button,{onClick:handleSendMessage,disabled:!inputMessage.trim()||isTyping,whileHover:{scale:1.05},whileTap:{scale:0.95},className:\"p-2.5 bg-gradient-to-r from-yellow-500 to-amber-600 text-white rounded-lg hover:from-yellow-600 hover:to-amber-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl disabled:hover:scale-100\",children:/*#__PURE__*/_jsx(PaperAirplaneIcon,{className:\"w-4 h-4\"})})]})})]})]})})]});};export default AIAssistant;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "motion", "AnimatePresence", "XMarkIcon", "PaperAirplaneIcon", "MinusIcon", "StarIcon", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "AIAssistantIcon", "_ref", "className", "animated", "svg", "viewBox", "fill", "xmlns", "animate", "scale", "transition", "duration", "repeat", "Infinity", "ease", "children", "x", "y", "width", "height", "rx", "stroke", "strokeWidth", "cx", "cy", "r", "circle", "d", "strokeLinecap", "x1", "y1", "x2", "y2", "opacity", "delay", "AIAssistant", "_ref2", "context", "isOpen", "setIsOpen", "isMinimized", "setIsMinimized", "getWelcomeMessage", "messages", "setMessages", "id", "type", "content", "timestamp", "Date", "inputMessage", "setInputMessage", "isTyping", "setIsTyping", "messagesEndRef", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "getQuickReplies", "quickReplies", "getAIResponse", "userMessage", "message", "toLowerCase", "includes", "handleSendMessage", "trim", "now", "toString", "prev", "setTimeout", "aiResponse", "Math", "random", "handleQuickReply", "reply", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "div", "initial", "exit", "button", "whileHover", "rotate", "whileTap", "onClick", "style", "animationDelay", "map", "ref", "length", "slice", "value", "onChange", "target", "onKeyPress", "placeholder", "disabled"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/AIAssistant/AIAssistant.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  ChatBubbleLeftRightIcon,\n  XMarkIcon,\n  PaperAirplaneIcon,\n  SparklesIcon,\n  MinusIcon,\n  StarIcon\n} from '@heroicons/react/24/outline';\nimport { SparklesIcon as SparklesIconSolid } from '@heroicons/react/24/solid';\n\n// Types\nimport { ChatMessage } from '../../types';\n\n// Custom AI Assistant Icon Component with Animation\nconst AIAssistantIcon: React.FC<{ className?: string; animated?: boolean }> = ({\n  className = \"w-7 h-7\",\n  animated = false\n}) => (\n  <motion.svg\n    className={className}\n    viewBox=\"0 0 64 64\"\n    fill=\"none\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n    animate={animated ? {\n      scale: [1, 1.05, 1],\n    } : {}}\n    transition={animated ? {\n      duration: 2,\n      repeat: Infinity,\n      ease: \"easeInOut\"\n    } : {}}\n  >\n    {/* Robot Head - Main */}\n    <rect\n      x=\"18\"\n      y=\"16\"\n      width=\"28\"\n      height=\"24\"\n      rx=\"8\"\n      fill=\"currentColor\"\n      stroke=\"rgba(255,255,255,0.4)\"\n      strokeWidth=\"1.5\"\n    />\n\n    {/* Robot Head - Inner glow */}\n    <rect\n      x=\"20\"\n      y=\"18\"\n      width=\"24\"\n      height=\"20\"\n      rx=\"6\"\n      fill=\"rgba(255,255,255,0.1)\"\n    />\n\n    {/* Robot Eyes - Outer glow */}\n    <circle cx=\"26\" cy=\"26\" r=\"4\" fill=\"rgba(255,255,255,0.3)\" />\n    <circle cx=\"38\" cy=\"26\" r=\"4\" fill=\"rgba(255,255,255,0.3)\" />\n\n    {/* Robot Eyes - Main */}\n    <circle cx=\"26\" cy=\"26\" r=\"3\" fill=\"rgba(255,255,255,0.95)\" />\n    <circle cx=\"38\" cy=\"26\" r=\"3\" fill=\"rgba(255,255,255,0.95)\" />\n\n    {/* Robot Eyes - Pupils with glow */}\n    <motion.circle\n      cx=\"26\"\n      cy=\"26\"\n      r=\"1.5\"\n      fill=\"rgba(59, 130, 246, 0.9)\"\n      animate={animated ? {\n        fill: [\"rgba(59, 130, 246, 0.9)\", \"rgba(34, 197, 94, 0.9)\", \"rgba(59, 130, 246, 0.9)\"]\n      } : {}}\n      transition={animated ? {\n        duration: 3,\n        repeat: Infinity,\n        ease: \"easeInOut\"\n      } : {}}\n    />\n    <motion.circle\n      cx=\"38\"\n      cy=\"26\"\n      r=\"1.5\"\n      fill=\"rgba(59, 130, 246, 0.9)\"\n      animate={animated ? {\n        fill: [\"rgba(59, 130, 246, 0.9)\", \"rgba(34, 197, 94, 0.9)\", \"rgba(59, 130, 246, 0.9)\"]\n      } : {}}\n      transition={animated ? {\n        duration: 3,\n        repeat: Infinity,\n        ease: \"easeInOut\"\n      } : {}}\n    />\n    <circle cx=\"26\" cy=\"25\" r=\"0.5\" fill=\"rgba(255,255,255,0.8)\" />\n    <circle cx=\"38\" cy=\"25\" r=\"0.5\" fill=\"rgba(255,255,255,0.8)\" />\n\n    {/* Robot Mouth - Smile */}\n    <path\n      d=\"M 28 32 Q 32 35 36 32\"\n      stroke=\"rgba(255,255,255,0.8)\"\n      strokeWidth=\"2\"\n      strokeLinecap=\"round\"\n      fill=\"none\"\n    />\n\n    {/* Robot Antennas */}\n    <line x1=\"28\" y1=\"16\" x2=\"28\" y2=\"10\" stroke=\"rgba(255,255,255,0.7)\" strokeWidth=\"2.5\" strokeLinecap=\"round\" />\n    <line x1=\"36\" y1=\"16\" x2=\"36\" y2=\"10\" stroke=\"rgba(255,255,255,0.7)\" strokeWidth=\"2.5\" strokeLinecap=\"round\" />\n\n    {/* Antenna Tips with glow */}\n    <circle cx=\"28\" cy=\"8\" r=\"2.5\" fill=\"rgba(255,255,255,0.9)\" />\n    <circle cx=\"36\" cy=\"8\" r=\"2.5\" fill=\"rgba(255,255,255,0.9)\" />\n    <motion.circle\n      cx=\"28\"\n      cy=\"8\"\n      r=\"1.5\"\n      fill=\"rgba(34, 197, 94, 0.8)\"\n      animate={animated ? {\n        opacity: [0.8, 1, 0.8],\n        scale: [1, 1.1, 1]\n      } : {}}\n      transition={animated ? {\n        duration: 1.5,\n        repeat: Infinity,\n        ease: \"easeInOut\"\n      } : {}}\n    />\n    <motion.circle\n      cx=\"36\"\n      cy=\"8\"\n      r=\"1.5\"\n      fill=\"rgba(34, 197, 94, 0.8)\"\n      animate={animated ? {\n        opacity: [0.8, 1, 0.8],\n        scale: [1, 1.1, 1]\n      } : {}}\n      transition={animated ? {\n        duration: 1.5,\n        repeat: Infinity,\n        ease: \"easeInOut\",\n        delay: 0.3\n      } : {}}\n    />\n\n    {/* Robot Body */}\n    <rect\n      x=\"22\"\n      y=\"40\"\n      width=\"20\"\n      height=\"16\"\n      rx=\"4\"\n      fill=\"currentColor\"\n      stroke=\"rgba(255,255,255,0.3)\"\n      strokeWidth=\"1.5\"\n    />\n\n    {/* Body Inner glow */}\n    <rect\n      x=\"24\"\n      y=\"42\"\n      width=\"16\"\n      height=\"12\"\n      rx=\"3\"\n      fill=\"rgba(255,255,255,0.1)\"\n    />\n\n    {/* Robot Arms */}\n    <rect x=\"14\" y=\"44\" width=\"8\" height=\"6\" rx=\"3\" fill=\"currentColor\" stroke=\"rgba(255,255,255,0.2)\" strokeWidth=\"1\" />\n    <rect x=\"42\" y=\"44\" width=\"8\" height=\"6\" rx=\"3\" fill=\"currentColor\" stroke=\"rgba(255,255,255,0.2)\" strokeWidth=\"1\" />\n\n    {/* Body Details */}\n    <circle cx=\"32\" cy=\"46\" r=\"1.5\" fill=\"rgba(255,255,255,0.6)\" />\n    <rect x=\"29\" y=\"50\" width=\"6\" height=\"1\" rx=\"0.5\" fill=\"rgba(255,255,255,0.5)\" />\n    <rect x=\"30\" y=\"52\" width=\"4\" height=\"1\" rx=\"0.5\" fill=\"rgba(255,255,255,0.4)\" />\n\n    {/* Decorative circuits */}\n    <path d=\"M 24 20 L 26 20 L 26 22\" stroke=\"rgba(255,255,255,0.3)\" strokeWidth=\"1\" fill=\"none\" />\n    <path d=\"M 40 20 L 38 20 L 38 22\" stroke=\"rgba(255,255,255,0.3)\" strokeWidth=\"1\" fill=\"none\" />\n  </motion.svg>\n);\n\n\n\ninterface AIAssistantProps {\n  context?: 'login' | 'student' | 'admin';\n}\n\nconst AIAssistant: React.FC<AIAssistantProps> = ({ context = 'student' }) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [isMinimized, setIsMinimized] = useState(false);\n\n  const getWelcomeMessage = () => {\n    switch (context) {\n      case 'login':\n        return 'مرحباً! أنا المساعد الذكي لمنصة ALaa Abd Hamied. هل تحتاج مساعدة في تسجيل الدخول أو لديك أسئلة حول المنصة؟';\n      case 'admin':\n        return 'مرحباً أيها المدير! أنا هنا لمساعدتك في إدارة المنصة والإجابة على أي استفسارات تقنية.';\n      default:\n        return 'مرحباً عزيزي الطالب! أنا المساعد الذكي لمنصة ALaa Abd Hamied. كيف يمكنني مساعدتك في رحلتك التعليمية اليوم؟';\n    }\n  };\n\n  const [messages, setMessages] = useState<ChatMessage[]>([\n    {\n      id: '1',\n      type: 'assistant',\n      content: getWelcomeMessage(),\n      timestamp: new Date()\n    }\n  ]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const getQuickReplies = () => {\n    switch (context) {\n      case 'login':\n        return [\n          'كيف أسجل الدخول كطالب؟',\n          'نسيت كود الوصول',\n          'مشكلة في تسجيل الدخول',\n          'ما هي متطلبات النظام؟',\n          'كيف أحصل على حساب؟',\n          'التواصل مع الدعم'\n        ];\n      case 'admin':\n        return [\n          'كيف أضيف كورس جديد؟',\n          'إدارة الطلاب',\n          'إنشاء اختبارات',\n          'تقارير الأداء',\n          'إعدادات النظام',\n          'النسخ الاحتياطي'\n        ];\n      default:\n        return [\n          'كيف أشاهد الكورسات؟',\n          'كيف أؤدي الاختبارات؟',\n          'كيف أحصل على الشهادة؟',\n          'مشكلة في تشغيل الفيديو',\n          'عرض تقدمي',\n          'التواصل مع الدعم'\n        ];\n    }\n  };\n\n  const quickReplies = getQuickReplies();\n\n  const getAIResponse = (userMessage: string): string => {\n    const message = userMessage.toLowerCase();\n\n    // Login context responses\n    if (context === 'login') {\n      if (message.includes('دخول') || message.includes('تسجيل')) {\n        return 'للطلاب: استخدم كود الوصول الخاص بك في خانة \"كود الوصول\". للمدراء: استخدم البريد الإلكتروني وكلمة المرور. تأكد من اختيار النوع الصحيح من أعلى الصفحة.';\n      }\n      if (message.includes('كود') || message.includes('نسيت')) {\n        return 'إذا نسيت كود الوصول، تواصل مع المدير أو فريق الدعم للحصول على كود جديد. كود الوصول يتكون من أرقام وحروف ويُعطى لك عند التسجيل.';\n      }\n      if (message.includes('حساب') || message.includes('تسجيل جديد')) {\n        return 'للحصول على حساب جديد، تواصل مع إدارة المنصة. سيتم إنشاء حساب لك وإرسال كود الوصول الخاص بك.';\n      }\n      if (message.includes('متطلبات') || message.includes('نظام')) {\n        return 'المنصة تعمل على جميع المتصفحات الحديثة (Chrome, Firefox, Safari, Edge). تحتاج اتصال إنترنت مستقر لمشاهدة الفيديوهات.';\n      }\n    }\n\n    if (message.includes('كورس') || message.includes('مشاهدة')) {\n      return context === 'login'\n        ? 'بعد تسجيل الدخول، ستجد جميع الكورسات المتاحة لك في لوحة التحكم الرئيسية.'\n        : 'لمشاهدة الكورسات، اذهب إلى قسم \"كورساتي\" من القائمة الرئيسية. ستجد جميع الكورسات المتاحة لك مع إمكانية متابعة التقدم.';\n    }\n    \n    if (message.includes('اختبار') || message.includes('امتحان')) {\n      return 'يمكنك الوصول للاختبارات من داخل كل كورس. بعد مشاهدة الفيديوهات المطلوبة، ستظهر لك الاختبارات المتاحة. تأكد من قراءة التعليمات قبل البدء.';\n    }\n    \n    if (message.includes('شهادة')) {\n      return 'للحصول على الشهادة، يجب إكمال جميع فيديوهات الكورس واجتياز الاختبارات بنجاح. ستظهر الشهادة تلقائياً في قسم \"شهاداتي\".';\n    }\n    \n    if (message.includes('فيديو') || message.includes('تشغيل')) {\n      return 'إذا واجهت مشكلة في تشغيل الفيديو، تأكد من اتصالك بالإنترنت وحاول تحديث الصفحة. إذا استمرت المشكلة، جرب متصفح آخر.';\n    }\n    \n    if (message.includes('مرور') || message.includes('كلمة')) {\n      return 'لا يمكن تغيير كلمة المرور للطلاب حيث يتم الدخول بكود الوصول. إذا نسيت الكود، تواصل مع المدير للحصول على كود جديد.';\n    }\n    \n    if (message.includes('دعم') || message.includes('مساعدة') || message.includes('مشكلة')) {\n      return 'يمكنك التواصل مع فريق الدعم من خلال النقر على \"اتصل بنا\" في أسفل الصفحة، أو إرسال رسالة مباشرة للمدير.';\n    }\n    \n    if (message.includes('مرحبا') || message.includes('السلام') || message.includes('أهلا')) {\n      return 'أهلاً وسهلاً بك! أنا هنا لمساعدتك في أي استفسار حول المنصة. ما الذي تحتاج مساعدة فيه؟';\n    }\n    \n    if (message.includes('شكرا') || message.includes('شكراً')) {\n      return 'العفو! أتمنى أن أكون قد ساعدتك. لا تتردد في سؤالي عن أي شيء آخر.';\n    }\n    \n    return 'أعتذر، لم أفهم سؤالك بوضوح. يمكنك تجربة إحدى الأسئلة الشائعة أدناه، أو إعادة صياغة سؤالك بطريقة أخرى.';\n  };\n\n  const handleSendMessage = async () => {\n    if (!inputMessage.trim()) return;\n\n    const userMessage: ChatMessage = {\n      id: Date.now().toString(),\n      type: 'user',\n      content: inputMessage,\n      timestamp: new Date()\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setInputMessage('');\n    setIsTyping(true);\n\n    // Simulate AI thinking time\n    setTimeout(() => {\n      const aiResponse: ChatMessage = {\n        id: (Date.now() + 1).toString(),\n        type: 'assistant',\n        content: getAIResponse(inputMessage),\n        timestamp: new Date()\n      };\n\n      setMessages(prev => [...prev, aiResponse]);\n      setIsTyping(false);\n    }, 1000 + Math.random() * 1000);\n  };\n\n  const handleQuickReply = (reply: string) => {\n    setInputMessage(reply);\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  return (\n    <>\n      {/* Chat Button */}\n      <AnimatePresence>\n        {!isOpen && (\n          <motion.div\n            initial={{ scale: 0, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            exit={{ scale: 0, opacity: 0 }}\n            className=\"fixed bottom-6 left-6 z-50\"\n          >\n            <motion.button\n              whileHover={{ scale: 1.1, rotate: 5 }}\n              whileTap={{ scale: 0.9 }}\n              onClick={() => setIsOpen(true)}\n              className=\"relative w-16 h-16 bg-gradient-to-br from-yellow-400 via-yellow-500 to-amber-600 text-white rounded-full shadow-2xl hover:shadow-yellow-500/25 transition-all duration-300 flex items-center justify-center group overflow-hidden\"\n            >\n              {/* Background glow effect */}\n              <div className=\"absolute inset-0 bg-gradient-to-br from-yellow-300 to-amber-500 rounded-full opacity-0 group-hover:opacity-20 transition-opacity duration-300\" />\n\n              {/* Sparkle animation background */}\n              <div className=\"absolute inset-0 rounded-full\">\n                <motion.div\n                  animate={{ rotate: 360 }}\n                  transition={{ duration: 8, repeat: Infinity, ease: \"linear\" }}\n                  className=\"absolute inset-2 border-2 border-yellow-300/30 rounded-full\"\n                />\n                <motion.div\n                  animate={{ rotate: -360 }}\n                  transition={{ duration: 12, repeat: Infinity, ease: \"linear\" }}\n                  className=\"absolute inset-1 border border-yellow-200/20 rounded-full\"\n                />\n              </div>\n\n              {/* Main icon */}\n              <div className=\"relative z-10 flex items-center justify-center\">\n                <AIAssistantIcon className=\"w-8 h-8 text-white drop-shadow-lg\" animated={true} />\n              </div>\n\n              {/* Floating sparkles */}\n              <motion.div\n                animate={{\n                  scale: [1, 1.2, 1],\n                  opacity: [0.7, 1, 0.7]\n                }}\n                transition={{\n                  duration: 2,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                }}\n                className=\"absolute -top-1 -right-1 w-3 h-3 bg-yellow-300 rounded-full shadow-lg\"\n              />\n\n              <motion.div\n                animate={{\n                  scale: [1, 1.3, 1],\n                  opacity: [0.5, 1, 0.5]\n                }}\n                transition={{\n                  duration: 2.5,\n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                  delay: 0.5\n                }}\n                className=\"absolute -bottom-1 -left-1 w-2 h-2 bg-amber-300 rounded-full shadow-lg\"\n              />\n\n              <motion.div\n                animate={{\n                  scale: [1, 1.1, 1],\n                  opacity: [0.6, 1, 0.6]\n                }}\n                transition={{\n                  duration: 3,\n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                  delay: 1\n                }}\n                className=\"absolute top-1 -left-2 w-1.5 h-1.5 bg-yellow-200 rounded-full shadow-lg\"\n              />\n\n              {/* Status indicator */}\n              <div className=\"absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full animate-pulse border-2 border-white shadow-lg\" />\n            </motion.button>\n\n            {/* Tooltip */}\n            <motion.div\n              initial={{ opacity: 0, x: -10 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: 2 }}\n              className=\"absolute right-20 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-gray-900 to-gray-800 text-white px-4 py-2 rounded-lg text-sm whitespace-nowrap shadow-xl border border-gray-700\"\n            >\n              <div className=\"flex items-center space-x-2 space-x-reverse\">\n                <AIAssistantIcon className=\"w-4 h-4 text-yellow-400\" />\n                <span>المساعد الذكي - اضغط للمساعدة</span>\n              </div>\n              <div className=\"absolute left-0 top-1/2 transform -translate-y-1/2 translate-x-1 w-2 h-2 bg-gray-900 rotate-45\" />\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Chat Window */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ opacity: 0, y: 100, scale: 0.9 }}\n            animate={{ \n              opacity: 1, \n              y: 0, \n              scale: 1,\n              height: isMinimized ? 60 : 500\n            }}\n            exit={{ opacity: 0, y: 100, scale: 0.9 }}\n            className=\"fixed bottom-6 left-6 w-80 bg-white rounded-xl shadow-2xl border border-gray-200 z-50 overflow-hidden\"\n          >\n            {/* Header */}\n            <div className=\"bg-gradient-to-r from-yellow-500 via-amber-500 to-yellow-600 text-white p-4 flex items-center justify-between relative overflow-hidden\">\n              {/* Background pattern */}\n              <div className=\"absolute inset-0 opacity-10\">\n                <div className=\"absolute top-2 left-4 w-2 h-2 bg-white rounded-full animate-pulse\" />\n                <div className=\"absolute top-6 right-8 w-1 h-1 bg-white rounded-full animate-pulse\" style={{ animationDelay: '0.5s' }} />\n                <div className=\"absolute bottom-3 left-12 w-1.5 h-1.5 bg-white rounded-full animate-pulse\" style={{ animationDelay: '1s' }} />\n              </div>\n\n              <div className=\"flex items-center relative z-10\">\n                <div className=\"w-10 h-10 bg-gradient-to-br from-white/30 to-white/10 rounded-full flex items-center justify-center ml-3 backdrop-blur-sm border border-white/20\">\n                  <AIAssistantIcon className=\"w-6 h-6 text-white drop-shadow-lg\" />\n                </div>\n                <div>\n                  <h3 className=\"font-bold text-sm flex items-center\">\n                    المساعد الذكي\n                    <StarIcon className=\"w-3 h-3 mr-1 text-yellow-200\" />\n                  </h3>\n                  <p className=\"text-xs opacity-90 flex items-center\">\n                    <div className=\"w-2 h-2 bg-green-400 rounded-full mr-1 animate-pulse\" />\n                    متاح الآن للمساعدة\n                  </p>\n                </div>\n              </div>\n              <div className=\"flex items-center space-x-2 space-x-reverse\">\n                <button\n                  onClick={() => setIsMinimized(!isMinimized)}\n                  className=\"p-1 hover:bg-white hover:bg-opacity-20 rounded\"\n                >\n                  <MinusIcon className=\"w-4 h-4\" />\n                </button>\n                <button\n                  onClick={() => setIsOpen(false)}\n                  className=\"p-1 hover:bg-white hover:bg-opacity-20 rounded\"\n                >\n                  <XMarkIcon className=\"w-4 h-4\" />\n                </button>\n              </div>\n            </div>\n\n            {/* Chat Content */}\n            {!isMinimized && (\n              <>\n                {/* Messages */}\n                <div className=\"h-80 overflow-y-auto p-4 space-y-4\">\n                  {messages.map((message) => (\n                    <motion.div\n                      key={message.id}\n                      initial={{ opacity: 0, y: 10 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      className={`flex ${message.type === 'user' ? 'justify-start' : 'justify-end'}`}\n                    >\n                      <div className={`\n                        max-w-xs px-4 py-3 rounded-lg text-sm shadow-sm\n                        ${message.type === 'user'\n                          ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-br-sm'\n                          : 'bg-gradient-to-r from-yellow-50 to-amber-50 text-gray-800 rounded-bl-sm border border-yellow-200/50'\n                        }\n                      `}>\n                        {message.content}\n                      </div>\n                    </motion.div>\n                  ))}\n\n                  {/* Typing Indicator */}\n                  {isTyping && (\n                    <motion.div\n                      initial={{ opacity: 0 }}\n                      animate={{ opacity: 1 }}\n                      className=\"flex justify-end\"\n                    >\n                      <div className=\"bg-gradient-to-r from-yellow-50 to-amber-50 px-4 py-3 rounded-lg rounded-bl-sm border border-yellow-200/50 shadow-sm\">\n                        <div className=\"flex space-x-1 items-center\">\n                          <AIAssistantIcon className=\"w-4 h-4 text-yellow-600 mr-2\" />\n                          <div className=\"w-2 h-2 bg-yellow-500 rounded-full animate-bounce\" />\n                          <div className=\"w-2 h-2 bg-amber-500 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }} />\n                          <div className=\"w-2 h-2 bg-yellow-600 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }} />\n                          <span className=\"text-xs text-gray-600 mr-2\">يكتب...</span>\n                        </div>\n                      </div>\n                    </motion.div>\n                  )}\n\n                  <div ref={messagesEndRef} />\n                </div>\n\n                {/* Quick Replies */}\n                {messages.length <= 2 && (\n                  <div className=\"px-4 pb-2\">\n                    <p className=\"text-xs text-gray-500 mb-2\">أسئلة شائعة:</p>\n                    <div className=\"flex flex-wrap gap-1\">\n                      {quickReplies.slice(0, 3).map((reply) => (\n                        <button\n                          key={reply}\n                          onClick={() => handleQuickReply(reply)}\n                          className=\"text-xs bg-gradient-to-r from-yellow-100 to-amber-100 hover:from-yellow-200 hover:to-amber-200 text-gray-700 px-3 py-1.5 rounded-full transition-all duration-200 border border-yellow-200/50 hover:border-yellow-300 shadow-sm hover:shadow-md\"\n                        >\n                          {reply}\n                        </button>\n                      ))}\n                    </div>\n                  </div>\n                )}\n\n                {/* Input */}\n                <div className=\"p-4 border-t border-gray-200\">\n                  <div className=\"flex items-center space-x-2 space-x-reverse\">\n                    <input\n                      type=\"text\"\n                      value={inputMessage}\n                      onChange={(e) => setInputMessage(e.target.value)}\n                      onKeyPress={handleKeyPress}\n                      placeholder=\"اكتب رسالتك...\"\n                      className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm\"\n                      disabled={isTyping}\n                    />\n                    <motion.button\n                      onClick={handleSendMessage}\n                      disabled={!inputMessage.trim() || isTyping}\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      className=\"p-2.5 bg-gradient-to-r from-yellow-500 to-amber-600 text-white rounded-lg hover:from-yellow-600 hover:to-amber-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl disabled:hover:scale-100\"\n                    >\n                      <PaperAirplaneIcon className=\"w-4 h-4\" />\n                    </motion.button>\n                  </div>\n                </div>\n              </>\n            )}\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </>\n  );\n};\n\nexport default AIAssistant;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,CAAEC,SAAS,KAAQ,OAAO,CAC1D,OAASC,MAAM,CAAEC,eAAe,KAAQ,eAAe,CACvD,OAEEC,SAAS,CACTC,iBAAiB,CAEjBC,SAAS,CACTC,QAAQ,KACH,6BAA6B,CAGpC;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAGA;AACA,KAAM,CAAAC,eAAqE,CAAGC,IAAA,MAAC,CAC7EC,SAAS,CAAG,SAAS,CACrBC,QAAQ,CAAG,KACb,CAAC,CAAAF,IAAA,oBACCJ,KAAA,CAACT,MAAM,CAACgB,GAAG,EACTF,SAAS,CAAEA,SAAU,CACrBG,OAAO,CAAC,WAAW,CACnBC,IAAI,CAAC,MAAM,CACXC,KAAK,CAAC,4BAA4B,CAClCC,OAAO,CAAEL,QAAQ,CAAG,CAClBM,KAAK,CAAE,CAAC,CAAC,CAAE,IAAI,CAAE,CAAC,CACpB,CAAC,CAAG,CAAC,CAAE,CACPC,UAAU,CAAEP,QAAQ,CAAG,CACrBQ,QAAQ,CAAE,CAAC,CACXC,MAAM,CAAEC,QAAQ,CAChBC,IAAI,CAAE,WACR,CAAC,CAAG,CAAC,CAAE,CAAAC,QAAA,eAGPpB,IAAA,SACEqB,CAAC,CAAC,IAAI,CACNC,CAAC,CAAC,IAAI,CACNC,KAAK,CAAC,IAAI,CACVC,MAAM,CAAC,IAAI,CACXC,EAAE,CAAC,GAAG,CACNd,IAAI,CAAC,cAAc,CACnBe,MAAM,CAAC,uBAAuB,CAC9BC,WAAW,CAAC,KAAK,CAClB,CAAC,cAGF3B,IAAA,SACEqB,CAAC,CAAC,IAAI,CACNC,CAAC,CAAC,IAAI,CACNC,KAAK,CAAC,IAAI,CACVC,MAAM,CAAC,IAAI,CACXC,EAAE,CAAC,GAAG,CACNd,IAAI,CAAC,uBAAuB,CAC7B,CAAC,cAGFX,IAAA,WAAQ4B,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,GAAG,CAACnB,IAAI,CAAC,uBAAuB,CAAE,CAAC,cAC7DX,IAAA,WAAQ4B,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,GAAG,CAACnB,IAAI,CAAC,uBAAuB,CAAE,CAAC,cAG7DX,IAAA,WAAQ4B,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,GAAG,CAACnB,IAAI,CAAC,wBAAwB,CAAE,CAAC,cAC9DX,IAAA,WAAQ4B,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,GAAG,CAACnB,IAAI,CAAC,wBAAwB,CAAE,CAAC,cAG9DX,IAAA,CAACP,MAAM,CAACsC,MAAM,EACZH,EAAE,CAAC,IAAI,CACPC,EAAE,CAAC,IAAI,CACPC,CAAC,CAAC,KAAK,CACPnB,IAAI,CAAC,yBAAyB,CAC9BE,OAAO,CAAEL,QAAQ,CAAG,CAClBG,IAAI,CAAE,CAAC,yBAAyB,CAAE,wBAAwB,CAAE,yBAAyB,CACvF,CAAC,CAAG,CAAC,CAAE,CACPI,UAAU,CAAEP,QAAQ,CAAG,CACrBQ,QAAQ,CAAE,CAAC,CACXC,MAAM,CAAEC,QAAQ,CAChBC,IAAI,CAAE,WACR,CAAC,CAAG,CAAC,CAAE,CACR,CAAC,cACFnB,IAAA,CAACP,MAAM,CAACsC,MAAM,EACZH,EAAE,CAAC,IAAI,CACPC,EAAE,CAAC,IAAI,CACPC,CAAC,CAAC,KAAK,CACPnB,IAAI,CAAC,yBAAyB,CAC9BE,OAAO,CAAEL,QAAQ,CAAG,CAClBG,IAAI,CAAE,CAAC,yBAAyB,CAAE,wBAAwB,CAAE,yBAAyB,CACvF,CAAC,CAAG,CAAC,CAAE,CACPI,UAAU,CAAEP,QAAQ,CAAG,CACrBQ,QAAQ,CAAE,CAAC,CACXC,MAAM,CAAEC,QAAQ,CAChBC,IAAI,CAAE,WACR,CAAC,CAAG,CAAC,CAAE,CACR,CAAC,cACFnB,IAAA,WAAQ4B,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,KAAK,CAACnB,IAAI,CAAC,uBAAuB,CAAE,CAAC,cAC/DX,IAAA,WAAQ4B,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,KAAK,CAACnB,IAAI,CAAC,uBAAuB,CAAE,CAAC,cAG/DX,IAAA,SACEgC,CAAC,CAAC,uBAAuB,CACzBN,MAAM,CAAC,uBAAuB,CAC9BC,WAAW,CAAC,GAAG,CACfM,aAAa,CAAC,OAAO,CACrBtB,IAAI,CAAC,MAAM,CACZ,CAAC,cAGFX,IAAA,SAAMkC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACX,MAAM,CAAC,uBAAuB,CAACC,WAAW,CAAC,KAAK,CAACM,aAAa,CAAC,OAAO,CAAE,CAAC,cAC/GjC,IAAA,SAAMkC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACX,MAAM,CAAC,uBAAuB,CAACC,WAAW,CAAC,KAAK,CAACM,aAAa,CAAC,OAAO,CAAE,CAAC,cAG/GjC,IAAA,WAAQ4B,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,GAAG,CAACC,CAAC,CAAC,KAAK,CAACnB,IAAI,CAAC,uBAAuB,CAAE,CAAC,cAC9DX,IAAA,WAAQ4B,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,GAAG,CAACC,CAAC,CAAC,KAAK,CAACnB,IAAI,CAAC,uBAAuB,CAAE,CAAC,cAC9DX,IAAA,CAACP,MAAM,CAACsC,MAAM,EACZH,EAAE,CAAC,IAAI,CACPC,EAAE,CAAC,GAAG,CACNC,CAAC,CAAC,KAAK,CACPnB,IAAI,CAAC,wBAAwB,CAC7BE,OAAO,CAAEL,QAAQ,CAAG,CAClB8B,OAAO,CAAE,CAAC,GAAG,CAAE,CAAC,CAAE,GAAG,CAAC,CACtBxB,KAAK,CAAE,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CACnB,CAAC,CAAG,CAAC,CAAE,CACPC,UAAU,CAAEP,QAAQ,CAAG,CACrBQ,QAAQ,CAAE,GAAG,CACbC,MAAM,CAAEC,QAAQ,CAChBC,IAAI,CAAE,WACR,CAAC,CAAG,CAAC,CAAE,CACR,CAAC,cACFnB,IAAA,CAACP,MAAM,CAACsC,MAAM,EACZH,EAAE,CAAC,IAAI,CACPC,EAAE,CAAC,GAAG,CACNC,CAAC,CAAC,KAAK,CACPnB,IAAI,CAAC,wBAAwB,CAC7BE,OAAO,CAAEL,QAAQ,CAAG,CAClB8B,OAAO,CAAE,CAAC,GAAG,CAAE,CAAC,CAAE,GAAG,CAAC,CACtBxB,KAAK,CAAE,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CACnB,CAAC,CAAG,CAAC,CAAE,CACPC,UAAU,CAAEP,QAAQ,CAAG,CACrBQ,QAAQ,CAAE,GAAG,CACbC,MAAM,CAAEC,QAAQ,CAChBC,IAAI,CAAE,WAAW,CACjBoB,KAAK,CAAE,GACT,CAAC,CAAG,CAAC,CAAE,CACR,CAAC,cAGFvC,IAAA,SACEqB,CAAC,CAAC,IAAI,CACNC,CAAC,CAAC,IAAI,CACNC,KAAK,CAAC,IAAI,CACVC,MAAM,CAAC,IAAI,CACXC,EAAE,CAAC,GAAG,CACNd,IAAI,CAAC,cAAc,CACnBe,MAAM,CAAC,uBAAuB,CAC9BC,WAAW,CAAC,KAAK,CAClB,CAAC,cAGF3B,IAAA,SACEqB,CAAC,CAAC,IAAI,CACNC,CAAC,CAAC,IAAI,CACNC,KAAK,CAAC,IAAI,CACVC,MAAM,CAAC,IAAI,CACXC,EAAE,CAAC,GAAG,CACNd,IAAI,CAAC,uBAAuB,CAC7B,CAAC,cAGFX,IAAA,SAAMqB,CAAC,CAAC,IAAI,CAACC,CAAC,CAAC,IAAI,CAACC,KAAK,CAAC,GAAG,CAACC,MAAM,CAAC,GAAG,CAACC,EAAE,CAAC,GAAG,CAACd,IAAI,CAAC,cAAc,CAACe,MAAM,CAAC,uBAAuB,CAACC,WAAW,CAAC,GAAG,CAAE,CAAC,cACrH3B,IAAA,SAAMqB,CAAC,CAAC,IAAI,CAACC,CAAC,CAAC,IAAI,CAACC,KAAK,CAAC,GAAG,CAACC,MAAM,CAAC,GAAG,CAACC,EAAE,CAAC,GAAG,CAACd,IAAI,CAAC,cAAc,CAACe,MAAM,CAAC,uBAAuB,CAACC,WAAW,CAAC,GAAG,CAAE,CAAC,cAGrH3B,IAAA,WAAQ4B,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,KAAK,CAACnB,IAAI,CAAC,uBAAuB,CAAE,CAAC,cAC/DX,IAAA,SAAMqB,CAAC,CAAC,IAAI,CAACC,CAAC,CAAC,IAAI,CAACC,KAAK,CAAC,GAAG,CAACC,MAAM,CAAC,GAAG,CAACC,EAAE,CAAC,KAAK,CAACd,IAAI,CAAC,uBAAuB,CAAE,CAAC,cACjFX,IAAA,SAAMqB,CAAC,CAAC,IAAI,CAACC,CAAC,CAAC,IAAI,CAACC,KAAK,CAAC,GAAG,CAACC,MAAM,CAAC,GAAG,CAACC,EAAE,CAAC,KAAK,CAACd,IAAI,CAAC,uBAAuB,CAAE,CAAC,cAGjFX,IAAA,SAAMgC,CAAC,CAAC,yBAAyB,CAACN,MAAM,CAAC,uBAAuB,CAACC,WAAW,CAAC,GAAG,CAAChB,IAAI,CAAC,MAAM,CAAE,CAAC,cAC/FX,IAAA,SAAMgC,CAAC,CAAC,yBAAyB,CAACN,MAAM,CAAC,uBAAuB,CAACC,WAAW,CAAC,GAAG,CAAChB,IAAI,CAAC,MAAM,CAAE,CAAC,EACrF,CAAC,EACd,CAQD,KAAM,CAAA6B,WAAuC,CAAGC,KAAA,EAA6B,IAA5B,CAAEC,OAAO,CAAG,SAAU,CAAC,CAAAD,KAAA,CACtE,KAAM,CAACE,MAAM,CAAEC,SAAS,CAAC,CAAGtD,QAAQ,CAAC,KAAK,CAAC,CAC3C,KAAM,CAACuD,WAAW,CAAEC,cAAc,CAAC,CAAGxD,QAAQ,CAAC,KAAK,CAAC,CAErD,KAAM,CAAAyD,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,OAAQL,OAAO,EACb,IAAK,OAAO,CACV,MAAO,4GAA4G,CACrH,IAAK,OAAO,CACV,MAAO,uFAAuF,CAChG,QACE,MAAO,4GAA4G,CACvH,CACF,CAAC,CAED,KAAM,CAACM,QAAQ,CAAEC,WAAW,CAAC,CAAG3D,QAAQ,CAAgB,CACtD,CACE4D,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,WAAW,CACjBC,OAAO,CAAEL,iBAAiB,CAAC,CAAC,CAC5BM,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CACtB,CAAC,CACF,CAAC,CACF,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGlE,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACmE,QAAQ,CAAEC,WAAW,CAAC,CAAGpE,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAAAqE,cAAc,CAAGpE,MAAM,CAAiB,IAAI,CAAC,CAEnD,KAAM,CAAAqE,cAAc,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAC3B,CAAAA,qBAAA,CAAAF,cAAc,CAACG,OAAO,UAAAD,qBAAA,iBAAtBA,qBAAA,CAAwBE,cAAc,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CAChE,CAAC,CAEDxE,SAAS,CAAC,IAAM,CACdoE,cAAc,CAAC,CAAC,CAClB,CAAC,CAAE,CAACZ,QAAQ,CAAC,CAAC,CAEd,KAAM,CAAAiB,eAAe,CAAGA,CAAA,GAAM,CAC5B,OAAQvB,OAAO,EACb,IAAK,OAAO,CACV,MAAO,CACL,wBAAwB,CACxB,iBAAiB,CACjB,uBAAuB,CACvB,uBAAuB,CACvB,oBAAoB,CACpB,kBAAkB,CACnB,CACH,IAAK,OAAO,CACV,MAAO,CACL,qBAAqB,CACrB,cAAc,CACd,gBAAgB,CAChB,eAAe,CACf,gBAAgB,CAChB,iBAAiB,CAClB,CACH,QACE,MAAO,CACL,qBAAqB,CACrB,sBAAsB,CACtB,uBAAuB,CACvB,wBAAwB,CACxB,WAAW,CACX,kBAAkB,CACnB,CACL,CACF,CAAC,CAED,KAAM,CAAAwB,YAAY,CAAGD,eAAe,CAAC,CAAC,CAEtC,KAAM,CAAAE,aAAa,CAAIC,WAAmB,EAAa,CACrD,KAAM,CAAAC,OAAO,CAAGD,WAAW,CAACE,WAAW,CAAC,CAAC,CAEzC;AACA,GAAI5B,OAAO,GAAK,OAAO,CAAE,CACvB,GAAI2B,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAIF,OAAO,CAACE,QAAQ,CAAC,OAAO,CAAC,CAAE,CACzD,MAAO,sJAAsJ,CAC/J,CACA,GAAIF,OAAO,CAACE,QAAQ,CAAC,KAAK,CAAC,EAAIF,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,CAAE,CACvD,MAAO,gIAAgI,CACzI,CACA,GAAIF,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAIF,OAAO,CAACE,QAAQ,CAAC,YAAY,CAAC,CAAE,CAC9D,MAAO,6FAA6F,CACtG,CACA,GAAIF,OAAO,CAACE,QAAQ,CAAC,SAAS,CAAC,EAAIF,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,CAAE,CAC3D,MAAO,sHAAsH,CAC/H,CACF,CAEA,GAAIF,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAIF,OAAO,CAACE,QAAQ,CAAC,QAAQ,CAAC,CAAE,CAC1D,MAAO,CAAA7B,OAAO,GAAK,OAAO,CACtB,0EAA0E,CAC1E,uHAAuH,CAC7H,CAEA,GAAI2B,OAAO,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAIF,OAAO,CAACE,QAAQ,CAAC,QAAQ,CAAC,CAAE,CAC5D,MAAO,0IAA0I,CACnJ,CAEA,GAAIF,OAAO,CAACE,QAAQ,CAAC,OAAO,CAAC,CAAE,CAC7B,MAAO,uHAAuH,CAChI,CAEA,GAAIF,OAAO,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAIF,OAAO,CAACE,QAAQ,CAAC,OAAO,CAAC,CAAE,CAC1D,MAAO,mHAAmH,CAC5H,CAEA,GAAIF,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAIF,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,CAAE,CACxD,MAAO,mHAAmH,CAC5H,CAEA,GAAIF,OAAO,CAACE,QAAQ,CAAC,KAAK,CAAC,EAAIF,OAAO,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAIF,OAAO,CAACE,QAAQ,CAAC,OAAO,CAAC,CAAE,CACtF,MAAO,wGAAwG,CACjH,CAEA,GAAIF,OAAO,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAIF,OAAO,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAIF,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,CAAE,CACvF,MAAO,uFAAuF,CAChG,CAEA,GAAIF,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAIF,OAAO,CAACE,QAAQ,CAAC,OAAO,CAAC,CAAE,CACzD,MAAO,kEAAkE,CAC3E,CAEA,MAAO,uGAAuG,CAChH,CAAC,CAED,KAAM,CAAAC,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CAACjB,YAAY,CAACkB,IAAI,CAAC,CAAC,CAAE,OAE1B,KAAM,CAAAL,WAAwB,CAAG,CAC/BlB,EAAE,CAAEI,IAAI,CAACoB,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CACzBxB,IAAI,CAAE,MAAM,CACZC,OAAO,CAAEG,YAAY,CACrBF,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CACtB,CAAC,CAEDL,WAAW,CAAC2B,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAER,WAAW,CAAC,CAAC,CAC3CZ,eAAe,CAAC,EAAE,CAAC,CACnBE,WAAW,CAAC,IAAI,CAAC,CAEjB;AACAmB,UAAU,CAAC,IAAM,CACf,KAAM,CAAAC,UAAuB,CAAG,CAC9B5B,EAAE,CAAE,CAACI,IAAI,CAACoB,GAAG,CAAC,CAAC,CAAG,CAAC,EAAEC,QAAQ,CAAC,CAAC,CAC/BxB,IAAI,CAAE,WAAW,CACjBC,OAAO,CAAEe,aAAa,CAACZ,YAAY,CAAC,CACpCF,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CACtB,CAAC,CAEDL,WAAW,CAAC2B,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAEE,UAAU,CAAC,CAAC,CAC1CpB,WAAW,CAAC,KAAK,CAAC,CACpB,CAAC,CAAE,IAAI,CAAGqB,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,IAAI,CAAC,CACjC,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAIC,KAAa,EAAK,CAC1C1B,eAAe,CAAC0B,KAAK,CAAC,CACxB,CAAC,CAED,KAAM,CAAAC,cAAc,CAAIC,CAAsB,EAAK,CACjD,GAAIA,CAAC,CAACC,GAAG,GAAK,OAAO,EAAI,CAACD,CAAC,CAACE,QAAQ,CAAE,CACpCF,CAAC,CAACG,cAAc,CAAC,CAAC,CAClBf,iBAAiB,CAAC,CAAC,CACrB,CACF,CAAC,CAED,mBACEtE,KAAA,CAAAE,SAAA,EAAAgB,QAAA,eAEEpB,IAAA,CAACN,eAAe,EAAA0B,QAAA,CACb,CAACuB,MAAM,eACNzC,KAAA,CAACT,MAAM,CAAC+F,GAAG,EACTC,OAAO,CAAE,CAAE3E,KAAK,CAAE,CAAC,CAAEwB,OAAO,CAAE,CAAE,CAAE,CAClCzB,OAAO,CAAE,CAAEC,KAAK,CAAE,CAAC,CAAEwB,OAAO,CAAE,CAAE,CAAE,CAClCoD,IAAI,CAAE,CAAE5E,KAAK,CAAE,CAAC,CAAEwB,OAAO,CAAE,CAAE,CAAE,CAC/B/B,SAAS,CAAC,4BAA4B,CAAAa,QAAA,eAEtClB,KAAA,CAACT,MAAM,CAACkG,MAAM,EACZC,UAAU,CAAE,CAAE9E,KAAK,CAAE,GAAG,CAAE+E,MAAM,CAAE,CAAE,CAAE,CACtCC,QAAQ,CAAE,CAAEhF,KAAK,CAAE,GAAI,CAAE,CACzBiF,OAAO,CAAEA,CAAA,GAAMnD,SAAS,CAAC,IAAI,CAAE,CAC/BrC,SAAS,CAAC,mOAAmO,CAAAa,QAAA,eAG7OpB,IAAA,QAAKO,SAAS,CAAC,+IAA+I,CAAE,CAAC,cAGjKL,KAAA,QAAKK,SAAS,CAAC,+BAA+B,CAAAa,QAAA,eAC5CpB,IAAA,CAACP,MAAM,CAAC+F,GAAG,EACT3E,OAAO,CAAE,CAAEgF,MAAM,CAAE,GAAI,CAAE,CACzB9E,UAAU,CAAE,CAAEC,QAAQ,CAAE,CAAC,CAAEC,MAAM,CAAEC,QAAQ,CAAEC,IAAI,CAAE,QAAS,CAAE,CAC9DZ,SAAS,CAAC,6DAA6D,CACxE,CAAC,cACFP,IAAA,CAACP,MAAM,CAAC+F,GAAG,EACT3E,OAAO,CAAE,CAAEgF,MAAM,CAAE,CAAC,GAAI,CAAE,CAC1B9E,UAAU,CAAE,CAAEC,QAAQ,CAAE,EAAE,CAAEC,MAAM,CAAEC,QAAQ,CAAEC,IAAI,CAAE,QAAS,CAAE,CAC/DZ,SAAS,CAAC,2DAA2D,CACtE,CAAC,EACC,CAAC,cAGNP,IAAA,QAAKO,SAAS,CAAC,gDAAgD,CAAAa,QAAA,cAC7DpB,IAAA,CAACK,eAAe,EAACE,SAAS,CAAC,mCAAmC,CAACC,QAAQ,CAAE,IAAK,CAAE,CAAC,CAC9E,CAAC,cAGNR,IAAA,CAACP,MAAM,CAAC+F,GAAG,EACT3E,OAAO,CAAE,CACPC,KAAK,CAAE,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CAClBwB,OAAO,CAAE,CAAC,GAAG,CAAE,CAAC,CAAE,GAAG,CACvB,CAAE,CACFvB,UAAU,CAAE,CACVC,QAAQ,CAAE,CAAC,CACXC,MAAM,CAAEC,QAAQ,CAChBC,IAAI,CAAE,WACR,CAAE,CACFZ,SAAS,CAAC,uEAAuE,CAClF,CAAC,cAEFP,IAAA,CAACP,MAAM,CAAC+F,GAAG,EACT3E,OAAO,CAAE,CACPC,KAAK,CAAE,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CAClBwB,OAAO,CAAE,CAAC,GAAG,CAAE,CAAC,CAAE,GAAG,CACvB,CAAE,CACFvB,UAAU,CAAE,CACVC,QAAQ,CAAE,GAAG,CACbC,MAAM,CAAEC,QAAQ,CAChBC,IAAI,CAAE,WAAW,CACjBoB,KAAK,CAAE,GACT,CAAE,CACFhC,SAAS,CAAC,wEAAwE,CACnF,CAAC,cAEFP,IAAA,CAACP,MAAM,CAAC+F,GAAG,EACT3E,OAAO,CAAE,CACPC,KAAK,CAAE,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CAClBwB,OAAO,CAAE,CAAC,GAAG,CAAE,CAAC,CAAE,GAAG,CACvB,CAAE,CACFvB,UAAU,CAAE,CACVC,QAAQ,CAAE,CAAC,CACXC,MAAM,CAAEC,QAAQ,CAChBC,IAAI,CAAE,WAAW,CACjBoB,KAAK,CAAE,CACT,CAAE,CACFhC,SAAS,CAAC,yEAAyE,CACpF,CAAC,cAGFP,IAAA,QAAKO,SAAS,CAAC,0GAA0G,CAAE,CAAC,EAC/G,CAAC,cAGhBL,KAAA,CAACT,MAAM,CAAC+F,GAAG,EACTC,OAAO,CAAE,CAAEnD,OAAO,CAAE,CAAC,CAAEjB,CAAC,CAAE,CAAC,EAAG,CAAE,CAChCR,OAAO,CAAE,CAAEyB,OAAO,CAAE,CAAC,CAAEjB,CAAC,CAAE,CAAE,CAAE,CAC9BN,UAAU,CAAE,CAAEwB,KAAK,CAAE,CAAE,CAAE,CACzBhC,SAAS,CAAC,4LAA4L,CAAAa,QAAA,eAEtMlB,KAAA,QAAKK,SAAS,CAAC,6CAA6C,CAAAa,QAAA,eAC1DpB,IAAA,CAACK,eAAe,EAACE,SAAS,CAAC,yBAAyB,CAAE,CAAC,cACvDP,IAAA,SAAAoB,QAAA,CAAM,uJAA6B,CAAM,CAAC,EACvC,CAAC,cACNpB,IAAA,QAAKO,SAAS,CAAC,gGAAgG,CAAE,CAAC,EACxG,CAAC,EACH,CACb,CACc,CAAC,cAGlBP,IAAA,CAACN,eAAe,EAAA0B,QAAA,CACbuB,MAAM,eACLzC,KAAA,CAACT,MAAM,CAAC+F,GAAG,EACTC,OAAO,CAAE,CAAEnD,OAAO,CAAE,CAAC,CAAEhB,CAAC,CAAE,GAAG,CAAER,KAAK,CAAE,GAAI,CAAE,CAC5CD,OAAO,CAAE,CACPyB,OAAO,CAAE,CAAC,CACVhB,CAAC,CAAE,CAAC,CACJR,KAAK,CAAE,CAAC,CACRU,MAAM,CAAEqB,WAAW,CAAG,EAAE,CAAG,GAC7B,CAAE,CACF6C,IAAI,CAAE,CAAEpD,OAAO,CAAE,CAAC,CAAEhB,CAAC,CAAE,GAAG,CAAER,KAAK,CAAE,GAAI,CAAE,CACzCP,SAAS,CAAC,uGAAuG,CAAAa,QAAA,eAGjHlB,KAAA,QAAKK,SAAS,CAAC,wIAAwI,CAAAa,QAAA,eAErJlB,KAAA,QAAKK,SAAS,CAAC,6BAA6B,CAAAa,QAAA,eAC1CpB,IAAA,QAAKO,SAAS,CAAC,mEAAmE,CAAE,CAAC,cACrFP,IAAA,QAAKO,SAAS,CAAC,oEAAoE,CAACyF,KAAK,CAAE,CAAEC,cAAc,CAAE,MAAO,CAAE,CAAE,CAAC,cACzHjG,IAAA,QAAKO,SAAS,CAAC,2EAA2E,CAACyF,KAAK,CAAE,CAAEC,cAAc,CAAE,IAAK,CAAE,CAAE,CAAC,EAC3H,CAAC,cAEN/F,KAAA,QAAKK,SAAS,CAAC,iCAAiC,CAAAa,QAAA,eAC9CpB,IAAA,QAAKO,SAAS,CAAC,kJAAkJ,CAAAa,QAAA,cAC/JpB,IAAA,CAACK,eAAe,EAACE,SAAS,CAAC,mCAAmC,CAAE,CAAC,CAC9D,CAAC,cACNL,KAAA,QAAAkB,QAAA,eACElB,KAAA,OAAIK,SAAS,CAAC,qCAAqC,CAAAa,QAAA,EAAC,2EAElD,cAAApB,IAAA,CAACF,QAAQ,EAACS,SAAS,CAAC,8BAA8B,CAAE,CAAC,EACnD,CAAC,cACLL,KAAA,MAAGK,SAAS,CAAC,sCAAsC,CAAAa,QAAA,eACjDpB,IAAA,QAAKO,SAAS,CAAC,sDAAsD,CAAE,CAAC,qGAE1E,EAAG,CAAC,EACD,CAAC,EACH,CAAC,cACNL,KAAA,QAAKK,SAAS,CAAC,6CAA6C,CAAAa,QAAA,eAC1DpB,IAAA,WACE+F,OAAO,CAAEA,CAAA,GAAMjD,cAAc,CAAC,CAACD,WAAW,CAAE,CAC5CtC,SAAS,CAAC,gDAAgD,CAAAa,QAAA,cAE1DpB,IAAA,CAACH,SAAS,EAACU,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CAAC,cACTP,IAAA,WACE+F,OAAO,CAAEA,CAAA,GAAMnD,SAAS,CAAC,KAAK,CAAE,CAChCrC,SAAS,CAAC,gDAAgD,CAAAa,QAAA,cAE1DpB,IAAA,CAACL,SAAS,EAACY,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CAAC,EACN,CAAC,EACH,CAAC,CAGL,CAACsC,WAAW,eACX3C,KAAA,CAAAE,SAAA,EAAAgB,QAAA,eAEElB,KAAA,QAAKK,SAAS,CAAC,oCAAoC,CAAAa,QAAA,EAChD4B,QAAQ,CAACkD,GAAG,CAAE7B,OAAO,eACpBrE,IAAA,CAACP,MAAM,CAAC+F,GAAG,EAETC,OAAO,CAAE,CAAEnD,OAAO,CAAE,CAAC,CAAEhB,CAAC,CAAE,EAAG,CAAE,CAC/BT,OAAO,CAAE,CAAEyB,OAAO,CAAE,CAAC,CAAEhB,CAAC,CAAE,CAAE,CAAE,CAC9Bf,SAAS,CAAE,QAAQ8D,OAAO,CAAClB,IAAI,GAAK,MAAM,CAAG,eAAe,CAAG,aAAa,EAAG,CAAA/B,QAAA,cAE/EpB,IAAA,QAAKO,SAAS,CAAE;AACtC;AACA,0BAA0B8D,OAAO,CAAClB,IAAI,GAAK,MAAM,CACrB,qEAAqE,CACrE,qGAAqG;AACjI,uBACwB,CAAA/B,QAAA,CACCiD,OAAO,CAACjB,OAAO,CACb,CAAC,EAbDiB,OAAO,CAACnB,EAcH,CACb,CAAC,CAGDO,QAAQ,eACPzD,IAAA,CAACP,MAAM,CAAC+F,GAAG,EACTC,OAAO,CAAE,CAAEnD,OAAO,CAAE,CAAE,CAAE,CACxBzB,OAAO,CAAE,CAAEyB,OAAO,CAAE,CAAE,CAAE,CACxB/B,SAAS,CAAC,kBAAkB,CAAAa,QAAA,cAE5BpB,IAAA,QAAKO,SAAS,CAAC,sHAAsH,CAAAa,QAAA,cACnIlB,KAAA,QAAKK,SAAS,CAAC,6BAA6B,CAAAa,QAAA,eAC1CpB,IAAA,CAACK,eAAe,EAACE,SAAS,CAAC,8BAA8B,CAAE,CAAC,cAC5DP,IAAA,QAAKO,SAAS,CAAC,mDAAmD,CAAE,CAAC,cACrEP,IAAA,QAAKO,SAAS,CAAC,kDAAkD,CAACyF,KAAK,CAAE,CAAEC,cAAc,CAAE,MAAO,CAAE,CAAE,CAAC,cACvGjG,IAAA,QAAKO,SAAS,CAAC,mDAAmD,CAACyF,KAAK,CAAE,CAAEC,cAAc,CAAE,MAAO,CAAE,CAAE,CAAC,cACxGjG,IAAA,SAAMO,SAAS,CAAC,4BAA4B,CAAAa,QAAA,CAAC,6BAAO,CAAM,CAAC,EACxD,CAAC,CACH,CAAC,CACI,CACb,cAEDpB,IAAA,QAAKmG,GAAG,CAAExC,cAAe,CAAE,CAAC,EACzB,CAAC,CAGLX,QAAQ,CAACoD,MAAM,EAAI,CAAC,eACnBlG,KAAA,QAAKK,SAAS,CAAC,WAAW,CAAAa,QAAA,eACxBpB,IAAA,MAAGO,SAAS,CAAC,4BAA4B,CAAAa,QAAA,CAAC,gEAAY,CAAG,CAAC,cAC1DpB,IAAA,QAAKO,SAAS,CAAC,sBAAsB,CAAAa,QAAA,CAClC8C,YAAY,CAACmC,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAACH,GAAG,CAAEhB,KAAK,eAClClF,IAAA,WAEE+F,OAAO,CAAEA,CAAA,GAAMd,gBAAgB,CAACC,KAAK,CAAE,CACvC3E,SAAS,CAAC,iPAAiP,CAAAa,QAAA,CAE1P8D,KAAK,EAJDA,KAKC,CACT,CAAC,CACC,CAAC,EACH,CACN,cAGDlF,IAAA,QAAKO,SAAS,CAAC,8BAA8B,CAAAa,QAAA,cAC3ClB,KAAA,QAAKK,SAAS,CAAC,6CAA6C,CAAAa,QAAA,eAC1DpB,IAAA,UACEmD,IAAI,CAAC,MAAM,CACXmD,KAAK,CAAE/C,YAAa,CACpBgD,QAAQ,CAAGnB,CAAC,EAAK5B,eAAe,CAAC4B,CAAC,CAACoB,MAAM,CAACF,KAAK,CAAE,CACjDG,UAAU,CAAEtB,cAAe,CAC3BuB,WAAW,CAAC,kEAAgB,CAC5BnG,SAAS,CAAC,yHAAyH,CACnIoG,QAAQ,CAAElD,QAAS,CACpB,CAAC,cACFzD,IAAA,CAACP,MAAM,CAACkG,MAAM,EACZI,OAAO,CAAEvB,iBAAkB,CAC3BmC,QAAQ,CAAE,CAACpD,YAAY,CAACkB,IAAI,CAAC,CAAC,EAAIhB,QAAS,CAC3CmC,UAAU,CAAE,CAAE9E,KAAK,CAAE,IAAK,CAAE,CAC5BgF,QAAQ,CAAE,CAAEhF,KAAK,CAAE,IAAK,CAAE,CAC1BP,SAAS,CAAC,mPAAmP,CAAAa,QAAA,cAE7PpB,IAAA,CAACJ,iBAAiB,EAACW,SAAS,CAAC,SAAS,CAAE,CAAC,CAC5B,CAAC,EACb,CAAC,CACH,CAAC,EACN,CACH,EACS,CACb,CACc,CAAC,EAClB,CAAC,CAEP,CAAC,CAED,cAAe,CAAAiC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}