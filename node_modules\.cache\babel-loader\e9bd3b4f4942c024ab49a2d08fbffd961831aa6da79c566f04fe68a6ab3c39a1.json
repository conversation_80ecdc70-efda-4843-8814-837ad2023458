{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0634\\u0631\\u0648\\u0639\\\\src\\\\components\\\\common\\\\LoadingSpinner.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoadingSpinner = ({\n  size = 'md',\n  color = 'primary',\n  text = 'جاري التحميل...'\n}) => {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12'\n  };\n  const colorClasses = {\n    primary: 'border-primary-600',\n    white: 'border-white',\n    gray: 'border-gray-600'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col items-center justify-center min-h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `\n            ${sizeClasses[size]} \n            border-4 \n            ${colorClasses[color]} \n            border-t-transparent \n            rounded-full \n            animate-spin\n          `\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `\n            absolute \n            inset-0 \n            ${sizeClasses[size]} \n            border-4 \n            border-transparent \n            border-t-${color === 'primary' ? 'primary-300' : color === 'white' ? 'gray-300' : 'gray-400'} \n            rounded-full \n            animate-spin \n            animation-delay-150\n          `\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this), text && /*#__PURE__*/_jsxDEV(\"p\", {\n      className: `\n          mt-4 \n          text-sm \n          font-medium \n          ${color === 'white' ? 'text-white' : 'text-gray-600'}\n          animate-pulse\n        `,\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n};\n_c = LoadingSpinner;\nexport default LoadingSpinner;\nvar _c;\n$RefreshReg$(_c, \"LoadingSpinner\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "LoadingSpinner", "size", "color", "text", "sizeClasses", "sm", "md", "lg", "colorClasses", "primary", "white", "gray", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/common/LoadingSpinner.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  color?: 'primary' | 'white' | 'gray';\n  text?: string;\n}\n\nconst LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ \n  size = 'md', \n  color = 'primary',\n  text = 'جاري التحميل...'\n}) => {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12'\n  };\n\n  const colorClasses = {\n    primary: 'border-primary-600',\n    white: 'border-white',\n    gray: 'border-gray-600'\n  };\n\n  return (\n    <div className=\"flex flex-col items-center justify-center min-h-screen\">\n      <div className=\"relative\">\n        <div \n          className={`\n            ${sizeClasses[size]} \n            border-4 \n            ${colorClasses[color]} \n            border-t-transparent \n            rounded-full \n            animate-spin\n          `}\n        />\n        <div \n          className={`\n            absolute \n            inset-0 \n            ${sizeClasses[size]} \n            border-4 \n            border-transparent \n            border-t-${color === 'primary' ? 'primary-300' : color === 'white' ? 'gray-300' : 'gray-400'} \n            rounded-full \n            animate-spin \n            animation-delay-150\n          `}\n        />\n      </div>\n      \n      {text && (\n        <p className={`\n          mt-4 \n          text-sm \n          font-medium \n          ${color === 'white' ? 'text-white' : 'text-gray-600'}\n          animate-pulse\n        `}>\n          {text}\n        </p>\n      )}\n    </div>\n  );\n};\n\nexport default LoadingSpinner;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQ1B,MAAMC,cAA6C,GAAGA,CAAC;EACrDC,IAAI,GAAG,IAAI;EACXC,KAAK,GAAG,SAAS;EACjBC,IAAI,GAAG;AACT,CAAC,KAAK;EACJ,MAAMC,WAAW,GAAG;IAClBC,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE;EACN,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBC,OAAO,EAAE,oBAAoB;IAC7BC,KAAK,EAAE,cAAc;IACrBC,IAAI,EAAE;EACR,CAAC;EAED,oBACEZ,OAAA;IAAKa,SAAS,EAAC,wDAAwD;IAAAC,QAAA,gBACrEd,OAAA;MAAKa,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACvBd,OAAA;QACEa,SAAS,EAAE;AACrB,cAAcR,WAAW,CAACH,IAAI,CAAC;AAC/B;AACA,cAAcO,YAAY,CAACN,KAAK,CAAC;AACjC;AACA;AACA;AACA;MAAY;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACFlB,OAAA;QACEa,SAAS,EAAE;AACrB;AACA;AACA,cAAcR,WAAW,CAACH,IAAI,CAAC;AAC/B;AACA;AACA,uBAAuBC,KAAK,KAAK,SAAS,GAAG,aAAa,GAAGA,KAAK,KAAK,OAAO,GAAG,UAAU,GAAG,UAAU;AACxG;AACA;AACA;AACA;MAAY;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAELd,IAAI,iBACHJ,OAAA;MAAGa,SAAS,EAAE;AACtB;AACA;AACA;AACA,YAAYV,KAAK,KAAK,OAAO,GAAG,YAAY,GAAG,eAAe;AAC9D;AACA,SAAU;MAAAW,QAAA,EACCV;IAAI;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACJ;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACC,EAAA,GA1DIlB,cAA6C;AA4DnD,eAAeA,cAAc;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}