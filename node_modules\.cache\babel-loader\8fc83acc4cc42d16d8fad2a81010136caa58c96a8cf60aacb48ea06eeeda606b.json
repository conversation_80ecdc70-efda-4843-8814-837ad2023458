{"ast": null, "code": "import React from'react';import{motion}from'framer-motion';import{ChartBarIcon,UsersIcon,AcademicCapIcon,TrophyIcon}from'@heroicons/react/24/outline';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AnalyticsPage=_ref=>{let{onBack}=_ref;return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4 space-x-reverse\",children:[onBack&&/*#__PURE__*/_jsx(\"button\",{onClick:onBack,className:\"p-2 text-gray-600 hover:text-gray-900 transition-colors\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-6 h-6\",fill:\"none\",stroke:\"currentColor\",viewBox:\"0 0 24 24\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M15 19l-7-7 7-7\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:\"\\u0627\\u0644\\u062A\\u062D\\u0644\\u064A\\u0644\\u0627\\u062A \\u0648\\u0627\\u0644\\u0625\\u062D\\u0635\\u0627\\u0626\\u064A\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"\\u062A\\u062D\\u0644\\u064A\\u0644 \\u0634\\u0627\\u0645\\u0644 \\u0644\\u0623\\u062F\\u0627\\u0621 \\u0627\\u0644\\u0645\\u0646\\u0635\\u0629 \\u0648\\u0627\\u0644\\u0637\\u0644\\u0627\\u0628\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",children:[/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:\"bg-white rounded-lg shadow-sm p-6 border border-gray-200\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-600\",children:\"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0637\\u0644\\u0627\\u0628\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold text-gray-900\",children:\"1,234\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"p-3 bg-blue-100 rounded-lg\",children:/*#__PURE__*/_jsx(UsersIcon,{className:\"w-6 h-6 text-blue-600\"})})]})}),/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0.1},className:\"bg-white rounded-lg shadow-sm p-6 border border-gray-200\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-600\",children:\"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold text-gray-900\",children:\"45\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"p-3 bg-green-100 rounded-lg\",children:/*#__PURE__*/_jsx(AcademicCapIcon,{className:\"w-6 h-6 text-green-600\"})})]})}),/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0.2},className:\"bg-white rounded-lg shadow-sm p-6 border border-gray-200\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-600\",children:\"\\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0645\\u0635\\u062F\\u0631\\u0629\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold text-gray-900\",children:\"567\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"p-3 bg-yellow-100 rounded-lg\",children:/*#__PURE__*/_jsx(TrophyIcon,{className:\"w-6 h-6 text-yellow-600\"})})]})}),/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0.3},className:\"bg-white rounded-lg shadow-sm p-6 border border-gray-200\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-600\",children:\"\\u0645\\u0639\\u062F\\u0644 \\u0627\\u0644\\u0625\\u0643\\u0645\\u0627\\u0644\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold text-gray-900\",children:\"78%\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"p-3 bg-purple-100 rounded-lg\",children:/*#__PURE__*/_jsx(ChartBarIcon,{className:\"w-6 h-6 text-purple-600\"})})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 lg:grid-cols-2 gap-6\",children:[/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0.4},className:\"bg-white rounded-lg shadow-sm p-6 border border-gray-200\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900 mb-4\",children:\"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u0637\\u0644\\u0627\\u0628 \\u0627\\u0644\\u0634\\u0647\\u0631\\u064A\"}),/*#__PURE__*/_jsx(\"div\",{className:\"h-64 bg-gray-100 rounded-lg flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-500\",children:\"\\u0631\\u0633\\u0645 \\u0628\\u064A\\u0627\\u0646\\u064A \\u0644\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u0637\\u0644\\u0627\\u0628\"})})]}),/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0.5},className:\"bg-white rounded-lg shadow-sm p-6 border border-gray-200\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900 mb-4\",children:\"\\u0623\\u062F\\u0627\\u0621 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"div\",{className:\"h-64 bg-gray-100 rounded-lg flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-500\",children:\"\\u0631\\u0633\\u0645 \\u0628\\u064A\\u0627\\u0646\\u064A \\u0644\\u0623\\u062F\\u0627\\u0621 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A\"})})]})]})]});};export default AnalyticsPage;", "map": {"version": 3, "names": ["React", "motion", "ChartBarIcon", "UsersIcon", "AcademicCapIcon", "TrophyIcon", "jsx", "_jsx", "jsxs", "_jsxs", "AnalyticsPage", "_ref", "onBack", "className", "children", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "div", "initial", "opacity", "y", "animate", "transition", "delay"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Admin/AnalyticsPage.tsx"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  ChartBarIcon,\n  UsersIcon,\n  AcademicCapIcon,\n  TrophyIcon\n} from '@heroicons/react/24/outline';\n\ninterface AnalyticsPageProps {\n  onBack?: () => void;\n}\n\nconst AnalyticsPage: React.FC<AnalyticsPageProps> = ({ onBack }) => {\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center space-x-4 space-x-reverse\">\n        {onBack && (\n          <button\n            onClick={onBack}\n            className=\"p-2 text-gray-600 hover:text-gray-900 transition-colors\"\n          >\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n            </svg>\n          </button>\n        )}\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">التحليلات والإحصائيات</h1>\n          <p className=\"text-gray-600\">تحليل شامل لأداء المنصة والطلاب</p>\n        </div>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"bg-white rounded-lg shadow-sm p-6 border border-gray-200\"\n        >\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">إجمالي الطلاب</p>\n              <p className=\"text-2xl font-bold text-gray-900\">1,234</p>\n            </div>\n            <div className=\"p-3 bg-blue-100 rounded-lg\">\n              <UsersIcon className=\"w-6 h-6 text-blue-600\" />\n            </div>\n          </div>\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.1 }}\n          className=\"bg-white rounded-lg shadow-sm p-6 border border-gray-200\"\n        >\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">إجمالي الكورسات</p>\n              <p className=\"text-2xl font-bold text-gray-900\">45</p>\n            </div>\n            <div className=\"p-3 bg-green-100 rounded-lg\">\n              <AcademicCapIcon className=\"w-6 h-6 text-green-600\" />\n            </div>\n          </div>\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.2 }}\n          className=\"bg-white rounded-lg shadow-sm p-6 border border-gray-200\"\n        >\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">الشهادات المصدرة</p>\n              <p className=\"text-2xl font-bold text-gray-900\">567</p>\n            </div>\n            <div className=\"p-3 bg-yellow-100 rounded-lg\">\n              <TrophyIcon className=\"w-6 h-6 text-yellow-600\" />\n            </div>\n          </div>\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.3 }}\n          className=\"bg-white rounded-lg shadow-sm p-6 border border-gray-200\"\n        >\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">معدل الإكمال</p>\n              <p className=\"text-2xl font-bold text-gray-900\">78%</p>\n            </div>\n            <div className=\"p-3 bg-purple-100 rounded-lg\">\n              <ChartBarIcon className=\"w-6 h-6 text-purple-600\" />\n            </div>\n          </div>\n        </motion.div>\n      </div>\n\n      {/* Charts Placeholder */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.4 }}\n          className=\"bg-white rounded-lg shadow-sm p-6 border border-gray-200\"\n        >\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">تسجيل الطلاب الشهري</h3>\n          <div className=\"h-64 bg-gray-100 rounded-lg flex items-center justify-center\">\n            <p className=\"text-gray-500\">رسم بياني لتسجيل الطلاب</p>\n          </div>\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.5 }}\n          className=\"bg-white rounded-lg shadow-sm p-6 border border-gray-200\"\n        >\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">أداء الكورسات</h3>\n          <div className=\"h-64 bg-gray-100 rounded-lg flex items-center justify-center\">\n            <p className=\"text-gray-500\">رسم بياني لأداء الكورسات</p>\n          </div>\n        </motion.div>\n      </div>\n    </div>\n  );\n};\n\nexport default AnalyticsPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,KAAQ,eAAe,CACtC,OACEC,YAAY,CACZC,SAAS,CACTC,eAAe,CACfC,UAAU,KACL,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAMrC,KAAM,CAAAC,aAA2C,CAAGC,IAAA,EAAgB,IAAf,CAAEC,MAAO,CAAC,CAAAD,IAAA,CAC7D,mBACEF,KAAA,QAAKI,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBL,KAAA,QAAKI,SAAS,CAAC,6CAA6C,CAAAC,QAAA,EACzDF,MAAM,eACLL,IAAA,WACEQ,OAAO,CAAEH,MAAO,CAChBC,SAAS,CAAC,yDAAyD,CAAAC,QAAA,cAEnEP,IAAA,QAAKM,SAAS,CAAC,SAAS,CAACG,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAJ,QAAA,cAC5EP,IAAA,SAAMY,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,WAAW,CAAE,CAAE,CAACC,CAAC,CAAC,iBAAiB,CAAE,CAAC,CACtF,CAAC,CACA,CACT,cACDb,KAAA,QAAAK,QAAA,eACEP,IAAA,OAAIM,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,2HAAqB,CAAI,CAAC,cAC3EP,IAAA,MAAGM,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,wKAA+B,CAAG,CAAC,EAC7D,CAAC,EACH,CAAC,cAGNL,KAAA,QAAKI,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnEP,IAAA,CAACN,MAAM,CAACsB,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9Bb,SAAS,CAAC,0DAA0D,CAAAC,QAAA,cAEpEL,KAAA,QAAKI,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDL,KAAA,QAAAK,QAAA,eACEP,IAAA,MAAGM,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,2EAAa,CAAG,CAAC,cAClEP,IAAA,MAAGM,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,OAAK,CAAG,CAAC,EACtD,CAAC,cACNP,IAAA,QAAKM,SAAS,CAAC,4BAA4B,CAAAC,QAAA,cACzCP,IAAA,CAACJ,SAAS,EAACU,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAC5C,CAAC,EACH,CAAC,CACI,CAAC,cAEbN,IAAA,CAACN,MAAM,CAACsB,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3BhB,SAAS,CAAC,0DAA0D,CAAAC,QAAA,cAEpEL,KAAA,QAAKI,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDL,KAAA,QAAAK,QAAA,eACEP,IAAA,MAAGM,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,uFAAe,CAAG,CAAC,cACpEP,IAAA,MAAGM,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,IAAE,CAAG,CAAC,EACnD,CAAC,cACNP,IAAA,QAAKM,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1CP,IAAA,CAACH,eAAe,EAACS,SAAS,CAAC,wBAAwB,CAAE,CAAC,CACnD,CAAC,EACH,CAAC,CACI,CAAC,cAEbN,IAAA,CAACN,MAAM,CAACsB,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3BhB,SAAS,CAAC,0DAA0D,CAAAC,QAAA,cAEpEL,KAAA,QAAKI,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDL,KAAA,QAAAK,QAAA,eACEP,IAAA,MAAGM,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,6FAAgB,CAAG,CAAC,cACrEP,IAAA,MAAGM,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,KAAG,CAAG,CAAC,EACpD,CAAC,cACNP,IAAA,QAAKM,SAAS,CAAC,8BAA8B,CAAAC,QAAA,cAC3CP,IAAA,CAACF,UAAU,EAACQ,SAAS,CAAC,yBAAyB,CAAE,CAAC,CAC/C,CAAC,EACH,CAAC,CACI,CAAC,cAEbN,IAAA,CAACN,MAAM,CAACsB,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3BhB,SAAS,CAAC,0DAA0D,CAAAC,QAAA,cAEpEL,KAAA,QAAKI,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDL,KAAA,QAAAK,QAAA,eACEP,IAAA,MAAGM,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,qEAAY,CAAG,CAAC,cACjEP,IAAA,MAAGM,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,KAAG,CAAG,CAAC,EACpD,CAAC,cACNP,IAAA,QAAKM,SAAS,CAAC,8BAA8B,CAAAC,QAAA,cAC3CP,IAAA,CAACL,YAAY,EAACW,SAAS,CAAC,yBAAyB,CAAE,CAAC,CACjD,CAAC,EACH,CAAC,CACI,CAAC,EACV,CAAC,cAGNJ,KAAA,QAAKI,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDL,KAAA,CAACR,MAAM,CAACsB,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3BhB,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eAEpEP,IAAA,OAAIM,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,0GAAmB,CAAI,CAAC,cACjFP,IAAA,QAAKM,SAAS,CAAC,8DAA8D,CAAAC,QAAA,cAC3EP,IAAA,MAAGM,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,6HAAuB,CAAG,CAAC,CACrD,CAAC,EACI,CAAC,cAEbL,KAAA,CAACR,MAAM,CAACsB,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3BhB,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eAEpEP,IAAA,OAAIM,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,2EAAa,CAAI,CAAC,cAC3EP,IAAA,QAAKM,SAAS,CAAC,8DAA8D,CAAAC,QAAA,cAC3EP,IAAA,MAAGM,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,mIAAwB,CAAG,CAAC,CACtD,CAAC,EACI,CAAC,EACV,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAJ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}