{"ast": null, "code": "import { addUniqueItem, removeItem } from '../../utils/array.mjs';\nimport { compareByDepth } from './compare-by-depth.mjs';\nclass FlatTree {\n  constructor() {\n    this.children = [];\n    this.isDirty = false;\n  }\n  add(child) {\n    addUniqueItem(this.children, child);\n    this.isDirty = true;\n  }\n  remove(child) {\n    removeItem(this.children, child);\n    this.isDirty = true;\n  }\n  forEach(callback) {\n    this.isDirty && this.children.sort(compareByDepth);\n    this.isDirty = false;\n    this.children.forEach(callback);\n  }\n}\nexport { FlatTree };", "map": {"version": 3, "names": ["addUniqueItem", "removeItem", "compareByDepth", "FlatTree", "constructor", "children", "isDirty", "add", "child", "remove", "for<PERSON>ach", "callback", "sort"], "sources": ["C:/Users/<USER>/Desktop/مشروع/node_modules/framer-motion/dist/es/render/utils/flat-tree.mjs"], "sourcesContent": ["import { addUniqueItem, removeItem } from '../../utils/array.mjs';\nimport { compareByDepth } from './compare-by-depth.mjs';\n\nclass FlatTree {\n    constructor() {\n        this.children = [];\n        this.isDirty = false;\n    }\n    add(child) {\n        addUniqueItem(this.children, child);\n        this.isDirty = true;\n    }\n    remove(child) {\n        removeItem(this.children, child);\n        this.isDirty = true;\n    }\n    forEach(callback) {\n        this.isDirty && this.children.sort(compareByDepth);\n        this.isDirty = false;\n        this.children.forEach(callback);\n    }\n}\n\nexport { FlatTree };\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,UAAU,QAAQ,uBAAuB;AACjE,SAASC,cAAc,QAAQ,wBAAwB;AAEvD,MAAMC,QAAQ,CAAC;EACXC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,OAAO,GAAG,KAAK;EACxB;EACAC,GAAGA,CAACC,KAAK,EAAE;IACPR,aAAa,CAAC,IAAI,CAACK,QAAQ,EAAEG,KAAK,CAAC;IACnC,IAAI,CAACF,OAAO,GAAG,IAAI;EACvB;EACAG,MAAMA,CAACD,KAAK,EAAE;IACVP,UAAU,CAAC,IAAI,CAACI,QAAQ,EAAEG,KAAK,CAAC;IAChC,IAAI,CAACF,OAAO,GAAG,IAAI;EACvB;EACAI,OAAOA,CAACC,QAAQ,EAAE;IACd,IAAI,CAACL,OAAO,IAAI,IAAI,CAACD,QAAQ,CAACO,IAAI,CAACV,cAAc,CAAC;IAClD,IAAI,CAACI,OAAO,GAAG,KAAK;IACpB,IAAI,CAACD,QAAQ,CAACK,OAAO,CAACC,QAAQ,CAAC;EACnC;AACJ;AAEA,SAASR,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}