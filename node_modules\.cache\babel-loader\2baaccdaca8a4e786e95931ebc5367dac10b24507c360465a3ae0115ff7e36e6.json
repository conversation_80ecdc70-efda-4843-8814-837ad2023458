{"ast": null, "code": "import { _registerComponent, registerVersion, _getProvider, getApp } from '@firebase/app';\nimport { FirebaseError, getModularInstance, getDefaultEmulatorHostnameAndPort } from '@firebase/util';\nimport { Component } from '@firebase/component';\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst LONG_TYPE = 'type.googleapis.com/google.protobuf.Int64Value';\nconst UNSIGNED_LONG_TYPE = 'type.googleapis.com/google.protobuf.UInt64Value';\nfunction mapValues(\n// { [k: string]: unknown } is no longer a wildcard assignment target after typescript 3.5\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\no, f) {\n  const result = {};\n  for (const key in o) {\n    if (o.hasOwnProperty(key)) {\n      result[key] = f(o[key]);\n    }\n  }\n  return result;\n}\n/**\r\n * Takes data and encodes it in a JSON-friendly way, such that types such as\r\n * Date are preserved.\r\n * @internal\r\n * @param data - Data to encode.\r\n */\nfunction encode(data) {\n  if (data == null) {\n    return null;\n  }\n  if (data instanceof Number) {\n    data = data.valueOf();\n  }\n  if (typeof data === 'number' && isFinite(data)) {\n    // Any number in JS is safe to put directly in JSON and parse as a double\n    // without any loss of precision.\n    return data;\n  }\n  if (data === true || data === false) {\n    return data;\n  }\n  if (Object.prototype.toString.call(data) === '[object String]') {\n    return data;\n  }\n  if (data instanceof Date) {\n    return data.toISOString();\n  }\n  if (Array.isArray(data)) {\n    return data.map(x => encode(x));\n  }\n  if (typeof data === 'function' || typeof data === 'object') {\n    return mapValues(data, x => encode(x));\n  }\n  // If we got this far, the data is not encodable.\n  throw new Error('Data cannot be encoded in JSON: ' + data);\n}\n/**\r\n * Takes data that's been encoded in a JSON-friendly form and returns a form\r\n * with richer datatypes, such as Dates, etc.\r\n * @internal\r\n * @param json - JSON to convert.\r\n */\nfunction decode(json) {\n  if (json == null) {\n    return json;\n  }\n  if (json['@type']) {\n    switch (json['@type']) {\n      case LONG_TYPE:\n      // Fall through and handle this the same as unsigned.\n      case UNSIGNED_LONG_TYPE:\n        {\n          // Technically, this could work return a valid number for malformed\n          // data if there was a number followed by garbage. But it's just not\n          // worth all the extra code to detect that case.\n          const value = Number(json['value']);\n          if (isNaN(value)) {\n            throw new Error('Data cannot be decoded from JSON: ' + json);\n          }\n          return value;\n        }\n      default:\n        {\n          throw new Error('Data cannot be decoded from JSON: ' + json);\n        }\n    }\n  }\n  if (Array.isArray(json)) {\n    return json.map(x => decode(x));\n  }\n  if (typeof json === 'function' || typeof json === 'object') {\n    return mapValues(json, x => decode(x));\n  }\n  // Anything else is safe to return.\n  return json;\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Type constant for Firebase Functions.\r\n */\nconst FUNCTIONS_TYPE = 'functions';\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Standard error codes for different ways a request can fail, as defined by:\r\n * https://github.com/googleapis/googleapis/blob/master/google/rpc/code.proto\r\n *\r\n * This map is used primarily to convert from a backend error code string to\r\n * a client SDK error code string, and make sure it's in the supported set.\r\n */\nconst errorCodeMap = {\n  OK: 'ok',\n  CANCELLED: 'cancelled',\n  UNKNOWN: 'unknown',\n  INVALID_ARGUMENT: 'invalid-argument',\n  DEADLINE_EXCEEDED: 'deadline-exceeded',\n  NOT_FOUND: 'not-found',\n  ALREADY_EXISTS: 'already-exists',\n  PERMISSION_DENIED: 'permission-denied',\n  UNAUTHENTICATED: 'unauthenticated',\n  RESOURCE_EXHAUSTED: 'resource-exhausted',\n  FAILED_PRECONDITION: 'failed-precondition',\n  ABORTED: 'aborted',\n  OUT_OF_RANGE: 'out-of-range',\n  UNIMPLEMENTED: 'unimplemented',\n  INTERNAL: 'internal',\n  UNAVAILABLE: 'unavailable',\n  DATA_LOSS: 'data-loss'\n};\n/**\r\n * An explicit error that can be thrown from a handler to send an error to the\r\n * client that called the function.\r\n */\nclass FunctionsError extends FirebaseError {\n  constructor(\n  /**\r\n   * A standard error code that will be returned to the client. This also\r\n   * determines the HTTP status code of the response, as defined in code.proto.\r\n   */\n  code, message,\n  /**\r\n   * Extra data to be converted to JSON and included in the error response.\r\n   */\n  details) {\n    super(`${FUNCTIONS_TYPE}/${code}`, message || '');\n    this.details = details;\n  }\n}\n/**\r\n * Takes an HTTP status code and returns the corresponding ErrorCode.\r\n * This is the standard HTTP status code -> error mapping defined in:\r\n * https://github.com/googleapis/googleapis/blob/master/google/rpc/code.proto\r\n *\r\n * @param status An HTTP status code.\r\n * @return The corresponding ErrorCode, or ErrorCode.UNKNOWN if none.\r\n */\nfunction codeForHTTPStatus(status) {\n  // Make sure any successful status is OK.\n  if (status >= 200 && status < 300) {\n    return 'ok';\n  }\n  switch (status) {\n    case 0:\n      // This can happen if the server returns 500.\n      return 'internal';\n    case 400:\n      return 'invalid-argument';\n    case 401:\n      return 'unauthenticated';\n    case 403:\n      return 'permission-denied';\n    case 404:\n      return 'not-found';\n    case 409:\n      return 'aborted';\n    case 429:\n      return 'resource-exhausted';\n    case 499:\n      return 'cancelled';\n    case 500:\n      return 'internal';\n    case 501:\n      return 'unimplemented';\n    case 503:\n      return 'unavailable';\n    case 504:\n      return 'deadline-exceeded';\n  }\n  return 'unknown';\n}\n/**\r\n * Takes an HTTP response and returns the corresponding Error, if any.\r\n */\nfunction _errorForResponse(status, bodyJSON) {\n  let code = codeForHTTPStatus(status);\n  // Start with reasonable defaults from the status code.\n  let description = code;\n  let details = undefined;\n  // Then look through the body for explicit details.\n  try {\n    const errorJSON = bodyJSON && bodyJSON.error;\n    if (errorJSON) {\n      const status = errorJSON.status;\n      if (typeof status === 'string') {\n        if (!errorCodeMap[status]) {\n          // They must've included an unknown error code in the body.\n          return new FunctionsError('internal', 'internal');\n        }\n        code = errorCodeMap[status];\n        // TODO(klimt): Add better default descriptions for error enums.\n        // The default description needs to be updated for the new code.\n        description = status;\n      }\n      const message = errorJSON.message;\n      if (typeof message === 'string') {\n        description = message;\n      }\n      details = errorJSON.details;\n      if (details !== undefined) {\n        details = decode(details);\n      }\n    }\n  } catch (e) {\n    // If we couldn't parse explicit error data, that's fine.\n  }\n  if (code === 'ok') {\n    // Technically, there's an edge case where a developer could explicitly\n    // return an error code of OK, and we will treat it as success, but that\n    // seems reasonable.\n    return null;\n  }\n  return new FunctionsError(code, description, details);\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Helper class to get metadata that should be included with a function call.\r\n * @internal\r\n */\nclass ContextProvider {\n  constructor(authProvider, messagingProvider, appCheckProvider) {\n    this.auth = null;\n    this.messaging = null;\n    this.appCheck = null;\n    this.auth = authProvider.getImmediate({\n      optional: true\n    });\n    this.messaging = messagingProvider.getImmediate({\n      optional: true\n    });\n    if (!this.auth) {\n      authProvider.get().then(auth => this.auth = auth, () => {\n        /* get() never rejects */\n      });\n    }\n    if (!this.messaging) {\n      messagingProvider.get().then(messaging => this.messaging = messaging, () => {\n        /* get() never rejects */\n      });\n    }\n    if (!this.appCheck) {\n      appCheckProvider.get().then(appCheck => this.appCheck = appCheck, () => {\n        /* get() never rejects */\n      });\n    }\n  }\n  async getAuthToken() {\n    if (!this.auth) {\n      return undefined;\n    }\n    try {\n      const token = await this.auth.getToken();\n      return token === null || token === void 0 ? void 0 : token.accessToken;\n    } catch (e) {\n      // If there's any error when trying to get the auth token, leave it off.\n      return undefined;\n    }\n  }\n  async getMessagingToken() {\n    if (!this.messaging || !('Notification' in self) || Notification.permission !== 'granted') {\n      return undefined;\n    }\n    try {\n      return await this.messaging.getToken();\n    } catch (e) {\n      // We don't warn on this, because it usually means messaging isn't set up.\n      // console.warn('Failed to retrieve instance id token.', e);\n      // If there's any error when trying to get the token, leave it off.\n      return undefined;\n    }\n  }\n  async getAppCheckToken(limitedUseAppCheckTokens) {\n    if (this.appCheck) {\n      const result = limitedUseAppCheckTokens ? await this.appCheck.getLimitedUseToken() : await this.appCheck.getToken();\n      if (result.error) {\n        // Do not send the App Check header to the functions endpoint if\n        // there was an error from the App Check exchange endpoint. The App\n        // Check SDK will already have logged the error to console.\n        return null;\n      }\n      return result.token;\n    }\n    return null;\n  }\n  async getContext(limitedUseAppCheckTokens) {\n    const authToken = await this.getAuthToken();\n    const messagingToken = await this.getMessagingToken();\n    const appCheckToken = await this.getAppCheckToken(limitedUseAppCheckTokens);\n    return {\n      authToken,\n      messagingToken,\n      appCheckToken\n    };\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst DEFAULT_REGION = 'us-central1';\n/**\r\n * Returns a Promise that will be rejected after the given duration.\r\n * The error will be of type FunctionsError.\r\n *\r\n * @param millis Number of milliseconds to wait before rejecting.\r\n */\nfunction failAfter(millis) {\n  // Node timers and browser timers are fundamentally incompatible, but we\n  // don't care about the value here\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let timer = null;\n  return {\n    promise: new Promise((_, reject) => {\n      timer = setTimeout(() => {\n        reject(new FunctionsError('deadline-exceeded', 'deadline-exceeded'));\n      }, millis);\n    }),\n    cancel: () => {\n      if (timer) {\n        clearTimeout(timer);\n      }\n    }\n  };\n}\n/**\r\n * The main class for the Firebase Functions SDK.\r\n * @internal\r\n */\nclass FunctionsService {\n  /**\r\n   * Creates a new Functions service for the given app.\r\n   * @param app - The FirebaseApp to use.\r\n   */\n  constructor(app, authProvider, messagingProvider, appCheckProvider) {\n    let regionOrCustomDomain = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : DEFAULT_REGION;\n    let fetchImpl = arguments.length > 5 ? arguments[5] : undefined;\n    this.app = app;\n    this.fetchImpl = fetchImpl;\n    this.emulatorOrigin = null;\n    this.contextProvider = new ContextProvider(authProvider, messagingProvider, appCheckProvider);\n    // Cancels all ongoing requests when resolved.\n    this.cancelAllRequests = new Promise(resolve => {\n      this.deleteService = () => {\n        return Promise.resolve(resolve());\n      };\n    });\n    // Resolve the region or custom domain overload by attempting to parse it.\n    try {\n      const url = new URL(regionOrCustomDomain);\n      this.customDomain = url.origin + (url.pathname === '/' ? '' : url.pathname);\n      this.region = DEFAULT_REGION;\n    } catch (e) {\n      this.customDomain = null;\n      this.region = regionOrCustomDomain;\n    }\n  }\n  _delete() {\n    return this.deleteService();\n  }\n  /**\r\n   * Returns the URL for a callable with the given name.\r\n   * @param name - The name of the callable.\r\n   * @internal\r\n   */\n  _url(name) {\n    const projectId = this.app.options.projectId;\n    if (this.emulatorOrigin !== null) {\n      const origin = this.emulatorOrigin;\n      return `${origin}/${projectId}/${this.region}/${name}`;\n    }\n    if (this.customDomain !== null) {\n      return `${this.customDomain}/${name}`;\n    }\n    return `https://${this.region}-${projectId}.cloudfunctions.net/${name}`;\n  }\n}\n/**\r\n * Modify this instance to communicate with the Cloud Functions emulator.\r\n *\r\n * Note: this must be called before this instance has been used to do any operations.\r\n *\r\n * @param host The emulator host (ex: localhost)\r\n * @param port The emulator port (ex: 5001)\r\n * @public\r\n */\nfunction connectFunctionsEmulator$1(functionsInstance, host, port) {\n  functionsInstance.emulatorOrigin = `http://${host}:${port}`;\n}\n/**\r\n * Returns a reference to the callable https trigger with the given name.\r\n * @param name - The name of the trigger.\r\n * @public\r\n */\nfunction httpsCallable$1(functionsInstance, name, options) {\n  return data => {\n    return call(functionsInstance, name, data, options || {});\n  };\n}\n/**\r\n * Returns a reference to the callable https trigger with the given url.\r\n * @param url - The url of the trigger.\r\n * @public\r\n */\nfunction httpsCallableFromURL$1(functionsInstance, url, options) {\n  return data => {\n    return callAtURL(functionsInstance, url, data, options || {});\n  };\n}\n/**\r\n * Does an HTTP POST and returns the completed response.\r\n * @param url The url to post to.\r\n * @param body The JSON body of the post.\r\n * @param headers The HTTP headers to include in the request.\r\n * @return A Promise that will succeed when the request finishes.\r\n */\nasync function postJSON(url, body, headers, fetchImpl) {\n  headers['Content-Type'] = 'application/json';\n  let response;\n  try {\n    response = await fetchImpl(url, {\n      method: 'POST',\n      body: JSON.stringify(body),\n      headers\n    });\n  } catch (e) {\n    // This could be an unhandled error on the backend, or it could be a\n    // network error. There's no way to know, since an unhandled error on the\n    // backend will fail to set the proper CORS header, and thus will be\n    // treated as a network error by fetch.\n    return {\n      status: 0,\n      json: null\n    };\n  }\n  let json = null;\n  try {\n    json = await response.json();\n  } catch (e) {\n    // If we fail to parse JSON, it will fail the same as an empty body.\n  }\n  return {\n    status: response.status,\n    json\n  };\n}\n/**\r\n * Calls a callable function asynchronously and returns the result.\r\n * @param name The name of the callable trigger.\r\n * @param data The data to pass as params to the function.s\r\n */\nfunction call(functionsInstance, name, data, options) {\n  const url = functionsInstance._url(name);\n  return callAtURL(functionsInstance, url, data, options);\n}\n/**\r\n * Calls a callable function asynchronously and returns the result.\r\n * @param url The url of the callable trigger.\r\n * @param data The data to pass as params to the function.s\r\n */\nasync function callAtURL(functionsInstance, url, data, options) {\n  // Encode any special types, such as dates, in the input data.\n  data = encode(data);\n  const body = {\n    data\n  };\n  // Add a header for the authToken.\n  const headers = {};\n  const context = await functionsInstance.contextProvider.getContext(options.limitedUseAppCheckTokens);\n  if (context.authToken) {\n    headers['Authorization'] = 'Bearer ' + context.authToken;\n  }\n  if (context.messagingToken) {\n    headers['Firebase-Instance-ID-Token'] = context.messagingToken;\n  }\n  if (context.appCheckToken !== null) {\n    headers['X-Firebase-AppCheck'] = context.appCheckToken;\n  }\n  // Default timeout to 70s, but let the options override it.\n  const timeout = options.timeout || 70000;\n  const failAfterHandle = failAfter(timeout);\n  const response = await Promise.race([postJSON(url, body, headers, functionsInstance.fetchImpl), failAfterHandle.promise, functionsInstance.cancelAllRequests]);\n  // Always clear the failAfter timeout\n  failAfterHandle.cancel();\n  // If service was deleted, interrupted response throws an error.\n  if (!response) {\n    throw new FunctionsError('cancelled', 'Firebase Functions instance was deleted.');\n  }\n  // Check for an error status, regardless of http status.\n  const error = _errorForResponse(response.status, response.json);\n  if (error) {\n    throw error;\n  }\n  if (!response.json) {\n    throw new FunctionsError('internal', 'Response is not valid JSON object.');\n  }\n  let responseData = response.json.data;\n  // TODO(klimt): For right now, allow \"result\" instead of \"data\", for\n  // backwards compatibility.\n  if (typeof responseData === 'undefined') {\n    responseData = response.json.result;\n  }\n  if (typeof responseData === 'undefined') {\n    // Consider the response malformed.\n    throw new FunctionsError('internal', 'Response is missing data field.');\n  }\n  // Decode any special types, such as dates, in the returned data.\n  const decodedData = decode(responseData);\n  return {\n    data: decodedData\n  };\n}\nconst name = \"@firebase/functions\";\nconst version = \"0.11.8\";\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst AUTH_INTERNAL_NAME = 'auth-internal';\nconst APP_CHECK_INTERNAL_NAME = 'app-check-internal';\nconst MESSAGING_INTERNAL_NAME = 'messaging-internal';\nfunction registerFunctions(fetchImpl, variant) {\n  const factory = (container, _ref) => {\n    let {\n      instanceIdentifier: regionOrCustomDomain\n    } = _ref;\n    // Dependencies\n    const app = container.getProvider('app').getImmediate();\n    const authProvider = container.getProvider(AUTH_INTERNAL_NAME);\n    const messagingProvider = container.getProvider(MESSAGING_INTERNAL_NAME);\n    const appCheckProvider = container.getProvider(APP_CHECK_INTERNAL_NAME);\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return new FunctionsService(app, authProvider, messagingProvider, appCheckProvider, regionOrCustomDomain, fetchImpl);\n  };\n  _registerComponent(new Component(FUNCTIONS_TYPE, factory, \"PUBLIC\" /* ComponentType.PUBLIC */).setMultipleInstances(true));\n  registerVersion(name, version, variant);\n  // BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\n  registerVersion(name, version, 'esm2017');\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Returns a {@link Functions} instance for the given app.\r\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\r\n * @param regionOrCustomDomain - one of:\r\n *   a) The region the callable functions are located in (ex: us-central1)\r\n *   b) A custom domain hosting the callable functions (ex: https://mydomain.com)\r\n * @public\r\n */\nfunction getFunctions() {\n  let app = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : getApp();\n  let regionOrCustomDomain = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : DEFAULT_REGION;\n  // Dependencies\n  const functionsProvider = _getProvider(getModularInstance(app), FUNCTIONS_TYPE);\n  const functionsInstance = functionsProvider.getImmediate({\n    identifier: regionOrCustomDomain\n  });\n  const emulator = getDefaultEmulatorHostnameAndPort('functions');\n  if (emulator) {\n    connectFunctionsEmulator(functionsInstance, ...emulator);\n  }\n  return functionsInstance;\n}\n/**\r\n * Modify this instance to communicate with the Cloud Functions emulator.\r\n *\r\n * Note: this must be called before this instance has been used to do any operations.\r\n *\r\n * @param host - The emulator host (ex: localhost)\r\n * @param port - The emulator port (ex: 5001)\r\n * @public\r\n */\nfunction connectFunctionsEmulator(functionsInstance, host, port) {\n  connectFunctionsEmulator$1(getModularInstance(functionsInstance), host, port);\n}\n/**\r\n * Returns a reference to the callable HTTPS trigger with the given name.\r\n * @param name - The name of the trigger.\r\n * @public\r\n */\nfunction httpsCallable(functionsInstance, name, options) {\n  return httpsCallable$1(getModularInstance(functionsInstance), name, options);\n}\n/**\r\n * Returns a reference to the callable HTTPS trigger with the specified url.\r\n * @param url - The url of the trigger.\r\n * @public\r\n */\nfunction httpsCallableFromURL(functionsInstance, url, options) {\n  return httpsCallableFromURL$1(getModularInstance(functionsInstance), url, options);\n}\n\n/**\r\n * Cloud Functions for Firebase\r\n *\r\n * @packageDocumentation\r\n */\nregisterFunctions(fetch.bind(self));\nexport { connectFunctionsEmulator, getFunctions, httpsCallable, httpsCallableFromURL };", "map": {"version": 3, "names": ["LONG_TYPE", "UNSIGNED_LONG_TYPE", "mapValues", "o", "f", "result", "key", "hasOwnProperty", "encode", "data", "Number", "valueOf", "isFinite", "Object", "prototype", "toString", "call", "Date", "toISOString", "Array", "isArray", "map", "x", "Error", "decode", "json", "value", "isNaN", "FUNCTIONS_TYPE", "errorCodeMap", "OK", "CANCELLED", "UNKNOWN", "INVALID_ARGUMENT", "DEADLINE_EXCEEDED", "NOT_FOUND", "ALREADY_EXISTS", "PERMISSION_DENIED", "UNAUTHENTICATED", "RESOURCE_EXHAUSTED", "FAILED_PRECONDITION", "ABORTED", "OUT_OF_RANGE", "UNIMPLEMENTED", "INTERNAL", "UNAVAILABLE", "DATA_LOSS", "FunctionsError", "FirebaseError", "constructor", "code", "message", "details", "codeForHTTPStatus", "status", "_errorForResponse", "bodyJSON", "description", "undefined", "errorJSON", "error", "e", "ContextProvider", "authProvider", "messagingProvider", "appCheckProvider", "auth", "messaging", "appCheck", "getImmediate", "optional", "get", "then", "getAuthToken", "token", "getToken", "accessToken", "getMessagingToken", "self", "Notification", "permission", "getAppCheckToken", "limitedUseAppCheckTokens", "getLimitedUseToken", "getContext", "authToken", "messagingToken", "appCheckToken", "DEFAULT_REGION", "failAfter", "millis", "timer", "promise", "Promise", "_", "reject", "setTimeout", "cancel", "clearTimeout", "FunctionsService", "app", "regionOrCustomDomain", "arguments", "length", "fetchImpl", "emulator<PERSON><PERSON><PERSON>", "contextProvider", "cancelAllRequests", "resolve", "deleteService", "url", "URL", "customDomain", "origin", "pathname", "region", "_delete", "_url", "name", "projectId", "options", "connectFunctionsEmulator$1", "connectFunctionsEmulator", "functionsInstance", "host", "port", "httpsCallable$1", "httpsCallable", "httpsCallableFromURL$1", "httpsCallableFromURL", "callAtURL", "postJSON", "body", "headers", "response", "method", "JSON", "stringify", "context", "timeout", "failAfterHandle", "race", "responseData", "decodedData", "AUTH_INTERNAL_NAME", "APP_CHECK_INTERNAL_NAME", "MESSAGING_INTERNAL_NAME", "registerFunctions", "variant", "factory", "container", "_ref", "instanceIdentifier", "get<PERSON><PERSON><PERSON>", "_registerComponent", "Component", "setMultipleInstances", "registerVersion", "version", "getFunctions", "getApp", "functionsProvider", "_get<PERSON><PERSON><PERSON>", "getModularInstance", "identifier", "emulator", "getDefaultEmulatorHostnameAndPort", "fetch", "bind"], "sources": ["C:\\Users\\<USER>\\Desktop\\مشروع\\node_modules\\@firebase\\functions\\src\\serializer.ts", "C:\\Users\\<USER>\\Desktop\\مشروع\\node_modules\\@firebase\\functions\\src\\constants.ts", "C:\\Users\\<USER>\\Desktop\\مشروع\\node_modules\\@firebase\\functions\\src\\error.ts", "C:\\Users\\<USER>\\Desktop\\مشروع\\node_modules\\@firebase\\functions\\src\\context.ts", "C:\\Users\\<USER>\\Desktop\\مشروع\\node_modules\\@firebase\\functions\\src\\service.ts", "C:\\Users\\<USER>\\Desktop\\مشروع\\node_modules\\@firebase\\functions\\src\\config.ts", "C:\\Users\\<USER>\\Desktop\\مشروع\\node_modules\\@firebase\\functions\\src\\api.ts", "C:\\Users\\<USER>\\Desktop\\مشروع\\node_modules\\@firebase\\functions\\src\\index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst LONG_TYPE = 'type.googleapis.com/google.protobuf.Int64Value';\nconst UNSIGNED_LONG_TYPE = 'type.googleapis.com/google.protobuf.UInt64Value';\n\nfunction mapValues(\n  // { [k: string]: unknown } is no longer a wildcard assignment target after typescript 3.5\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  o: { [key: string]: any },\n  f: (arg0: unknown) => unknown\n): object {\n  const result: { [key: string]: unknown } = {};\n  for (const key in o) {\n    if (o.hasOwnProperty(key)) {\n      result[key] = f(o[key]);\n    }\n  }\n  return result;\n}\n\n/**\n * Takes data and encodes it in a JSON-friendly way, such that types such as\n * Date are preserved.\n * @internal\n * @param data - Data to encode.\n */\nexport function encode(data: unknown): unknown {\n  if (data == null) {\n    return null;\n  }\n  if (data instanceof Number) {\n    data = data.valueOf();\n  }\n  if (typeof data === 'number' && isFinite(data)) {\n    // Any number in JS is safe to put directly in JSON and parse as a double\n    // without any loss of precision.\n    return data;\n  }\n  if (data === true || data === false) {\n    return data;\n  }\n  if (Object.prototype.toString.call(data) === '[object String]') {\n    return data;\n  }\n  if (data instanceof Date) {\n    return data.toISOString();\n  }\n  if (Array.isArray(data)) {\n    return data.map(x => encode(x));\n  }\n  if (typeof data === 'function' || typeof data === 'object') {\n    return mapValues(data!, x => encode(x));\n  }\n  // If we got this far, the data is not encodable.\n  throw new Error('Data cannot be encoded in JSON: ' + data);\n}\n\n/**\n * Takes data that's been encoded in a JSON-friendly form and returns a form\n * with richer datatypes, such as Dates, etc.\n * @internal\n * @param json - JSON to convert.\n */\nexport function decode(json: unknown): unknown {\n  if (json == null) {\n    return json;\n  }\n  if ((json as { [key: string]: unknown })['@type']) {\n    switch ((json as { [key: string]: unknown })['@type']) {\n      case LONG_TYPE:\n      // Fall through and handle this the same as unsigned.\n      case UNSIGNED_LONG_TYPE: {\n        // Technically, this could work return a valid number for malformed\n        // data if there was a number followed by garbage. But it's just not\n        // worth all the extra code to detect that case.\n        const value = Number((json as { [key: string]: unknown })['value']);\n        if (isNaN(value)) {\n          throw new Error('Data cannot be decoded from JSON: ' + json);\n        }\n        return value;\n      }\n      default: {\n        throw new Error('Data cannot be decoded from JSON: ' + json);\n      }\n    }\n  }\n  if (Array.isArray(json)) {\n    return json.map(x => decode(x));\n  }\n  if (typeof json === 'function' || typeof json === 'object') {\n    return mapValues(json!, x => decode(x));\n  }\n  // Anything else is safe to return.\n  return json;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Type constant for Firebase Functions.\n */\nexport const FUNCTIONS_TYPE = 'functions';\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FunctionsErrorCodeCore as FunctionsErrorCode } from './public-types';\nimport { decode } from './serializer';\nimport { HttpResponseBody } from './service';\nimport { FirebaseError } from '@firebase/util';\nimport { FUNCTIONS_TYPE } from './constants';\n\n/**\n * Standard error codes for different ways a request can fail, as defined by:\n * https://github.com/googleapis/googleapis/blob/master/google/rpc/code.proto\n *\n * This map is used primarily to convert from a backend error code string to\n * a client SDK error code string, and make sure it's in the supported set.\n */\nconst errorCodeMap: { [name: string]: FunctionsErrorCode } = {\n  OK: 'ok',\n  CANCELLED: 'cancelled',\n  UNKNOWN: 'unknown',\n  INVALID_ARGUMENT: 'invalid-argument',\n  DEADLINE_EXCEEDED: 'deadline-exceeded',\n  NOT_FOUND: 'not-found',\n  ALREADY_EXISTS: 'already-exists',\n  PERMISSION_DENIED: 'permission-denied',\n  UNAUTHENTICATED: 'unauthenticated',\n  RESOURCE_EXHAUSTED: 'resource-exhausted',\n  FAILED_PRECONDITION: 'failed-precondition',\n  ABORTED: 'aborted',\n  OUT_OF_RANGE: 'out-of-range',\n  UNIMPLEMENTED: 'unimplemented',\n  INTERNAL: 'internal',\n  UNAVAILABLE: 'unavailable',\n  DATA_LOSS: 'data-loss'\n};\n\n/**\n * An explicit error that can be thrown from a handler to send an error to the\n * client that called the function.\n */\nexport class FunctionsError extends FirebaseError {\n  constructor(\n    /**\n     * A standard error code that will be returned to the client. This also\n     * determines the HTTP status code of the response, as defined in code.proto.\n     */\n    code: FunctionsErrorCode,\n    message?: string,\n    /**\n     * Extra data to be converted to JSON and included in the error response.\n     */\n    readonly details?: unknown\n  ) {\n    super(`${FUNCTIONS_TYPE}/${code}`, message || '');\n  }\n}\n\n/**\n * Takes an HTTP status code and returns the corresponding ErrorCode.\n * This is the standard HTTP status code -> error mapping defined in:\n * https://github.com/googleapis/googleapis/blob/master/google/rpc/code.proto\n *\n * @param status An HTTP status code.\n * @return The corresponding ErrorCode, or ErrorCode.UNKNOWN if none.\n */\nfunction codeForHTTPStatus(status: number): FunctionsErrorCode {\n  // Make sure any successful status is OK.\n  if (status >= 200 && status < 300) {\n    return 'ok';\n  }\n  switch (status) {\n    case 0:\n      // This can happen if the server returns 500.\n      return 'internal';\n    case 400:\n      return 'invalid-argument';\n    case 401:\n      return 'unauthenticated';\n    case 403:\n      return 'permission-denied';\n    case 404:\n      return 'not-found';\n    case 409:\n      return 'aborted';\n    case 429:\n      return 'resource-exhausted';\n    case 499:\n      return 'cancelled';\n    case 500:\n      return 'internal';\n    case 501:\n      return 'unimplemented';\n    case 503:\n      return 'unavailable';\n    case 504:\n      return 'deadline-exceeded';\n    default: // ignore\n  }\n  return 'unknown';\n}\n\n/**\n * Takes an HTTP response and returns the corresponding Error, if any.\n */\nexport function _errorForResponse(\n  status: number,\n  bodyJSON: HttpResponseBody | null\n): Error | null {\n  let code = codeForHTTPStatus(status);\n\n  // Start with reasonable defaults from the status code.\n  let description: string = code;\n\n  let details: unknown = undefined;\n\n  // Then look through the body for explicit details.\n  try {\n    const errorJSON = bodyJSON && bodyJSON.error;\n    if (errorJSON) {\n      const status = errorJSON.status;\n      if (typeof status === 'string') {\n        if (!errorCodeMap[status]) {\n          // They must've included an unknown error code in the body.\n          return new FunctionsError('internal', 'internal');\n        }\n        code = errorCodeMap[status];\n\n        // TODO(klimt): Add better default descriptions for error enums.\n        // The default description needs to be updated for the new code.\n        description = status;\n      }\n\n      const message = errorJSON.message;\n      if (typeof message === 'string') {\n        description = message;\n      }\n\n      details = errorJSON.details;\n      if (details !== undefined) {\n        details = decode(details);\n      }\n    }\n  } catch (e) {\n    // If we couldn't parse explicit error data, that's fine.\n  }\n\n  if (code === 'ok') {\n    // Technically, there's an edge case where a developer could explicitly\n    // return an error code of OK, and we will treat it as success, but that\n    // seems reasonable.\n    return null;\n  }\n\n  return new FunctionsError(code, description, details);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Provider } from '@firebase/component';\nimport {\n  AppCheckInternalComponentName,\n  FirebaseAppCheckInternal\n} from '@firebase/app-check-interop-types';\nimport {\n  MessagingInternal,\n  MessagingInternalComponentName\n} from '@firebase/messaging-interop-types';\nimport {\n  FirebaseAuthInternal,\n  FirebaseAuthInternalName\n} from '@firebase/auth-interop-types';\n\n/**\n * The metadata that should be supplied with function calls.\n * @internal\n */\nexport interface Context {\n  authToken?: string;\n  messagingToken?: string;\n  appCheckToken: string | null;\n}\n\n/**\n * Helper class to get metadata that should be included with a function call.\n * @internal\n */\nexport class ContextProvider {\n  private auth: FirebaseAuthInternal | null = null;\n  private messaging: MessagingInternal | null = null;\n  private appCheck: FirebaseAppCheckInternal | null = null;\n  constructor(\n    authProvider: Provider<FirebaseAuthInternalName>,\n    messagingProvider: Provider<MessagingInternalComponentName>,\n    appCheckProvider: Provider<AppCheckInternalComponentName>\n  ) {\n    this.auth = authProvider.getImmediate({ optional: true });\n    this.messaging = messagingProvider.getImmediate({\n      optional: true\n    });\n\n    if (!this.auth) {\n      authProvider.get().then(\n        auth => (this.auth = auth),\n        () => {\n          /* get() never rejects */\n        }\n      );\n    }\n\n    if (!this.messaging) {\n      messagingProvider.get().then(\n        messaging => (this.messaging = messaging),\n        () => {\n          /* get() never rejects */\n        }\n      );\n    }\n\n    if (!this.appCheck) {\n      appCheckProvider.get().then(\n        appCheck => (this.appCheck = appCheck),\n        () => {\n          /* get() never rejects */\n        }\n      );\n    }\n  }\n\n  async getAuthToken(): Promise<string | undefined> {\n    if (!this.auth) {\n      return undefined;\n    }\n\n    try {\n      const token = await this.auth.getToken();\n      return token?.accessToken;\n    } catch (e) {\n      // If there's any error when trying to get the auth token, leave it off.\n      return undefined;\n    }\n  }\n\n  async getMessagingToken(): Promise<string | undefined> {\n    if (\n      !this.messaging ||\n      !('Notification' in self) ||\n      Notification.permission !== 'granted'\n    ) {\n      return undefined;\n    }\n\n    try {\n      return await this.messaging.getToken();\n    } catch (e) {\n      // We don't warn on this, because it usually means messaging isn't set up.\n      // console.warn('Failed to retrieve instance id token.', e);\n\n      // If there's any error when trying to get the token, leave it off.\n      return undefined;\n    }\n  }\n\n  async getAppCheckToken(\n    limitedUseAppCheckTokens?: boolean\n  ): Promise<string | null> {\n    if (this.appCheck) {\n      const result = limitedUseAppCheckTokens\n        ? await this.appCheck.getLimitedUseToken()\n        : await this.appCheck.getToken();\n      if (result.error) {\n        // Do not send the App Check header to the functions endpoint if\n        // there was an error from the App Check exchange endpoint. The App\n        // Check SDK will already have logged the error to console.\n        return null;\n      }\n      return result.token;\n    }\n    return null;\n  }\n\n  async getContext(limitedUseAppCheckTokens?: boolean): Promise<Context> {\n    const authToken = await this.getAuthToken();\n    const messagingToken = await this.getMessagingToken();\n    const appCheckToken = await this.getAppCheckToken(limitedUseAppCheckTokens);\n    return { authToken, messagingToken, appCheckToken };\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, _FirebaseService } from '@firebase/app';\nimport {\n  HttpsCallable,\n  HttpsCallableResult,\n  HttpsCallableOptions\n} from './public-types';\nimport { _errorForResponse, FunctionsError } from './error';\nimport { ContextProvider } from './context';\nimport { encode, decode } from './serializer';\nimport { Provider } from '@firebase/component';\nimport { FirebaseAuthInternalName } from '@firebase/auth-interop-types';\nimport { MessagingInternalComponentName } from '@firebase/messaging-interop-types';\nimport { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';\n\nexport const DEFAULT_REGION = 'us-central1';\n\n/**\n * The response to an http request.\n */\ninterface HttpResponse {\n  status: number;\n  json: HttpResponseBody | null;\n}\n/**\n * Describes the shape of the HttpResponse body.\n * It makes functions that would otherwise take {} able to access the\n * possible elements in the body more easily\n */\nexport interface HttpResponseBody {\n  data?: unknown;\n  result?: unknown;\n  error?: {\n    message?: unknown;\n    status?: unknown;\n    details?: unknown;\n  };\n}\n\ninterface CancellablePromise<T> {\n  promise: Promise<T>;\n  cancel: () => void;\n}\n\n/**\n * Returns a Promise that will be rejected after the given duration.\n * The error will be of type FunctionsError.\n *\n * @param millis Number of milliseconds to wait before rejecting.\n */\nfunction failAfter(millis: number): CancellablePromise<never> {\n  // Node timers and browser timers are fundamentally incompatible, but we\n  // don't care about the value here\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let timer: any | null = null;\n  return {\n    promise: new Promise((_, reject) => {\n      timer = setTimeout(() => {\n        reject(new FunctionsError('deadline-exceeded', 'deadline-exceeded'));\n      }, millis);\n    }),\n    cancel: () => {\n      if (timer) {\n        clearTimeout(timer);\n      }\n    }\n  };\n}\n\n/**\n * The main class for the Firebase Functions SDK.\n * @internal\n */\nexport class FunctionsService implements _FirebaseService {\n  readonly contextProvider: ContextProvider;\n  emulatorOrigin: string | null = null;\n  cancelAllRequests: Promise<void>;\n  deleteService!: () => Promise<void>;\n  region: string;\n  customDomain: string | null;\n\n  /**\n   * Creates a new Functions service for the given app.\n   * @param app - The FirebaseApp to use.\n   */\n  constructor(\n    readonly app: FirebaseApp,\n    authProvider: Provider<FirebaseAuthInternalName>,\n    messagingProvider: Provider<MessagingInternalComponentName>,\n    appCheckProvider: Provider<AppCheckInternalComponentName>,\n    regionOrCustomDomain: string = DEFAULT_REGION,\n    readonly fetchImpl: typeof fetch\n  ) {\n    this.contextProvider = new ContextProvider(\n      authProvider,\n      messagingProvider,\n      appCheckProvider\n    );\n    // Cancels all ongoing requests when resolved.\n    this.cancelAllRequests = new Promise(resolve => {\n      this.deleteService = () => {\n        return Promise.resolve(resolve());\n      };\n    });\n\n    // Resolve the region or custom domain overload by attempting to parse it.\n    try {\n      const url = new URL(regionOrCustomDomain);\n      this.customDomain =\n        url.origin + (url.pathname === '/' ? '' : url.pathname);\n      this.region = DEFAULT_REGION;\n    } catch (e) {\n      this.customDomain = null;\n      this.region = regionOrCustomDomain;\n    }\n  }\n\n  _delete(): Promise<void> {\n    return this.deleteService();\n  }\n\n  /**\n   * Returns the URL for a callable with the given name.\n   * @param name - The name of the callable.\n   * @internal\n   */\n  _url(name: string): string {\n    const projectId = this.app.options.projectId;\n    if (this.emulatorOrigin !== null) {\n      const origin = this.emulatorOrigin;\n      return `${origin}/${projectId}/${this.region}/${name}`;\n    }\n\n    if (this.customDomain !== null) {\n      return `${this.customDomain}/${name}`;\n    }\n\n    return `https://${this.region}-${projectId}.cloudfunctions.net/${name}`;\n  }\n}\n\n/**\n * Modify this instance to communicate with the Cloud Functions emulator.\n *\n * Note: this must be called before this instance has been used to do any operations.\n *\n * @param host The emulator host (ex: localhost)\n * @param port The emulator port (ex: 5001)\n * @public\n */\nexport function connectFunctionsEmulator(\n  functionsInstance: FunctionsService,\n  host: string,\n  port: number\n): void {\n  functionsInstance.emulatorOrigin = `http://${host}:${port}`;\n}\n\n/**\n * Returns a reference to the callable https trigger with the given name.\n * @param name - The name of the trigger.\n * @public\n */\nexport function httpsCallable<RequestData, ResponseData>(\n  functionsInstance: FunctionsService,\n  name: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData> {\n  return (data => {\n    return call(functionsInstance, name, data, options || {});\n  }) as HttpsCallable<RequestData, ResponseData>;\n}\n\n/**\n * Returns a reference to the callable https trigger with the given url.\n * @param url - The url of the trigger.\n * @public\n */\nexport function httpsCallableFromURL<RequestData, ResponseData>(\n  functionsInstance: FunctionsService,\n  url: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData> {\n  return (data => {\n    return callAtURL(functionsInstance, url, data, options || {});\n  }) as HttpsCallable<RequestData, ResponseData>;\n}\n\n/**\n * Does an HTTP POST and returns the completed response.\n * @param url The url to post to.\n * @param body The JSON body of the post.\n * @param headers The HTTP headers to include in the request.\n * @return A Promise that will succeed when the request finishes.\n */\nasync function postJSON(\n  url: string,\n  body: unknown,\n  headers: { [key: string]: string },\n  fetchImpl: typeof fetch\n): Promise<HttpResponse> {\n  headers['Content-Type'] = 'application/json';\n\n  let response: Response;\n  try {\n    response = await fetchImpl(url, {\n      method: 'POST',\n      body: JSON.stringify(body),\n      headers\n    });\n  } catch (e) {\n    // This could be an unhandled error on the backend, or it could be a\n    // network error. There's no way to know, since an unhandled error on the\n    // backend will fail to set the proper CORS header, and thus will be\n    // treated as a network error by fetch.\n    return {\n      status: 0,\n      json: null\n    };\n  }\n  let json: HttpResponseBody | null = null;\n  try {\n    json = await response.json();\n  } catch (e) {\n    // If we fail to parse JSON, it will fail the same as an empty body.\n  }\n  return {\n    status: response.status,\n    json\n  };\n}\n\n/**\n * Calls a callable function asynchronously and returns the result.\n * @param name The name of the callable trigger.\n * @param data The data to pass as params to the function.s\n */\nfunction call(\n  functionsInstance: FunctionsService,\n  name: string,\n  data: unknown,\n  options: HttpsCallableOptions\n): Promise<HttpsCallableResult> {\n  const url = functionsInstance._url(name);\n  return callAtURL(functionsInstance, url, data, options);\n}\n\n/**\n * Calls a callable function asynchronously and returns the result.\n * @param url The url of the callable trigger.\n * @param data The data to pass as params to the function.s\n */\nasync function callAtURL(\n  functionsInstance: FunctionsService,\n  url: string,\n  data: unknown,\n  options: HttpsCallableOptions\n): Promise<HttpsCallableResult> {\n  // Encode any special types, such as dates, in the input data.\n  data = encode(data);\n  const body = { data };\n\n  // Add a header for the authToken.\n  const headers: { [key: string]: string } = {};\n  const context = await functionsInstance.contextProvider.getContext(\n    options.limitedUseAppCheckTokens\n  );\n  if (context.authToken) {\n    headers['Authorization'] = 'Bearer ' + context.authToken;\n  }\n  if (context.messagingToken) {\n    headers['Firebase-Instance-ID-Token'] = context.messagingToken;\n  }\n  if (context.appCheckToken !== null) {\n    headers['X-Firebase-AppCheck'] = context.appCheckToken;\n  }\n\n  // Default timeout to 70s, but let the options override it.\n  const timeout = options.timeout || 70000;\n\n  const failAfterHandle = failAfter(timeout);\n  const response = await Promise.race([\n    postJSON(url, body, headers, functionsInstance.fetchImpl),\n    failAfterHandle.promise,\n    functionsInstance.cancelAllRequests\n  ]);\n\n  // Always clear the failAfter timeout\n  failAfterHandle.cancel();\n\n  // If service was deleted, interrupted response throws an error.\n  if (!response) {\n    throw new FunctionsError(\n      'cancelled',\n      'Firebase Functions instance was deleted.'\n    );\n  }\n\n  // Check for an error status, regardless of http status.\n  const error = _errorForResponse(response.status, response.json);\n  if (error) {\n    throw error;\n  }\n\n  if (!response.json) {\n    throw new FunctionsError('internal', 'Response is not valid JSON object.');\n  }\n\n  let responseData = response.json.data;\n  // TODO(klimt): For right now, allow \"result\" instead of \"data\", for\n  // backwards compatibility.\n  if (typeof responseData === 'undefined') {\n    responseData = response.json.result;\n  }\n  if (typeof responseData === 'undefined') {\n    // Consider the response malformed.\n    throw new FunctionsError('internal', 'Response is missing data field.');\n  }\n\n  // Decode any special types, such as dates, in the returned data.\n  const decodedData = decode(responseData);\n\n  return { data: decodedData };\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _registerComponent, registerVersion } from '@firebase/app';\nimport { FunctionsService } from './service';\nimport {\n  Component,\n  ComponentType,\n  ComponentContainer,\n  InstanceFactory\n} from '@firebase/component';\nimport { FUNCTIONS_TYPE } from './constants';\nimport { FirebaseAuthInternalName } from '@firebase/auth-interop-types';\nimport { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';\nimport { MessagingInternalComponentName } from '@firebase/messaging-interop-types';\nimport { name, version } from '../package.json';\n\nconst AUTH_INTERNAL_NAME: FirebaseAuthInternalName = 'auth-internal';\nconst APP_CHECK_INTERNAL_NAME: AppCheckInternalComponentName =\n  'app-check-internal';\nconst MESSAGING_INTERNAL_NAME: MessagingInternalComponentName =\n  'messaging-internal';\n\nexport function registerFunctions(\n  fetchImpl: typeof fetch,\n  variant?: string\n): void {\n  const factory: InstanceFactory<'functions'> = (\n    container: ComponentContainer,\n    { instanceIdentifier: regionOrCustomDomain }\n  ) => {\n    // Dependencies\n    const app = container.getProvider('app').getImmediate();\n    const authProvider = container.getProvider(AUTH_INTERNAL_NAME);\n    const messagingProvider = container.getProvider(MESSAGING_INTERNAL_NAME);\n    const appCheckProvider = container.getProvider(APP_CHECK_INTERNAL_NAME);\n\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return new FunctionsService(\n      app,\n      authProvider,\n      messagingProvider,\n      appCheckProvider,\n      regionOrCustomDomain,\n      fetchImpl\n    );\n  };\n\n  _registerComponent(\n    new Component(\n      FUNCTIONS_TYPE,\n      factory,\n      ComponentType.PUBLIC\n    ).setMultipleInstances(true)\n  );\n\n  registerVersion(name, version, variant);\n  // BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _getProvider, FirebaseApp, getApp } from '@firebase/app';\nimport { FUNCTIONS_TYPE } from './constants';\n\nimport { Provider } from '@firebase/component';\nimport { Functions, HttpsCallableOptions, HttpsCallable } from './public-types';\nimport {\n  FunctionsService,\n  DEFAULT_REGION,\n  connectFunctionsEmulator as _connectFunctionsEmulator,\n  httpsCallable as _httpsCallable,\n  httpsCallableFromURL as _httpsCallableFromURL\n} from './service';\nimport {\n  getModularInstance,\n  getDefaultEmulatorHostnameAndPort\n} from '@firebase/util';\n\nexport * from './public-types';\n\n/**\n * Returns a {@link Functions} instance for the given app.\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n * @param regionOrCustomDomain - one of:\n *   a) The region the callable functions are located in (ex: us-central1)\n *   b) A custom domain hosting the callable functions (ex: https://mydomain.com)\n * @public\n */\nexport function getFunctions(\n  app: FirebaseApp = getApp(),\n  regionOrCustomDomain: string = DEFAULT_REGION\n): Functions {\n  // Dependencies\n  const functionsProvider: Provider<'functions'> = _getProvider(\n    getModularInstance(app),\n    FUNCTIONS_TYPE\n  );\n  const functionsInstance = functionsProvider.getImmediate({\n    identifier: regionOrCustomDomain\n  });\n  const emulator = getDefaultEmulatorHostnameAndPort('functions');\n  if (emulator) {\n    connectFunctionsEmulator(functionsInstance, ...emulator);\n  }\n  return functionsInstance;\n}\n\n/**\n * Modify this instance to communicate with the Cloud Functions emulator.\n *\n * Note: this must be called before this instance has been used to do any operations.\n *\n * @param host - The emulator host (ex: localhost)\n * @param port - The emulator port (ex: 5001)\n * @public\n */\nexport function connectFunctionsEmulator(\n  functionsInstance: Functions,\n  host: string,\n  port: number\n): void {\n  _connectFunctionsEmulator(\n    getModularInstance<FunctionsService>(functionsInstance as FunctionsService),\n    host,\n    port\n  );\n}\n\n/**\n * Returns a reference to the callable HTTPS trigger with the given name.\n * @param name - The name of the trigger.\n * @public\n */\nexport function httpsCallable<RequestData = unknown, ResponseData = unknown>(\n  functionsInstance: Functions,\n  name: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData> {\n  return _httpsCallable<RequestData, ResponseData>(\n    getModularInstance<FunctionsService>(functionsInstance as FunctionsService),\n    name,\n    options\n  );\n}\n\n/**\n * Returns a reference to the callable HTTPS trigger with the specified url.\n * @param url - The url of the trigger.\n * @public\n */\nexport function httpsCallableFromURL<\n  RequestData = unknown,\n  ResponseData = unknown\n>(\n  functionsInstance: Functions,\n  url: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData> {\n  return _httpsCallableFromURL<RequestData, ResponseData>(\n    getModularInstance<FunctionsService>(functionsInstance as FunctionsService),\n    url,\n    options\n  );\n}\n", "/**\n * Cloud Functions for Firebase\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { registerFunctions } from './config';\n\nexport * from './api';\nexport * from './public-types';\n\nregisterFunctions(fetch.bind(self));\n"], "mappings": ";;;;AAAA;;;;;;;;;;;;;;;AAeG;AACH,MAAMA,SAAS,GAAG,gDAAgD;AAClE,MAAMC,kBAAkB,GAAG,iDAAiD;AAE5E,SAASC,SAASA;AAChB;AACA;AACAC,CAAyB,EACzBC,CAA6B;EAE7B,MAAMC,MAAM,GAA+B,EAAE;EAC7C,KAAK,MAAMC,GAAG,IAAIH,CAAC,EAAE;IACnB,IAAIA,CAAC,CAACI,cAAc,CAACD,GAAG,CAAC,EAAE;MACzBD,MAAM,CAACC,GAAG,CAAC,GAAGF,CAAC,CAACD,CAAC,CAACG,GAAG,CAAC,CAAC;IACxB;EACF;EACD,OAAOD,MAAM;AACf;AAEA;;;;;AAKG;AACG,SAAUG,MAAMA,CAACC,IAAa;EAClC,IAAIA,IAAI,IAAI,IAAI,EAAE;IAChB,OAAO,IAAI;EACZ;EACD,IAAIA,IAAI,YAAYC,MAAM,EAAE;IAC1BD,IAAI,GAAGA,IAAI,CAACE,OAAO,EAAE;EACtB;EACD,IAAI,OAAOF,IAAI,KAAK,QAAQ,IAAIG,QAAQ,CAACH,IAAI,CAAC,EAAE;;;IAG9C,OAAOA,IAAI;EACZ;EACD,IAAIA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,EAAE;IACnC,OAAOA,IAAI;EACZ;EACD,IAAII,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,IAAI,CAAC,KAAK,iBAAiB,EAAE;IAC9D,OAAOA,IAAI;EACZ;EACD,IAAIA,IAAI,YAAYQ,IAAI,EAAE;IACxB,OAAOR,IAAI,CAACS,WAAW,EAAE;EAC1B;EACD,IAAIC,KAAK,CAACC,OAAO,CAACX,IAAI,CAAC,EAAE;IACvB,OAAOA,IAAI,CAACY,GAAG,CAACC,CAAC,IAAId,MAAM,CAACc,CAAC,CAAC,CAAC;EAChC;EACD,IAAI,OAAOb,IAAI,KAAK,UAAU,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC1D,OAAOP,SAAS,CAACO,IAAK,EAAEa,CAAC,IAAId,MAAM,CAACc,CAAC,CAAC,CAAC;EACxC;;EAED,MAAM,IAAIC,KAAK,CAAC,kCAAkC,GAAGd,IAAI,CAAC;AAC5D;AAEA;;;;;AAKG;AACG,SAAUe,MAAMA,CAACC,IAAa;EAClC,IAAIA,IAAI,IAAI,IAAI,EAAE;IAChB,OAAOA,IAAI;EACZ;EACD,IAAKA,IAAmC,CAAC,OAAO,CAAC,EAAE;IACjD,QAASA,IAAmC,CAAC,OAAO,CAAC;MACnD,KAAKzB,SAAS;;MAEd,KAAKC,kBAAkB;QAAE;;;;UAIvB,MAAMyB,KAAK,GAAGhB,MAAM,CAAEe,IAAmC,CAAC,OAAO,CAAC,CAAC;UACnE,IAAIE,KAAK,CAACD,KAAK,CAAC,EAAE;YAChB,MAAM,IAAIH,KAAK,CAAC,oCAAoC,GAAGE,IAAI,CAAC;UAC7D;UACD,OAAOC,KAAK;QACb;MACD;QAAS;UACP,MAAM,IAAIH,KAAK,CAAC,oCAAoC,GAAGE,IAAI,CAAC;QAC7D;IACF;EACF;EACD,IAAIN,KAAK,CAACC,OAAO,CAACK,IAAI,CAAC,EAAE;IACvB,OAAOA,IAAI,CAACJ,GAAG,CAACC,CAAC,IAAIE,MAAM,CAACF,CAAC,CAAC,CAAC;EAChC;EACD,IAAI,OAAOG,IAAI,KAAK,UAAU,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC1D,OAAOvB,SAAS,CAACuB,IAAK,EAAEH,CAAC,IAAIE,MAAM,CAACF,CAAC,CAAC,CAAC;EACxC;;EAED,OAAOG,IAAI;AACb;;AC5GA;;;;;;;;;;;;;;;AAeG;AAEH;;AAEG;AACI,MAAMG,cAAc,GAAG,WAAW;;ACpBzC;;;;;;;;;;;;;;;AAeG;AAQH;;;;;;AAMG;AACH,MAAMC,YAAY,GAA2C;EAC3DC,EAAE,EAAE,IAAI;EACRC,SAAS,EAAE,WAAW;EACtBC,OAAO,EAAE,SAAS;EAClBC,gBAAgB,EAAE,kBAAkB;EACpCC,iBAAiB,EAAE,mBAAmB;EACtCC,SAAS,EAAE,WAAW;EACtBC,cAAc,EAAE,gBAAgB;EAChCC,iBAAiB,EAAE,mBAAmB;EACtCC,eAAe,EAAE,iBAAiB;EAClCC,kBAAkB,EAAE,oBAAoB;EACxCC,mBAAmB,EAAE,qBAAqB;EAC1CC,OAAO,EAAE,SAAS;EAClBC,YAAY,EAAE,cAAc;EAC5BC,aAAa,EAAE,eAAe;EAC9BC,QAAQ,EAAE,UAAU;EACpBC,WAAW,EAAE,aAAa;EAC1BC,SAAS,EAAE;CACZ;AAED;;;AAGG;AACG,MAAOC,cAAe,SAAQC,aAAa;EAC/CC;EACE;;;AAGG;EACHC,IAAwB,EACxBC,OAAgB;EAChB;;AAEG;EACMC,OAAiB;IAE1B,KAAK,CAAC,GAAGxB,cAAc,IAAIsB,IAAI,EAAE,EAAEC,OAAO,IAAI,EAAE,CAAC;IAFxC,IAAO,CAAAC,OAAA,GAAPA,OAAO;;AAInB;AAED;;;;;;;AAOG;AACH,SAASC,iBAAiBA,CAACC,MAAc;;EAEvC,IAAIA,MAAM,IAAI,GAAG,IAAIA,MAAM,GAAG,GAAG,EAAE;IACjC,OAAO,IAAI;EACZ;EACD,QAAQA,MAAM;IACZ,KAAK,CAAC;;MAEJ,OAAO,UAAU;IACnB,KAAK,GAAG;MACN,OAAO,kBAAkB;IAC3B,KAAK,GAAG;MACN,OAAO,iBAAiB;IAC1B,KAAK,GAAG;MACN,OAAO,mBAAmB;IAC5B,KAAK,GAAG;MACN,OAAO,WAAW;IACpB,KAAK,GAAG;MACN,OAAO,SAAS;IAClB,KAAK,GAAG;MACN,OAAO,oBAAoB;IAC7B,KAAK,GAAG;MACN,OAAO,WAAW;IACpB,KAAK,GAAG;MACN,OAAO,UAAU;IACnB,KAAK,GAAG;MACN,OAAO,eAAe;IACxB,KAAK,GAAG;MACN,OAAO,aAAa;IACtB,KAAK,GAAG;MACN,OAAO,mBAAmB;EAE7B;EACD,OAAO,SAAS;AAClB;AAEA;;AAEG;AACa,SAAAC,iBAAiBA,CAC/BD,MAAc,EACdE,QAAiC;EAEjC,IAAIN,IAAI,GAAGG,iBAAiB,CAACC,MAAM,CAAC;;EAGpC,IAAIG,WAAW,GAAWP,IAAI;EAE9B,IAAIE,OAAO,GAAYM,SAAS;;EAGhC,IAAI;IACF,MAAMC,SAAS,GAAGH,QAAQ,IAAIA,QAAQ,CAACI,KAAK;IAC5C,IAAID,SAAS,EAAE;MACb,MAAML,MAAM,GAAGK,SAAS,CAACL,MAAM;MAC/B,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;QAC9B,IAAI,CAACzB,YAAY,CAACyB,MAAM,CAAC,EAAE;;UAEzB,OAAO,IAAIP,cAAc,CAAC,UAAU,EAAE,UAAU,CAAC;QAClD;QACDG,IAAI,GAAGrB,YAAY,CAACyB,MAAM,CAAC;;;QAI3BG,WAAW,GAAGH,MAAM;MACrB;MAED,MAAMH,OAAO,GAAGQ,SAAS,CAACR,OAAO;MACjC,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;QAC/BM,WAAW,GAAGN,OAAO;MACtB;MAEDC,OAAO,GAAGO,SAAS,CAACP,OAAO;MAC3B,IAAIA,OAAO,KAAKM,SAAS,EAAE;QACzBN,OAAO,GAAG5B,MAAM,CAAC4B,OAAO,CAAC;MAC1B;IACF;EACF,EAAC,OAAOS,CAAC,EAAE;;;EAIZ,IAAIX,IAAI,KAAK,IAAI,EAAE;;;;IAIjB,OAAO,IAAI;EACZ;EAED,OAAO,IAAIH,cAAc,CAACG,IAAI,EAAEO,WAAW,EAAEL,OAAO,CAAC;AACvD;;ACxKA;;;;;;;;;;;;;;;AAeG;AA0BH;;;AAGG;MACUU,eAAe;EAI1Bb,YACEc,YAAgD,EAChDC,iBAA2D,EAC3DC,gBAAyD;IANnD,IAAI,CAAAC,IAAA,GAAgC,IAAI;IACxC,IAAS,CAAAC,SAAA,GAA6B,IAAI;IAC1C,IAAQ,CAAAC,QAAA,GAAoC,IAAI;IAMtD,IAAI,CAACF,IAAI,GAAGH,YAAY,CAACM,YAAY,CAAC;MAAEC,QAAQ,EAAE;IAAI,CAAE,CAAC;IACzD,IAAI,CAACH,SAAS,GAAGH,iBAAiB,CAACK,YAAY,CAAC;MAC9CC,QAAQ,EAAE;IACX,EAAC;IAEF,IAAI,CAAC,IAAI,CAACJ,IAAI,EAAE;MACdH,YAAY,CAACQ,GAAG,EAAE,CAACC,IAAI,CACrBN,IAAI,IAAK,IAAI,CAACA,IAAI,GAAGA,IAAK,EAC1B,MAAK;;OAEJ,CACF;IACF;IAED,IAAI,CAAC,IAAI,CAACC,SAAS,EAAE;MACnBH,iBAAiB,CAACO,GAAG,EAAE,CAACC,IAAI,CAC1BL,SAAS,IAAK,IAAI,CAACA,SAAS,GAAGA,SAAU,EACzC,MAAK;;OAEJ,CACF;IACF;IAED,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;MAClBH,gBAAgB,CAACM,GAAG,EAAE,CAACC,IAAI,CACzBJ,QAAQ,IAAK,IAAI,CAACA,QAAQ,GAAGA,QAAS,EACtC,MAAK;;OAEJ,CACF;IACF;;EAGH,MAAMK,YAAYA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACP,IAAI,EAAE;MACd,OAAOR,SAAS;IACjB;IAED,IAAI;MACF,MAAMgB,KAAK,GAAG,MAAM,IAAI,CAACR,IAAI,CAACS,QAAQ,EAAE;MACxC,OAAOD,KAAK,KAAL,QAAAA,KAAK,uBAALA,KAAK,CAAEE,WAAW;IAC1B,EAAC,OAAOf,CAAC,EAAE;;MAEV,OAAOH,SAAS;IACjB;;EAGH,MAAMmB,iBAAiBA,CAAA;IACrB,IACE,CAAC,IAAI,CAACV,SAAS,IACf,EAAE,cAAc,IAAIW,IAAI,CAAC,IACzBC,YAAY,CAACC,UAAU,KAAK,SAAS,EACrC;MACA,OAAOtB,SAAS;IACjB;IAED,IAAI;MACF,OAAO,MAAM,IAAI,CAACS,SAAS,CAACQ,QAAQ,EAAE;IACvC,EAAC,OAAOd,CAAC,EAAE;;;;MAKV,OAAOH,SAAS;IACjB;;EAGH,MAAMuB,gBAAgBA,CACpBC,wBAAkC;IAElC,IAAI,IAAI,CAACd,QAAQ,EAAE;MACjB,MAAM/D,MAAM,GAAG6E,wBAAwB,GACnC,MAAM,IAAI,CAACd,QAAQ,CAACe,kBAAkB,EAAE,GACxC,MAAM,IAAI,CAACf,QAAQ,CAACO,QAAQ,EAAE;MAClC,IAAItE,MAAM,CAACuD,KAAK,EAAE;;;;QAIhB,OAAO,IAAI;MACZ;MACD,OAAOvD,MAAM,CAACqE,KAAK;IACpB;IACD,OAAO,IAAI;;EAGb,MAAMU,UAAUA,CAACF,wBAAkC;IACjD,MAAMG,SAAS,GAAG,MAAM,IAAI,CAACZ,YAAY,EAAE;IAC3C,MAAMa,cAAc,GAAG,MAAM,IAAI,CAACT,iBAAiB,EAAE;IACrD,MAAMU,aAAa,GAAG,MAAM,IAAI,CAACN,gBAAgB,CAACC,wBAAwB,CAAC;IAC3E,OAAO;MAAEG,SAAS;MAAEC,cAAc;MAAEC;IAAa,CAAE;;AAEtD;;ACjJD;;;;;;;;;;;;;;;AAeG;AAgBI,MAAMC,cAAc,GAAG,aAAa;AA6B3C;;;;;AAKG;AACH,SAASC,SAASA,CAACC,MAAc;;;;EAI/B,IAAIC,KAAK,GAAe,IAAI;EAC5B,OAAO;IACLC,OAAO,EAAE,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAI;MACjCJ,KAAK,GAAGK,UAAU,CAAC,MAAK;QACtBD,MAAM,CAAC,IAAIhD,cAAc,CAAC,mBAAmB,EAAE,mBAAmB,CAAC,CAAC;OACrE,EAAE2C,MAAM,CAAC;IACZ,CAAC,CAAC;IACFO,MAAM,EAAEA,CAAA,KAAK;MACX,IAAIN,KAAK,EAAE;QACTO,YAAY,CAACP,KAAK,CAAC;MACpB;;GAEJ;AACH;AAEA;;;AAGG;MACUQ,gBAAgB;EAQ3B;;;AAGG;EACHlD,WACWA,CAAAmD,GAAgB,EACzBrC,YAAgD,EAChDC,iBAA2D,EAC3DC,gBAAyD,EAEzB;IAAA,IADhCoC,oBAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA5C,SAAA,GAAA4C,SAAA,MAA+Bd,cAAc;IAAA,IACpCgB,SAAuB,GAAAF,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAA5C,SAAA;IALvB,IAAG,CAAA0C,GAAA,GAAHA,GAAG;IAKH,IAAS,CAAAI,SAAA,GAATA,SAAS;IAhBpB,IAAc,CAAAC,cAAA,GAAkB,IAAI;IAkBlC,IAAI,CAACC,eAAe,GAAG,IAAI5C,eAAe,CACxCC,YAAY,EACZC,iBAAiB,EACjBC,gBAAgB,CACjB;;IAED,IAAI,CAAC0C,iBAAiB,GAAG,IAAId,OAAO,CAACe,OAAO,IAAG;MAC7C,IAAI,CAACC,aAAa,GAAG,MAAK;QACxB,OAAOhB,OAAO,CAACe,OAAO,CAACA,OAAO,EAAE,CAAC;MACnC,CAAC;IACH,CAAC,CAAC;;IAGF,IAAI;MACF,MAAME,GAAG,GAAG,IAAIC,GAAG,CAACV,oBAAoB,CAAC;MACzC,IAAI,CAACW,YAAY,GACfF,GAAG,CAACG,MAAM,IAAIH,GAAG,CAACI,QAAQ,KAAK,GAAG,GAAG,EAAE,GAAGJ,GAAG,CAACI,QAAQ,CAAC;MACzD,IAAI,CAACC,MAAM,GAAG3B,cAAc;IAC7B,EAAC,OAAO3B,CAAC,EAAE;MACV,IAAI,CAACmD,YAAY,GAAG,IAAI;MACxB,IAAI,CAACG,MAAM,GAAGd,oBAAoB;IACnC;;EAGHe,OAAOA,CAAA;IACL,OAAO,IAAI,CAACP,aAAa,EAAE;;EAG7B;;;;AAIG;EACHQ,IAAIA,CAACC,IAAY;IACf,MAAMC,SAAS,GAAG,IAAI,CAACnB,GAAG,CAACoB,OAAO,CAACD,SAAS;IAC5C,IAAI,IAAI,CAACd,cAAc,KAAK,IAAI,EAAE;MAChC,MAAMQ,MAAM,GAAG,IAAI,CAACR,cAAc;MAClC,OAAO,GAAGQ,MAAM,IAAIM,SAAS,IAAI,IAAI,CAACJ,MAAM,IAAIG,IAAI,EAAE;IACvD;IAED,IAAI,IAAI,CAACN,YAAY,KAAK,IAAI,EAAE;MAC9B,OAAO,GAAG,IAAI,CAACA,YAAY,IAAIM,IAAI,EAAE;IACtC;IAED,OAAO,WAAW,IAAI,CAACH,MAAM,IAAII,SAAS,uBAAuBD,IAAI,EAAE;;AAE1E;AAED;;;;;;;;AAQG;SACaG,0BAAwBC,CACtCC,iBAAmC,EACnCC,IAAY,EACZC,IAAY;EAEZF,iBAAiB,CAAClB,cAAc,GAAG,UAAUmB,IAAI,IAAIC,IAAI,EAAE;AAC7D;AAEA;;;;AAIG;SACaC,eAAaC,CAC3BJ,iBAAmC,EACnCL,IAAY,EACZE,OAA8B;EAE9B,OAAQ/G,IAAI,IAAG;IACb,OAAOO,IAAI,CAAC2G,iBAAiB,EAAEL,IAAI,EAAE7G,IAAI,EAAE+G,OAAO,IAAI,EAAE,CAAC;EAC3D,CAAC;AACH;AAEA;;;;AAIG;SACaQ,sBAAoBC,CAClCN,iBAAmC,EACnCb,GAAW,EACXU,OAA8B;EAE9B,OAAQ/G,IAAI,IAAG;IACb,OAAOyH,SAAS,CAACP,iBAAiB,EAAEb,GAAG,EAAErG,IAAI,EAAE+G,OAAO,IAAI,EAAE,CAAC;EAC/D,CAAC;AACH;AAEA;;;;;;AAMG;AACH,eAAeW,QAAQA,CACrBrB,GAAW,EACXsB,IAAa,EACbC,OAAkC,EAClC7B,SAAuB;EAEvB6B,OAAO,CAAC,cAAc,CAAC,GAAG,kBAAkB;EAE5C,IAAIC,QAAkB;EACtB,IAAI;IACFA,QAAQ,GAAG,MAAM9B,SAAS,CAACM,GAAG,EAAE;MAC9ByB,MAAM,EAAE,MAAM;MACdH,IAAI,EAAEI,IAAI,CAACC,SAAS,CAACL,IAAI,CAAC;MAC1BC;IACD,EAAC;EACH,EAAC,OAAOxE,CAAC,EAAE;;;;;IAKV,OAAO;MACLP,MAAM,EAAE,CAAC;MACT7B,IAAI,EAAE;KACP;EACF;EACD,IAAIA,IAAI,GAA4B,IAAI;EACxC,IAAI;IACFA,IAAI,GAAG,MAAM6G,QAAQ,CAAC7G,IAAI,EAAE;EAC7B,EAAC,OAAOoC,CAAC,EAAE;;;EAGZ,OAAO;IACLP,MAAM,EAAEgF,QAAQ,CAAChF,MAAM;IACvB7B;GACD;AACH;AAEA;;;;AAIG;AACH,SAAST,IAAIA,CACX2G,iBAAmC,EACnCL,IAAY,EACZ7G,IAAa,EACb+G,OAA6B;EAE7B,MAAMV,GAAG,GAAGa,iBAAiB,CAACN,IAAI,CAACC,IAAI,CAAC;EACxC,OAAOY,SAAS,CAACP,iBAAiB,EAAEb,GAAG,EAAErG,IAAI,EAAE+G,OAAO,CAAC;AACzD;AAEA;;;;AAIG;AACH,eAAeU,SAASA,CACtBP,iBAAmC,EACnCb,GAAW,EACXrG,IAAa,EACb+G,OAA6B;;EAG7B/G,IAAI,GAAGD,MAAM,CAACC,IAAI,CAAC;EACnB,MAAM2H,IAAI,GAAG;IAAE3H;EAAI,CAAE;;EAGrB,MAAM4H,OAAO,GAA8B,EAAE;EAC7C,MAAMK,OAAO,GAAG,MAAMf,iBAAiB,CAACjB,eAAe,CAACtB,UAAU,CAChEoC,OAAO,CAACtC,wBAAwB,CACjC;EACD,IAAIwD,OAAO,CAACrD,SAAS,EAAE;IACrBgD,OAAO,CAAC,eAAe,CAAC,GAAG,SAAS,GAAGK,OAAO,CAACrD,SAAS;EACzD;EACD,IAAIqD,OAAO,CAACpD,cAAc,EAAE;IAC1B+C,OAAO,CAAC,4BAA4B,CAAC,GAAGK,OAAO,CAACpD,cAAc;EAC/D;EACD,IAAIoD,OAAO,CAACnD,aAAa,KAAK,IAAI,EAAE;IAClC8C,OAAO,CAAC,qBAAqB,CAAC,GAAGK,OAAO,CAACnD,aAAa;EACvD;;EAGD,MAAMoD,OAAO,GAAGnB,OAAO,CAACmB,OAAO,IAAI,KAAK;EAExC,MAAMC,eAAe,GAAGnD,SAAS,CAACkD,OAAO,CAAC;EAC1C,MAAML,QAAQ,GAAG,MAAMzC,OAAO,CAACgD,IAAI,CAAC,CAClCV,QAAQ,CAACrB,GAAG,EAAEsB,IAAI,EAAEC,OAAO,EAAEV,iBAAiB,CAACnB,SAAS,CAAC,EACzDoC,eAAe,CAAChD,OAAO,EACvB+B,iBAAiB,CAAChB,iBAAiB,CACpC,CAAC;;EAGFiC,eAAe,CAAC3C,MAAM,EAAE;;EAGxB,IAAI,CAACqC,QAAQ,EAAE;IACb,MAAM,IAAIvF,cAAc,CACtB,WAAW,EACX,0CAA0C,CAC3C;EACF;;EAGD,MAAMa,KAAK,GAAGL,iBAAiB,CAAC+E,QAAQ,CAAChF,MAAM,EAAEgF,QAAQ,CAAC7G,IAAI,CAAC;EAC/D,IAAImC,KAAK,EAAE;IACT,MAAMA,KAAK;EACZ;EAED,IAAI,CAAC0E,QAAQ,CAAC7G,IAAI,EAAE;IAClB,MAAM,IAAIsB,cAAc,CAAC,UAAU,EAAE,oCAAoC,CAAC;EAC3E;EAED,IAAI+F,YAAY,GAAGR,QAAQ,CAAC7G,IAAI,CAAChB,IAAI;;;EAGrC,IAAI,OAAOqI,YAAY,KAAK,WAAW,EAAE;IACvCA,YAAY,GAAGR,QAAQ,CAAC7G,IAAI,CAACpB,MAAM;EACpC;EACD,IAAI,OAAOyI,YAAY,KAAK,WAAW,EAAE;;IAEvC,MAAM,IAAI/F,cAAc,CAAC,UAAU,EAAE,iCAAiC,CAAC;EACxE;;EAGD,MAAMgG,WAAW,GAAGvH,MAAM,CAACsH,YAAY,CAAC;EAExC,OAAO;IAAErI,IAAI,EAAEsI;EAAW,CAAE;AAC9B;;;;ACnVA;;;;;;;;;;;;;;;AAeG;AAgBH,MAAMC,kBAAkB,GAA6B,eAAe;AACpE,MAAMC,uBAAuB,GAC3B,oBAAoB;AACtB,MAAMC,uBAAuB,GAC3B,oBAAoB;AAEN,SAAAC,iBAAiBA,CAC/B3C,SAAuB,EACvB4C,OAAgB;EAEhB,MAAMC,OAAO,GAAiCA,CAC5CC,SAA6B,EAAAC,IAAA,KAE3B;IAAA,IADF;MAAEC,kBAAkB,EAAEnD;IAAoB,CAAE,GAAAkD,IAAA;;IAG5C,MAAMnD,GAAG,GAAGkD,SAAS,CAACG,WAAW,CAAC,KAAK,CAAC,CAACpF,YAAY,EAAE;IACvD,MAAMN,YAAY,GAAGuF,SAAS,CAACG,WAAW,CAACT,kBAAkB,CAAC;IAC9D,MAAMhF,iBAAiB,GAAGsF,SAAS,CAACG,WAAW,CAACP,uBAAuB,CAAC;IACxE,MAAMjF,gBAAgB,GAAGqF,SAAS,CAACG,WAAW,CAACR,uBAAuB,CAAC;;IAGvE,OAAO,IAAI9C,gBAAgB,CACzBC,GAAG,EACHrC,YAAY,EACZC,iBAAiB,EACjBC,gBAAgB,EAChBoC,oBAAoB,EACpBG,SAAS,CACV;EACH,CAAC;EAEDkD,kBAAkB,CAChB,IAAIC,SAAS,CACX/H,cAAc,EACdyH,OAAO,EAER,qCAACO,oBAAoB,CAAC,IAAI,CAAC,CAC7B;EAEDC,eAAe,CAACvC,IAAI,EAAEwC,OAAO,EAAEV,OAAO,CAAC;;EAEvCS,eAAe,CAACvC,IAAI,EAAEwC,OAAO,EAAE,SAAkB,CAAC;AACpD;;ACzEA;;;;;;;;;;;;;;;AAeG;AAqBH;;;;;;;AAOG;AACG,SAAUC,YAAYA,CAAA,EAEmB;EAAA,IAD7C3D,GAAA,GAAAE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA5C,SAAA,GAAA4C,SAAA,MAAmB0D,MAAM,EAAE;EAAA,IAC3B3D,oBAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA5C,SAAA,GAAA4C,SAAA,MAA+Bd,cAAc;;EAG7C,MAAMyE,iBAAiB,GAA0BC,YAAY,CAC3DC,kBAAkB,CAAC/D,GAAG,CAAC,EACvBxE,cAAc,CACf;EACD,MAAM+F,iBAAiB,GAAGsC,iBAAiB,CAAC5F,YAAY,CAAC;IACvD+F,UAAU,EAAE/D;EACb,EAAC;EACF,MAAMgE,QAAQ,GAAGC,iCAAiC,CAAC,WAAW,CAAC;EAC/D,IAAID,QAAQ,EAAE;IACZ3C,wBAAwB,CAACC,iBAAiB,EAAE,GAAG0C,QAAQ,CAAC;EACzD;EACD,OAAO1C,iBAAiB;AAC1B;AAEA;;;;;;;;AAQG;SACaD,wBAAwBA,CACtCC,iBAA4B,EAC5BC,IAAY,EACZC,IAAY;EAEZJ,0BAAyB,CACvB0C,kBAAkB,CAAmBxC,iBAAqC,CAAC,EAC3EC,IAAI,EACJC,IAAI,CACL;AACH;AAEA;;;;AAIG;SACaE,aAAaA,CAC3BJ,iBAA4B,EAC5BL,IAAY,EACZE,OAA8B;EAE9B,OAAOM,eAAc,CACnBqC,kBAAkB,CAAmBxC,iBAAqC,CAAC,EAC3EL,IAAI,EACJE,OAAO,CACR;AACH;AAEA;;;;AAIG;SACaS,oBAAoBA,CAIlCN,iBAA4B,EAC5Bb,GAAW,EACXU,OAA8B;EAE9B,OAAOQ,sBAAqB,CAC1BmC,kBAAkB,CAAmBxC,iBAAqC,CAAC,EAC3Eb,GAAG,EACHU,OAAO,CACR;AACH;;ACvHA;;;;AAIG;AAuBH2B,iBAAiB,CAACoB,KAAK,CAACC,IAAI,CAAC1F,IAAI,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}