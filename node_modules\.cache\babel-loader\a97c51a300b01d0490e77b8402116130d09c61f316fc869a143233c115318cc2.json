{"ast": null, "code": "import{initializeApp}from'firebase/app';import{getAuth}from'firebase/auth';import{getFirestore}from'firebase/firestore';import{getStorage}from'firebase/storage';import{getFunctions}from'firebase/functions';const firebaseConfig={// يجب إضافة إعدادات Firebase الخاصة بك هنا\napiKey:\"your-api-key\",authDomain:\"your-project.firebaseapp.com\",projectId:\"your-project-id\",storageBucket:\"your-project.appspot.com\",messagingSenderId:\"123456789\",appId:\"your-app-id\"};// Initialize Firebase\nconst app=initializeApp(firebaseConfig);// Initialize Firebase services\nexport const auth=getAuth(app);export const db=getFirestore(app);export const storage=getStorage(app);export const functions=getFunctions(app);export default app;", "map": {"version": 3, "names": ["initializeApp", "getAuth", "getFirestore", "getStorage", "getFunctions", "firebaseConfig", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "projectId", "storageBucket", "messagingSenderId", "appId", "app", "auth", "db", "storage", "functions"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/config/firebase.ts"], "sourcesContent": ["import { initializeApp } from 'firebase/app';\nimport { getAuth } from 'firebase/auth';\nimport { getFirestore } from 'firebase/firestore';\nimport { getStorage } from 'firebase/storage';\nimport { getFunctions } from 'firebase/functions';\n\nconst firebaseConfig = {\n  // يجب إضافة إعدادات Firebase الخاصة بك هنا\n  apiKey: \"your-api-key\",\n  authDomain: \"your-project.firebaseapp.com\",\n  projectId: \"your-project-id\",\n  storageBucket: \"your-project.appspot.com\",\n  messagingSenderId: \"123456789\",\n  appId: \"your-app-id\"\n};\n\n// Initialize Firebase\nconst app = initializeApp(firebaseConfig);\n\n// Initialize Firebase services\nexport const auth = getAuth(app);\nexport const db = getFirestore(app);\nexport const storage = getStorage(app);\nexport const functions = getFunctions(app);\n\nexport default app;\n"], "mappings": "AAAA,OAASA,aAAa,KAAQ,cAAc,CAC5C,OAASC,OAAO,KAAQ,eAAe,CACvC,OAASC,YAAY,KAAQ,oBAAoB,CACjD,OAASC,UAAU,KAAQ,kBAAkB,CAC7C,OAASC,YAAY,KAAQ,oBAAoB,CAEjD,KAAM,CAAAC,cAAc,CAAG,CACrB;AACAC,MAAM,CAAE,cAAc,CACtBC,UAAU,CAAE,8BAA8B,CAC1CC,SAAS,CAAE,iBAAiB,CAC5BC,aAAa,CAAE,0BAA0B,CACzCC,iBAAiB,CAAE,WAAW,CAC9BC,KAAK,CAAE,aACT,CAAC,CAED;AACA,KAAM,CAAAC,GAAG,CAAGZ,aAAa,CAACK,cAAc,CAAC,CAEzC;AACA,MAAO,MAAM,CAAAQ,IAAI,CAAGZ,OAAO,CAACW,GAAG,CAAC,CAChC,MAAO,MAAM,CAAAE,EAAE,CAAGZ,YAAY,CAACU,GAAG,CAAC,CACnC,MAAO,MAAM,CAAAG,OAAO,CAAGZ,UAAU,CAACS,GAAG,CAAC,CACtC,MAAO,MAAM,CAAAI,SAAS,CAAGZ,YAAY,CAACQ,GAAG,CAAC,CAE1C,cAAe,CAAAA,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}