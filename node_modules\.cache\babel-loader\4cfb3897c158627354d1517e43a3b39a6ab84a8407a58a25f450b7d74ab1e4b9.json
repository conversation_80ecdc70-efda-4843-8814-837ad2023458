{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0634\\u0631\\u0648\\u0639\\\\src\\\\components\\\\Admin\\\\SettingsPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { CogIcon, BellIcon, ShieldCheckIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SettingsPage = ({\n  onBack\n}) => {\n  _s();\n  const [settings, setSettings] = useState({\n    siteName: 'منصة ALaa Abd Hamied',\n    siteDescription: 'منصة تعليمية متقدمة',\n    allowRegistration: true,\n    requireEmailVerification: true,\n    enableNotifications: true,\n    defaultLanguage: 'ar'\n  });\n  const handleSettingChange = (key, value) => {\n    setSettings(prev => ({\n      ...prev,\n      [key]: value\n    }));\n  };\n  const handleSaveSettings = () => {\n    // TODO: Implement save settings functionality\n    console.log('Save settings:', settings);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-4 space-x-reverse\",\n      children: [onBack && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onBack,\n        className: \"p-2 text-gray-600 hover:text-gray-900 transition-colors\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-6 h-6\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M15 19l-7-7 7-7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0645\\u0646\\u0635\\u0629 \\u0627\\u0644\\u0639\\u0627\\u0645\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"bg-white rounded-lg shadow-sm p-6 border border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3 space-x-reverse mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(CogIcon, {\n            className: \"w-6 h-6 text-gray-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: \"\\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0639\\u0627\\u0645\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0648\\u0642\\u0639\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: settings.siteName,\n              onChange: e => handleSettingChange('siteName', e.target.value),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"\\u0648\\u0635\\u0641 \\u0627\\u0644\\u0645\\u0648\\u0642\\u0639\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: settings.siteDescription,\n              onChange: e => handleSettingChange('siteDescription', e.target.value),\n              rows: 3,\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"\\u0627\\u0644\\u0644\\u063A\\u0629 \\u0627\\u0644\\u0627\\u0641\\u062A\\u0631\\u0627\\u0636\\u064A\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: settings.defaultLanguage,\n              onChange: e => handleSettingChange('defaultLanguage', e.target.value),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"ar\",\n                children: \"\\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"en\",\n                children: \"English\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.1\n        },\n        className: \"bg-white rounded-lg shadow-sm p-6 border border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3 space-x-reverse mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(ShieldCheckIcon, {\n            className: \"w-6 h-6 text-gray-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: \"\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\\u064A\\u0646\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: \"\\u0627\\u0644\\u0633\\u0645\\u0627\\u062D \\u0628\\u0627\\u0644\\u062A\\u0633\\u062C\\u064A\\u0644\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"\\u0627\\u0644\\u0633\\u0645\\u0627\\u062D \\u0644\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\\u064A\\u0646 \\u0627\\u0644\\u062C\\u062F\\u062F \\u0628\\u0627\\u0644\\u062A\\u0633\\u062C\\u064A\\u0644\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"relative inline-flex items-center cursor-pointer\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: settings.allowRegistration,\n                onChange: e => handleSettingChange('allowRegistration', e.target.checked),\n                className: \"sr-only peer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: \"\\u062A\\u0623\\u0643\\u064A\\u062F \\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"\\u0637\\u0644\\u0628 \\u062A\\u0623\\u0643\\u064A\\u062F \\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A \\u0639\\u0646\\u062F \\u0627\\u0644\\u062A\\u0633\\u062C\\u064A\\u0644\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"relative inline-flex items-center cursor-pointer\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: settings.requireEmailVerification,\n                onChange: e => handleSettingChange('requireEmailVerification', e.target.checked),\n                className: \"sr-only peer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        className: \"bg-white rounded-lg shadow-sm p-6 border border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3 space-x-reverse mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(BellIcon, {\n            className: \"w-6 h-6 text-gray-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: \"\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0625\\u0634\\u0639\\u0627\\u0631\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: \"\\u062A\\u0641\\u0639\\u064A\\u0644 \\u0627\\u0644\\u0625\\u0634\\u0639\\u0627\\u0631\\u0627\\u062A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"\\u0625\\u0631\\u0633\\u0627\\u0644 \\u0625\\u0634\\u0639\\u0627\\u0631\\u0627\\u062A \\u0644\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\\u064A\\u0646\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"relative inline-flex items-center cursor-pointer\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: settings.enableNotifications,\n                onChange: e => handleSettingChange('enableNotifications', e.target.checked),\n                className: \"sr-only peer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-end\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleSaveSettings,\n          className: \"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n          children: \"\\u062D\\u0641\\u0638 \\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 5\n  }, this);\n};\n_s(SettingsPage, \"JfDU4s6OCmd19FheRSXj2s6v3Ig=\");\n_c = SettingsPage;\nexport default SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");", "map": {"version": 3, "names": ["React", "useState", "motion", "CogIcon", "BellIcon", "ShieldCheckIcon", "jsxDEV", "_jsxDEV", "SettingsPage", "onBack", "_s", "settings", "setSettings", "siteName", "siteDescription", "allowRegistration", "requireEmailVerification", "enableNotifications", "defaultLanguage", "handleSettingChange", "key", "value", "prev", "handleSaveSettings", "console", "log", "className", "children", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "y", "animate", "type", "onChange", "e", "target", "rows", "transition", "delay", "checked", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Admin/SettingsPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  CogIcon,\n  BellIcon,\n  ShieldCheckIcon,\n  GlobeAltIcon\n} from '@heroicons/react/24/outline';\n\ninterface SettingsPageProps {\n  onBack?: () => void;\n}\n\nconst SettingsPage: React.FC<SettingsPageProps> = ({ onBack }) => {\n  const [settings, setSettings] = useState({\n    siteName: 'منصة ALaa Abd Hamied',\n    siteDescription: 'منصة تعليمية متقدمة',\n    allowRegistration: true,\n    requireEmailVerification: true,\n    enableNotifications: true,\n    defaultLanguage: 'ar'\n  });\n\n  const handleSettingChange = (key: string, value: any) => {\n    setSettings(prev => ({\n      ...prev,\n      [key]: value\n    }));\n  };\n\n  const handleSaveSettings = () => {\n    // TODO: Implement save settings functionality\n    console.log('Save settings:', settings);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center space-x-4 space-x-reverse\">\n        {onBack && (\n          <button\n            onClick={onBack}\n            className=\"p-2 text-gray-600 hover:text-gray-900 transition-colors\"\n          >\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n            </svg>\n          </button>\n        )}\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">إعدادات النظام</h1>\n          <p className=\"text-gray-600\">إدارة إعدادات المنصة العامة</p>\n        </div>\n      </div>\n\n      {/* Settings Sections */}\n      <div className=\"space-y-6\">\n        {/* General Settings */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"bg-white rounded-lg shadow-sm p-6 border border-gray-200\"\n        >\n          <div className=\"flex items-center space-x-3 space-x-reverse mb-6\">\n            <CogIcon className=\"w-6 h-6 text-gray-600\" />\n            <h2 className=\"text-lg font-semibold text-gray-900\">الإعدادات العامة</h2>\n          </div>\n\n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                اسم الموقع\n              </label>\n              <input\n                type=\"text\"\n                value={settings.siteName}\n                onChange={(e) => handleSettingChange('siteName', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                وصف الموقع\n              </label>\n              <textarea\n                value={settings.siteDescription}\n                onChange={(e) => handleSettingChange('siteDescription', e.target.value)}\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                اللغة الافتراضية\n              </label>\n              <select\n                value={settings.defaultLanguage}\n                onChange={(e) => handleSettingChange('defaultLanguage', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                <option value=\"ar\">العربية</option>\n                <option value=\"en\">English</option>\n              </select>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* User Settings */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.1 }}\n          className=\"bg-white rounded-lg shadow-sm p-6 border border-gray-200\"\n        >\n          <div className=\"flex items-center space-x-3 space-x-reverse mb-6\">\n            <ShieldCheckIcon className=\"w-6 h-6 text-gray-600\" />\n            <h2 className=\"text-lg font-semibold text-gray-900\">إعدادات المستخدمين</h2>\n          </div>\n\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h3 className=\"text-sm font-medium text-gray-900\">السماح بالتسجيل</h3>\n                <p className=\"text-sm text-gray-600\">السماح للمستخدمين الجدد بالتسجيل</p>\n              </div>\n              <label className=\"relative inline-flex items-center cursor-pointer\">\n                <input\n                  type=\"checkbox\"\n                  checked={settings.allowRegistration}\n                  onChange={(e) => handleSettingChange('allowRegistration', e.target.checked)}\n                  className=\"sr-only peer\"\n                />\n                <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n              </label>\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h3 className=\"text-sm font-medium text-gray-900\">تأكيد البريد الإلكتروني</h3>\n                <p className=\"text-sm text-gray-600\">طلب تأكيد البريد الإلكتروني عند التسجيل</p>\n              </div>\n              <label className=\"relative inline-flex items-center cursor-pointer\">\n                <input\n                  type=\"checkbox\"\n                  checked={settings.requireEmailVerification}\n                  onChange={(e) => handleSettingChange('requireEmailVerification', e.target.checked)}\n                  className=\"sr-only peer\"\n                />\n                <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n              </label>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Notification Settings */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.2 }}\n          className=\"bg-white rounded-lg shadow-sm p-6 border border-gray-200\"\n        >\n          <div className=\"flex items-center space-x-3 space-x-reverse mb-6\">\n            <BellIcon className=\"w-6 h-6 text-gray-600\" />\n            <h2 className=\"text-lg font-semibold text-gray-900\">إعدادات الإشعارات</h2>\n          </div>\n\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h3 className=\"text-sm font-medium text-gray-900\">تفعيل الإشعارات</h3>\n                <p className=\"text-sm text-gray-600\">إرسال إشعارات للمستخدمين</p>\n              </div>\n              <label className=\"relative inline-flex items-center cursor-pointer\">\n                <input\n                  type=\"checkbox\"\n                  checked={settings.enableNotifications}\n                  onChange={(e) => handleSettingChange('enableNotifications', e.target.checked)}\n                  className=\"sr-only peer\"\n                />\n                <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n              </label>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Save Button */}\n        <div className=\"flex justify-end\">\n          <button\n            onClick={handleSaveSettings}\n            className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            حفظ الإعدادات\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SettingsPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,OAAO,EACPC,QAAQ,EACRC,eAAe,QAEV,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMrC,MAAMC,YAAyC,GAAGA,CAAC;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAChE,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC;IACvCY,QAAQ,EAAE,sBAAsB;IAChCC,eAAe,EAAE,qBAAqB;IACtCC,iBAAiB,EAAE,IAAI;IACvBC,wBAAwB,EAAE,IAAI;IAC9BC,mBAAmB,EAAE,IAAI;IACzBC,eAAe,EAAE;EACnB,CAAC,CAAC;EAEF,MAAMC,mBAAmB,GAAGA,CAACC,GAAW,EAAEC,KAAU,KAAK;IACvDT,WAAW,CAACU,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACF,GAAG,GAAGC;IACT,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,kBAAkB,GAAGA,CAAA,KAAM;IAC/B;IACAC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEd,QAAQ,CAAC;EACzC,CAAC;EAED,oBACEJ,OAAA;IAAKmB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBpB,OAAA;MAAKmB,SAAS,EAAC,6CAA6C;MAAAC,QAAA,GACzDlB,MAAM,iBACLF,OAAA;QACEqB,OAAO,EAAEnB,MAAO;QAChBiB,SAAS,EAAC,yDAAyD;QAAAC,QAAA,eAEnEpB,OAAA;UAAKmB,SAAS,EAAC,SAAS;UAACG,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAJ,QAAA,eAC5EpB,OAAA;YAAMyB,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,WAAW,EAAE,CAAE;YAACC,CAAC,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACT,eACDhC,OAAA;QAAAoB,QAAA,gBACEpB,OAAA;UAAImB,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAc;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpEhC,OAAA;UAAGmB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA2B;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhC,OAAA;MAAKmB,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAExBpB,OAAA,CAACL,MAAM,CAACsC,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BjB,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBAEpEpB,OAAA;UAAKmB,SAAS,EAAC,kDAAkD;UAAAC,QAAA,gBAC/DpB,OAAA,CAACJ,OAAO;YAACuB,SAAS,EAAC;UAAuB;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7ChC,OAAA;YAAImB,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAgB;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE,CAAC,eAENhC,OAAA;UAAKmB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBpB,OAAA;YAAAoB,QAAA,gBACEpB,OAAA;cAAOmB,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhC,OAAA;cACEsC,IAAI,EAAC,MAAM;cACXxB,KAAK,EAAEV,QAAQ,CAACE,QAAS;cACzBiC,QAAQ,EAAGC,CAAC,IAAK5B,mBAAmB,CAAC,UAAU,EAAE4B,CAAC,CAACC,MAAM,CAAC3B,KAAK,CAAE;cACjEK,SAAS,EAAC;YAAwG;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENhC,OAAA;YAAAoB,QAAA,gBACEpB,OAAA;cAAOmB,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhC,OAAA;cACEc,KAAK,EAAEV,QAAQ,CAACG,eAAgB;cAChCgC,QAAQ,EAAGC,CAAC,IAAK5B,mBAAmB,CAAC,iBAAiB,EAAE4B,CAAC,CAACC,MAAM,CAAC3B,KAAK,CAAE;cACxE4B,IAAI,EAAE,CAAE;cACRvB,SAAS,EAAC;YAAwG;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENhC,OAAA;YAAAoB,QAAA,gBACEpB,OAAA;cAAOmB,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhC,OAAA;cACEc,KAAK,EAAEV,QAAQ,CAACO,eAAgB;cAChC4B,QAAQ,EAAGC,CAAC,IAAK5B,mBAAmB,CAAC,iBAAiB,EAAE4B,CAAC,CAACC,MAAM,CAAC3B,KAAK,CAAE;cACxEK,SAAS,EAAC,wGAAwG;cAAAC,QAAA,gBAElHpB,OAAA;gBAAQc,KAAK,EAAC,IAAI;gBAAAM,QAAA,EAAC;cAAO;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnChC,OAAA;gBAAQc,KAAK,EAAC,IAAI;gBAAAM,QAAA,EAAC;cAAO;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbhC,OAAA,CAACL,MAAM,CAACsC,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BO,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BzB,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBAEpEpB,OAAA;UAAKmB,SAAS,EAAC,kDAAkD;UAAAC,QAAA,gBAC/DpB,OAAA,CAACF,eAAe;YAACqB,SAAS,EAAC;UAAuB;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrDhC,OAAA;YAAImB,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAkB;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE,CAAC,eAENhC,OAAA;UAAKmB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBpB,OAAA;YAAKmB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDpB,OAAA;cAAAoB,QAAA,gBACEpB,OAAA;gBAAImB,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAe;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtEhC,OAAA;gBAAGmB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAgC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eACNhC,OAAA;cAAOmB,SAAS,EAAC,kDAAkD;cAAAC,QAAA,gBACjEpB,OAAA;gBACEsC,IAAI,EAAC,UAAU;gBACfO,OAAO,EAAEzC,QAAQ,CAACI,iBAAkB;gBACpC+B,QAAQ,EAAGC,CAAC,IAAK5B,mBAAmB,CAAC,mBAAmB,EAAE4B,CAAC,CAACC,MAAM,CAACI,OAAO,CAAE;gBAC5E1B,SAAS,EAAC;cAAc;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eACFhC,OAAA;gBAAKmB,SAAS,EAAC;cAAyX;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1Y,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENhC,OAAA;YAAKmB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDpB,OAAA;cAAAoB,QAAA,gBACEpB,OAAA;gBAAImB,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAuB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9EhC,OAAA;gBAAGmB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAuC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC,eACNhC,OAAA;cAAOmB,SAAS,EAAC,kDAAkD;cAAAC,QAAA,gBACjEpB,OAAA;gBACEsC,IAAI,EAAC,UAAU;gBACfO,OAAO,EAAEzC,QAAQ,CAACK,wBAAyB;gBAC3C8B,QAAQ,EAAGC,CAAC,IAAK5B,mBAAmB,CAAC,0BAA0B,EAAE4B,CAAC,CAACC,MAAM,CAACI,OAAO,CAAE;gBACnF1B,SAAS,EAAC;cAAc;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eACFhC,OAAA;gBAAKmB,SAAS,EAAC;cAAyX;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1Y,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbhC,OAAA,CAACL,MAAM,CAACsC,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BO,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BzB,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBAEpEpB,OAAA;UAAKmB,SAAS,EAAC,kDAAkD;UAAAC,QAAA,gBAC/DpB,OAAA,CAACH,QAAQ;YAACsB,SAAS,EAAC;UAAuB;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9ChC,OAAA;YAAImB,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAiB;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC,eAENhC,OAAA;UAAKmB,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxBpB,OAAA;YAAKmB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDpB,OAAA;cAAAoB,QAAA,gBACEpB,OAAA;gBAAImB,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAe;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtEhC,OAAA;gBAAGmB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAwB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC,eACNhC,OAAA;cAAOmB,SAAS,EAAC,kDAAkD;cAAAC,QAAA,gBACjEpB,OAAA;gBACEsC,IAAI,EAAC,UAAU;gBACfO,OAAO,EAAEzC,QAAQ,CAACM,mBAAoB;gBACtC6B,QAAQ,EAAGC,CAAC,IAAK5B,mBAAmB,CAAC,qBAAqB,EAAE4B,CAAC,CAACC,MAAM,CAACI,OAAO,CAAE;gBAC9E1B,SAAS,EAAC;cAAc;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eACFhC,OAAA;gBAAKmB,SAAS,EAAC;cAAyX;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1Y,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbhC,OAAA;QAAKmB,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BpB,OAAA;UACEqB,OAAO,EAAEL,kBAAmB;UAC5BG,SAAS,EAAC,iFAAiF;UAAAC,QAAA,EAC5F;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7B,EAAA,CA1LIF,YAAyC;AAAA6C,EAAA,GAAzC7C,YAAyC;AA4L/C,eAAeA,YAAY;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}