{"ast": null, "code": "export const mockStudents=[{id:'student-001',email:'<EMAIL>',name:'أحمد محمد',role:'student',avatar:'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',accessCode:'STU001',enrolledCourses:['1','2'],completedCourses:['1'],certificates:['cert-001'],createdAt:new Date('2024-01-15'),lastLogin:new Date()},{id:'student-002',email:'<EMAIL>',name:'فاطمة أحمد',role:'student',avatar:'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',accessCode:'STU002',enrolledCourses:['2'],completedCourses:[],certificates:[],createdAt:new Date('2024-01-20'),lastLogin:new Date()},{id:'student-003',email:'<EMAIL>',name:'محمد علي',role:'student',avatar:'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',accessCode:'STU003',enrolledCourses:['1'],completedCourses:[],certificates:[],createdAt:new Date('2024-02-01'),lastLogin:new Date()}];// بيانات تسجيل دخول الطلاب للاختبار\nexport const studentCredentials=[{email:'<EMAIL>',password:'Student@123',accessCode:'STU001'},{email:'<EMAIL>',password:'Student@123',accessCode:'STU002'},{email:'<EMAIL>',password:'Student@123',accessCode:'STU003'}];", "map": {"version": 3, "names": ["mockStudents", "id", "email", "name", "role", "avatar", "accessCode", "enrolledCourses", "completedCourses", "certificates", "createdAt", "Date", "lastLogin", "studentCredentials", "password"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/data/mockStudents.ts"], "sourcesContent": ["import { Student } from '../types';\n\nexport const mockStudents: Student[] = [\n  {\n    id: 'student-001',\n    email: '<EMAIL>',\n    name: 'أحمد محمد',\n    role: 'student',\n    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n    accessCode: 'STU001',\n    enrolledCourses: ['1', '2'],\n    completedCourses: ['1'],\n    certificates: ['cert-001'],\n    createdAt: new Date('2024-01-15'),\n    lastLogin: new Date()\n  },\n  {\n    id: 'student-002',\n    email: '<EMAIL>',\n    name: 'فاطمة أحمد',\n    role: 'student',\n    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',\n    accessCode: 'STU002',\n    enrolledCourses: ['2'],\n    completedCourses: [],\n    certificates: [],\n    createdAt: new Date('2024-01-20'),\n    lastLogin: new Date()\n  },\n  {\n    id: 'student-003',\n    email: '<EMAIL>',\n    name: 'محمد علي',\n    role: 'student',\n    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\n    accessCode: 'STU003',\n    enrolledCourses: ['1'],\n    completedCourses: [],\n    certificates: [],\n    createdAt: new Date('2024-02-01'),\n    lastLogin: new Date()\n  }\n];\n\n// بيانات تسجيل دخول الطلاب للاختبار\nexport const studentCredentials = [\n  { email: '<EMAIL>', password: 'Student@123', accessCode: 'STU001' },\n  { email: '<EMAIL>', password: 'Student@123', accessCode: 'STU002' },\n  { email: '<EMAIL>', password: 'Student@123', accessCode: 'STU003' }\n];\n"], "mappings": "AAEA,MAAO,MAAM,CAAAA,YAAuB,CAAG,CACrC,CACEC,EAAE,CAAE,aAAa,CACjBC,KAAK,CAAE,sBAAsB,CAC7BC,IAAI,CAAE,WAAW,CACjBC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,6FAA6F,CACrGC,UAAU,CAAE,QAAQ,CACpBC,eAAe,CAAE,CAAC,GAAG,CAAE,GAAG,CAAC,CAC3BC,gBAAgB,CAAE,CAAC,GAAG,CAAC,CACvBC,YAAY,CAAE,CAAC,UAAU,CAAC,CAC1BC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAAC,CACjCC,SAAS,CAAE,GAAI,CAAAD,IAAI,CAAC,CACtB,CAAC,CACD,CACEV,EAAE,CAAE,aAAa,CACjBC,KAAK,CAAE,sBAAsB,CAC7BC,IAAI,CAAE,YAAY,CAClBC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,6FAA6F,CACrGC,UAAU,CAAE,QAAQ,CACpBC,eAAe,CAAE,CAAC,GAAG,CAAC,CACtBC,gBAAgB,CAAE,EAAE,CACpBC,YAAY,CAAE,EAAE,CAChBC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAAC,CACjCC,SAAS,CAAE,GAAI,CAAAD,IAAI,CAAC,CACtB,CAAC,CACD,CACEV,EAAE,CAAE,aAAa,CACjBC,KAAK,CAAE,sBAAsB,CAC7BC,IAAI,CAAE,UAAU,CAChBC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,6FAA6F,CACrGC,UAAU,CAAE,QAAQ,CACpBC,eAAe,CAAE,CAAC,GAAG,CAAC,CACtBC,gBAAgB,CAAE,EAAE,CACpBC,YAAY,CAAE,EAAE,CAChBC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAAC,CACjCC,SAAS,CAAE,GAAI,CAAAD,IAAI,CAAC,CACtB,CAAC,CACF,CAED;AACA,MAAO,MAAM,CAAAE,kBAAkB,CAAG,CAChC,CAAEX,KAAK,CAAE,sBAAsB,CAAEY,QAAQ,CAAE,aAAa,CAAER,UAAU,CAAE,QAAS,CAAC,CAChF,CAAEJ,KAAK,CAAE,sBAAsB,CAAEY,QAAQ,CAAE,aAAa,CAAER,UAAU,CAAE,QAAS,CAAC,CAChF,CAAEJ,KAAK,CAAE,sBAAsB,CAAEY,QAAQ,CAAE,aAAa,CAAER,UAAU,CAAE,QAAS,CAAC,CACjF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}