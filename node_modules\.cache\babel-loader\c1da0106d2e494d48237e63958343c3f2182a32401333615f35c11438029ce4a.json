{"ast": null, "code": "/**\n * We keep these listed seperately as we use the lowercase tag names as part\n * of the runtime bundle to detect SVG components\n */\nconst lowercaseSVGElements = [\"animate\", \"circle\", \"defs\", \"desc\", \"ellipse\", \"g\", \"image\", \"line\", \"filter\", \"marker\", \"mask\", \"metadata\", \"path\", \"pattern\", \"polygon\", \"polyline\", \"rect\", \"stop\", \"switch\", \"symbol\", \"svg\", \"text\", \"tspan\", \"use\", \"view\"];\nexport { lowercaseSVGElements };", "map": {"version": 3, "names": ["lowercaseSVGElements"], "sources": ["C:/Users/<USER>/Desktop/مشروع/node_modules/framer-motion/dist/es/render/svg/lowercase-elements.mjs"], "sourcesContent": ["/**\n * We keep these listed seperately as we use the lowercase tag names as part\n * of the runtime bundle to detect SVG components\n */\nconst lowercaseSVGElements = [\n    \"animate\",\n    \"circle\",\n    \"defs\",\n    \"desc\",\n    \"ellipse\",\n    \"g\",\n    \"image\",\n    \"line\",\n    \"filter\",\n    \"marker\",\n    \"mask\",\n    \"metadata\",\n    \"path\",\n    \"pattern\",\n    \"polygon\",\n    \"polyline\",\n    \"rect\",\n    \"stop\",\n    \"switch\",\n    \"symbol\",\n    \"svg\",\n    \"text\",\n    \"tspan\",\n    \"use\",\n    \"view\",\n];\n\nexport { lowercaseSVGElements };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,MAAMA,oBAAoB,GAAG,CACzB,SAAS,EACT,QAAQ,EACR,MAAM,EACN,MAAM,EACN,SAAS,EACT,GAAG,EACH,OAAO,EACP,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,UAAU,EACV,MAAM,EACN,SAAS,EACT,SAAS,EACT,UAAU,EACV,MAAM,EACN,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,KAAK,EACL,MAAM,EACN,OAAO,EACP,KAAK,EACL,MAAM,CACT;AAED,SAASA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}