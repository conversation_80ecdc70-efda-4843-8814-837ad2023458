{"ast": null, "code": "import { noop } from '../utils/noop.mjs';\n\n/*\n  Bezier function generator\n  This has been modified from <PERSON><PERSON><PERSON><PERSON>'s BezierEasing\n  https://github.com/gre/bezier-easing/blob/master/src/index.js\n  https://github.com/gre/bezier-easing/blob/master/LICENSE\n  \n  I've removed the newtonRaphsonIterate algo because in benchmarking it\n  wasn't noticiably faster than binarySubdivision, indeed removing it\n  usually improved times, depending on the curve.\n  I also removed the lookup table, as for the added bundle size and loop we're\n  only cutting ~4 or so subdivision iterations. I bumped the max iterations up\n  to 12 to compensate and this still tended to be faster for no perceivable\n  loss in accuracy.\n  Usage\n    const easeOut = cubicBezier(.17,.67,.83,.67);\n    const x = easeOut(0.5); // returns 0.627...\n*/\n// Returns x(t) given t, x1, and x2, or y(t) given t, y1, and y2.\nconst calcBezier = (t, a1, a2) => (((1.0 - 3.0 * a2 + 3.0 * a1) * t + (3.0 * a2 - 6.0 * a1)) * t + 3.0 * a1) * t;\nconst subdivisionPrecision = 0.0000001;\nconst subdivisionMaxIterations = 12;\nfunction binarySubdivide(x, lowerBound, upperBound, mX1, mX2) {\n  let currentX;\n  let currentT;\n  let i = 0;\n  do {\n    currentT = lowerBound + (upperBound - lowerBound) / 2.0;\n    currentX = calcBezier(currentT, mX1, mX2) - x;\n    if (currentX > 0.0) {\n      upperBound = currentT;\n    } else {\n      lowerBound = currentT;\n    }\n  } while (Math.abs(currentX) > subdivisionPrecision && ++i < subdivisionMaxIterations);\n  return currentT;\n}\nfunction cubicBezier(mX1, mY1, mX2, mY2) {\n  // If this is a linear gradient, return linear easing\n  if (mX1 === mY1 && mX2 === mY2) return noop;\n  const getTForX = aX => binarySubdivide(aX, 0, 1, mX1, mX2);\n  // If animation is at start/end, return t without easing\n  return t => t === 0 || t === 1 ? t : calcBezier(getTForX(t), mY1, mY2);\n}\nexport { cubicBezier };", "map": {"version": 3, "names": ["noop", "calcBezier", "t", "a1", "a2", "subdivisionPrecision", "subdivisionMaxIterations", "binarySubdivide", "x", "lowerBound", "upperBound", "mX1", "mX2", "currentX", "currentT", "i", "Math", "abs", "cubicBezier", "mY1", "mY2", "getTForX", "aX"], "sources": ["C:/Users/<USER>/Desktop/مشروع/node_modules/framer-motion/dist/es/easing/cubic-bezier.mjs"], "sourcesContent": ["import { noop } from '../utils/noop.mjs';\n\n/*\n  Bezier function generator\n  This has been modified from <PERSON><PERSON><PERSON><PERSON>'s BezierEasing\n  https://github.com/gre/bezier-easing/blob/master/src/index.js\n  https://github.com/gre/bezier-easing/blob/master/LICENSE\n  \n  I've removed the newtonRaphsonIterate algo because in benchmarking it\n  wasn't noticiably faster than binarySubdivision, indeed removing it\n  usually improved times, depending on the curve.\n  I also removed the lookup table, as for the added bundle size and loop we're\n  only cutting ~4 or so subdivision iterations. I bumped the max iterations up\n  to 12 to compensate and this still tended to be faster for no perceivable\n  loss in accuracy.\n  Usage\n    const easeOut = cubicBezier(.17,.67,.83,.67);\n    const x = easeOut(0.5); // returns 0.627...\n*/\n// Returns x(t) given t, x1, and x2, or y(t) given t, y1, and y2.\nconst calcBezier = (t, a1, a2) => (((1.0 - 3.0 * a2 + 3.0 * a1) * t + (3.0 * a2 - 6.0 * a1)) * t + 3.0 * a1) *\n    t;\nconst subdivisionPrecision = 0.0000001;\nconst subdivisionMaxIterations = 12;\nfunction binarySubdivide(x, lowerBound, upperBound, mX1, mX2) {\n    let currentX;\n    let currentT;\n    let i = 0;\n    do {\n        currentT = lowerBound + (upperBound - lowerBound) / 2.0;\n        currentX = calcBezier(currentT, mX1, mX2) - x;\n        if (currentX > 0.0) {\n            upperBound = currentT;\n        }\n        else {\n            lowerBound = currentT;\n        }\n    } while (Math.abs(currentX) > subdivisionPrecision &&\n        ++i < subdivisionMaxIterations);\n    return currentT;\n}\nfunction cubicBezier(mX1, mY1, mX2, mY2) {\n    // If this is a linear gradient, return linear easing\n    if (mX1 === mY1 && mX2 === mY2)\n        return noop;\n    const getTForX = (aX) => binarySubdivide(aX, 0, 1, mX1, mX2);\n    // If animation is at start/end, return t without easing\n    return (t) => t === 0 || t === 1 ? t : calcBezier(getTForX(t), mY1, mY2);\n}\n\nexport { cubicBezier };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,mBAAmB;;AAExC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,GAAGA,CAACC,CAAC,EAAEC,EAAE,EAAEC,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAGA,EAAE,GAAG,GAAG,GAAGD,EAAE,IAAID,CAAC,IAAI,GAAG,GAAGE,EAAE,GAAG,GAAG,GAAGD,EAAE,CAAC,IAAID,CAAC,GAAG,GAAG,GAAGC,EAAE,IACvGD,CAAC;AACL,MAAMG,oBAAoB,GAAG,SAAS;AACtC,MAAMC,wBAAwB,GAAG,EAAE;AACnC,SAASC,eAAeA,CAACC,CAAC,EAAEC,UAAU,EAAEC,UAAU,EAAEC,GAAG,EAAEC,GAAG,EAAE;EAC1D,IAAIC,QAAQ;EACZ,IAAIC,QAAQ;EACZ,IAAIC,CAAC,GAAG,CAAC;EACT,GAAG;IACCD,QAAQ,GAAGL,UAAU,GAAG,CAACC,UAAU,GAAGD,UAAU,IAAI,GAAG;IACvDI,QAAQ,GAAGZ,UAAU,CAACa,QAAQ,EAAEH,GAAG,EAAEC,GAAG,CAAC,GAAGJ,CAAC;IAC7C,IAAIK,QAAQ,GAAG,GAAG,EAAE;MAChBH,UAAU,GAAGI,QAAQ;IACzB,CAAC,MACI;MACDL,UAAU,GAAGK,QAAQ;IACzB;EACJ,CAAC,QAAQE,IAAI,CAACC,GAAG,CAACJ,QAAQ,CAAC,GAAGR,oBAAoB,IAC9C,EAAEU,CAAC,GAAGT,wBAAwB;EAClC,OAAOQ,QAAQ;AACnB;AACA,SAASI,WAAWA,CAACP,GAAG,EAAEQ,GAAG,EAAEP,GAAG,EAAEQ,GAAG,EAAE;EACrC;EACA,IAAIT,GAAG,KAAKQ,GAAG,IAAIP,GAAG,KAAKQ,GAAG,EAC1B,OAAOpB,IAAI;EACf,MAAMqB,QAAQ,GAAIC,EAAE,IAAKf,eAAe,CAACe,EAAE,EAAE,CAAC,EAAE,CAAC,EAAEX,GAAG,EAAEC,GAAG,CAAC;EAC5D;EACA,OAAQV,CAAC,IAAKA,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,GAAGA,CAAC,GAAGD,UAAU,CAACoB,QAAQ,CAACnB,CAAC,CAAC,EAAEiB,GAAG,EAAEC,GAAG,CAAC;AAC5E;AAEA,SAASF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}