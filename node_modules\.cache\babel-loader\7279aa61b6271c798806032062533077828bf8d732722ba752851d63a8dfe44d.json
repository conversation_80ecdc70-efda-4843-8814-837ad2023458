{"ast": null, "code": "import { easingDefinitionToFunction } from '../../easing/utils/map.mjs';\nfunction getOriginIndex(from, total) {\n  if (from === \"first\") {\n    return 0;\n  } else {\n    const lastIndex = total - 1;\n    return from === \"last\" ? lastIndex : lastIndex / 2;\n  }\n}\nfunction stagger() {\n  let duration = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0.1;\n  let {\n    startDelay = 0,\n    from = 0,\n    ease\n  } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  return (i, total) => {\n    const fromIndex = typeof from === \"number\" ? from : getOriginIndex(from, total);\n    const distance = Math.abs(fromIndex - i);\n    let delay = duration * distance;\n    if (ease) {\n      const maxDelay = total * duration;\n      const easingFunction = easingDefinitionToFunction(ease);\n      delay = easingFunction(delay / maxDelay) * maxDelay;\n    }\n    return startDelay + delay;\n  };\n}\nexport { getOriginIndex, stagger };", "map": {"version": 3, "names": ["easingDefinitionToFunction", "getOriginIndex", "from", "total", "lastIndex", "stagger", "duration", "arguments", "length", "undefined", "startDelay", "ease", "i", "fromIndex", "distance", "Math", "abs", "delay", "max<PERSON><PERSON><PERSON>", "easingFunction"], "sources": ["C:/Users/<USER>/Desktop/مشروع/node_modules/framer-motion/dist/es/animation/utils/stagger.mjs"], "sourcesContent": ["import { easingDefinitionToFunction } from '../../easing/utils/map.mjs';\n\nfunction getOriginIndex(from, total) {\n    if (from === \"first\") {\n        return 0;\n    }\n    else {\n        const lastIndex = total - 1;\n        return from === \"last\" ? lastIndex : lastIndex / 2;\n    }\n}\nfunction stagger(duration = 0.1, { startDelay = 0, from = 0, ease } = {}) {\n    return (i, total) => {\n        const fromIndex = typeof from === \"number\" ? from : getOriginIndex(from, total);\n        const distance = Math.abs(fromIndex - i);\n        let delay = duration * distance;\n        if (ease) {\n            const maxDelay = total * duration;\n            const easingFunction = easingDefinitionToFunction(ease);\n            delay = easingFunction(delay / maxDelay) * maxDelay;\n        }\n        return startDelay + delay;\n    };\n}\n\nexport { getOriginIndex, stagger };\n"], "mappings": "AAAA,SAASA,0BAA0B,QAAQ,4BAA4B;AAEvE,SAASC,cAAcA,CAACC,IAAI,EAAEC,KAAK,EAAE;EACjC,IAAID,IAAI,KAAK,OAAO,EAAE;IAClB,OAAO,CAAC;EACZ,CAAC,MACI;IACD,MAAME,SAAS,GAAGD,KAAK,GAAG,CAAC;IAC3B,OAAOD,IAAI,KAAK,MAAM,GAAGE,SAAS,GAAGA,SAAS,GAAG,CAAC;EACtD;AACJ;AACA,SAASC,OAAOA,CAAA,EAA0D;EAAA,IAAzDC,QAAQ,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,GAAG;EAAA,IAAE;IAAEG,UAAU,GAAG,CAAC;IAAER,IAAI,GAAG,CAAC;IAAES;EAAK,CAAC,GAAAJ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACpE,OAAO,CAACK,CAAC,EAAET,KAAK,KAAK;IACjB,MAAMU,SAAS,GAAG,OAAOX,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAGD,cAAc,CAACC,IAAI,EAAEC,KAAK,CAAC;IAC/E,MAAMW,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACH,SAAS,GAAGD,CAAC,CAAC;IACxC,IAAIK,KAAK,GAAGX,QAAQ,GAAGQ,QAAQ;IAC/B,IAAIH,IAAI,EAAE;MACN,MAAMO,QAAQ,GAAGf,KAAK,GAAGG,QAAQ;MACjC,MAAMa,cAAc,GAAGnB,0BAA0B,CAACW,IAAI,CAAC;MACvDM,KAAK,GAAGE,cAAc,CAACF,KAAK,GAAGC,QAAQ,CAAC,GAAGA,QAAQ;IACvD;IACA,OAAOR,UAAU,GAAGO,KAAK;EAC7B,CAAC;AACL;AAEA,SAAShB,cAAc,EAAEI,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}