{"ast": null, "code": "import { initializeApp } from 'firebase/app';\nimport { getAuth } from 'firebase/auth';\nimport { getFirestore } from 'firebase/firestore';\nimport { getStorage } from 'firebase/storage';\nimport { getFunctions } from 'firebase/functions';\nconst firebaseConfig = {\n  // يجب إضافة إعدادات Firebase الخاصة بك هنا\n  apiKey: \"your-api-key\",\n  authDomain: \"your-project.firebaseapp.com\",\n  projectId: \"your-project-id\",\n  storageBucket: \"your-project.appspot.com\",\n  messagingSenderId: \"123456789\",\n  appId: \"your-app-id\"\n};\n\n// Initialize Firebase\nconst app = initializeApp(firebaseConfig);\n\n// Initialize Firebase services\nexport const auth = getAuth(app);\nexport const db = getFirestore(app);\nexport const storage = getStorage(app);\nexport const functions = getFunctions(app);\nexport default app;", "map": {"version": 3, "names": ["initializeApp", "getAuth", "getFirestore", "getStorage", "getFunctions", "firebaseConfig", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "projectId", "storageBucket", "messagingSenderId", "appId", "app", "auth", "db", "storage", "functions"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/config/firebase.ts"], "sourcesContent": ["import { initializeApp } from 'firebase/app';\nimport { getAuth } from 'firebase/auth';\nimport { getFirestore } from 'firebase/firestore';\nimport { getStorage } from 'firebase/storage';\nimport { getFunctions } from 'firebase/functions';\n\nconst firebaseConfig = {\n  // يجب إضافة إعدادات Firebase الخاصة بك هنا\n  apiKey: \"your-api-key\",\n  authDomain: \"your-project.firebaseapp.com\",\n  projectId: \"your-project-id\",\n  storageBucket: \"your-project.appspot.com\",\n  messagingSenderId: \"123456789\",\n  appId: \"your-app-id\"\n};\n\n// Initialize Firebase\nconst app = initializeApp(firebaseConfig);\n\n// Initialize Firebase services\nexport const auth = getAuth(app);\nexport const db = getFirestore(app);\nexport const storage = getStorage(app);\nexport const functions = getFunctions(app);\n\nexport default app;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,cAAc;AAC5C,SAASC,OAAO,QAAQ,eAAe;AACvC,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,YAAY,QAAQ,oBAAoB;AAEjD,MAAMC,cAAc,GAAG;EACrB;EACAC,MAAM,EAAE,cAAc;EACtBC,UAAU,EAAE,8BAA8B;EAC1CC,SAAS,EAAE,iBAAiB;EAC5BC,aAAa,EAAE,0BAA0B;EACzCC,iBAAiB,EAAE,WAAW;EAC9BC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,MAAMC,GAAG,GAAGZ,aAAa,CAACK,cAAc,CAAC;;AAEzC;AACA,OAAO,MAAMQ,IAAI,GAAGZ,OAAO,CAACW,GAAG,CAAC;AAChC,OAAO,MAAME,EAAE,GAAGZ,YAAY,CAACU,GAAG,CAAC;AACnC,OAAO,MAAMG,OAAO,GAAGZ,UAAU,CAACS,GAAG,CAAC;AACtC,OAAO,MAAMI,SAAS,GAAGZ,YAAY,CAACQ,GAAG,CAAC;AAE1C,eAAeA,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}