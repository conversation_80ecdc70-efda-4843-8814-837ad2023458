{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0634\\u0631\\u0648\\u0639\\\\src\\\\components\\\\Admin\\\\AdminHeader.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Bars3Icon, BellIcon, UserCircleIcon, ArrowRightOnRectangleIcon, CogIcon } from '@heroicons/react/24/outline';\n\n// Types\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminHeader = ({\n  user,\n  onMenuClick,\n  onLogout\n}) => {\n  _s();\n  const [showProfileMenu, setShowProfileMenu] = useState(false);\n  const [showNotifications, setShowNotifications] = useState(false);\n\n  // Mock notifications\n  const notifications = [{\n    id: 1,\n    title: 'طالب جديد',\n    message: 'انضم طالب جديد إلى كورس البرمجة',\n    time: 'منذ 5 دقائق',\n    unread: true\n  }, {\n    id: 2,\n    title: 'اختبار مكتمل',\n    message: 'أكمل أحمد محمد اختبار JavaScript',\n    time: 'منذ 15 دقيقة',\n    unread: true\n  }, {\n    id: 3,\n    title: 'شهادة جديدة',\n    message: 'تم إصدار شهادة جديدة لسارة أحمد',\n    time: 'منذ ساعة',\n    unread: false\n  }];\n  const unreadCount = notifications.filter(n => n.unread).length;\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"bg-white shadow-sm border-b border-gray-200\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between px-6 py-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onMenuClick,\n          className: \"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100\",\n          children: /*#__PURE__*/_jsxDEV(Bars3Icon, {\n            className: \"w-6 h-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden lg:block\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: [\"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B\\u060C \", user.name || 'المدير']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0645\\u0646\\u0635\\u0629 ALaa Abd Hamied \\u0644\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4 space-x-reverse\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowNotifications(!showNotifications),\n            className: \"relative p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors duration-200\",\n            children: [/*#__PURE__*/_jsxDEV(BellIcon, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this), unreadCount > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\",\n              children: unreadCount\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n            children: showNotifications && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 10\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              exit: {\n                opacity: 0,\n                y: 10\n              },\n              className: \"absolute left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 border-b border-gray-200\",\n                children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-gray-900\",\n                  children: \"\\u0627\\u0644\\u0625\\u0634\\u0639\\u0627\\u0631\\u0627\\u062A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 98,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-h-96 overflow-y-auto\",\n                children: notifications.map(notification => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer ${notification.unread ? 'bg-blue-50' : ''}`,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: notification.title\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 110,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-gray-600 mt-1\",\n                        children: notification.message\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 113,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-gray-400 mt-2\",\n                        children: notification.time\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 116,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 109,\n                      columnNumber: 27\n                    }, this), notification.unread && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-2 h-2 bg-blue-500 rounded-full mt-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 121,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 108,\n                    columnNumber: 25\n                  }, this)\n                }, notification.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 102,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 border-t border-gray-200\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"text-sm text-primary-600 hover:text-primary-700 font-medium\",\n                  children: \"\\u0639\\u0631\\u0636 \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0625\\u0634\\u0639\\u0627\\u0631\\u0627\\u062A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 128,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowProfileMenu(!showProfileMenu),\n            className: \"flex items-center space-x-2 space-x-reverse p-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors duration-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(UserCircleIcon, {\n                className: \"w-5 h-5 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden md:block text-right\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium\",\n                children: user.name || 'المدير'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500\",\n                children: user.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n            children: showProfileMenu && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 10\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              exit: {\n                opacity: 0,\n                y: 10\n              },\n              className: \"absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"py-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                  children: [/*#__PURE__*/_jsxDEV(UserCircleIcon, {\n                    className: \"w-4 h-4 ml-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 162,\n                    columnNumber: 23\n                  }, this), \"\\u0627\\u0644\\u0645\\u0644\\u0641 \\u0627\\u0644\\u0634\\u062E\\u0635\\u064A\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                  children: [/*#__PURE__*/_jsxDEV(CogIcon, {\n                    className: \"w-4 h-4 ml-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 166,\n                    columnNumber: 23\n                  }, this), \"\\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n                  className: \"my-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: onLogout,\n                  className: \"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50\",\n                  children: [/*#__PURE__*/_jsxDEV(ArrowRightOnRectangleIcon, {\n                    className: \"w-4 h-4 ml-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 23\n                  }, this), \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062E\\u0631\\u0648\\u062C\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"lg:hidden px-6 pb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-xl font-bold text-gray-900\",\n        children: [\"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B\\u060C \", user.name || 'المدير']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-500\",\n        children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0645\\u0646\\u0635\\u0629 ALaa Abd Hamied\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this), (showProfileMenu || showNotifications) && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 z-40\",\n      onClick: () => {\n        setShowProfileMenu(false);\n        setShowNotifications(false);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminHeader, \"rfKiX5INgVg5PzqP3u43RjrzxuM=\");\n_c = AdminHeader;\nexport default AdminHeader;\nvar _c;\n$RefreshReg$(_c, \"AdminHeader\");", "map": {"version": 3, "names": ["React", "useState", "motion", "AnimatePresence", "Bars3Icon", "BellIcon", "UserCircleIcon", "ArrowRightOnRectangleIcon", "CogIcon", "jsxDEV", "_jsxDEV", "Ad<PERSON><PERSON><PERSON><PERSON>", "user", "onMenuClick", "onLogout", "_s", "showProfileMenu", "setShowProfileMenu", "showNotifications", "setShowNotifications", "notifications", "id", "title", "message", "time", "unread", "unreadCount", "filter", "n", "length", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "div", "initial", "opacity", "y", "animate", "exit", "map", "notification", "email", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Admin/AdminHeader.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Bars3Icon,\n  BellIcon,\n  UserCircleIcon,\n  ArrowRightOnRectangleIcon,\n  CogIcon\n} from '@heroicons/react/24/outline';\n\n// Types\nimport { Admin } from '../../types';\n\ninterface AdminHeaderProps {\n  user: Admin;\n  onMenuClick: () => void;\n  onLogout: () => void;\n}\n\nconst AdminHeader: React.FC<AdminHeaderProps> = ({ user, onMenuClick, onLogout }) => {\n  const [showProfileMenu, setShowProfileMenu] = useState(false);\n  const [showNotifications, setShowNotifications] = useState(false);\n\n  // Mock notifications\n  const notifications = [\n    {\n      id: 1,\n      title: 'طالب جديد',\n      message: 'انضم طالب جديد إلى كورس البرمجة',\n      time: 'منذ 5 دقائق',\n      unread: true\n    },\n    {\n      id: 2,\n      title: 'اختبار مكتمل',\n      message: 'أكمل أحمد محمد اختبار JavaScript',\n      time: 'منذ 15 دقيقة',\n      unread: true\n    },\n    {\n      id: 3,\n      title: 'شهادة جديدة',\n      message: 'تم إصدار شهادة جديدة لسارة أحمد',\n      time: 'منذ ساعة',\n      unread: false\n    }\n  ];\n\n  const unreadCount = notifications.filter(n => n.unread).length;\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200\">\n      <div className=\"flex items-center justify-between px-6 py-4\">\n        {/* Left Side - Menu Button */}\n        <div className=\"flex items-center\">\n          <button\n            onClick={onMenuClick}\n            className=\"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100\"\n          >\n            <Bars3Icon className=\"w-6 h-6\" />\n          </button>\n          \n          <div className=\"hidden lg:block\">\n            <h1 className=\"text-2xl font-bold text-gray-900\">\n              مرحباً، {user.name || 'المدير'}\n            </h1>\n            <p className=\"text-sm text-gray-500\">\n              إدارة منصة ALaa Abd Hamied للكورسات\n            </p>\n          </div>\n        </div>\n\n        {/* Right Side - Notifications & Profile */}\n        <div className=\"flex items-center space-x-4 space-x-reverse\">\n          {/* Notifications */}\n          <div className=\"relative\">\n            <button\n              onClick={() => setShowNotifications(!showNotifications)}\n              className=\"relative p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors duration-200\"\n            >\n              <BellIcon className=\"w-6 h-6\" />\n              {unreadCount > 0 && (\n                <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\">\n                  {unreadCount}\n                </span>\n              )}\n            </button>\n\n            <AnimatePresence>\n              {showNotifications && (\n                <motion.div\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  exit={{ opacity: 0, y: 10 }}\n                  className=\"absolute left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50\"\n                >\n                  <div className=\"p-4 border-b border-gray-200\">\n                    <h3 className=\"text-lg font-semibold text-gray-900\">الإشعارات</h3>\n                  </div>\n                  <div className=\"max-h-96 overflow-y-auto\">\n                    {notifications.map((notification) => (\n                      <div\n                        key={notification.id}\n                        className={`p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer ${\n                          notification.unread ? 'bg-blue-50' : ''\n                        }`}\n                      >\n                        <div className=\"flex items-start\">\n                          <div className=\"flex-1\">\n                            <h4 className=\"text-sm font-medium text-gray-900\">\n                              {notification.title}\n                            </h4>\n                            <p className=\"text-sm text-gray-600 mt-1\">\n                              {notification.message}\n                            </p>\n                            <p className=\"text-xs text-gray-400 mt-2\">\n                              {notification.time}\n                            </p>\n                          </div>\n                          {notification.unread && (\n                            <div className=\"w-2 h-2 bg-blue-500 rounded-full mt-2\"></div>\n                          )}\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                  <div className=\"p-4 border-t border-gray-200\">\n                    <button className=\"text-sm text-primary-600 hover:text-primary-700 font-medium\">\n                      عرض جميع الإشعارات\n                    </button>\n                  </div>\n                </motion.div>\n              )}\n            </AnimatePresence>\n          </div>\n\n          {/* Profile Menu */}\n          <div className=\"relative\">\n            <button\n              onClick={() => setShowProfileMenu(!showProfileMenu)}\n              className=\"flex items-center space-x-2 space-x-reverse p-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors duration-200\"\n            >\n              <div className=\"w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center\">\n                <UserCircleIcon className=\"w-5 h-5 text-white\" />\n              </div>\n              <div className=\"hidden md:block text-right\">\n                <p className=\"text-sm font-medium\">{user.name || 'المدير'}</p>\n                <p className=\"text-xs text-gray-500\">{user.email}</p>\n              </div>\n            </button>\n\n            <AnimatePresence>\n              {showProfileMenu && (\n                <motion.div\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  exit={{ opacity: 0, y: 10 }}\n                  className=\"absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50\"\n                >\n                  <div className=\"py-2\">\n                    <button className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">\n                      <UserCircleIcon className=\"w-4 h-4 ml-2\" />\n                      الملف الشخصي\n                    </button>\n                    <button className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">\n                      <CogIcon className=\"w-4 h-4 ml-2\" />\n                      الإعدادات\n                    </button>\n                    <hr className=\"my-2\" />\n                    <button\n                      onClick={onLogout}\n                      className=\"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50\"\n                    >\n                      <ArrowRightOnRectangleIcon className=\"w-4 h-4 ml-2\" />\n                      تسجيل الخروج\n                    </button>\n                  </div>\n                </motion.div>\n              )}\n            </AnimatePresence>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Title */}\n      <div className=\"lg:hidden px-6 pb-4\">\n        <h1 className=\"text-xl font-bold text-gray-900\">\n          مرحباً، {user.name || 'المدير'}\n        </h1>\n        <p className=\"text-sm text-gray-500\">\n          إدارة منصة ALaa Abd Hamied\n        </p>\n      </div>\n\n      {/* Click outside to close menus */}\n      {(showProfileMenu || showNotifications) && (\n        <div\n          className=\"fixed inset-0 z-40\"\n          onClick={() => {\n            setShowProfileMenu(false);\n            setShowNotifications(false);\n          }}\n        />\n      )}\n    </header>\n  );\n};\n\nexport default AdminHeader;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,SAAS,EACTC,QAAQ,EACRC,cAAc,EACdC,yBAAyB,EACzBC,OAAO,QACF,6BAA6B;;AAEpC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AASA,MAAMC,WAAuC,GAAGA,CAAC;EAAEC,IAAI;EAAEC,WAAW;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACnF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACiB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAMmB,aAAa,GAAG,CACpB;IACEC,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,WAAW;IAClBC,OAAO,EAAE,iCAAiC;IAC1CC,IAAI,EAAE,aAAa;IACnBC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,cAAc;IACrBC,OAAO,EAAE,kCAAkC;IAC3CC,IAAI,EAAE,cAAc;IACpBC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAE,iCAAiC;IAC1CC,IAAI,EAAE,UAAU;IAChBC,MAAM,EAAE;EACV,CAAC,CACF;EAED,MAAMC,WAAW,GAAGN,aAAa,CAACO,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACH,MAAM,CAAC,CAACI,MAAM;EAE9D,oBACEnB,OAAA;IAAQoB,SAAS,EAAC,6CAA6C;IAAAC,QAAA,gBAC7DrB,OAAA;MAAKoB,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1DrB,OAAA;QAAKoB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCrB,OAAA;UACEsB,OAAO,EAAEnB,WAAY;UACrBiB,SAAS,EAAC,8EAA8E;UAAAC,QAAA,eAExFrB,OAAA,CAACN,SAAS;YAAC0B,SAAS,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eAET1B,OAAA;UAAKoB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BrB,OAAA;YAAIoB,SAAS,EAAC,kCAAkC;YAAAC,QAAA,GAAC,6CACvC,EAACnB,IAAI,CAACyB,IAAI,IAAI,QAAQ;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACL1B,OAAA;YAAGoB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAErC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1B,OAAA;QAAKoB,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAE1DrB,OAAA;UAAKoB,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBrB,OAAA;YACEsB,OAAO,EAAEA,CAAA,KAAMb,oBAAoB,CAAC,CAACD,iBAAiB,CAAE;YACxDY,SAAS,EAAC,8GAA8G;YAAAC,QAAA,gBAExHrB,OAAA,CAACL,QAAQ;cAACyB,SAAS,EAAC;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAC/BV,WAAW,GAAG,CAAC,iBACdhB,OAAA;cAAMoB,SAAS,EAAC,8GAA8G;cAAAC,QAAA,EAC3HL;YAAW;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eAET1B,OAAA,CAACP,eAAe;YAAA4B,QAAA,EACbb,iBAAiB,iBAChBR,OAAA,CAACR,MAAM,CAACoC,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,IAAI,EAAE;gBAAEH,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC5BX,SAAS,EAAC,qFAAqF;cAAAC,QAAA,gBAE/FrB,OAAA;gBAAKoB,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,eAC3CrB,OAAA;kBAAIoB,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,eACN1B,OAAA;gBAAKoB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EACtCX,aAAa,CAACwB,GAAG,CAAEC,YAAY,iBAC9BnC,OAAA;kBAEEoB,SAAS,EAAE,gEACTe,YAAY,CAACpB,MAAM,GAAG,YAAY,GAAG,EAAE,EACtC;kBAAAM,QAAA,eAEHrB,OAAA;oBAAKoB,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BrB,OAAA;sBAAKoB,SAAS,EAAC,QAAQ;sBAAAC,QAAA,gBACrBrB,OAAA;wBAAIoB,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAC9Cc,YAAY,CAACvB;sBAAK;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjB,CAAC,eACL1B,OAAA;wBAAGoB,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,EACtCc,YAAY,CAACtB;sBAAO;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC,eACJ1B,OAAA;wBAAGoB,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,EACtCc,YAAY,CAACrB;sBAAI;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC,EACLS,YAAY,CAACpB,MAAM,iBAClBf,OAAA;sBAAKoB,SAAS,EAAC;oBAAuC;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAC7D;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC,GApBDS,YAAY,CAACxB,EAAE;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAqBjB,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN1B,OAAA;gBAAKoB,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,eAC3CrB,OAAA;kBAAQoB,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,EAAC;gBAEhF;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UACb;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eAGN1B,OAAA;UAAKoB,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBrB,OAAA;YACEsB,OAAO,EAAEA,CAAA,KAAMf,kBAAkB,CAAC,CAACD,eAAe,CAAE;YACpDc,SAAS,EAAC,2HAA2H;YAAAC,QAAA,gBAErIrB,OAAA;cAAKoB,SAAS,EAAC,sEAAsE;cAAAC,QAAA,eACnFrB,OAAA,CAACJ,cAAc;gBAACwB,SAAS,EAAC;cAAoB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACN1B,OAAA;cAAKoB,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzCrB,OAAA;gBAAGoB,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEnB,IAAI,CAACyB,IAAI,IAAI;cAAQ;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9D1B,OAAA;gBAAGoB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAEnB,IAAI,CAACkC;cAAK;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAET1B,OAAA,CAACP,eAAe;YAAA4B,QAAA,EACbf,eAAe,iBACdN,OAAA,CAACR,MAAM,CAACoC,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,IAAI,EAAE;gBAAEH,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC5BX,SAAS,EAAC,qFAAqF;cAAAC,QAAA,eAE/FrB,OAAA;gBAAKoB,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBrB,OAAA;kBAAQoB,SAAS,EAAC,4EAA4E;kBAAAC,QAAA,gBAC5FrB,OAAA,CAACJ,cAAc;oBAACwB,SAAS,EAAC;kBAAc;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,uEAE7C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT1B,OAAA;kBAAQoB,SAAS,EAAC,4EAA4E;kBAAAC,QAAA,gBAC5FrB,OAAA,CAACF,OAAO;oBAACsB,SAAS,EAAC;kBAAc;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,0DAEtC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT1B,OAAA;kBAAIoB,SAAS,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvB1B,OAAA;kBACEsB,OAAO,EAAElB,QAAS;kBAClBgB,SAAS,EAAC,yEAAyE;kBAAAC,QAAA,gBAEnFrB,OAAA,CAACH,yBAAyB;oBAACuB,SAAS,EAAC;kBAAc;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,uEAExD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UACb;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1B,OAAA;MAAKoB,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClCrB,OAAA;QAAIoB,SAAS,EAAC,iCAAiC;QAAAC,QAAA,GAAC,6CACtC,EAACnB,IAAI,CAACyB,IAAI,IAAI,QAAQ;MAAA;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eACL1B,OAAA;QAAGoB,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EAAC;MAErC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,EAGL,CAACpB,eAAe,IAAIE,iBAAiB,kBACpCR,OAAA;MACEoB,SAAS,EAAC,oBAAoB;MAC9BE,OAAO,EAAEA,CAAA,KAAM;QACbf,kBAAkB,CAAC,KAAK,CAAC;QACzBE,oBAAoB,CAAC,KAAK,CAAC;MAC7B;IAAE;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAACrB,EAAA,CA3LIJ,WAAuC;AAAAoC,EAAA,GAAvCpC,WAAuC;AA6L7C,eAAeA,WAAW;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}