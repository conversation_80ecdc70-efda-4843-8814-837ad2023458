import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  DocumentIcon,
  CheckBadgeIcon,
  EyeIcon,
  ArrowDownTrayIcon,
  ShareIcon
} from '@heroicons/react/24/outline';

// Types
import { Student } from '../../types';

interface MyCertificatesProps {
  user?: Student;
  onBack?: () => void;
}

const MyCertificates: React.FC<MyCertificatesProps> = ({ user, onBack }) => {
  // Mock certificates data
  const certificates = [
    {
      id: '1',
      courseTitle: 'أساسيات البرمجة',
      issuedDate: new Date('2024-03-01'),
      verificationCode: 'CERT-2024-001',
      score: 85,
      instructorName: '<PERSON>aa <PERSON>',
      certificateUrl: '/certificates/cert1.pdf'
    },
    {
      id: '2',
      courseTitle: 'تطوير المواقع المتقدم',
      issuedDate: new Date('2024-02-15'),
      verificationCode: 'CERT-2024-002',
      score: 92,
      instructorName: '<PERSON><PERSON> <PERSON>',
      certificateUrl: '/certificates/cert2.pdf'
    }
  ];

  const handleViewCertificate = (certificateId: string) => {
    console.log('View certificate:', certificateId);
    // TODO: Implement certificate viewer
  };

  const handleDownloadCertificate = (certificateId: string) => {
    console.log('Download certificate:', certificateId);
    // TODO: Implement certificate download
  };

  const handleShareCertificate = (certificateId: string) => {
    console.log('Share certificate:', certificateId);
    // TODO: Implement certificate sharing
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4 space-x-reverse">
        {onBack && (
          <button
            onClick={onBack}
            className="p-2 text-gray-600 hover:text-gray-900 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
        )}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">شهاداتي</h1>
          <p className="text-gray-600">جميع الشهادات التي حصلت عليها</p>
        </div>
      </div>

      {/* Certificates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {certificates.map((certificate, index) => (
          <motion.div
            key={certificate.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow"
          >
            {/* Certificate Preview */}
            <div className="bg-gradient-to-br from-blue-600 to-purple-700 p-6 text-white">
              <div className="text-center">
                <CheckBadgeIcon className="w-12 h-12 mx-auto mb-3 text-yellow-300" />
                <h3 className="text-lg font-bold mb-2">شهادة إتمام</h3>
                <p className="text-blue-100 text-sm">{certificate.courseTitle}</p>
              </div>
            </div>

            {/* Certificate Details */}
            <div className="p-6">
              <div className="space-y-3 mb-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">تاريخ الإصدار:</span>
                  <span className="font-medium text-gray-900">
                    {certificate.issuedDate.toLocaleDateString('ar-SA')}
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">النتيجة:</span>
                  <span className="font-medium text-green-600">{certificate.score}%</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">المدرب:</span>
                  <span className="font-medium text-gray-900">{certificate.instructorName}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">رمز التحقق:</span>
                  <span className="font-mono text-xs bg-gray-100 px-2 py-1 rounded">
                    {certificate.verificationCode}
                  </span>
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <button
                    onClick={() => handleViewCertificate(certificate.id)}
                    className="p-2 text-gray-600 hover:text-blue-600 transition-colors"
                    title="عرض الشهادة"
                  >
                    <EyeIcon className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleDownloadCertificate(certificate.id)}
                    className="p-2 text-gray-600 hover:text-green-600 transition-colors"
                    title="تحميل الشهادة"
                  >
                    <ArrowDownTrayIcon className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleShareCertificate(certificate.id)}
                    className="p-2 text-gray-600 hover:text-purple-600 transition-colors"
                    title="مشاركة الشهادة"
                  >
                    <ShareIcon className="w-4 h-4" />
                  </button>
                </div>
                <button
                  onClick={() => handleViewCertificate(certificate.id)}
                  className="text-sm bg-blue-600 text-white px-3 py-1 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  عرض
                </button>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {certificates.length === 0 && (
        <div className="text-center py-12">
          <DocumentIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد شهادات</h3>
          <p className="text-gray-600">لم تحصل على أي شهادات بعد. أكمل الكورسات للحصول على الشهادات</p>
        </div>
      )}

      {/* Achievement Stats */}
      {certificates.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-lg shadow-sm p-6"
        >
          <h2 className="text-lg font-semibold text-gray-900 mb-4">إحصائيات الإنجازات</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600 mb-1">{certificates.length}</div>
              <div className="text-sm text-gray-600">إجمالي الشهادات</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600 mb-1">
                {Math.round(certificates.reduce((sum, cert) => sum + cert.score, 0) / certificates.length)}%
              </div>
              <div className="text-sm text-gray-600">متوسط النتائج</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600 mb-1">
                {certificates.filter(cert => cert.score >= 90).length}
              </div>
              <div className="text-sm text-gray-600">شهادات ممتازة</div>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default MyCertificates;
