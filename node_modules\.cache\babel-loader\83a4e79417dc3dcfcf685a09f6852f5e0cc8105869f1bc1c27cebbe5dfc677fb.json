{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0634\\u0631\\u0648\\u0639\\\\src\\\\components\\\\Admin\\\\RecentActivity.tsx\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { UserPlusIcon, AcademicCapIcon, ClipboardDocumentCheckIcon, DocumentTextIcon, EyeIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RecentActivity = () => {\n  const activities = [{\n    id: '1',\n    type: 'student_joined',\n    title: 'طالب جديد',\n    description: 'انضم أحمد محمد إلى المنصة',\n    time: 'منذ 5 دقائق',\n    user: 'أحمد محمد'\n  }, {\n    id: '2',\n    type: 'course_completed',\n    title: 'كورس مكتمل',\n    description: 'أكملت سارة أحمد كورس \"أساسيات البرمجة\"',\n    time: 'منذ 15 دقيقة',\n    user: 'سارة أحمد'\n  }, {\n    id: '3',\n    type: 'quiz_taken',\n    title: 'اختبار جديد',\n    description: 'أدى محمد علي اختبار JavaScript',\n    time: 'منذ 30 دقيقة',\n    user: 'محمد علي'\n  }, {\n    id: '4',\n    type: 'certificate_issued',\n    title: 'شهادة جديدة',\n    description: 'تم إصدار شهادة لفاطمة حسن',\n    time: 'منذ ساعة',\n    user: 'فاطمة حسن'\n  }, {\n    id: '5',\n    type: 'video_watched',\n    title: 'مشاهدة فيديو',\n    description: 'شاهد خالد أحمد فيديو \"المتغيرات في JavaScript\"',\n    time: 'منذ ساعتين',\n    user: 'خالد أحمد'\n  }];\n  const getActivityIcon = type => {\n    switch (type) {\n      case 'student_joined':\n        return UserPlusIcon;\n      case 'course_completed':\n        return AcademicCapIcon;\n      case 'quiz_taken':\n        return ClipboardDocumentCheckIcon;\n      case 'certificate_issued':\n        return DocumentTextIcon;\n      case 'video_watched':\n        return EyeIcon;\n      default:\n        return UserPlusIcon;\n    }\n  };\n  const getActivityColor = type => {\n    switch (type) {\n      case 'student_joined':\n        return 'bg-blue-100 text-blue-600';\n      case 'course_completed':\n        return 'bg-green-100 text-green-600';\n      case 'quiz_taken':\n        return 'bg-orange-100 text-orange-600';\n      case 'certificate_issued':\n        return 'bg-purple-100 text-purple-600';\n      case 'video_watched':\n        return 'bg-indigo-100 text-indigo-600';\n      default:\n        return 'bg-gray-100 text-gray-600';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-xl p-6 shadow-sm\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-bold text-gray-900\",\n        children: \"\\u0627\\u0644\\u0646\\u0634\\u0627\\u0637 \\u0627\\u0644\\u0623\\u062E\\u064A\\u0631\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"text-sm text-primary-600 hover:text-primary-700 font-medium\",\n        children: \"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u0643\\u0644\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: activities.map((activity, index) => {\n        const Icon = getActivityIcon(activity.type);\n        const colorClass = getActivityColor(activity.type);\n        return /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: 20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            delay: index * 0.1\n          },\n          className: \"flex items-start space-x-3 space-x-reverse p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `w-10 h-10 rounded-full flex items-center justify-center ${colorClass}`,\n            children: /*#__PURE__*/_jsxDEV(Icon, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: activity.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs text-gray-500\",\n                children: activity.time\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600 mt-1\",\n              children: activity.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 17\n            }, this), activity.user && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"inline-block mt-2 px-2 py-1 bg-gray-100 text-xs text-gray-700 rounded-full\",\n              children: activity.user\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 15\n          }, this)]\n        }, activity.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6 pt-4 border-t border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"text-sm text-primary-600 hover:text-primary-700 font-medium\",\n          children: \"\\u062A\\u062D\\u062F\\u064A\\u062B \\u0627\\u0644\\u0646\\u0634\\u0627\\u0637\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 5\n  }, this);\n};\n_c = RecentActivity;\nexport default RecentActivity;\nvar _c;\n$RefreshReg$(_c, \"RecentActivity\");", "map": {"version": 3, "names": ["React", "motion", "UserPlusIcon", "AcademicCapIcon", "ClipboardDocumentCheckIcon", "DocumentTextIcon", "EyeIcon", "jsxDEV", "_jsxDEV", "RecentActivity", "activities", "id", "type", "title", "description", "time", "user", "getActivityIcon", "getActivityColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "activity", "index", "Icon", "colorClass", "div", "initial", "opacity", "x", "animate", "transition", "delay", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Admin/RecentActivity.tsx"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  UserPlusIcon,\n  AcademicCapIcon,\n  ClipboardDocumentCheckIcon,\n  DocumentTextIcon,\n  EyeIcon\n} from '@heroicons/react/24/outline';\n\ninterface Activity {\n  id: string;\n  type: 'student_joined' | 'course_completed' | 'quiz_taken' | 'certificate_issued' | 'video_watched';\n  title: string;\n  description: string;\n  time: string;\n  user?: string;\n}\n\nconst RecentActivity: React.FC = () => {\n  const activities: Activity[] = [\n    {\n      id: '1',\n      type: 'student_joined',\n      title: 'طالب جديد',\n      description: 'انضم أحمد محمد إلى المنصة',\n      time: 'منذ 5 دقائق',\n      user: 'أحمد محمد'\n    },\n    {\n      id: '2',\n      type: 'course_completed',\n      title: 'كورس مكتمل',\n      description: 'أكملت سارة أحمد كورس \"أساسيات البرمجة\"',\n      time: 'منذ 15 دقيقة',\n      user: 'سارة أحمد'\n    },\n    {\n      id: '3',\n      type: 'quiz_taken',\n      title: 'اختبار جديد',\n      description: 'أدى محمد علي اختبار JavaScript',\n      time: 'منذ 30 دقيقة',\n      user: 'محمد علي'\n    },\n    {\n      id: '4',\n      type: 'certificate_issued',\n      title: 'شهادة جديدة',\n      description: 'تم إصدار شهادة لفاطمة حسن',\n      time: 'منذ ساعة',\n      user: 'فاطمة حسن'\n    },\n    {\n      id: '5',\n      type: 'video_watched',\n      title: 'مشاهدة فيديو',\n      description: 'شاهد خالد أحمد فيديو \"المتغيرات في JavaScript\"',\n      time: 'منذ ساعتين',\n      user: 'خالد أحمد'\n    }\n  ];\n\n  const getActivityIcon = (type: Activity['type']) => {\n    switch (type) {\n      case 'student_joined':\n        return UserPlusIcon;\n      case 'course_completed':\n        return AcademicCapIcon;\n      case 'quiz_taken':\n        return ClipboardDocumentCheckIcon;\n      case 'certificate_issued':\n        return DocumentTextIcon;\n      case 'video_watched':\n        return EyeIcon;\n      default:\n        return UserPlusIcon;\n    }\n  };\n\n  const getActivityColor = (type: Activity['type']) => {\n    switch (type) {\n      case 'student_joined':\n        return 'bg-blue-100 text-blue-600';\n      case 'course_completed':\n        return 'bg-green-100 text-green-600';\n      case 'quiz_taken':\n        return 'bg-orange-100 text-orange-600';\n      case 'certificate_issued':\n        return 'bg-purple-100 text-purple-600';\n      case 'video_watched':\n        return 'bg-indigo-100 text-indigo-600';\n      default:\n        return 'bg-gray-100 text-gray-600';\n    }\n  };\n\n  return (\n    <div className=\"bg-white rounded-xl p-6 shadow-sm\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-lg font-bold text-gray-900\">النشاط الأخير</h3>\n        <button className=\"text-sm text-primary-600 hover:text-primary-700 font-medium\">\n          عرض الكل\n        </button>\n      </div>\n      \n      <div className=\"space-y-4\">\n        {activities.map((activity, index) => {\n          const Icon = getActivityIcon(activity.type);\n          const colorClass = getActivityColor(activity.type);\n          \n          return (\n            <motion.div\n              key={activity.id}\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: index * 0.1 }}\n              className=\"flex items-start space-x-3 space-x-reverse p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200\"\n            >\n              <div className={`w-10 h-10 rounded-full flex items-center justify-center ${colorClass}`}>\n                <Icon className=\"w-5 h-5\" />\n              </div>\n              \n              <div className=\"flex-1 min-w-0\">\n                <div className=\"flex items-center justify-between\">\n                  <h4 className=\"text-sm font-medium text-gray-900\">\n                    {activity.title}\n                  </h4>\n                  <span className=\"text-xs text-gray-500\">\n                    {activity.time}\n                  </span>\n                </div>\n                <p className=\"text-sm text-gray-600 mt-1\">\n                  {activity.description}\n                </p>\n                {activity.user && (\n                  <span className=\"inline-block mt-2 px-2 py-1 bg-gray-100 text-xs text-gray-700 rounded-full\">\n                    {activity.user}\n                  </span>\n                )}\n              </div>\n            </motion.div>\n          );\n        })}\n      </div>\n      \n      <div className=\"mt-6 pt-4 border-t border-gray-200\">\n        <div className=\"flex items-center justify-center\">\n          <button className=\"text-sm text-primary-600 hover:text-primary-700 font-medium\">\n            تحديث النشاط\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RecentActivity;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,YAAY,EACZC,eAAe,EACfC,0BAA0B,EAC1BC,gBAAgB,EAChBC,OAAO,QACF,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAWrC,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EACrC,MAAMC,UAAsB,GAAG,CAC7B;IACEC,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,WAAW;IAClBC,WAAW,EAAE,2BAA2B;IACxCC,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE;EACR,CAAC,EACD;IACEL,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE,YAAY;IACnBC,WAAW,EAAE,wCAAwC;IACrDC,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE;EACR,CAAC,EACD;IACEL,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,aAAa;IACpBC,WAAW,EAAE,gCAAgC;IAC7CC,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE;EACR,CAAC,EACD;IACEL,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,oBAAoB;IAC1BC,KAAK,EAAE,aAAa;IACpBC,WAAW,EAAE,2BAA2B;IACxCC,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE;EACR,CAAC,EACD;IACEL,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE,gDAAgD;IAC7DC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE;EACR,CAAC,CACF;EAED,MAAMC,eAAe,GAAIL,IAAsB,IAAK;IAClD,QAAQA,IAAI;MACV,KAAK,gBAAgB;QACnB,OAAOV,YAAY;MACrB,KAAK,kBAAkB;QACrB,OAAOC,eAAe;MACxB,KAAK,YAAY;QACf,OAAOC,0BAA0B;MACnC,KAAK,oBAAoB;QACvB,OAAOC,gBAAgB;MACzB,KAAK,eAAe;QAClB,OAAOC,OAAO;MAChB;QACE,OAAOJ,YAAY;IACvB;EACF,CAAC;EAED,MAAMgB,gBAAgB,GAAIN,IAAsB,IAAK;IACnD,QAAQA,IAAI;MACV,KAAK,gBAAgB;QACnB,OAAO,2BAA2B;MACpC,KAAK,kBAAkB;QACrB,OAAO,6BAA6B;MACtC,KAAK,YAAY;QACf,OAAO,+BAA+B;MACxC,KAAK,oBAAoB;QACvB,OAAO,+BAA+B;MACxC,KAAK,eAAe;QAClB,OAAO,+BAA+B;MACxC;QACE,OAAO,2BAA2B;IACtC;EACF,CAAC;EAED,oBACEJ,OAAA;IAAKW,SAAS,EAAC,mCAAmC;IAAAC,QAAA,gBAChDZ,OAAA;MAAKW,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDZ,OAAA;QAAIW,SAAS,EAAC,iCAAiC;QAAAC,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClEhB,OAAA;QAAQW,SAAS,EAAC,6DAA6D;QAAAC,QAAA,EAAC;MAEhF;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENhB,OAAA;MAAKW,SAAS,EAAC,WAAW;MAAAC,QAAA,EACvBV,UAAU,CAACe,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;QACnC,MAAMC,IAAI,GAAGX,eAAe,CAACS,QAAQ,CAACd,IAAI,CAAC;QAC3C,MAAMiB,UAAU,GAAGX,gBAAgB,CAACQ,QAAQ,CAACd,IAAI,CAAC;QAElD,oBACEJ,OAAA,CAACP,MAAM,CAAC6B,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,KAAK,EAAET,KAAK,GAAG;UAAI,CAAE;UACnCR,SAAS,EAAC,2GAA2G;UAAAC,QAAA,gBAErHZ,OAAA;YAAKW,SAAS,EAAE,2DAA2DU,UAAU,EAAG;YAAAT,QAAA,eACtFZ,OAAA,CAACoB,IAAI;cAACT,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eAENhB,OAAA;YAAKW,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BZ,OAAA;cAAKW,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDZ,OAAA;gBAAIW,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAC9CM,QAAQ,CAACb;cAAK;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eACLhB,OAAA;gBAAMW,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACpCM,QAAQ,CAACX;cAAI;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNhB,OAAA;cAAGW,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EACtCM,QAAQ,CAACZ;YAAW;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,EACHE,QAAQ,CAACV,IAAI,iBACZR,OAAA;cAAMW,SAAS,EAAC,4EAA4E;cAAAC,QAAA,EACzFM,QAAQ,CAACV;YAAI;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,GA3BDE,QAAQ,CAACf,EAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4BN,CAAC;MAEjB,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENhB,OAAA;MAAKW,SAAS,EAAC,oCAAoC;MAAAC,QAAA,eACjDZ,OAAA;QAAKW,SAAS,EAAC,kCAAkC;QAAAC,QAAA,eAC/CZ,OAAA;UAAQW,SAAS,EAAC,6DAA6D;UAAAC,QAAA,EAAC;QAEhF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACa,EAAA,GAxII5B,cAAwB;AA0I9B,eAAeA,cAAc;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}