[{"C:\\Users\\<USER>\\Desktop\\مشروع\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\App.tsx": "2", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\services\\authService.ts": "3", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\pages\\LoginPage.tsx": "4", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\pages\\student\\StudentDashboard.tsx": "5", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\pages\\admin\\AdminDashboard.tsx": "6", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\LoadingSpinner.tsx": "7", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\AIAssistant\\AIAssistant.tsx": "8", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\config\\firebase.ts": "9", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentSidebar.tsx": "10", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentOverview.tsx": "11", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentHeader.tsx": "12", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\DashboardOverview.tsx": "13", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\AdminSidebar.tsx": "14", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\AdminHeader.tsx": "15", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CategoriesManagement.tsx": "16", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\services\\courseService.ts": "17", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\RecentActivity.tsx": "18", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\StatsCard.tsx": "19", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\QuickActions.tsx": "20", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CategoryModal.tsx": "21", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\ConfirmDialog.tsx": "22"}, {"size": 287, "mtime": 1752677446241, "results": "23", "hashOfConfig": "24"}, {"size": 4005, "mtime": 1752676853576, "results": "25", "hashOfConfig": "24"}, {"size": 4713, "mtime": 1752676793004, "results": "26", "hashOfConfig": "24"}, {"size": 9581, "mtime": 1752676940188, "results": "27", "hashOfConfig": "24"}, {"size": 2637, "mtime": 1752677395385, "results": "28", "hashOfConfig": "24"}, {"size": 2898, "mtime": 1752676959952, "results": "29", "hashOfConfig": "24"}, {"size": 1550, "mtime": 1752676894244, "results": "30", "hashOfConfig": "24"}, {"size": 11670, "mtime": 1752677373124, "results": "31", "hashOfConfig": "24"}, {"size": 819, "mtime": 1752676739914, "results": "32", "hashOfConfig": "24"}, {"size": 7073, "mtime": 1752677434384, "results": "33", "hashOfConfig": "24"}, {"size": 10172, "mtime": 1752677555299, "results": "34", "hashOfConfig": "24"}, {"size": 4700, "mtime": 1752677494064, "results": "35", "hashOfConfig": "24"}, {"size": 7261, "mtime": 1752677072553, "results": "36", "hashOfConfig": "24"}, {"size": 5922, "mtime": 1752676990944, "results": "37", "hashOfConfig": "24"}, {"size": 7988, "mtime": 1752677031791, "results": "38", "hashOfConfig": "24"}, {"size": 9350, "mtime": 1752677208435, "results": "39", "hashOfConfig": "24"}, {"size": 8385, "mtime": 1752676831470, "results": "40", "hashOfConfig": "24"}, {"size": 4977, "mtime": 1752677160727, "results": "41", "hashOfConfig": "24"}, {"size": 2740, "mtime": 1752677099694, "results": "42", "hashOfConfig": "24"}, {"size": 3926, "mtime": 1752677124767, "results": "43", "hashOfConfig": "24"}, {"size": 8369, "mtime": 1752677265900, "results": "44", "hashOfConfig": "24"}, {"size": 5660, "mtime": 1752677299906, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1hbgu1t", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\App.tsx", ["112"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\services\\authService.ts", ["113"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\pages\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\pages\\student\\StudentDashboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\pages\\admin\\AdminDashboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\AIAssistant\\AIAssistant.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\config\\firebase.ts", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentSidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentOverview.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentHeader.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\DashboardOverview.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\AdminSidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\AdminHeader.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CategoriesManagement.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\services\\courseService.ts", ["114"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\RecentActivity.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\StatsCard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\QuickActions.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CategoryModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\ConfirmDialog.tsx", [], [], {"ruleId": "115", "severity": 1, "message": "116", "line": 25, "column": 10, "nodeType": "117", "messageId": "118", "endLine": 25, "endColumn": 22}, {"ruleId": "115", "severity": 1, "message": "119", "line": 9, "column": 10, "nodeType": "117", "messageId": "118", "endLine": 9, "endColumn": 14}, {"ruleId": "115", "severity": 1, "message": "120", "line": 14, "column": 44, "nodeType": "117", "messageId": "118", "endLine": 14, "endColumn": 56}, "@typescript-eslint/no-unused-vars", "'firebaseUser' is assigned a value but never used.", "Identifier", "unusedVar", "'User' is defined but never used.", "'deleteObject' is defined but never used."]