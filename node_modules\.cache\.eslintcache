[{"C:\\Users\\<USER>\\Desktop\\مشروع\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\App.tsx": "2", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\services\\authService.ts": "3", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\pages\\LoginPage.tsx": "4", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\pages\\student\\StudentDashboard.tsx": "5", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\pages\\admin\\AdminDashboard.tsx": "6", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\LoadingSpinner.tsx": "7", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\AIAssistant\\AIAssistant.tsx": "8", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\config\\firebase.ts": "9", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentSidebar.tsx": "10", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentOverview.tsx": "11", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentHeader.tsx": "12", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\DashboardOverview.tsx": "13", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\AdminSidebar.tsx": "14", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\AdminHeader.tsx": "15", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CategoriesManagement.tsx": "16", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\services\\courseService.ts": "17", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\RecentActivity.tsx": "18", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\StatsCard.tsx": "19", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\QuickActions.tsx": "20", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CategoryModal.tsx": "21", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\ConfirmDialog.tsx": "22", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CoursesManagement.tsx": "23", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\StudentsManagement.tsx": "24", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\QuizzesManagement.tsx": "25", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CertificatesManagement.tsx": "26", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\AnalyticsPage.tsx": "27", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\SettingsPage.tsx": "28", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\MyCourses.tsx": "29", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\CourseViewer.tsx": "30", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\QuizPage.tsx": "31", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\MyCertificates.tsx": "32", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentProfile.tsx": "33", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\data\\defaultAdmin.ts": "34", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\data\\mockStudents.ts": "35", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\services\\dataService.ts": "36", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\data\\mockCourses.ts": "37"}, {"size": 287, "mtime": 1752677446241, "results": "38", "hashOfConfig": "39"}, {"size": 4189, "mtime": 1752682096005, "results": "40", "hashOfConfig": "39"}, {"size": 5223, "mtime": 1752682275017, "results": "41", "hashOfConfig": "39"}, {"size": 9727, "mtime": 1752683813227, "results": "42", "hashOfConfig": "39"}, {"size": 2772, "mtime": 1752679863852, "results": "43", "hashOfConfig": "39"}, {"size": 2898, "mtime": 1752676959952, "results": "44", "hashOfConfig": "39"}, {"size": 1550, "mtime": 1752676894244, "results": "45", "hashOfConfig": "39"}, {"size": 24414, "mtime": 1752680595727, "results": "46", "hashOfConfig": "39"}, {"size": 833, "mtime": 1752681975589, "results": "47", "hashOfConfig": "39"}, {"size": 7073, "mtime": 1752677434384, "results": "48", "hashOfConfig": "39"}, {"size": 10172, "mtime": 1752677555299, "results": "49", "hashOfConfig": "39"}, {"size": 4700, "mtime": 1752677494064, "results": "50", "hashOfConfig": "39"}, {"size": 7678, "mtime": 1752682443047, "results": "51", "hashOfConfig": "39"}, {"size": 5922, "mtime": 1752676990944, "results": "52", "hashOfConfig": "39"}, {"size": 7988, "mtime": 1752677031791, "results": "53", "hashOfConfig": "39"}, {"size": 9350, "mtime": 1752677208435, "results": "54", "hashOfConfig": "39"}, {"size": 8385, "mtime": 1752676831470, "results": "55", "hashOfConfig": "39"}, {"size": 4977, "mtime": 1752677160727, "results": "56", "hashOfConfig": "39"}, {"size": 2740, "mtime": 1752677099694, "results": "57", "hashOfConfig": "39"}, {"size": 3926, "mtime": 1752677124767, "results": "58", "hashOfConfig": "39"}, {"size": 8369, "mtime": 1752677265900, "results": "59", "hashOfConfig": "39"}, {"size": 5660, "mtime": 1752677299906, "results": "60", "hashOfConfig": "39"}, {"size": 8741, "mtime": 1752682891266, "results": "61", "hashOfConfig": "39"}, {"size": 12664, "mtime": 1752680733544, "results": "62", "hashOfConfig": "39"}, {"size": 8183, "mtime": 1752680806170, "results": "63", "hashOfConfig": "39"}, {"size": 7983, "mtime": 1752680848971, "results": "64", "hashOfConfig": "39"}, {"size": 5141, "mtime": 1752680890303, "results": "65", "hashOfConfig": "39"}, {"size": 8915, "mtime": 1752680928401, "results": "66", "hashOfConfig": "39"}, {"size": 9159, "mtime": 1752681456104, "results": "67", "hashOfConfig": "39"}, {"size": 10942, "mtime": 1752681273164, "results": "68", "hashOfConfig": "39"}, {"size": 11382, "mtime": 1752681329670, "results": "69", "hashOfConfig": "39"}, {"size": 8199, "mtime": 1752681362339, "results": "70", "hashOfConfig": "39"}, {"size": 15050, "mtime": 1752681387765, "results": "71", "hashOfConfig": "39"}, {"size": 661, "mtime": 1752682975032, "results": "72", "hashOfConfig": "39"}, {"size": 1547, "mtime": 1752683720413, "results": "73", "hashOfConfig": "39"}, {"size": 3520, "mtime": 1752683428392, "results": "74", "hashOfConfig": "39"}, {"size": 3100, "mtime": 1752683364344, "results": "75", "hashOfConfig": "39"}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1hbgu1t", {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\App.tsx", ["187"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\services\\authService.ts", ["188", "189"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\pages\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\pages\\student\\StudentDashboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\pages\\admin\\AdminDashboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\AIAssistant\\AIAssistant.tsx", ["190", "191", "192"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\config\\firebase.ts", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentSidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentOverview.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentHeader.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\DashboardOverview.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\AdminSidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\AdminHeader.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CategoriesManagement.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\services\\courseService.ts", ["193"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\RecentActivity.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\StatsCard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\QuickActions.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CategoryModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\ConfirmDialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CoursesManagement.tsx", ["194"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\StudentsManagement.tsx", ["195"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\QuizzesManagement.tsx", ["196"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CertificatesManagement.tsx", ["197"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\AnalyticsPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\SettingsPage.tsx", ["198"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\MyCourses.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\CourseViewer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\QuizPage.tsx", ["199"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\MyCertificates.tsx", ["200"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentProfile.tsx", ["201", "202"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\data\\defaultAdmin.ts", ["203"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\data\\mockStudents.ts", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\services\\dataService.ts", ["204", "205", "206"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\data\\mockCourses.ts", [], [], {"ruleId": "207", "severity": 1, "message": "208", "line": 34, "column": 10, "nodeType": "209", "messageId": "210", "endLine": 34, "endColumn": 22}, {"ruleId": "207", "severity": 1, "message": "211", "line": 9, "column": 10, "nodeType": "209", "messageId": "210", "endLine": 9, "endColumn": 14}, {"ruleId": "207", "severity": 1, "message": "212", "line": 11, "column": 24, "nodeType": "209", "messageId": "210", "endLine": 11, "endColumn": 42}, {"ruleId": "207", "severity": 1, "message": "213", "line": 4, "column": 3, "nodeType": "209", "messageId": "210", "endLine": 4, "endColumn": 26}, {"ruleId": "207", "severity": 1, "message": "214", "line": 7, "column": 3, "nodeType": "209", "messageId": "210", "endLine": 7, "endColumn": 15}, {"ruleId": "207", "severity": 1, "message": "215", "line": 11, "column": 26, "nodeType": "209", "messageId": "210", "endLine": 11, "endColumn": 43}, {"ruleId": "207", "severity": 1, "message": "216", "line": 14, "column": 44, "nodeType": "209", "messageId": "210", "endLine": 14, "endColumn": 56}, {"ruleId": "207", "severity": 1, "message": "217", "line": 26, "column": 10, "nodeType": "209", "messageId": "210", "endLine": 26, "endColumn": 17}, {"ruleId": "218", "severity": 1, "message": "219", "line": 64, "column": 6, "nodeType": "220", "endLine": 64, "endColumn": 8, "suggestions": "221"}, {"ruleId": "218", "severity": 1, "message": "222", "line": 62, "column": 6, "nodeType": "220", "endLine": 62, "endColumn": 8, "suggestions": "223"}, {"ruleId": "218", "severity": 1, "message": "224", "line": 47, "column": 6, "nodeType": "220", "endLine": 47, "endColumn": 8, "suggestions": "225"}, {"ruleId": "207", "severity": 1, "message": "226", "line": 7, "column": 3, "nodeType": "209", "messageId": "210", "endLine": 7, "endColumn": 15}, {"ruleId": "207", "severity": 1, "message": "227", "line": 22, "column": 20, "nodeType": "209", "messageId": "210", "endLine": 22, "endColumn": 31}, {"ruleId": "207", "severity": 1, "message": "228", "line": 1, "column": 17, "nodeType": "209", "messageId": "210", "endLine": 1, "endColumn": 25}, {"ruleId": "207", "severity": 1, "message": "229", "line": 5, "column": 3, "nodeType": "209", "messageId": "210", "endLine": 5, "endColumn": 15}, {"ruleId": "207", "severity": 1, "message": "230", "line": 6, "column": 3, "nodeType": "209", "messageId": "210", "endLine": 6, "endColumn": 10}, {"ruleId": "207", "severity": 1, "message": "211", "line": 1, "column": 10, "nodeType": "209", "messageId": "210", "endLine": 1, "endColumn": 14}, {"ruleId": "207", "severity": 1, "message": "231", "line": 2, "column": 22, "nodeType": "209", "messageId": "210", "endLine": 2, "endColumn": 29}, {"ruleId": "207", "severity": 1, "message": "232", "line": 2, "column": 36, "nodeType": "209", "messageId": "210", "endLine": 2, "endColumn": 42}, {"ruleId": "207", "severity": 1, "message": "233", "line": 4, "column": 23, "nodeType": "209", "messageId": "210", "endLine": 4, "endColumn": 33}, "@typescript-eslint/no-unused-vars", "'firebaseUser' is assigned a value but never used.", "Identifier", "unusedVar", "'User' is defined but never used.", "'studentCredentials' is defined but never used.", "'ChatBubbleLeftRightIcon' is defined but never used.", "'SparklesIcon' is defined but never used.", "'SparklesIconSolid' is defined but never used.", "'deleteObject' is defined but never used.", "'loading' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook React.useEffect has a missing dependency: 'mockStudents'. Either include it or remove the dependency array.", "ArrayExpression", ["234"], "React Hook React.useEffect has a missing dependency: 'mockQuizzes'. Either include it or remove the dependency array.", ["235"], "React Hook React.useEffect has a missing dependency: 'mockCertificates'. Either include it or remove the dependency array.", ["236"], "'GlobeAltIcon' is defined but never used.", "'setTimeLeft' is assigned a value but never used.", "'useState' is defined but never used.", "'EnvelopeIcon' is defined but never used.", "'KeyIcon' is defined but never used.", "'getDocs' is defined but never used.", "'getDoc' is defined but never used.", "'mockVideos' is defined but never used.", {"desc": "237", "fix": "238"}, {"desc": "239", "fix": "240"}, {"desc": "241", "fix": "242"}, "Update the dependencies array to be: [mockStudents]", {"range": "243", "text": "244"}, "Update the dependencies array to be: [mockQuizzes]", {"range": "245", "text": "246"}, "Update the dependencies array to be: [mockCertificates]", {"range": "247", "text": "248"}, [1562, 1564], "[mockStudents]", [1436, 1438], "[mockQuizzes]", [1181, 1183], "[mockCertificates]"]