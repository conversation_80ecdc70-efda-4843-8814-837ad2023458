[{"C:\\Users\\<USER>\\Desktop\\مشروع\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\App.tsx": "2", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\services\\authService.ts": "3", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\pages\\LoginPage.tsx": "4", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\pages\\student\\StudentDashboard.tsx": "5", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\pages\\admin\\AdminDashboard.tsx": "6", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\LoadingSpinner.tsx": "7", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\AIAssistant\\AIAssistant.tsx": "8", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\config\\firebase.ts": "9", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentSidebar.tsx": "10", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentOverview.tsx": "11", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentHeader.tsx": "12", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\DashboardOverview.tsx": "13", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\AdminSidebar.tsx": "14", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\AdminHeader.tsx": "15", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CategoriesManagement.tsx": "16", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\services\\courseService.ts": "17", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\RecentActivity.tsx": "18", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\StatsCard.tsx": "19", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\QuickActions.tsx": "20", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CategoryModal.tsx": "21", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\ConfirmDialog.tsx": "22", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CoursesManagement.tsx": "23", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\StudentsManagement.tsx": "24", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\QuizzesManagement.tsx": "25", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CertificatesManagement.tsx": "26", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\AnalyticsPage.tsx": "27", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\SettingsPage.tsx": "28", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\MyCourses.tsx": "29", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\CourseViewer.tsx": "30", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\QuizPage.tsx": "31", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\MyCertificates.tsx": "32", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentProfile.tsx": "33"}, {"size": 287, "mtime": 1752677446241, "results": "34", "hashOfConfig": "35"}, {"size": 4215, "mtime": 1752679255548, "results": "36", "hashOfConfig": "35"}, {"size": 4713, "mtime": 1752676793004, "results": "37", "hashOfConfig": "35"}, {"size": 9727, "mtime": 1752679847376, "results": "38", "hashOfConfig": "35"}, {"size": 2772, "mtime": 1752679863852, "results": "39", "hashOfConfig": "35"}, {"size": 2898, "mtime": 1752676959952, "results": "40", "hashOfConfig": "35"}, {"size": 1550, "mtime": 1752676894244, "results": "41", "hashOfConfig": "35"}, {"size": 24414, "mtime": 1752680595727, "results": "42", "hashOfConfig": "35"}, {"size": 819, "mtime": 1752676739914, "results": "43", "hashOfConfig": "35"}, {"size": 7073, "mtime": 1752677434384, "results": "44", "hashOfConfig": "35"}, {"size": 10172, "mtime": 1752677555299, "results": "45", "hashOfConfig": "35"}, {"size": 4700, "mtime": 1752677494064, "results": "46", "hashOfConfig": "35"}, {"size": 7307, "mtime": 1752679325532, "results": "47", "hashOfConfig": "35"}, {"size": 5922, "mtime": 1752676990944, "results": "48", "hashOfConfig": "35"}, {"size": 7988, "mtime": 1752677031791, "results": "49", "hashOfConfig": "35"}, {"size": 9350, "mtime": 1752677208435, "results": "50", "hashOfConfig": "35"}, {"size": 8385, "mtime": 1752676831470, "results": "51", "hashOfConfig": "35"}, {"size": 4977, "mtime": 1752677160727, "results": "52", "hashOfConfig": "35"}, {"size": 2740, "mtime": 1752677099694, "results": "53", "hashOfConfig": "35"}, {"size": 3926, "mtime": 1752677124767, "results": "54", "hashOfConfig": "35"}, {"size": 8369, "mtime": 1752677265900, "results": "55", "hashOfConfig": "35"}, {"size": 5660, "mtime": 1752677299906, "results": "56", "hashOfConfig": "35"}, {"size": 9096, "mtime": 1752680692348, "results": "57", "hashOfConfig": "35"}, {"size": 12664, "mtime": 1752680733544, "results": "58", "hashOfConfig": "35"}, {"size": 8183, "mtime": 1752680806170, "results": "59", "hashOfConfig": "35"}, {"size": 7983, "mtime": 1752680848971, "results": "60", "hashOfConfig": "35"}, {"size": 5141, "mtime": 1752680890303, "results": "61", "hashOfConfig": "35"}, {"size": 8915, "mtime": 1752680928401, "results": "62", "hashOfConfig": "35"}, {"size": 9159, "mtime": 1752681456104, "results": "63", "hashOfConfig": "35"}, {"size": 10942, "mtime": 1752681273164, "results": "64", "hashOfConfig": "35"}, {"size": 11382, "mtime": 1752681329670, "results": "65", "hashOfConfig": "35"}, {"size": 8199, "mtime": 1752681362339, "results": "66", "hashOfConfig": "35"}, {"size": 15050, "mtime": 1752681387765, "results": "67", "hashOfConfig": "35"}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1hbgu1t", {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\App.tsx", ["167"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\services\\authService.ts", ["168"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\pages\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\pages\\student\\StudentDashboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\pages\\admin\\AdminDashboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\AIAssistant\\AIAssistant.tsx", ["169", "170", "171"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\config\\firebase.ts", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentSidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentOverview.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentHeader.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\DashboardOverview.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\AdminSidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\AdminHeader.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CategoriesManagement.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\services\\courseService.ts", ["172"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\RecentActivity.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\StatsCard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\QuickActions.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CategoryModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\ConfirmDialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CoursesManagement.tsx", ["173"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\StudentsManagement.tsx", ["174"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\QuizzesManagement.tsx", ["175"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CertificatesManagement.tsx", ["176"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\AnalyticsPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\SettingsPage.tsx", ["177"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\MyCourses.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\CourseViewer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\QuizPage.tsx", ["178"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\MyCertificates.tsx", ["179"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentProfile.tsx", ["180", "181"], [], {"ruleId": "182", "severity": 1, "message": "183", "line": 34, "column": 10, "nodeType": "184", "messageId": "185", "endLine": 34, "endColumn": 22}, {"ruleId": "182", "severity": 1, "message": "186", "line": 9, "column": 10, "nodeType": "184", "messageId": "185", "endLine": 9, "endColumn": 14}, {"ruleId": "182", "severity": 1, "message": "187", "line": 4, "column": 3, "nodeType": "184", "messageId": "185", "endLine": 4, "endColumn": 26}, {"ruleId": "182", "severity": 1, "message": "188", "line": 7, "column": 3, "nodeType": "184", "messageId": "185", "endLine": 7, "endColumn": 15}, {"ruleId": "182", "severity": 1, "message": "189", "line": 11, "column": 26, "nodeType": "184", "messageId": "185", "endLine": 11, "endColumn": 43}, {"ruleId": "182", "severity": 1, "message": "190", "line": 14, "column": 44, "nodeType": "184", "messageId": "185", "endLine": 14, "endColumn": 56}, {"ruleId": "191", "severity": 1, "message": "192", "line": 58, "column": 6, "nodeType": "193", "endLine": 58, "endColumn": 8, "suggestions": "194"}, {"ruleId": "191", "severity": 1, "message": "195", "line": 64, "column": 6, "nodeType": "193", "endLine": 64, "endColumn": 8, "suggestions": "196"}, {"ruleId": "191", "severity": 1, "message": "197", "line": 62, "column": 6, "nodeType": "193", "endLine": 62, "endColumn": 8, "suggestions": "198"}, {"ruleId": "191", "severity": 1, "message": "199", "line": 47, "column": 6, "nodeType": "193", "endLine": 47, "endColumn": 8, "suggestions": "200"}, {"ruleId": "182", "severity": 1, "message": "201", "line": 7, "column": 3, "nodeType": "184", "messageId": "185", "endLine": 7, "endColumn": 15}, {"ruleId": "182", "severity": 1, "message": "202", "line": 22, "column": 20, "nodeType": "184", "messageId": "185", "endLine": 22, "endColumn": 31}, {"ruleId": "182", "severity": 1, "message": "203", "line": 1, "column": 17, "nodeType": "184", "messageId": "185", "endLine": 1, "endColumn": 25}, {"ruleId": "182", "severity": 1, "message": "204", "line": 5, "column": 3, "nodeType": "184", "messageId": "185", "endLine": 5, "endColumn": 15}, {"ruleId": "182", "severity": 1, "message": "205", "line": 6, "column": 3, "nodeType": "184", "messageId": "185", "endLine": 6, "endColumn": 10}, "@typescript-eslint/no-unused-vars", "'firebaseUser' is assigned a value but never used.", "Identifier", "unusedVar", "'User' is defined but never used.", "'ChatBubbleLeftRightIcon' is defined but never used.", "'SparklesIcon' is defined but never used.", "'SparklesIconSolid' is defined but never used.", "'deleteObject' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook React.useEffect has a missing dependency: 'mockCourses'. Either include it or remove the dependency array.", "ArrayExpression", ["206"], "React Hook React.useEffect has a missing dependency: 'mockStudents'. Either include it or remove the dependency array.", ["207"], "React Hook React.useEffect has a missing dependency: 'mockQuizzes'. Either include it or remove the dependency array.", ["208"], "React Hook React.useEffect has a missing dependency: 'mockCertificates'. Either include it or remove the dependency array.", ["209"], "'GlobeAltIcon' is defined but never used.", "'setTimeLeft' is assigned a value but never used.", "'useState' is defined but never used.", "'EnvelopeIcon' is defined but never used.", "'KeyIcon' is defined but never used.", {"desc": "210", "fix": "211"}, {"desc": "212", "fix": "213"}, {"desc": "214", "fix": "215"}, {"desc": "216", "fix": "217"}, "Update the dependencies array to be: [mockCourses]", {"range": "218", "text": "219"}, "Update the dependencies array to be: [mockStudents]", {"range": "220", "text": "221"}, "Update the dependencies array to be: [mockQuizzes]", {"range": "222", "text": "223"}, "Update the dependencies array to be: [mockCertificates]", {"range": "224", "text": "225"}, [1348, 1350], "[mockCourses]", [1562, 1564], "[mockStudents]", [1436, 1438], "[mockQuizzes]", [1181, 1183], "[mockCertificates]"]