{"ast": null, "code": "import { useContext, useId, useEffect } from 'react';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\n\n/**\n * When a component is the child of `AnimatePresence`, it can use `usePresence`\n * to access information about whether it's still present in the React tree.\n *\n * ```jsx\n * import { usePresence } from \"framer-motion\"\n *\n * export const Component = () => {\n *   const [isPresent, safeToRemove] = usePresence()\n *\n *   useEffect(() => {\n *     !isPresent && setTimeout(safeToRemove, 1000)\n *   }, [isPresent])\n *\n *   return <div />\n * }\n * ```\n *\n * If `isPresent` is `false`, it means that a component has been removed the tree, but\n * `AnimatePresence` won't really remove it until `safeToRemove` has been called.\n *\n * @public\n */\nfunction usePresence() {\n  const context = useContext(PresenceContext);\n  if (context === null) return [true, null];\n  const {\n    isPresent,\n    onExitComplete,\n    register\n  } = context;\n  // It's safe to call the following hooks conditionally (after an early return) because the context will always\n  // either be null or non-null for the lifespan of the component.\n  const id = useId();\n  useEffect(() => register(id), []);\n  const safeToRemove = () => onExitComplete && onExitComplete(id);\n  return !isPresent && onExitComplete ? [false, safeToRemove] : [true];\n}\n/**\n * Similar to `usePresence`, except `useIsPresent` simply returns whether or not the component is present.\n * There is no `safeToRemove` function.\n *\n * ```jsx\n * import { useIsPresent } from \"framer-motion\"\n *\n * export const Component = () => {\n *   const isPresent = useIsPresent()\n *\n *   useEffect(() => {\n *     !isPresent && console.log(\"I've been removed!\")\n *   }, [isPresent])\n *\n *   return <div />\n * }\n * ```\n *\n * @public\n */\nfunction useIsPresent() {\n  return isPresent(useContext(PresenceContext));\n}\nfunction isPresent(context) {\n  return context === null ? true : context.isPresent;\n}\nexport { isPresent, useIsPresent, usePresence };", "map": {"version": 3, "names": ["useContext", "useId", "useEffect", "PresenceContext", "usePresence", "context", "isPresent", "onExitComplete", "register", "id", "safeToRemove", "useIsPresent"], "sources": ["C:/Users/<USER>/Desktop/مشروع/node_modules/framer-motion/dist/es/components/AnimatePresence/use-presence.mjs"], "sourcesContent": ["import { useContext, useId, useEffect } from 'react';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\n\n/**\n * When a component is the child of `AnimatePresence`, it can use `usePresence`\n * to access information about whether it's still present in the React tree.\n *\n * ```jsx\n * import { usePresence } from \"framer-motion\"\n *\n * export const Component = () => {\n *   const [isPresent, safeToRemove] = usePresence()\n *\n *   useEffect(() => {\n *     !isPresent && setTimeout(safeToRemove, 1000)\n *   }, [isPresent])\n *\n *   return <div />\n * }\n * ```\n *\n * If `isPresent` is `false`, it means that a component has been removed the tree, but\n * `AnimatePresence` won't really remove it until `safeToRemove` has been called.\n *\n * @public\n */\nfunction usePresence() {\n    const context = useContext(PresenceContext);\n    if (context === null)\n        return [true, null];\n    const { isPresent, onExitComplete, register } = context;\n    // It's safe to call the following hooks conditionally (after an early return) because the context will always\n    // either be null or non-null for the lifespan of the component.\n    const id = useId();\n    useEffect(() => register(id), []);\n    const safeToRemove = () => onExitComplete && onExitComplete(id);\n    return !isPresent && onExitComplete ? [false, safeToRemove] : [true];\n}\n/**\n * Similar to `usePresence`, except `useIsPresent` simply returns whether or not the component is present.\n * There is no `safeToRemove` function.\n *\n * ```jsx\n * import { useIsPresent } from \"framer-motion\"\n *\n * export const Component = () => {\n *   const isPresent = useIsPresent()\n *\n *   useEffect(() => {\n *     !isPresent && console.log(\"I've been removed!\")\n *   }, [isPresent])\n *\n *   return <div />\n * }\n * ```\n *\n * @public\n */\nfunction useIsPresent() {\n    return isPresent(useContext(PresenceContext));\n}\nfunction isPresent(context) {\n    return context === null ? true : context.isPresent;\n}\n\nexport { isPresent, useIsPresent, usePresence };\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,KAAK,EAAEC,SAAS,QAAQ,OAAO;AACpD,SAASC,eAAe,QAAQ,mCAAmC;;AAEnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAAA,EAAG;EACnB,MAAMC,OAAO,GAAGL,UAAU,CAACG,eAAe,CAAC;EAC3C,IAAIE,OAAO,KAAK,IAAI,EAChB,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC;EACvB,MAAM;IAAEC,SAAS;IAAEC,cAAc;IAAEC;EAAS,CAAC,GAAGH,OAAO;EACvD;EACA;EACA,MAAMI,EAAE,GAAGR,KAAK,CAAC,CAAC;EAClBC,SAAS,CAAC,MAAMM,QAAQ,CAACC,EAAE,CAAC,EAAE,EAAE,CAAC;EACjC,MAAMC,YAAY,GAAGA,CAAA,KAAMH,cAAc,IAAIA,cAAc,CAACE,EAAE,CAAC;EAC/D,OAAO,CAACH,SAAS,IAAIC,cAAc,GAAG,CAAC,KAAK,EAAEG,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAAA,EAAG;EACpB,OAAOL,SAAS,CAACN,UAAU,CAACG,eAAe,CAAC,CAAC;AACjD;AACA,SAASG,SAASA,CAACD,OAAO,EAAE;EACxB,OAAOA,OAAO,KAAK,IAAI,GAAG,IAAI,GAAGA,OAAO,CAACC,SAAS;AACtD;AAEA,SAASA,SAAS,EAAEK,YAAY,EAAEP,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}