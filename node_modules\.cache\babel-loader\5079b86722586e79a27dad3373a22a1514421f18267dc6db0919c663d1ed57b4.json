{"ast": null, "code": "import * as React from \"react\";\nfunction TvIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M19.5 6h-15v9h15V6Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M3.375 3C2.339 3 1.5 3.84 1.5 4.875v11.25C1.5 17.16 2.34 18 3.375 18H9.75v1.5H6A.75.75 0 0 0 6 21h12a.75.75 0 0 0 0-1.5h-3.75V18h6.375c1.035 0 1.875-.84 1.875-1.875V4.875C22.5 3.839 21.66 3 20.625 3H3.375Zm0 13.5h17.25a.375.375 0 0 0 .375-.375V4.875a.375.375 0 0 0-.375-.375H3.375A.375.375 0 0 0 3 4.875v11.25c0 .207.168.375.375.375Z\",\n    clipRule: \"evenodd\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(TvIcon);\nexport default ForwardRef;", "map": {"version": 3, "names": ["React", "TvIcon", "title", "titleId", "props", "svgRef", "createElement", "Object", "assign", "xmlns", "viewBox", "fill", "ref", "id", "d", "fillRule", "clipRule", "ForwardRef", "forwardRef"], "sources": ["C:/Users/<USER>/Desktop/مشروع/node_modules/@heroicons/react/24/solid/esm/TvIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction TvIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M19.5 6h-15v9h15V6Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M3.375 3C2.339 3 1.5 3.84 1.5 4.875v11.25C1.5 17.16 2.34 18 3.375 18H9.75v1.5H6A.75.75 0 0 0 6 21h12a.75.75 0 0 0 0-1.5h-3.75V18h6.375c1.035 0 1.875-.84 1.875-1.875V4.875C22.5 3.839 21.66 3 20.625 3H3.375Zm0 13.5h17.25a.375.375 0 0 0 .375-.375V4.875a.375.375 0 0 0-.375-.375H3.375A.375.375 0 0 0 3 4.875v11.25c0 .207.168.375.375.375Z\",\n    clipRule: \"evenodd\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(TvIcon);\nexport default ForwardRef;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAMA,CAAC;EACdC,KAAK;EACLC,OAAO;EACP,GAAGC;AACL,CAAC,EAAEC,MAAM,EAAE;EACT,OAAO,aAAaL,KAAK,CAACM,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,4BAA4B;IACnCC,OAAO,EAAE,WAAW;IACpBC,IAAI,EAAE,cAAc;IACpB,aAAa,EAAE,MAAM;IACrB,WAAW,EAAE,MAAM;IACnBC,GAAG,EAAEP,MAAM;IACX,iBAAiB,EAAEF;EACrB,CAAC,EAAEC,KAAK,CAAC,EAAEF,KAAK,GAAG,aAAaF,KAAK,CAACM,aAAa,CAAC,OAAO,EAAE;IAC3DO,EAAE,EAAEV;EACN,CAAC,EAAED,KAAK,CAAC,GAAG,IAAI,EAAE,aAAaF,KAAK,CAACM,aAAa,CAAC,MAAM,EAAE;IACzDQ,CAAC,EAAE;EACL,CAAC,CAAC,EAAE,aAAad,KAAK,CAACM,aAAa,CAAC,MAAM,EAAE;IAC3CS,QAAQ,EAAE,SAAS;IACnBD,CAAC,EAAE,+UAA+U;IAClVE,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC;AACL;AACA,MAAMC,UAAU,GAAG,aAAcjB,KAAK,CAACkB,UAAU,CAACjB,MAAM,CAAC;AACzD,eAAegB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}