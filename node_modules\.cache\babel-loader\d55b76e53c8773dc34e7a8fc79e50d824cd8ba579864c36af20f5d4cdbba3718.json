{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0634\\u0631\\u0648\\u0639\\\\src\\\\components\\\\Student\\\\MyCertificates.tsx\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { DocumentIcon, CheckBadgeIcon, EyeIcon, ArrowDownTrayIcon, ShareIcon } from '@heroicons/react/24/outline';\n\n// Types\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MyCertificates = ({\n  user,\n  onBack\n}) => {\n  // Mock certificates data\n  const certificates = [{\n    id: '1',\n    courseTitle: 'أساسيات البرمجة',\n    issuedDate: new Date('2024-03-01'),\n    verificationCode: 'CERT-2024-001',\n    score: 85,\n    instructorName: 'ALaa <PERSON>',\n    certificateUrl: '/certificates/cert1.pdf'\n  }, {\n    id: '2',\n    courseTitle: 'تطوير المواقع المتقدم',\n    issuedDate: new Date('2024-02-15'),\n    verificationCode: 'CERT-2024-002',\n    score: 92,\n    instructorName: '<PERSON><PERSON> <PERSON>',\n    certificateUrl: '/certificates/cert2.pdf'\n  }];\n  const handleViewCertificate = certificateId => {\n    console.log('View certificate:', certificateId);\n    // TODO: Implement certificate viewer\n  };\n  const handleDownloadCertificate = certificateId => {\n    console.log('Download certificate:', certificateId);\n    // TODO: Implement certificate download\n  };\n  const handleShareCertificate = certificateId => {\n    console.log('Share certificate:', certificateId);\n    // TODO: Implement certificate sharing\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-4 space-x-reverse\",\n      children: [onBack && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onBack,\n        className: \"p-2 text-gray-600 hover:text-gray-900 transition-colors\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-6 h-6\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M15 19l-7-7 7-7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"\\u0634\\u0647\\u0627\\u062F\\u0627\\u062A\\u064A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u062A\\u064A \\u062D\\u0635\\u0644\\u062A \\u0639\\u0644\\u064A\\u0647\\u0627\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n      children: certificates.map((certificate, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: index * 0.1\n        },\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-br from-blue-600 to-purple-700 p-6 text-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(CheckBadgeIcon, {\n              className: \"w-12 h-12 mx-auto mb-3 text-yellow-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-bold mb-2\",\n              children: \"\\u0634\\u0647\\u0627\\u062F\\u0629 \\u0625\\u062A\\u0645\\u0627\\u0645\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-blue-100 text-sm\",\n              children: certificate.courseTitle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600\",\n                children: \"\\u062A\\u0627\\u0631\\u064A\\u062E \\u0627\\u0644\\u0625\\u0635\\u062F\\u0627\\u0631:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium text-gray-900\",\n                children: certificate.issuedDate.toLocaleDateString('ar-SA')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600\",\n                children: \"\\u0627\\u0644\\u0646\\u062A\\u064A\\u062C\\u0629:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium text-green-600\",\n                children: [certificate.score, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600\",\n                children: \"\\u0627\\u0644\\u0645\\u062F\\u0631\\u0628:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium text-gray-900\",\n                children: certificate.instructorName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600\",\n                children: \"\\u0631\\u0645\\u0632 \\u0627\\u0644\\u062A\\u062D\\u0642\\u0642:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-mono text-xs bg-gray-100 px-2 py-1 rounded\",\n                children: certificate.verificationCode\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between pt-4 border-t border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 space-x-reverse\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleViewCertificate(certificate.id),\n                className: \"p-2 text-gray-600 hover:text-blue-600 transition-colors\",\n                title: \"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0629\",\n                children: /*#__PURE__*/_jsxDEV(EyeIcon, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 129,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleDownloadCertificate(certificate.id),\n                className: \"p-2 text-gray-600 hover:text-green-600 transition-colors\",\n                title: \"\\u062A\\u062D\\u0645\\u064A\\u0644 \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0629\",\n                children: /*#__PURE__*/_jsxDEV(ArrowDownTrayIcon, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleShareCertificate(certificate.id),\n                className: \"p-2 text-gray-600 hover:text-purple-600 transition-colors\",\n                title: \"\\u0645\\u0634\\u0627\\u0631\\u0643\\u0629 \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0629\",\n                children: /*#__PURE__*/_jsxDEV(ShareIcon, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleViewCertificate(certificate.id),\n              className: \"text-sm bg-blue-600 text-white px-3 py-1 rounded-lg hover:bg-blue-700 transition-colors\",\n              children: \"\\u0639\\u0631\\u0636\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 13\n        }, this)]\n      }, certificate.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this), certificates.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(DocumentIcon, {\n        className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-2\",\n        children: \"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0634\\u0647\\u0627\\u062F\\u0627\\u062A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: \"\\u0644\\u0645 \\u062A\\u062D\\u0635\\u0644 \\u0639\\u0644\\u0649 \\u0623\\u064A \\u0634\\u0647\\u0627\\u062F\\u0627\\u062A \\u0628\\u0639\\u062F. \\u0623\\u0643\\u0645\\u0644 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A \\u0644\\u0644\\u062D\\u0635\\u0648\\u0644 \\u0639\\u0644\\u0649 \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0627\\u062A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 9\n    }, this), certificates.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.3\n      },\n      className: \"bg-white rounded-lg shadow-sm p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-lg font-semibold text-gray-900 mb-4\",\n        children: \"\\u0625\\u062D\\u0635\\u0627\\u0626\\u064A\\u0627\\u062A \\u0627\\u0644\\u0625\\u0646\\u062C\\u0627\\u0632\\u0627\\u062A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center p-4 bg-blue-50 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-blue-600 mb-1\",\n            children: certificates.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600\",\n            children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center p-4 bg-green-50 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-green-600 mb-1\",\n            children: [Math.round(certificates.reduce((sum, cert) => sum + cert.score, 0) / certificates.length), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600\",\n            children: \"\\u0645\\u062A\\u0648\\u0633\\u0637 \\u0627\\u0644\\u0646\\u062A\\u0627\\u0626\\u062C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center p-4 bg-purple-50 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-purple-600 mb-1\",\n            children: certificates.filter(cert => cert.score >= 90).length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600\",\n            children: \"\\u0634\\u0647\\u0627\\u062F\\u0627\\u062A \\u0645\\u0645\\u062A\\u0627\\u0632\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n};\n_c = MyCertificates;\nexport default MyCertificates;\nvar _c;\n$RefreshReg$(_c, \"MyCertificates\");", "map": {"version": 3, "names": ["React", "motion", "DocumentIcon", "CheckBadgeIcon", "EyeIcon", "ArrowDownTrayIcon", "ShareIcon", "jsxDEV", "_jsxDEV", "MyCertificates", "user", "onBack", "certificates", "id", "courseTitle", "issuedDate", "Date", "verificationCode", "score", "<PERSON><PERSON><PERSON>", "certificateUrl", "handleViewCertificate", "certificateId", "console", "log", "handleDownloadCertificate", "handleShareCertificate", "className", "children", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "certificate", "index", "div", "initial", "opacity", "y", "animate", "transition", "delay", "toLocaleDateString", "title", "length", "Math", "round", "reduce", "sum", "cert", "filter", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Student/MyCertificates.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  DocumentIcon,\n  CheckBadgeIcon,\n  EyeIcon,\n  ArrowDownTrayIcon,\n  ShareIcon\n} from '@heroicons/react/24/outline';\n\n// Types\nimport { Student } from '../../types';\n\ninterface MyCertificatesProps {\n  user?: Student;\n  onBack?: () => void;\n}\n\nconst MyCertificates: React.FC<MyCertificatesProps> = ({ user, onBack }) => {\n  // Mock certificates data\n  const certificates = [\n    {\n      id: '1',\n      courseTitle: 'أساسيات البرمجة',\n      issuedDate: new Date('2024-03-01'),\n      verificationCode: 'CERT-2024-001',\n      score: 85,\n      instructorName: '<PERSON>aa <PERSON>',\n      certificateUrl: '/certificates/cert1.pdf'\n    },\n    {\n      id: '2',\n      courseTitle: 'تطوير المواقع المتقدم',\n      issuedDate: new Date('2024-02-15'),\n      verificationCode: 'CERT-2024-002',\n      score: 92,\n      instructorName: '<PERSON><PERSON> <PERSON>',\n      certificateUrl: '/certificates/cert2.pdf'\n    }\n  ];\n\n  const handleViewCertificate = (certificateId: string) => {\n    console.log('View certificate:', certificateId);\n    // TODO: Implement certificate viewer\n  };\n\n  const handleDownloadCertificate = (certificateId: string) => {\n    console.log('Download certificate:', certificateId);\n    // TODO: Implement certificate download\n  };\n\n  const handleShareCertificate = (certificateId: string) => {\n    console.log('Share certificate:', certificateId);\n    // TODO: Implement certificate sharing\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center space-x-4 space-x-reverse\">\n        {onBack && (\n          <button\n            onClick={onBack}\n            className=\"p-2 text-gray-600 hover:text-gray-900 transition-colors\"\n          >\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n            </svg>\n          </button>\n        )}\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">شهاداتي</h1>\n          <p className=\"text-gray-600\">جميع الشهادات التي حصلت عليها</p>\n        </div>\n      </div>\n\n      {/* Certificates Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {certificates.map((certificate, index) => (\n          <motion.div\n            key={certificate.id}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: index * 0.1 }}\n            className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow\"\n          >\n            {/* Certificate Preview */}\n            <div className=\"bg-gradient-to-br from-blue-600 to-purple-700 p-6 text-white\">\n              <div className=\"text-center\">\n                <CheckBadgeIcon className=\"w-12 h-12 mx-auto mb-3 text-yellow-300\" />\n                <h3 className=\"text-lg font-bold mb-2\">شهادة إتمام</h3>\n                <p className=\"text-blue-100 text-sm\">{certificate.courseTitle}</p>\n              </div>\n            </div>\n\n            {/* Certificate Details */}\n            <div className=\"p-6\">\n              <div className=\"space-y-3 mb-4\">\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"text-gray-600\">تاريخ الإصدار:</span>\n                  <span className=\"font-medium text-gray-900\">\n                    {certificate.issuedDate.toLocaleDateString('ar-SA')}\n                  </span>\n                </div>\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"text-gray-600\">النتيجة:</span>\n                  <span className=\"font-medium text-green-600\">{certificate.score}%</span>\n                </div>\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"text-gray-600\">المدرب:</span>\n                  <span className=\"font-medium text-gray-900\">{certificate.instructorName}</span>\n                </div>\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"text-gray-600\">رمز التحقق:</span>\n                  <span className=\"font-mono text-xs bg-gray-100 px-2 py-1 rounded\">\n                    {certificate.verificationCode}\n                  </span>\n                </div>\n              </div>\n\n              {/* Actions */}\n              <div className=\"flex items-center justify-between pt-4 border-t border-gray-200\">\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <button\n                    onClick={() => handleViewCertificate(certificate.id)}\n                    className=\"p-2 text-gray-600 hover:text-blue-600 transition-colors\"\n                    title=\"عرض الشهادة\"\n                  >\n                    <EyeIcon className=\"w-4 h-4\" />\n                  </button>\n                  <button\n                    onClick={() => handleDownloadCertificate(certificate.id)}\n                    className=\"p-2 text-gray-600 hover:text-green-600 transition-colors\"\n                    title=\"تحميل الشهادة\"\n                  >\n                    <ArrowDownTrayIcon className=\"w-4 h-4\" />\n                  </button>\n                  <button\n                    onClick={() => handleShareCertificate(certificate.id)}\n                    className=\"p-2 text-gray-600 hover:text-purple-600 transition-colors\"\n                    title=\"مشاركة الشهادة\"\n                  >\n                    <ShareIcon className=\"w-4 h-4\" />\n                  </button>\n                </div>\n                <button\n                  onClick={() => handleViewCertificate(certificate.id)}\n                  className=\"text-sm bg-blue-600 text-white px-3 py-1 rounded-lg hover:bg-blue-700 transition-colors\"\n                >\n                  عرض\n                </button>\n              </div>\n            </div>\n          </motion.div>\n        ))}\n      </div>\n\n      {certificates.length === 0 && (\n        <div className=\"text-center py-12\">\n          <DocumentIcon className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">لا توجد شهادات</h3>\n          <p className=\"text-gray-600\">لم تحصل على أي شهادات بعد. أكمل الكورسات للحصول على الشهادات</p>\n        </div>\n      )}\n\n      {/* Achievement Stats */}\n      {certificates.length > 0 && (\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.3 }}\n          className=\"bg-white rounded-lg shadow-sm p-6\"\n        >\n          <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">إحصائيات الإنجازات</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div className=\"text-center p-4 bg-blue-50 rounded-lg\">\n              <div className=\"text-2xl font-bold text-blue-600 mb-1\">{certificates.length}</div>\n              <div className=\"text-sm text-gray-600\">إجمالي الشهادات</div>\n            </div>\n            <div className=\"text-center p-4 bg-green-50 rounded-lg\">\n              <div className=\"text-2xl font-bold text-green-600 mb-1\">\n                {Math.round(certificates.reduce((sum, cert) => sum + cert.score, 0) / certificates.length)}%\n              </div>\n              <div className=\"text-sm text-gray-600\">متوسط النتائج</div>\n            </div>\n            <div className=\"text-center p-4 bg-purple-50 rounded-lg\">\n              <div className=\"text-2xl font-bold text-purple-600 mb-1\">\n                {certificates.filter(cert => cert.score >= 90).length}\n              </div>\n              <div className=\"text-sm text-gray-600\">شهادات ممتازة</div>\n            </div>\n          </div>\n        </motion.div>\n      )}\n    </div>\n  );\n};\n\nexport default MyCertificates;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAoB,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,YAAY,EACZC,cAAc,EACdC,OAAO,EACPC,iBAAiB,EACjBC,SAAS,QACJ,6BAA6B;;AAEpC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAQA,MAAMC,cAA6C,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAO,CAAC,KAAK;EAC1E;EACA,MAAMC,YAAY,GAAG,CACnB;IACEC,EAAE,EAAE,GAAG;IACPC,WAAW,EAAE,iBAAiB;IAC9BC,UAAU,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;IAClCC,gBAAgB,EAAE,eAAe;IACjCC,KAAK,EAAE,EAAE;IACTC,cAAc,EAAE,iBAAiB;IACjCC,cAAc,EAAE;EAClB,CAAC,EACD;IACEP,EAAE,EAAE,GAAG;IACPC,WAAW,EAAE,uBAAuB;IACpCC,UAAU,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;IAClCC,gBAAgB,EAAE,eAAe;IACjCC,KAAK,EAAE,EAAE;IACTC,cAAc,EAAE,iBAAiB;IACjCC,cAAc,EAAE;EAClB,CAAC,CACF;EAED,MAAMC,qBAAqB,GAAIC,aAAqB,IAAK;IACvDC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEF,aAAa,CAAC;IAC/C;EACF,CAAC;EAED,MAAMG,yBAAyB,GAAIH,aAAqB,IAAK;IAC3DC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEF,aAAa,CAAC;IACnD;EACF,CAAC;EAED,MAAMI,sBAAsB,GAAIJ,aAAqB,IAAK;IACxDC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,aAAa,CAAC;IAChD;EACF,CAAC;EAED,oBACEd,OAAA;IAAKmB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBpB,OAAA;MAAKmB,SAAS,EAAC,6CAA6C;MAAAC,QAAA,GACzDjB,MAAM,iBACLH,OAAA;QACEqB,OAAO,EAAElB,MAAO;QAChBgB,SAAS,EAAC,yDAAyD;QAAAC,QAAA,eAEnEpB,OAAA;UAAKmB,SAAS,EAAC,SAAS;UAACG,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAJ,QAAA,eAC5EpB,OAAA;YAAMyB,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,WAAW,EAAE,CAAE;YAACC,CAAC,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACT,eACDhC,OAAA;QAAAoB,QAAA,gBACEpB,OAAA;UAAImB,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAO;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7DhC,OAAA;UAAGmB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA6B;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhC,OAAA;MAAKmB,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClEhB,YAAY,CAAC6B,GAAG,CAAC,CAACC,WAAW,EAAEC,KAAK,kBACnCnC,OAAA,CAACP,MAAM,CAAC2C,GAAG;QAETC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAEP,KAAK,GAAG;QAAI,CAAE;QACnChB,SAAS,EAAC,wGAAwG;QAAAC,QAAA,gBAGlHpB,OAAA;UAAKmB,SAAS,EAAC,8DAA8D;UAAAC,QAAA,eAC3EpB,OAAA;YAAKmB,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BpB,OAAA,CAACL,cAAc;cAACwB,SAAS,EAAC;YAAwC;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrEhC,OAAA;cAAImB,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAW;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvDhC,OAAA;cAAGmB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAEc,WAAW,CAAC5B;YAAW;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhC,OAAA;UAAKmB,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBpB,OAAA;YAAKmB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BpB,OAAA;cAAKmB,SAAS,EAAC,2CAA2C;cAAAC,QAAA,gBACxDpB,OAAA;gBAAMmB,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAc;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrDhC,OAAA;gBAAMmB,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EACxCc,WAAW,CAAC3B,UAAU,CAACoC,kBAAkB,CAAC,OAAO;cAAC;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNhC,OAAA;cAAKmB,SAAS,EAAC,2CAA2C;cAAAC,QAAA,gBACxDpB,OAAA;gBAAMmB,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAQ;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/ChC,OAAA;gBAAMmB,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,GAAEc,WAAW,CAACxB,KAAK,EAAC,GAAC;cAAA;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,eACNhC,OAAA;cAAKmB,SAAS,EAAC,2CAA2C;cAAAC,QAAA,gBACxDpB,OAAA;gBAAMmB,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAO;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9ChC,OAAA;gBAAMmB,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAEc,WAAW,CAACvB;cAAc;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5E,CAAC,eACNhC,OAAA;cAAKmB,SAAS,EAAC,2CAA2C;cAAAC,QAAA,gBACxDpB,OAAA;gBAAMmB,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAW;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClDhC,OAAA;gBAAMmB,SAAS,EAAC,iDAAiD;gBAAAC,QAAA,EAC9Dc,WAAW,CAACzB;cAAgB;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNhC,OAAA;YAAKmB,SAAS,EAAC,iEAAiE;YAAAC,QAAA,gBAC9EpB,OAAA;cAAKmB,SAAS,EAAC,6CAA6C;cAAAC,QAAA,gBAC1DpB,OAAA;gBACEqB,OAAO,EAAEA,CAAA,KAAMR,qBAAqB,CAACqB,WAAW,CAAC7B,EAAE,CAAE;gBACrDc,SAAS,EAAC,yDAAyD;gBACnEyB,KAAK,EAAC,+DAAa;gBAAAxB,QAAA,eAEnBpB,OAAA,CAACJ,OAAO;kBAACuB,SAAS,EAAC;gBAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eACThC,OAAA;gBACEqB,OAAO,EAAEA,CAAA,KAAMJ,yBAAyB,CAACiB,WAAW,CAAC7B,EAAE,CAAE;gBACzDc,SAAS,EAAC,0DAA0D;gBACpEyB,KAAK,EAAC,2EAAe;gBAAAxB,QAAA,eAErBpB,OAAA,CAACH,iBAAiB;kBAACsB,SAAS,EAAC;gBAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACThC,OAAA;gBACEqB,OAAO,EAAEA,CAAA,KAAMH,sBAAsB,CAACgB,WAAW,CAAC7B,EAAE,CAAE;gBACtDc,SAAS,EAAC,2DAA2D;gBACrEyB,KAAK,EAAC,iFAAgB;gBAAAxB,QAAA,eAEtBpB,OAAA,CAACF,SAAS;kBAACqB,SAAS,EAAC;gBAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNhC,OAAA;cACEqB,OAAO,EAAEA,CAAA,KAAMR,qBAAqB,CAACqB,WAAW,CAAC7B,EAAE,CAAE;cACrDc,SAAS,EAAC,yFAAyF;cAAAC,QAAA,EACpG;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,GAxEDE,WAAW,CAAC7B,EAAE;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAyET,CACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAEL5B,YAAY,CAACyC,MAAM,KAAK,CAAC,iBACxB7C,OAAA;MAAKmB,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCpB,OAAA,CAACN,YAAY;QAACyB,SAAS,EAAC;MAAsC;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjEhC,OAAA;QAAImB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAc;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1EhC,OAAA;QAAGmB,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAA4D;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1F,CACN,EAGA5B,YAAY,CAACyC,MAAM,GAAG,CAAC,iBACtB7C,OAAA,CAACP,MAAM,CAAC2C,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BE,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAC3BvB,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAE7CpB,OAAA;QAAImB,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAAkB;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChFhC,OAAA;QAAKmB,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDpB,OAAA;UAAKmB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDpB,OAAA;YAAKmB,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAEhB,YAAY,CAACyC;UAAM;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClFhC,OAAA;YAAKmB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAe;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACNhC,OAAA;UAAKmB,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDpB,OAAA;YAAKmB,SAAS,EAAC,wCAAwC;YAAAC,QAAA,GACpD0B,IAAI,CAACC,KAAK,CAAC3C,YAAY,CAAC4C,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAACxC,KAAK,EAAE,CAAC,CAAC,GAAGN,YAAY,CAACyC,MAAM,CAAC,EAAC,GAC7F;UAAA;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNhC,OAAA;YAAKmB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAa;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,eACNhC,OAAA;UAAKmB,SAAS,EAAC,yCAAyC;UAAAC,QAAA,gBACtDpB,OAAA;YAAKmB,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EACrDhB,YAAY,CAAC+C,MAAM,CAACD,IAAI,IAAIA,IAAI,CAACxC,KAAK,IAAI,EAAE,CAAC,CAACmC;UAAM;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACNhC,OAAA;YAAKmB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAa;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACb;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACoB,EAAA,GAlLInD,cAA6C;AAoLnD,eAAeA,cAAc;AAAC,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}