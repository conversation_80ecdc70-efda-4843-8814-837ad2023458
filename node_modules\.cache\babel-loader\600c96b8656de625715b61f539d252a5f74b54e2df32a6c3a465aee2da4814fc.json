{"ast": null, "code": "import{initializeApp}from'firebase/app';import{getAuth}from'firebase/auth';import{getFirestore}from'firebase/firestore';import{getStorage}from'firebase/storage';import{getFunctions}from'firebase/functions';const firebaseConfig={apiKey:\"AIzaSyDXQJ8QJ8QJ8QJ8QJ8QJ8QJ8QJ8QJ8QJ8Q\",authDomain:\"alaa-courses-platform.firebaseapp.com\",projectId:\"alaa-courses-platform\",storageBucket:\"alaa-courses-platform.appspot.com\",messagingSenderId:\"341945258779\",appId:\"1:341945258779:web:a1b2c3d4e5f6g7h8i9j0k1l2\"};// Initialize Firebase\nconst app=initializeApp(firebaseConfig);// Initialize Firebase services\nexport const auth=getAuth(app);export const db=getFirestore(app);export const storage=getStorage(app);export const functions=getFunctions(app);export default app;", "map": {"version": 3, "names": ["initializeApp", "getAuth", "getFirestore", "getStorage", "getFunctions", "firebaseConfig", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "projectId", "storageBucket", "messagingSenderId", "appId", "app", "auth", "db", "storage", "functions"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/config/firebase.ts"], "sourcesContent": ["import { initializeApp } from 'firebase/app';\nimport { getAuth } from 'firebase/auth';\nimport { getFirestore } from 'firebase/firestore';\nimport { getStorage } from 'firebase/storage';\nimport { getFunctions } from 'firebase/functions';\n\nconst firebaseConfig = {\n  apiKey: \"AIzaSyDXQJ8QJ8QJ8QJ8QJ8QJ8QJ8QJ8QJ8QJ8Q\",\n  authDomain: \"alaa-courses-platform.firebaseapp.com\",\n  projectId: \"alaa-courses-platform\",\n  storageBucket: \"alaa-courses-platform.appspot.com\",\n  messagingSenderId: \"341945258779\",\n  appId: \"1:341945258779:web:a1b2c3d4e5f6g7h8i9j0k1l2\"\n};\n\n// Initialize Firebase\nconst app = initializeApp(firebaseConfig);\n\n// Initialize Firebase services\nexport const auth = getAuth(app);\nexport const db = getFirestore(app);\nexport const storage = getStorage(app);\nexport const functions = getFunctions(app);\n\nexport default app;\n"], "mappings": "AAAA,OAASA,aAAa,KAAQ,cAAc,CAC5C,OAASC,OAAO,KAAQ,eAAe,CACvC,OAASC,YAAY,KAAQ,oBAAoB,CACjD,OAASC,UAAU,KAAQ,kBAAkB,CAC7C,OAASC,YAAY,KAAQ,oBAAoB,CAEjD,KAAM,CAAAC,cAAc,CAAG,CACrBC,MAAM,CAAE,yCAAyC,CACjDC,UAAU,CAAE,uCAAuC,CACnDC,SAAS,CAAE,uBAAuB,CAClCC,aAAa,CAAE,mCAAmC,CAClDC,iBAAiB,CAAE,cAAc,CACjCC,KAAK,CAAE,6CACT,CAAC,CAED;AACA,KAAM,CAAAC,GAAG,CAAGZ,aAAa,CAACK,cAAc,CAAC,CAEzC;AACA,MAAO,MAAM,CAAAQ,IAAI,CAAGZ,OAAO,CAACW,GAAG,CAAC,CAChC,MAAO,MAAM,CAAAE,EAAE,CAAGZ,YAAY,CAACU,GAAG,CAAC,CACnC,MAAO,MAAM,CAAAG,OAAO,CAAGZ,UAAU,CAACS,GAAG,CAAC,CACtC,MAAO,MAAM,CAAAI,SAAS,CAAGZ,YAAY,CAACQ,GAAG,CAAC,CAE1C,cAAe,CAAAA,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}