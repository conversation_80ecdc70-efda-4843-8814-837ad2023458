{"ast": null, "code": "import React,{useState}from'react';import{motion}from'framer-motion';import{ClipboardDocumentListIcon,ClockIcon,CheckCircleIcon,XCircleIcon}from'@heroicons/react/24/outline';// Types\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const QuizPage=_ref=>{let{user,quizId,onBack}=_ref;const[currentQuestion,setCurrentQuestion]=useState(0);const[answers,setAnswers]=useState({});const[timeLeft,setTimeLeft]=useState(1800);// 30 minutes in seconds\nconst[quizCompleted,setQuizCompleted]=useState(false);const[score,setScore]=useState(null);// Mock quiz data\nconst quiz={id:quizId,title:'اختبار أساسيات البرمجة',description:'اختبار شامل لأساسيات البرمجة',timeLimit:30,passingScore:70,questions:[{id:1,question:'ما هو المتغير في البرمجة؟',type:'multiple-choice',options:['مكان لتخزين البيانات','نوع من الدوال','أمر برمجي','لا شيء مما سبق'],correctAnswer:0},{id:2,question:'أي من التالي يُستخدم لإنشاء حلقة تكرارية؟',type:'multiple-choice',options:['if','for','function','variable'],correctAnswer:1},{id:3,question:'البرمجة الكائنية تعتمد على مفهوم الكلاسات',type:'true-false',options:['صحيح','خطأ'],correctAnswer:0}]};// Format time\nconst formatTime=seconds=>{const minutes=Math.floor(seconds/60);const remainingSeconds=seconds%60;return`${minutes}:${remainingSeconds.toString().padStart(2,'0')}`;};const handleAnswerSelect=answerIndex=>{setAnswers(prev=>({...prev,[currentQuestion]:answerIndex}));};const handleNextQuestion=()=>{if(currentQuestion<quiz.questions.length-1){setCurrentQuestion(prev=>prev+1);}};const handlePrevQuestion=()=>{if(currentQuestion>0){setCurrentQuestion(prev=>prev-1);}};const handleSubmitQuiz=()=>{// Calculate score\nlet correctAnswers=0;quiz.questions.forEach((question,index)=>{if(answers[index]===question.correctAnswer){correctAnswers++;}});const finalScore=Math.round(correctAnswers/quiz.questions.length*100);setScore(finalScore);setQuizCompleted(true);};const currentQuestionData=quiz.questions[currentQuestion];const progress=(currentQuestion+1)/quiz.questions.length*100;if(quizCompleted){return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4 space-x-reverse\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:onBack,className:\"p-2 text-gray-600 hover:text-gray-900 transition-colors\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-6 h-6\",fill:\"none\",stroke:\"currentColor\",viewBox:\"0 0 24 24\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M15 19l-7-7 7-7\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:\"\\u0646\\u062A\\u064A\\u062C\\u0629 \\u0627\\u0644\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:quiz.title})]})]}),/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:\"bg-white rounded-lg shadow-sm p-8 text-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-6\",children:[score>=quiz.passingScore?/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"w-16 h-16 text-green-600 mx-auto mb-4\"}):/*#__PURE__*/_jsx(XCircleIcon,{className:\"w-16 h-16 text-red-600 mx-auto mb-4\"}),/*#__PURE__*/_jsxs(\"h2\",{className:\"text-3xl font-bold text-gray-900 mb-2\",children:[score,\"%\"]}),/*#__PURE__*/_jsx(\"p\",{className:`text-lg ${score>=quiz.passingScore?'text-green-600':'text-red-600'}`,children:score>=quiz.passingScore?'مبروك! لقد نجحت في الاختبار':'للأسف، لم تحقق الدرجة المطلوبة'})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gray-50 rounded-lg p-4\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:\"\\u0627\\u0644\\u0646\\u062A\\u064A\\u062C\\u0629\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-2xl font-bold text-gray-900\",children:[score,\"%\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gray-50 rounded-lg p-4\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:\"\\u0627\\u0644\\u062F\\u0631\\u062C\\u0629 \\u0627\\u0644\\u0645\\u0637\\u0644\\u0648\\u0628\\u0629\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-2xl font-bold text-gray-900\",children:[quiz.passingScore,\"%\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gray-50 rounded-lg p-4\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:\"\\u0627\\u0644\\u0623\\u0633\\u0626\\u0644\\u0629 \\u0627\\u0644\\u0635\\u062D\\u064A\\u062D\\u0629\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-2xl font-bold text-gray-900\",children:[Math.round(score/100*quiz.questions.length),\"/\",quiz.questions.length]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-center space-x-4 space-x-reverse\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:onBack,className:\"px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",children:\"\\u0627\\u0644\\u0639\\u0648\\u062F\\u0629 \\u0644\\u0644\\u0643\\u0648\\u0631\\u0633\"}),score<quiz.passingScore&&/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setQuizCompleted(false);setCurrentQuestion(0);setAnswers({});setScore(null);},className:\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",children:\"\\u0625\\u0639\\u0627\\u062F\\u0629 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0648\\u0644\\u0629\"})]})]})]});}return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4 space-x-reverse\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:onBack,className:\"p-2 text-gray-600 hover:text-gray-900 transition-colors\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-6 h-6\",fill:\"none\",stroke:\"currentColor\",viewBox:\"0 0 24 24\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M15 19l-7-7 7-7\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:quiz.title}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:quiz.description})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 space-x-reverse bg-red-50 px-4 py-2 rounded-lg\",children:[/*#__PURE__*/_jsx(ClockIcon,{className:\"w-5 h-5 text-red-600\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-red-600 font-medium\",children:formatTime(timeLeft)})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-sm p-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-2\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm text-gray-600\",children:[\"\\u0627\\u0644\\u0633\\u0624\\u0627\\u0644 \",currentQuestion+1,\" \\u0645\\u0646 \",quiz.questions.length]}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm text-gray-600\",children:[Math.round(progress),\"%\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"w-full bg-gray-200 rounded-full h-2\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-blue-600 h-2 rounded-full transition-all duration-300\",style:{width:`${progress}%`}})})]}),/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},className:\"bg-white rounded-lg shadow-sm p-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 space-x-reverse mb-4\",children:[/*#__PURE__*/_jsx(ClipboardDocumentListIcon,{className:\"w-6 h-6 text-blue-600\"}),/*#__PURE__*/_jsxs(\"h2\",{className:\"text-lg font-semibold text-gray-900\",children:[\"\\u0627\\u0644\\u0633\\u0624\\u0627\\u0644 \",currentQuestion+1]})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-900 text-lg leading-relaxed\",children:currentQuestionData.question})]}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-3 mb-6\",children:currentQuestionData.options.map((option,index)=>/*#__PURE__*/_jsxs(\"label\",{className:`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-colors ${answers[currentQuestion]===index?'border-blue-500 bg-blue-50':'border-gray-200 hover:border-gray-300'}`,children:[/*#__PURE__*/_jsx(\"input\",{type:\"radio\",name:`question-${currentQuestion}`,value:index,checked:answers[currentQuestion]===index,onChange:()=>handleAnswerSelect(index),className:\"sr-only\"}),/*#__PURE__*/_jsx(\"div\",{className:`w-4 h-4 rounded-full border-2 mr-3 ${answers[currentQuestion]===index?'border-blue-500 bg-blue-500':'border-gray-300'}`,children:answers[currentQuestion]===index&&/*#__PURE__*/_jsx(\"div\",{className:\"w-2 h-2 bg-white rounded-full mx-auto mt-0.5\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-900\",children:option})]},index))}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:handlePrevQuestion,disabled:currentQuestion===0,className:\"px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",children:\"\\u0627\\u0644\\u0633\\u0624\\u0627\\u0644 \\u0627\\u0644\\u0633\\u0627\\u0628\\u0642\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex space-x-2 space-x-reverse\",children:currentQuestion===quiz.questions.length-1?/*#__PURE__*/_jsx(\"button\",{onClick:handleSubmitQuiz,className:\"px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",children:\"\\u0625\\u0646\\u0647\\u0627\\u0621 \\u0627\\u0644\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\"}):/*#__PURE__*/_jsx(\"button\",{onClick:handleNextQuestion,className:\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",children:\"\\u0627\\u0644\\u0633\\u0624\\u0627\\u0644 \\u0627\\u0644\\u062A\\u0627\\u0644\\u064A\"})})]})]},currentQuestion)]});};export default QuizPage;", "map": {"version": 3, "names": ["React", "useState", "motion", "ClipboardDocumentListIcon", "ClockIcon", "CheckCircleIcon", "XCircleIcon", "jsx", "_jsx", "jsxs", "_jsxs", "QuizPage", "_ref", "user", "quizId", "onBack", "currentQuestion", "setCurrentQuestion", "answers", "setAnswers", "timeLeft", "setTimeLeft", "quizCompleted", "setQuizCompleted", "score", "setScore", "quiz", "id", "title", "description", "timeLimit", "passingScore", "questions", "question", "type", "options", "<PERSON><PERSON><PERSON><PERSON>", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "toString", "padStart", "handleAnswerSelect", "answerIndex", "prev", "handleNextQuestion", "length", "handlePrevQuestion", "handleSubmitQuiz", "correctAnswers", "for<PERSON>ach", "index", "finalScore", "round", "currentQuestionData", "progress", "className", "children", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "div", "initial", "opacity", "y", "animate", "style", "width", "x", "map", "option", "name", "value", "checked", "onChange", "disabled"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Student/QuizPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  ClipboardDocumentListIcon,\n  ClockIcon,\n  CheckCircleIcon,\n  XCircleIcon\n} from '@heroicons/react/24/outline';\n\n// Types\nimport { Student } from '../../types';\n\ninterface QuizPageProps {\n  user?: Student;\n  quizId?: string;\n  onBack?: () => void;\n}\n\nconst QuizPage: React.FC<QuizPageProps> = ({ user, quizId, onBack }) => {\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [answers, setAnswers] = useState<{ [key: number]: any }>({});\n  const [timeLeft, setTimeLeft] = useState(1800); // 30 minutes in seconds\n  const [quizCompleted, setQuizCompleted] = useState(false);\n  const [score, setScore] = useState<number | null>(null);\n\n  // Mock quiz data\n  const quiz = {\n    id: quizId,\n    title: 'اختبار أساسيات البرمجة',\n    description: 'اختبار شامل لأساسيات البرمجة',\n    timeLimit: 30,\n    passingScore: 70,\n    questions: [\n      {\n        id: 1,\n        question: 'ما هو المتغير في البرمجة؟',\n        type: 'multiple-choice',\n        options: [\n          'مكان لتخزين البيانات',\n          'نوع من الدوال',\n          'أمر برمجي',\n          'لا شيء مما سبق'\n        ],\n        correctAnswer: 0\n      },\n      {\n        id: 2,\n        question: 'أي من التالي يُستخدم لإنشاء حلقة تكرارية؟',\n        type: 'multiple-choice',\n        options: [\n          'if',\n          'for',\n          'function',\n          'variable'\n        ],\n        correctAnswer: 1\n      },\n      {\n        id: 3,\n        question: 'البرمجة الكائنية تعتمد على مفهوم الكلاسات',\n        type: 'true-false',\n        options: ['صحيح', 'خطأ'],\n        correctAnswer: 0\n      }\n    ]\n  };\n\n  // Format time\n  const formatTime = (seconds: number) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  const handleAnswerSelect = (answerIndex: number) => {\n    setAnswers(prev => ({\n      ...prev,\n      [currentQuestion]: answerIndex\n    }));\n  };\n\n  const handleNextQuestion = () => {\n    if (currentQuestion < quiz.questions.length - 1) {\n      setCurrentQuestion(prev => prev + 1);\n    }\n  };\n\n  const handlePrevQuestion = () => {\n    if (currentQuestion > 0) {\n      setCurrentQuestion(prev => prev - 1);\n    }\n  };\n\n  const handleSubmitQuiz = () => {\n    // Calculate score\n    let correctAnswers = 0;\n    quiz.questions.forEach((question, index) => {\n      if (answers[index] === question.correctAnswer) {\n        correctAnswers++;\n      }\n    });\n    \n    const finalScore = Math.round((correctAnswers / quiz.questions.length) * 100);\n    setScore(finalScore);\n    setQuizCompleted(true);\n  };\n\n  const currentQuestionData = quiz.questions[currentQuestion];\n  const progress = ((currentQuestion + 1) / quiz.questions.length) * 100;\n\n  if (quizCompleted) {\n    return (\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"flex items-center space-x-4 space-x-reverse\">\n          <button\n            onClick={onBack}\n            className=\"p-2 text-gray-600 hover:text-gray-900 transition-colors\"\n          >\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n            </svg>\n          </button>\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">نتيجة الاختبار</h1>\n            <p className=\"text-gray-600\">{quiz.title}</p>\n          </div>\n        </div>\n\n        {/* Results */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"bg-white rounded-lg shadow-sm p-8 text-center\"\n        >\n          <div className=\"mb-6\">\n            {score! >= quiz.passingScore ? (\n              <CheckCircleIcon className=\"w-16 h-16 text-green-600 mx-auto mb-4\" />\n            ) : (\n              <XCircleIcon className=\"w-16 h-16 text-red-600 mx-auto mb-4\" />\n            )}\n            \n            <h2 className=\"text-3xl font-bold text-gray-900 mb-2\">{score}%</h2>\n            <p className={`text-lg ${score! >= quiz.passingScore ? 'text-green-600' : 'text-red-600'}`}>\n              {score! >= quiz.passingScore ? 'مبروك! لقد نجحت في الاختبار' : 'للأسف، لم تحقق الدرجة المطلوبة'}\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\">\n            <div className=\"bg-gray-50 rounded-lg p-4\">\n              <p className=\"text-sm text-gray-600\">النتيجة</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{score}%</p>\n            </div>\n            <div className=\"bg-gray-50 rounded-lg p-4\">\n              <p className=\"text-sm text-gray-600\">الدرجة المطلوبة</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{quiz.passingScore}%</p>\n            </div>\n            <div className=\"bg-gray-50 rounded-lg p-4\">\n              <p className=\"text-sm text-gray-600\">الأسئلة الصحيحة</p>\n              <p className=\"text-2xl font-bold text-gray-900\">\n                {Math.round((score! / 100) * quiz.questions.length)}/{quiz.questions.length}\n              </p>\n            </div>\n          </div>\n\n          <div className=\"flex justify-center space-x-4 space-x-reverse\">\n            <button\n              onClick={onBack}\n              className=\"px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\"\n            >\n              العودة للكورس\n            </button>\n            {score! < quiz.passingScore && (\n              <button\n                onClick={() => {\n                  setQuizCompleted(false);\n                  setCurrentQuestion(0);\n                  setAnswers({});\n                  setScore(null);\n                }}\n                className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n              >\n                إعادة المحاولة\n              </button>\n            )}\n          </div>\n        </motion.div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4 space-x-reverse\">\n          <button\n            onClick={onBack}\n            className=\"p-2 text-gray-600 hover:text-gray-900 transition-colors\"\n          >\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n            </svg>\n          </button>\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">{quiz.title}</h1>\n            <p className=\"text-gray-600\">{quiz.description}</p>\n          </div>\n        </div>\n        \n        {/* Timer */}\n        <div className=\"flex items-center space-x-2 space-x-reverse bg-red-50 px-4 py-2 rounded-lg\">\n          <ClockIcon className=\"w-5 h-5 text-red-600\" />\n          <span className=\"text-red-600 font-medium\">{formatTime(timeLeft)}</span>\n        </div>\n      </div>\n\n      {/* Progress */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <div className=\"flex items-center justify-between mb-2\">\n          <span className=\"text-sm text-gray-600\">السؤال {currentQuestion + 1} من {quiz.questions.length}</span>\n          <span className=\"text-sm text-gray-600\">{Math.round(progress)}%</span>\n        </div>\n        <div className=\"w-full bg-gray-200 rounded-full h-2\">\n          <div\n            className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n            style={{ width: `${progress}%` }}\n          ></div>\n        </div>\n      </div>\n\n      {/* Question */}\n      <motion.div\n        key={currentQuestion}\n        initial={{ opacity: 0, x: 20 }}\n        animate={{ opacity: 1, x: 0 }}\n        className=\"bg-white rounded-lg shadow-sm p-6\"\n      >\n        <div className=\"mb-6\">\n          <div className=\"flex items-center space-x-3 space-x-reverse mb-4\">\n            <ClipboardDocumentListIcon className=\"w-6 h-6 text-blue-600\" />\n            <h2 className=\"text-lg font-semibold text-gray-900\">\n              السؤال {currentQuestion + 1}\n            </h2>\n          </div>\n          <p className=\"text-gray-900 text-lg leading-relaxed\">\n            {currentQuestionData.question}\n          </p>\n        </div>\n\n        {/* Answer Options */}\n        <div className=\"space-y-3 mb-6\">\n          {currentQuestionData.options.map((option, index) => (\n            <label\n              key={index}\n              className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-colors ${\n                answers[currentQuestion] === index\n                  ? 'border-blue-500 bg-blue-50'\n                  : 'border-gray-200 hover:border-gray-300'\n              }`}\n            >\n              <input\n                type=\"radio\"\n                name={`question-${currentQuestion}`}\n                value={index}\n                checked={answers[currentQuestion] === index}\n                onChange={() => handleAnswerSelect(index)}\n                className=\"sr-only\"\n              />\n              <div className={`w-4 h-4 rounded-full border-2 mr-3 ${\n                answers[currentQuestion] === index\n                  ? 'border-blue-500 bg-blue-500'\n                  : 'border-gray-300'\n              }`}>\n                {answers[currentQuestion] === index && (\n                  <div className=\"w-2 h-2 bg-white rounded-full mx-auto mt-0.5\"></div>\n                )}\n              </div>\n              <span className=\"text-gray-900\">{option}</span>\n            </label>\n          ))}\n        </div>\n\n        {/* Navigation */}\n        <div className=\"flex items-center justify-between\">\n          <button\n            onClick={handlePrevQuestion}\n            disabled={currentQuestion === 0}\n            className=\"px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          >\n            السؤال السابق\n          </button>\n\n          <div className=\"flex space-x-2 space-x-reverse\">\n            {currentQuestion === quiz.questions.length - 1 ? (\n              <button\n                onClick={handleSubmitQuiz}\n                className=\"px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"\n              >\n                إنهاء الاختبار\n              </button>\n            ) : (\n              <button\n                onClick={handleNextQuestion}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n              >\n                السؤال التالي\n              </button>\n            )}\n          </div>\n        </div>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default QuizPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,MAAM,KAAQ,eAAe,CACtC,OACEC,yBAAyB,CACzBC,SAAS,CACTC,eAAe,CACfC,WAAW,KACN,6BAA6B,CAEpC;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBASA,KAAM,CAAAC,QAAiC,CAAGC,IAAA,EAA8B,IAA7B,CAAEC,IAAI,CAAEC,MAAM,CAAEC,MAAO,CAAC,CAAAH,IAAA,CACjE,KAAM,CAACI,eAAe,CAAEC,kBAAkB,CAAC,CAAGhB,QAAQ,CAAC,CAAC,CAAC,CACzD,KAAM,CAACiB,OAAO,CAAEC,UAAU,CAAC,CAAGlB,QAAQ,CAAyB,CAAC,CAAC,CAAC,CAClE,KAAM,CAACmB,QAAQ,CAAEC,WAAW,CAAC,CAAGpB,QAAQ,CAAC,IAAI,CAAC,CAAE;AAChD,KAAM,CAACqB,aAAa,CAAEC,gBAAgB,CAAC,CAAGtB,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACuB,KAAK,CAAEC,QAAQ,CAAC,CAAGxB,QAAQ,CAAgB,IAAI,CAAC,CAEvD;AACA,KAAM,CAAAyB,IAAI,CAAG,CACXC,EAAE,CAAEb,MAAM,CACVc,KAAK,CAAE,wBAAwB,CAC/BC,WAAW,CAAE,8BAA8B,CAC3CC,SAAS,CAAE,EAAE,CACbC,YAAY,CAAE,EAAE,CAChBC,SAAS,CAAE,CACT,CACEL,EAAE,CAAE,CAAC,CACLM,QAAQ,CAAE,2BAA2B,CACrCC,IAAI,CAAE,iBAAiB,CACvBC,OAAO,CAAE,CACP,sBAAsB,CACtB,eAAe,CACf,WAAW,CACX,gBAAgB,CACjB,CACDC,aAAa,CAAE,CACjB,CAAC,CACD,CACET,EAAE,CAAE,CAAC,CACLM,QAAQ,CAAE,2CAA2C,CACrDC,IAAI,CAAE,iBAAiB,CACvBC,OAAO,CAAE,CACP,IAAI,CACJ,KAAK,CACL,UAAU,CACV,UAAU,CACX,CACDC,aAAa,CAAE,CACjB,CAAC,CACD,CACET,EAAE,CAAE,CAAC,CACLM,QAAQ,CAAE,2CAA2C,CACrDC,IAAI,CAAE,YAAY,CAClBC,OAAO,CAAE,CAAC,MAAM,CAAE,KAAK,CAAC,CACxBC,aAAa,CAAE,CACjB,CAAC,CAEL,CAAC,CAED;AACA,KAAM,CAAAC,UAAU,CAAIC,OAAe,EAAK,CACtC,KAAM,CAAAC,OAAO,CAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAG,EAAE,CAAC,CACxC,KAAM,CAAAI,gBAAgB,CAAGJ,OAAO,CAAG,EAAE,CACrC,MAAO,GAAGC,OAAO,IAAIG,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAAE,CACrE,CAAC,CAED,KAAM,CAAAC,kBAAkB,CAAIC,WAAmB,EAAK,CAClD3B,UAAU,CAAC4B,IAAI,GAAK,CAClB,GAAGA,IAAI,CACP,CAAC/B,eAAe,EAAG8B,WACrB,CAAC,CAAC,CAAC,CACL,CAAC,CAED,KAAM,CAAAE,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,GAAIhC,eAAe,CAAGU,IAAI,CAACM,SAAS,CAACiB,MAAM,CAAG,CAAC,CAAE,CAC/ChC,kBAAkB,CAAC8B,IAAI,EAAIA,IAAI,CAAG,CAAC,CAAC,CACtC,CACF,CAAC,CAED,KAAM,CAAAG,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,GAAIlC,eAAe,CAAG,CAAC,CAAE,CACvBC,kBAAkB,CAAC8B,IAAI,EAAIA,IAAI,CAAG,CAAC,CAAC,CACtC,CACF,CAAC,CAED,KAAM,CAAAI,gBAAgB,CAAGA,CAAA,GAAM,CAC7B;AACA,GAAI,CAAAC,cAAc,CAAG,CAAC,CACtB1B,IAAI,CAACM,SAAS,CAACqB,OAAO,CAAC,CAACpB,QAAQ,CAAEqB,KAAK,GAAK,CAC1C,GAAIpC,OAAO,CAACoC,KAAK,CAAC,GAAKrB,QAAQ,CAACG,aAAa,CAAE,CAC7CgB,cAAc,EAAE,CAClB,CACF,CAAC,CAAC,CAEF,KAAM,CAAAG,UAAU,CAAGf,IAAI,CAACgB,KAAK,CAAEJ,cAAc,CAAG1B,IAAI,CAACM,SAAS,CAACiB,MAAM,CAAI,GAAG,CAAC,CAC7ExB,QAAQ,CAAC8B,UAAU,CAAC,CACpBhC,gBAAgB,CAAC,IAAI,CAAC,CACxB,CAAC,CAED,KAAM,CAAAkC,mBAAmB,CAAG/B,IAAI,CAACM,SAAS,CAAChB,eAAe,CAAC,CAC3D,KAAM,CAAA0C,QAAQ,CAAI,CAAC1C,eAAe,CAAG,CAAC,EAAIU,IAAI,CAACM,SAAS,CAACiB,MAAM,CAAI,GAAG,CAEtE,GAAI3B,aAAa,CAAE,CACjB,mBACEZ,KAAA,QAAKiD,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBlD,KAAA,QAAKiD,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DpD,IAAA,WACEqD,OAAO,CAAE9C,MAAO,CAChB4C,SAAS,CAAC,yDAAyD,CAAAC,QAAA,cAEnEpD,IAAA,QAAKmD,SAAS,CAAC,SAAS,CAACG,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAJ,QAAA,cAC5EpD,IAAA,SAAMyD,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,WAAW,CAAE,CAAE,CAACC,CAAC,CAAC,iBAAiB,CAAE,CAAC,CACtF,CAAC,CACA,CAAC,cACT1D,KAAA,QAAAkD,QAAA,eACEpD,IAAA,OAAImD,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,iFAAc,CAAI,CAAC,cACpEpD,IAAA,MAAGmD,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAElC,IAAI,CAACE,KAAK,CAAI,CAAC,EAC1C,CAAC,EACH,CAAC,cAGNlB,KAAA,CAACR,MAAM,CAACmE,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9Bb,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eAEzDlD,KAAA,QAAKiD,SAAS,CAAC,MAAM,CAAAC,QAAA,EAClBpC,KAAK,EAAKE,IAAI,CAACK,YAAY,cAC1BvB,IAAA,CAACH,eAAe,EAACsD,SAAS,CAAC,uCAAuC,CAAE,CAAC,cAErEnD,IAAA,CAACF,WAAW,EAACqD,SAAS,CAAC,qCAAqC,CAAE,CAC/D,cAEDjD,KAAA,OAAIiD,SAAS,CAAC,uCAAuC,CAAAC,QAAA,EAAEpC,KAAK,CAAC,GAAC,EAAI,CAAC,cACnEhB,IAAA,MAAGmD,SAAS,CAAE,WAAWnC,KAAK,EAAKE,IAAI,CAACK,YAAY,CAAG,gBAAgB,CAAG,cAAc,EAAG,CAAA6B,QAAA,CACxFpC,KAAK,EAAKE,IAAI,CAACK,YAAY,CAAG,6BAA6B,CAAG,gCAAgC,CAC9F,CAAC,EACD,CAAC,cAENrB,KAAA,QAAKiD,SAAS,CAAC,4CAA4C,CAAAC,QAAA,eACzDlD,KAAA,QAAKiD,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxCpD,IAAA,MAAGmD,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,4CAAO,CAAG,CAAC,cAChDlD,KAAA,MAAGiD,SAAS,CAAC,kCAAkC,CAAAC,QAAA,EAAEpC,KAAK,CAAC,GAAC,EAAG,CAAC,EACzD,CAAC,cACNd,KAAA,QAAKiD,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxCpD,IAAA,MAAGmD,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,uFAAe,CAAG,CAAC,cACxDlD,KAAA,MAAGiD,SAAS,CAAC,kCAAkC,CAAAC,QAAA,EAAElC,IAAI,CAACK,YAAY,CAAC,GAAC,EAAG,CAAC,EACrE,CAAC,cACNrB,KAAA,QAAKiD,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxCpD,IAAA,MAAGmD,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,uFAAe,CAAG,CAAC,cACxDlD,KAAA,MAAGiD,SAAS,CAAC,kCAAkC,CAAAC,QAAA,EAC5CpB,IAAI,CAACgB,KAAK,CAAEhC,KAAK,CAAI,GAAG,CAAIE,IAAI,CAACM,SAAS,CAACiB,MAAM,CAAC,CAAC,GAAC,CAACvB,IAAI,CAACM,SAAS,CAACiB,MAAM,EAC1E,CAAC,EACD,CAAC,EACH,CAAC,cAENvC,KAAA,QAAKiD,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eAC5DpD,IAAA,WACEqD,OAAO,CAAE9C,MAAO,CAChB4C,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAC5F,2EAED,CAAQ,CAAC,CACRpC,KAAK,CAAIE,IAAI,CAACK,YAAY,eACzBvB,IAAA,WACEqD,OAAO,CAAEA,CAAA,GAAM,CACbtC,gBAAgB,CAAC,KAAK,CAAC,CACvBN,kBAAkB,CAAC,CAAC,CAAC,CACrBE,UAAU,CAAC,CAAC,CAAC,CAAC,CACdM,QAAQ,CAAC,IAAI,CAAC,CAChB,CAAE,CACFkC,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAC5F,iFAED,CAAQ,CACT,EACE,CAAC,EACI,CAAC,EACV,CAAC,CAEV,CAEA,mBACElD,KAAA,QAAKiD,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBlD,KAAA,QAAKiD,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDlD,KAAA,QAAKiD,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DpD,IAAA,WACEqD,OAAO,CAAE9C,MAAO,CAChB4C,SAAS,CAAC,yDAAyD,CAAAC,QAAA,cAEnEpD,IAAA,QAAKmD,SAAS,CAAC,SAAS,CAACG,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAJ,QAAA,cAC5EpD,IAAA,SAAMyD,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,WAAW,CAAE,CAAE,CAACC,CAAC,CAAC,iBAAiB,CAAE,CAAC,CACtF,CAAC,CACA,CAAC,cACT1D,KAAA,QAAAkD,QAAA,eACEpD,IAAA,OAAImD,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAElC,IAAI,CAACE,KAAK,CAAK,CAAC,cAClEpB,IAAA,MAAGmD,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAElC,IAAI,CAACG,WAAW,CAAI,CAAC,EAChD,CAAC,EACH,CAAC,cAGNnB,KAAA,QAAKiD,SAAS,CAAC,4EAA4E,CAAAC,QAAA,eACzFpD,IAAA,CAACJ,SAAS,EAACuD,SAAS,CAAC,sBAAsB,CAAE,CAAC,cAC9CnD,IAAA,SAAMmD,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CAAEvB,UAAU,CAACjB,QAAQ,CAAC,CAAO,CAAC,EACrE,CAAC,EACH,CAAC,cAGNV,KAAA,QAAKiD,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDlD,KAAA,QAAKiD,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDlD,KAAA,SAAMiD,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,uCAAO,CAAC5C,eAAe,CAAG,CAAC,CAAC,gBAAI,CAACU,IAAI,CAACM,SAAS,CAACiB,MAAM,EAAO,CAAC,cACtGvC,KAAA,SAAMiD,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAEpB,IAAI,CAACgB,KAAK,CAACE,QAAQ,CAAC,CAAC,GAAC,EAAM,CAAC,EACnE,CAAC,cACNlD,IAAA,QAAKmD,SAAS,CAAC,qCAAqC,CAAAC,QAAA,cAClDpD,IAAA,QACEmD,SAAS,CAAC,0DAA0D,CACpEe,KAAK,CAAE,CAAEC,KAAK,CAAE,GAAGjB,QAAQ,GAAI,CAAE,CAC7B,CAAC,CACJ,CAAC,EACH,CAAC,cAGNhD,KAAA,CAACR,MAAM,CAACmE,GAAG,EAETC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEK,CAAC,CAAE,EAAG,CAAE,CAC/BH,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEK,CAAC,CAAE,CAAE,CAAE,CAC9BjB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAE7ClD,KAAA,QAAKiD,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBlD,KAAA,QAAKiD,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eAC/DpD,IAAA,CAACL,yBAAyB,EAACwD,SAAS,CAAC,uBAAuB,CAAE,CAAC,cAC/DjD,KAAA,OAAIiD,SAAS,CAAC,qCAAqC,CAAAC,QAAA,EAAC,uCAC3C,CAAC5C,eAAe,CAAG,CAAC,EACzB,CAAC,EACF,CAAC,cACNR,IAAA,MAAGmD,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CACjDH,mBAAmB,CAACxB,QAAQ,CAC5B,CAAC,EACD,CAAC,cAGNzB,IAAA,QAAKmD,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAC5BH,mBAAmB,CAACtB,OAAO,CAAC0C,GAAG,CAAC,CAACC,MAAM,CAAExB,KAAK,gBAC7C5C,KAAA,UAEEiD,SAAS,CAAE,8EACTzC,OAAO,CAACF,eAAe,CAAC,GAAKsC,KAAK,CAC9B,4BAA4B,CAC5B,uCAAuC,EAC1C,CAAAM,QAAA,eAEHpD,IAAA,UACE0B,IAAI,CAAC,OAAO,CACZ6C,IAAI,CAAE,YAAY/D,eAAe,EAAG,CACpCgE,KAAK,CAAE1B,KAAM,CACb2B,OAAO,CAAE/D,OAAO,CAACF,eAAe,CAAC,GAAKsC,KAAM,CAC5C4B,QAAQ,CAAEA,CAAA,GAAMrC,kBAAkB,CAACS,KAAK,CAAE,CAC1CK,SAAS,CAAC,SAAS,CACpB,CAAC,cACFnD,IAAA,QAAKmD,SAAS,CAAE,sCACdzC,OAAO,CAACF,eAAe,CAAC,GAAKsC,KAAK,CAC9B,6BAA6B,CAC7B,iBAAiB,EACpB,CAAAM,QAAA,CACA1C,OAAO,CAACF,eAAe,CAAC,GAAKsC,KAAK,eACjC9C,IAAA,QAAKmD,SAAS,CAAC,8CAA8C,CAAM,CACpE,CACE,CAAC,cACNnD,IAAA,SAAMmD,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEkB,MAAM,CAAO,CAAC,GAxB1CxB,KAyBA,CACR,CAAC,CACC,CAAC,cAGN5C,KAAA,QAAKiD,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDpD,IAAA,WACEqD,OAAO,CAAEX,kBAAmB,CAC5BiC,QAAQ,CAAEnE,eAAe,GAAK,CAAE,CAChC2C,SAAS,CAAC,oIAAoI,CAAAC,QAAA,CAC/I,2EAED,CAAQ,CAAC,cAETpD,IAAA,QAAKmD,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAC5C5C,eAAe,GAAKU,IAAI,CAACM,SAAS,CAACiB,MAAM,CAAG,CAAC,cAC5CzC,IAAA,WACEqD,OAAO,CAAEV,gBAAiB,CAC1BQ,SAAS,CAAC,mFAAmF,CAAAC,QAAA,CAC9F,iFAED,CAAQ,CAAC,cAETpD,IAAA,WACEqD,OAAO,CAAEb,kBAAmB,CAC5BW,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAC5F,2EAED,CAAQ,CACT,CACE,CAAC,EACH,CAAC,GA7ED5C,eA8EK,CAAC,EACV,CAAC,CAEV,CAAC,CAED,cAAe,CAAAL,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}