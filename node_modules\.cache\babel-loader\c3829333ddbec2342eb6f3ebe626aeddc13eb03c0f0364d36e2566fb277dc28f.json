{"ast": null, "code": "import{signInWithEmailAndPassword,signOut,onAuthStateChanged}from'firebase/auth';import{doc,getDoc,setDoc}from'firebase/firestore';import{auth,db}from'../config/firebase';import{defaultAdmin,adminCredentials}from'../data/defaultAdmin';import{mockStudents}from'../data/mockStudents';class AuthService{// Admin login\nasync loginAdmin(email,password){try{var _adminData$createdAt;// Check for default admin credentials\nif(email===adminCredentials.email&&password===adminCredentials.password){return defaultAdmin;}const userCredential=await signInWithEmailAndPassword(auth,email,password);const user=userCredential.user;// Get admin data from Firestore\nconst adminDoc=await getDoc(doc(db,'admins',user.uid));if(!adminDoc.exists()){throw new Error('المستخدم غير مخول كمدير');}const adminData=adminDoc.data();return{id:user.uid,email:user.email,role:'admin',name:adminData.name,avatar:adminData.avatar,permissions:adminData.permissions||[],createdAt:((_adminData$createdAt=adminData.createdAt)===null||_adminData$createdAt===void 0?void 0:_adminData$createdAt.toDate())||new Date()};}catch(error){throw new Error(this.getErrorMessage(error.code));}}// Student login with access code\nasync loginStudent(accessCode){try{var _studentData$createdA;// Check for mock students first\nconst mockStudent=mockStudents.find(student=>student.accessCode===accessCode);if(mockStudent){return mockStudent;}// Find student by access code in Firestore\nconst studentsRef=doc(db,'students',accessCode);const studentDoc=await getDoc(studentsRef);if(!studentDoc.exists()){throw new Error('كود الدخول غير صحيح');}const studentData=studentDoc.data();if(!studentData.isActive){throw new Error('الحساب غير مفعل');}return{id:studentData.id,email:studentData.email||'',role:'student',name:studentData.name,avatar:studentData.avatar,accessCode:accessCode,enrolledCourses:studentData.enrolledCourses||[],completedCourses:studentData.completedCourses||[],certificates:studentData.certificates||[],createdAt:((_studentData$createdA=studentData.createdAt)===null||_studentData$createdA===void 0?void 0:_studentData$createdA.toDate())||new Date()};}catch(error){throw new Error(error.message||'حدث خطأ في تسجيل الدخول');}}// Generate access code for student\ngenerateAccessCode(){return Math.floor(1000000+Math.random()*9000000).toString();}// Create student account\nasync createStudent(studentData){try{const accessCode=this.generateAccessCode();// Check if access code already exists\nconst existingStudent=await getDoc(doc(db,'students',accessCode));if(existingStudent.exists()){// Generate new code if exists\nreturn this.createStudent(studentData);}const student={id:accessCode,name:studentData.name,email:studentData.email||'',accessCode:accessCode,enrolledCourses:studentData.enrolledCourses||[],completedCourses:[],certificates:[],isActive:true,createdAt:new Date()};await setDoc(doc(db,'students',accessCode),student);return accessCode;}catch(error){throw new Error('فشل في إنشاء حساب الطالب');}}// Logout\nasync logout(){try{await signOut(auth);}catch(error){throw new Error('فشل في تسجيل الخروج');}}// Get current user\ngetCurrentUser(){return new Promise(resolve=>{const unsubscribe=onAuthStateChanged(auth,user=>{unsubscribe();resolve(user);});});}// Auth state listener\nonAuthStateChange(callback){return onAuthStateChanged(auth,callback);}getErrorMessage(errorCode){switch(errorCode){case'auth/user-not-found':return'المستخدم غير موجود';case'auth/wrong-password':return'كلمة المرور غير صحيحة';case'auth/invalid-email':return'البريد الإلكتروني غير صحيح';case'auth/user-disabled':return'الحساب معطل';case'auth/too-many-requests':return'محاولات كثيرة، حاول مرة أخرى لاحقاً';default:return'حدث خطأ في تسجيل الدخول';}}}export const authService=new AuthService();", "map": {"version": 3, "names": ["signInWithEmailAndPassword", "signOut", "onAuthStateChanged", "doc", "getDoc", "setDoc", "auth", "db", "defaultAdmin", "adminCredentials", "mockStudents", "AuthService", "loginAdmin", "email", "password", "_adminData$createdAt", "userCredential", "user", "adminDoc", "uid", "exists", "Error", "adminData", "data", "id", "role", "name", "avatar", "permissions", "createdAt", "toDate", "Date", "error", "getErrorMessage", "code", "loginStudent", "accessCode", "_studentData$createdA", "mockStudent", "find", "student", "studentsRef", "studentDoc", "studentData", "isActive", "enrolledCourses", "completedCourses", "certificates", "message", "generateAccessCode", "Math", "floor", "random", "toString", "createStudent", "existingStudent", "logout", "getCurrentUser", "Promise", "resolve", "unsubscribe", "onAuthStateChange", "callback", "errorCode", "authService"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/services/authService.ts"], "sourcesContent": ["import { \n  signInWithEmailAndPassword, \n  signOut, \n  onAuthStateChanged,\n  User as FirebaseUser\n} from 'firebase/auth';\nimport { doc, getDoc, setDoc } from 'firebase/firestore';\nimport { auth, db } from '../config/firebase';\nimport { User, Student, Admin } from '../types';\nimport { defaultAdmin, adminCredentials } from '../data/defaultAdmin';\nimport { mockStudents, studentCredentials } from '../data/mockStudents';\n\nclass AuthService {\n  // Admin login\n  async loginAdmin(email: string, password: string): Promise<Admin> {\n    try {\n      // Check for default admin credentials\n      if (email === adminCredentials.email && password === adminCredentials.password) {\n        return defaultAdmin as Admin;\n      }\n\n      const userCredential = await signInWithEmailAndPassword(auth, email, password);\n      const user = userCredential.user;\n      \n      // Get admin data from Firestore\n      const adminDoc = await getDoc(doc(db, 'admins', user.uid));\n      if (!adminDoc.exists()) {\n        throw new Error('المستخدم غير مخول كمدير');\n      }\n      \n      const adminData = adminDoc.data();\n      return {\n        id: user.uid,\n        email: user.email!,\n        role: 'admin',\n        name: adminData.name,\n        avatar: adminData.avatar,\n        permissions: adminData.permissions || [],\n        createdAt: adminData.createdAt?.toDate() || new Date()\n      };\n    } catch (error: any) {\n      throw new Error(this.getErrorMessage(error.code));\n    }\n  }\n\n  // Student login with access code\n  async loginStudent(accessCode: string): Promise<Student> {\n    try {\n      // Check for mock students first\n      const mockStudent = mockStudents.find(student => student.accessCode === accessCode);\n      if (mockStudent) {\n        return mockStudent;\n      }\n\n      // Find student by access code in Firestore\n      const studentsRef = doc(db, 'students', accessCode);\n      const studentDoc = await getDoc(studentsRef);\n\n      if (!studentDoc.exists()) {\n        throw new Error('كود الدخول غير صحيح');\n      }\n      \n      const studentData = studentDoc.data();\n      if (!studentData.isActive) {\n        throw new Error('الحساب غير مفعل');\n      }\n      \n      return {\n        id: studentData.id,\n        email: studentData.email || '',\n        role: 'student',\n        name: studentData.name,\n        avatar: studentData.avatar,\n        accessCode: accessCode,\n        enrolledCourses: studentData.enrolledCourses || [],\n        completedCourses: studentData.completedCourses || [],\n        certificates: studentData.certificates || [],\n        createdAt: studentData.createdAt?.toDate() || new Date()\n      };\n    } catch (error: any) {\n      throw new Error(error.message || 'حدث خطأ في تسجيل الدخول');\n    }\n  }\n\n  // Generate access code for student\n  generateAccessCode(): string {\n    return Math.floor(1000000 + Math.random() * 9000000).toString();\n  }\n\n  // Create student account\n  async createStudent(studentData: {\n    name: string;\n    email?: string;\n    enrolledCourses?: string[];\n  }): Promise<string> {\n    try {\n      const accessCode = this.generateAccessCode();\n      \n      // Check if access code already exists\n      const existingStudent = await getDoc(doc(db, 'students', accessCode));\n      if (existingStudent.exists()) {\n        // Generate new code if exists\n        return this.createStudent(studentData);\n      }\n      \n      const student = {\n        id: accessCode,\n        name: studentData.name,\n        email: studentData.email || '',\n        accessCode: accessCode,\n        enrolledCourses: studentData.enrolledCourses || [],\n        completedCourses: [],\n        certificates: [],\n        isActive: true,\n        createdAt: new Date()\n      };\n      \n      await setDoc(doc(db, 'students', accessCode), student);\n      return accessCode;\n    } catch (error: any) {\n      throw new Error('فشل في إنشاء حساب الطالب');\n    }\n  }\n\n  // Logout\n  async logout(): Promise<void> {\n    try {\n      await signOut(auth);\n    } catch (error: any) {\n      throw new Error('فشل في تسجيل الخروج');\n    }\n  }\n\n  // Get current user\n  getCurrentUser(): Promise<FirebaseUser | null> {\n    return new Promise((resolve) => {\n      const unsubscribe = onAuthStateChanged(auth, (user) => {\n        unsubscribe();\n        resolve(user);\n      });\n    });\n  }\n\n  // Auth state listener\n  onAuthStateChange(callback: (user: FirebaseUser | null) => void) {\n    return onAuthStateChanged(auth, callback);\n  }\n\n  private getErrorMessage(errorCode: string): string {\n    switch (errorCode) {\n      case 'auth/user-not-found':\n        return 'المستخدم غير موجود';\n      case 'auth/wrong-password':\n        return 'كلمة المرور غير صحيحة';\n      case 'auth/invalid-email':\n        return 'البريد الإلكتروني غير صحيح';\n      case 'auth/user-disabled':\n        return 'الحساب معطل';\n      case 'auth/too-many-requests':\n        return 'محاولات كثيرة، حاول مرة أخرى لاحقاً';\n      default:\n        return 'حدث خطأ في تسجيل الدخول';\n    }\n  }\n}\n\nexport const authService = new AuthService();\n"], "mappings": "AAAA,OACEA,0BAA0B,CAC1BC,OAAO,CACPC,kBAAkB,KAEb,eAAe,CACtB,OAASC,GAAG,CAAEC,MAAM,CAAEC,MAAM,KAAQ,oBAAoB,CACxD,OAASC,IAAI,CAAEC,EAAE,KAAQ,oBAAoB,CAE7C,OAASC,YAAY,CAAEC,gBAAgB,KAAQ,sBAAsB,CACrE,OAASC,YAAY,KAA4B,sBAAsB,CAEvE,KAAM,CAAAC,WAAY,CAChB;AACA,KAAM,CAAAC,UAAUA,CAACC,KAAa,CAAEC,QAAgB,CAAkB,CAChE,GAAI,KAAAC,oBAAA,CACF;AACA,GAAIF,KAAK,GAAKJ,gBAAgB,CAACI,KAAK,EAAIC,QAAQ,GAAKL,gBAAgB,CAACK,QAAQ,CAAE,CAC9E,MAAO,CAAAN,YAAY,CACrB,CAEA,KAAM,CAAAQ,cAAc,CAAG,KAAM,CAAAhB,0BAA0B,CAACM,IAAI,CAAEO,KAAK,CAAEC,QAAQ,CAAC,CAC9E,KAAM,CAAAG,IAAI,CAAGD,cAAc,CAACC,IAAI,CAEhC;AACA,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAd,MAAM,CAACD,GAAG,CAACI,EAAE,CAAE,QAAQ,CAAEU,IAAI,CAACE,GAAG,CAAC,CAAC,CAC1D,GAAI,CAACD,QAAQ,CAACE,MAAM,CAAC,CAAC,CAAE,CACtB,KAAM,IAAI,CAAAC,KAAK,CAAC,yBAAyB,CAAC,CAC5C,CAEA,KAAM,CAAAC,SAAS,CAAGJ,QAAQ,CAACK,IAAI,CAAC,CAAC,CACjC,MAAO,CACLC,EAAE,CAAEP,IAAI,CAACE,GAAG,CACZN,KAAK,CAAEI,IAAI,CAACJ,KAAM,CAClBY,IAAI,CAAE,OAAO,CACbC,IAAI,CAAEJ,SAAS,CAACI,IAAI,CACpBC,MAAM,CAAEL,SAAS,CAACK,MAAM,CACxBC,WAAW,CAAEN,SAAS,CAACM,WAAW,EAAI,EAAE,CACxCC,SAAS,CAAE,EAAAd,oBAAA,CAAAO,SAAS,CAACO,SAAS,UAAAd,oBAAA,iBAAnBA,oBAAA,CAAqBe,MAAM,CAAC,CAAC,GAAI,GAAI,CAAAC,IAAI,CAAC,CACvD,CAAC,CACH,CAAE,MAAOC,KAAU,CAAE,CACnB,KAAM,IAAI,CAAAX,KAAK,CAAC,IAAI,CAACY,eAAe,CAACD,KAAK,CAACE,IAAI,CAAC,CAAC,CACnD,CACF,CAEA;AACA,KAAM,CAAAC,YAAYA,CAACC,UAAkB,CAAoB,CACvD,GAAI,KAAAC,qBAAA,CACF;AACA,KAAM,CAAAC,WAAW,CAAG5B,YAAY,CAAC6B,IAAI,CAACC,OAAO,EAAIA,OAAO,CAACJ,UAAU,GAAKA,UAAU,CAAC,CACnF,GAAIE,WAAW,CAAE,CACf,MAAO,CAAAA,WAAW,CACpB,CAEA;AACA,KAAM,CAAAG,WAAW,CAAGtC,GAAG,CAACI,EAAE,CAAE,UAAU,CAAE6B,UAAU,CAAC,CACnD,KAAM,CAAAM,UAAU,CAAG,KAAM,CAAAtC,MAAM,CAACqC,WAAW,CAAC,CAE5C,GAAI,CAACC,UAAU,CAACtB,MAAM,CAAC,CAAC,CAAE,CACxB,KAAM,IAAI,CAAAC,KAAK,CAAC,qBAAqB,CAAC,CACxC,CAEA,KAAM,CAAAsB,WAAW,CAAGD,UAAU,CAACnB,IAAI,CAAC,CAAC,CACrC,GAAI,CAACoB,WAAW,CAACC,QAAQ,CAAE,CACzB,KAAM,IAAI,CAAAvB,KAAK,CAAC,iBAAiB,CAAC,CACpC,CAEA,MAAO,CACLG,EAAE,CAAEmB,WAAW,CAACnB,EAAE,CAClBX,KAAK,CAAE8B,WAAW,CAAC9B,KAAK,EAAI,EAAE,CAC9BY,IAAI,CAAE,SAAS,CACfC,IAAI,CAAEiB,WAAW,CAACjB,IAAI,CACtBC,MAAM,CAAEgB,WAAW,CAAChB,MAAM,CAC1BS,UAAU,CAAEA,UAAU,CACtBS,eAAe,CAAEF,WAAW,CAACE,eAAe,EAAI,EAAE,CAClDC,gBAAgB,CAAEH,WAAW,CAACG,gBAAgB,EAAI,EAAE,CACpDC,YAAY,CAAEJ,WAAW,CAACI,YAAY,EAAI,EAAE,CAC5ClB,SAAS,CAAE,EAAAQ,qBAAA,CAAAM,WAAW,CAACd,SAAS,UAAAQ,qBAAA,iBAArBA,qBAAA,CAAuBP,MAAM,CAAC,CAAC,GAAI,GAAI,CAAAC,IAAI,CAAC,CACzD,CAAC,CACH,CAAE,MAAOC,KAAU,CAAE,CACnB,KAAM,IAAI,CAAAX,KAAK,CAACW,KAAK,CAACgB,OAAO,EAAI,yBAAyB,CAAC,CAC7D,CACF,CAEA;AACAC,kBAAkBA,CAAA,CAAW,CAC3B,MAAO,CAAAC,IAAI,CAACC,KAAK,CAAC,OAAO,CAAGD,IAAI,CAACE,MAAM,CAAC,CAAC,CAAG,OAAO,CAAC,CAACC,QAAQ,CAAC,CAAC,CACjE,CAEA;AACA,KAAM,CAAAC,aAAaA,CAACX,WAInB,CAAmB,CAClB,GAAI,CACF,KAAM,CAAAP,UAAU,CAAG,IAAI,CAACa,kBAAkB,CAAC,CAAC,CAE5C;AACA,KAAM,CAAAM,eAAe,CAAG,KAAM,CAAAnD,MAAM,CAACD,GAAG,CAACI,EAAE,CAAE,UAAU,CAAE6B,UAAU,CAAC,CAAC,CACrE,GAAImB,eAAe,CAACnC,MAAM,CAAC,CAAC,CAAE,CAC5B;AACA,MAAO,KAAI,CAACkC,aAAa,CAACX,WAAW,CAAC,CACxC,CAEA,KAAM,CAAAH,OAAO,CAAG,CACdhB,EAAE,CAAEY,UAAU,CACdV,IAAI,CAAEiB,WAAW,CAACjB,IAAI,CACtBb,KAAK,CAAE8B,WAAW,CAAC9B,KAAK,EAAI,EAAE,CAC9BuB,UAAU,CAAEA,UAAU,CACtBS,eAAe,CAAEF,WAAW,CAACE,eAAe,EAAI,EAAE,CAClDC,gBAAgB,CAAE,EAAE,CACpBC,YAAY,CAAE,EAAE,CAChBH,QAAQ,CAAE,IAAI,CACdf,SAAS,CAAE,GAAI,CAAAE,IAAI,CAAC,CACtB,CAAC,CAED,KAAM,CAAA1B,MAAM,CAACF,GAAG,CAACI,EAAE,CAAE,UAAU,CAAE6B,UAAU,CAAC,CAAEI,OAAO,CAAC,CACtD,MAAO,CAAAJ,UAAU,CACnB,CAAE,MAAOJ,KAAU,CAAE,CACnB,KAAM,IAAI,CAAAX,KAAK,CAAC,0BAA0B,CAAC,CAC7C,CACF,CAEA;AACA,KAAM,CAAAmC,MAAMA,CAAA,CAAkB,CAC5B,GAAI,CACF,KAAM,CAAAvD,OAAO,CAACK,IAAI,CAAC,CACrB,CAAE,MAAO0B,KAAU,CAAE,CACnB,KAAM,IAAI,CAAAX,KAAK,CAAC,qBAAqB,CAAC,CACxC,CACF,CAEA;AACAoC,cAAcA,CAAA,CAAiC,CAC7C,MAAO,IAAI,CAAAC,OAAO,CAAEC,OAAO,EAAK,CAC9B,KAAM,CAAAC,WAAW,CAAG1D,kBAAkB,CAACI,IAAI,CAAGW,IAAI,EAAK,CACrD2C,WAAW,CAAC,CAAC,CACbD,OAAO,CAAC1C,IAAI,CAAC,CACf,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAEA;AACA4C,iBAAiBA,CAACC,QAA6C,CAAE,CAC/D,MAAO,CAAA5D,kBAAkB,CAACI,IAAI,CAAEwD,QAAQ,CAAC,CAC3C,CAEQ7B,eAAeA,CAAC8B,SAAiB,CAAU,CACjD,OAAQA,SAAS,EACf,IAAK,qBAAqB,CACxB,MAAO,oBAAoB,CAC7B,IAAK,qBAAqB,CACxB,MAAO,uBAAuB,CAChC,IAAK,oBAAoB,CACvB,MAAO,4BAA4B,CACrC,IAAK,oBAAoB,CACvB,MAAO,aAAa,CACtB,IAAK,wBAAwB,CAC3B,MAAO,qCAAqC,CAC9C,QACE,MAAO,yBAAyB,CACpC,CACF,CACF,CAEA,MAAO,MAAM,CAAAC,WAAW,CAAG,GAAI,CAAArD,WAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}