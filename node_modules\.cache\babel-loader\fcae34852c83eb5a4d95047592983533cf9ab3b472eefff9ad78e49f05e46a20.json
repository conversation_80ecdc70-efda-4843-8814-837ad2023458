{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0634\\u0631\\u0648\\u0639\\\\src\\\\components\\\\Student\\\\StudentProfile.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { UserIcon, CameraIcon, CheckCircleIcon, AcademicCapIcon, TrophyIcon } from '@heroicons/react/24/outline';\n\n// Types\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StudentProfile = ({\n  user,\n  onBack\n}) => {\n  _s();\n  const [activeTab, setActiveTab] = useState('profile');\n  const [isEditing, setIsEditing] = useState(false);\n\n  // Mock student data\n  const [studentData, setStudentData] = useState({\n    name: 'أحمد محمد',\n    email: '<EMAIL>',\n    accessCode: 'STU001',\n    joinDate: new Date('2024-01-15'),\n    avatar: null,\n    bio: 'طالب مهتم بتعلم البرمجة وتطوير المواقع',\n    phone: '+966501234567',\n    city: 'الرياض'\n  });\n  const [passwordData, setPasswordData] = useState({\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: ''\n  });\n\n  // Mock stats\n  const stats = {\n    enrolledCourses: 5,\n    completedCourses: 3,\n    certificates: 2,\n    totalWatchTime: 45,\n    // hours\n    averageScore: 87\n  };\n  const handleSaveProfile = () => {\n    // TODO: Implement save profile functionality\n    console.log('Save profile:', studentData);\n    setIsEditing(false);\n  };\n  const handleChangePassword = () => {\n    // TODO: Implement change password functionality\n    console.log('Change password');\n    setPasswordData({\n      currentPassword: '',\n      newPassword: '',\n      confirmPassword: ''\n    });\n  };\n  const handleAvatarChange = event => {\n    // TODO: Implement avatar upload\n    console.log('Avatar change:', event.target.files);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-4 space-x-reverse\",\n      children: [onBack && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onBack,\n        className: \"p-2 text-gray-600 hover:text-gray-900 transition-colors\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-6 h-6\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M15 19l-7-7 7-7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"\\u0627\\u0644\\u0645\\u0644\\u0641 \\u0627\\u0644\\u0634\\u062E\\u0635\\u064A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A\\u0643 \\u0627\\u0644\\u0634\\u062E\\u0635\\u064A\\u0629 \\u0648\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A\\u0643\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"lg:col-span-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-sm p-6 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative inline-block mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto\",\n              children: studentData.avatar ? /*#__PURE__*/_jsxDEV(\"img\", {\n                src: studentData.avatar,\n                alt: \"Profile\",\n                className: \"w-24 h-24 rounded-full object-cover\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(UserIcon, {\n                className: \"w-12 h-12 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"absolute bottom-0 right-0 bg-blue-600 text-white p-2 rounded-full cursor-pointer hover:bg-blue-700 transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(CameraIcon, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                accept: \"image/*\",\n                onChange: handleAvatarChange,\n                className: \"hidden\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900 mb-1\",\n            children: studentData.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-2\",\n            children: studentData.email\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 mb-4\",\n            children: [\"\\u0631\\u0645\\u0632 \\u0627\\u0644\\u0648\\u0635\\u0648\\u0644: \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-mono bg-gray-100 px-2 py-1 rounded\",\n              children: studentData.accessCode\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 27\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: [\"\\u0639\\u0636\\u0648 \\u0645\\u0646\\u0630 \", studentData.joinDate.toLocaleDateString('ar-SA')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-sm p-6 mt-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4\",\n            children: \"\\u0625\\u062D\\u0635\\u0627\\u0626\\u064A\\u0627\\u062A\\u064A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2 space-x-reverse\",\n                children: [/*#__PURE__*/_jsxDEV(AcademicCapIcon, {\n                  className: \"w-5 h-5 text-blue-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 140,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"\\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A \\u0627\\u0644\\u0645\\u0633\\u062C\\u0644\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-semibold text-gray-900\",\n                children: stats.enrolledCourses\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2 space-x-reverse\",\n                children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                  className: \"w-5 h-5 text-green-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"\\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A \\u0627\\u0644\\u0645\\u0643\\u062A\\u0645\\u0644\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-semibold text-gray-900\",\n                children: stats.completedCourses\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2 space-x-reverse\",\n                children: [/*#__PURE__*/_jsxDEV(TrophyIcon, {\n                  className: \"w-5 h-5 text-yellow-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"\\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0627\\u062A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-semibold text-gray-900\",\n                children: stats.certificates\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: \"\\u0633\\u0627\\u0639\\u0627\\u062A \\u0627\\u0644\\u0645\\u0634\\u0627\\u0647\\u062F\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-semibold text-gray-900\",\n                children: [stats.totalWatchTime, \"h\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: \"\\u0645\\u062A\\u0648\\u0633\\u0637 \\u0627\\u0644\\u0646\\u062A\\u0627\\u0626\\u062C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-semibold text-green-600\",\n                children: [stats.averageScore, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.1\n        },\n        className: \"lg:col-span-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-sm p-6 mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-4 space-x-reverse border-b border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab('profile'),\n              className: `pb-2 px-1 border-b-2 transition-colors ${activeTab === 'profile' ? 'border-blue-600 text-blue-600' : 'border-transparent text-gray-600 hover:text-gray-900'}`,\n              children: \"\\u0627\\u0644\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0634\\u062E\\u0635\\u064A\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab('security'),\n              className: `pb-2 px-1 border-b-2 transition-colors ${activeTab === 'security' ? 'border-blue-600 text-blue-600' : 'border-transparent text-gray-600 hover:text-gray-900'}`,\n              children: \"\\u0627\\u0644\\u0623\\u0645\\u0627\\u0646\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-sm p-6\",\n          children: [activeTab === 'profile' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900\",\n                children: \"\\u0627\\u0644\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0634\\u062E\\u0635\\u064A\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setIsEditing(!isEditing),\n                className: \"text-blue-600 hover:text-blue-700 transition-colors\",\n                children: isEditing ? 'إلغاء' : 'تعديل'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"\\u0627\\u0644\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0643\\u0627\\u0645\\u0644\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: studentData.name,\n                  onChange: e => setStudentData(prev => ({\n                    ...prev,\n                    name: e.target.value\n                  })),\n                  disabled: !isEditing,\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"\\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  value: studentData.email,\n                  onChange: e => setStudentData(prev => ({\n                    ...prev,\n                    email: e.target.value\n                  })),\n                  disabled: !isEditing,\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0647\\u0627\\u062A\\u0641\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"tel\",\n                  value: studentData.phone,\n                  onChange: e => setStudentData(prev => ({\n                    ...prev,\n                    phone: e.target.value\n                  })),\n                  disabled: !isEditing,\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"\\u0627\\u0644\\u0645\\u062F\\u064A\\u0646\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: studentData.city,\n                  onChange: e => setStudentData(prev => ({\n                    ...prev,\n                    city: e.target.value\n                  })),\n                  disabled: !isEditing,\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"\\u0646\\u0628\\u0630\\u0629 \\u0634\\u062E\\u0635\\u064A\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: studentData.bio,\n                onChange: e => setStudentData(prev => ({\n                  ...prev,\n                  bio: e.target.value\n                })),\n                disabled: !isEditing,\n                rows: 3,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 17\n            }, this), isEditing && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-end space-x-3 space-x-reverse\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setIsEditing(false),\n                className: \"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors\",\n                children: \"\\u0625\\u0644\\u063A\\u0627\\u0621\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleSaveProfile,\n                className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                children: \"\\u062D\\u0641\\u0638 \\u0627\\u0644\\u062A\\u063A\\u064A\\u064A\\u0631\\u0627\\u062A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this), activeTab === 'security' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900\",\n              children: \"\\u062A\\u063A\\u064A\\u064A\\u0631 \\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"\\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631 \\u0627\\u0644\\u062D\\u0627\\u0644\\u064A\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"password\",\n                  value: passwordData.currentPassword,\n                  onChange: e => setPasswordData(prev => ({\n                    ...prev,\n                    currentPassword: e.target.value\n                  })),\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"\\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631 \\u0627\\u0644\\u062C\\u062F\\u064A\\u062F\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"password\",\n                  value: passwordData.newPassword,\n                  onChange: e => setPasswordData(prev => ({\n                    ...prev,\n                    newPassword: e.target.value\n                  })),\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"\\u062A\\u0623\\u0643\\u064A\\u062F \\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631 \\u0627\\u0644\\u062C\\u062F\\u064A\\u062F\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"password\",\n                  value: passwordData.confirmPassword,\n                  onChange: e => setPasswordData(prev => ({\n                    ...prev,\n                    confirmPassword: e.target.value\n                  })),\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleChangePassword,\n                className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n                children: \"\\u062A\\u063A\\u064A\\u064A\\u0631 \\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 5\n  }, this);\n};\n_s(StudentProfile, \"fzt2vmjQXndlTsy+vlQx32c47RY=\");\n_c = StudentProfile;\nexport default StudentProfile;\nvar _c;\n$RefreshReg$(_c, \"StudentProfile\");", "map": {"version": 3, "names": ["React", "useState", "motion", "UserIcon", "CameraIcon", "CheckCircleIcon", "AcademicCapIcon", "TrophyIcon", "jsxDEV", "_jsxDEV", "StudentProfile", "user", "onBack", "_s", "activeTab", "setActiveTab", "isEditing", "setIsEditing", "studentData", "setStudentData", "name", "email", "accessCode", "joinDate", "Date", "avatar", "bio", "phone", "city", "passwordData", "setPasswordData", "currentPassword", "newPassword", "confirmPassword", "stats", "enrolledCourses", "completedCourses", "certificates", "totalWatchTime", "averageScore", "handleSaveProfile", "console", "log", "handleChangePassword", "handleAvatarChange", "event", "target", "files", "className", "children", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "y", "animate", "src", "alt", "type", "accept", "onChange", "toLocaleDateString", "transition", "delay", "value", "e", "prev", "disabled", "rows", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Student/StudentProfile.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  UserIcon,\n  EnvelopeIcon,\n  KeyIcon,\n  CameraIcon,\n  CheckCircleIcon,\n  AcademicCapIcon,\n  TrophyIcon\n} from '@heroicons/react/24/outline';\n\n// Types\nimport { Student } from '../../types';\n\ninterface StudentProfileProps {\n  user?: Student;\n  onBack?: () => void;\n}\n\nconst StudentProfile: React.FC<StudentProfileProps> = ({ user, onBack }) => {\n  const [activeTab, setActiveTab] = useState('profile');\n  const [isEditing, setIsEditing] = useState(false);\n  \n  // Mock student data\n  const [studentData, setStudentData] = useState({\n    name: 'أحمد محمد',\n    email: '<EMAIL>',\n    accessCode: 'STU001',\n    joinDate: new Date('2024-01-15'),\n    avatar: null,\n    bio: 'طالب مهتم بتعلم البرمجة وتطوير المواقع',\n    phone: '+966501234567',\n    city: 'الرياض'\n  });\n\n  const [passwordData, setPasswordData] = useState({\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: ''\n  });\n\n  // Mock stats\n  const stats = {\n    enrolledCourses: 5,\n    completedCourses: 3,\n    certificates: 2,\n    totalWatchTime: 45, // hours\n    averageScore: 87\n  };\n\n  const handleSaveProfile = () => {\n    // TODO: Implement save profile functionality\n    console.log('Save profile:', studentData);\n    setIsEditing(false);\n  };\n\n  const handleChangePassword = () => {\n    // TODO: Implement change password functionality\n    console.log('Change password');\n    setPasswordData({\n      currentPassword: '',\n      newPassword: '',\n      confirmPassword: ''\n    });\n  };\n\n  const handleAvatarChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n    // TODO: Implement avatar upload\n    console.log('Avatar change:', event.target.files);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center space-x-4 space-x-reverse\">\n        {onBack && (\n          <button\n            onClick={onBack}\n            className=\"p-2 text-gray-600 hover:text-gray-900 transition-colors\"\n          >\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n            </svg>\n          </button>\n        )}\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">الملف الشخصي</h1>\n          <p className=\"text-gray-600\">إدارة معلوماتك الشخصية وإعداداتك</p>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Profile Card */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"lg:col-span-1\"\n        >\n          <div className=\"bg-white rounded-lg shadow-sm p-6 text-center\">\n            <div className=\"relative inline-block mb-4\">\n              <div className=\"w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto\">\n                {studentData.avatar ? (\n                  <img\n                    src={studentData.avatar}\n                    alt=\"Profile\"\n                    className=\"w-24 h-24 rounded-full object-cover\"\n                  />\n                ) : (\n                  <UserIcon className=\"w-12 h-12 text-blue-600\" />\n                )}\n              </div>\n              <label className=\"absolute bottom-0 right-0 bg-blue-600 text-white p-2 rounded-full cursor-pointer hover:bg-blue-700 transition-colors\">\n                <CameraIcon className=\"w-4 h-4\" />\n                <input\n                  type=\"file\"\n                  accept=\"image/*\"\n                  onChange={handleAvatarChange}\n                  className=\"hidden\"\n                />\n              </label>\n            </div>\n            \n            <h2 className=\"text-xl font-bold text-gray-900 mb-1\">{studentData.name}</h2>\n            <p className=\"text-gray-600 mb-2\">{studentData.email}</p>\n            <p className=\"text-sm text-gray-500 mb-4\">\n              رمز الوصول: <span className=\"font-mono bg-gray-100 px-2 py-1 rounded\">{studentData.accessCode}</span>\n            </p>\n            <p className=\"text-sm text-gray-500\">\n              عضو منذ {studentData.joinDate.toLocaleDateString('ar-SA')}\n            </p>\n          </div>\n\n          {/* Stats */}\n          <div className=\"bg-white rounded-lg shadow-sm p-6 mt-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">إحصائياتي</h3>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <AcademicCapIcon className=\"w-5 h-5 text-blue-600\" />\n                  <span className=\"text-sm text-gray-600\">الكورسات المسجلة</span>\n                </div>\n                <span className=\"font-semibold text-gray-900\">{stats.enrolledCourses}</span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <CheckCircleIcon className=\"w-5 h-5 text-green-600\" />\n                  <span className=\"text-sm text-gray-600\">الكورسات المكتملة</span>\n                </div>\n                <span className=\"font-semibold text-gray-900\">{stats.completedCourses}</span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <TrophyIcon className=\"w-5 h-5 text-yellow-600\" />\n                  <span className=\"text-sm text-gray-600\">الشهادات</span>\n                </div>\n                <span className=\"font-semibold text-gray-900\">{stats.certificates}</span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">ساعات المشاهدة</span>\n                <span className=\"font-semibold text-gray-900\">{stats.totalWatchTime}h</span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">متوسط النتائج</span>\n                <span className=\"font-semibold text-green-600\">{stats.averageScore}%</span>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Main Content */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.1 }}\n          className=\"lg:col-span-2\"\n        >\n          {/* Tabs */}\n          <div className=\"bg-white rounded-lg shadow-sm p-6 mb-6\">\n            <div className=\"flex space-x-4 space-x-reverse border-b border-gray-200\">\n              <button\n                onClick={() => setActiveTab('profile')}\n                className={`pb-2 px-1 border-b-2 transition-colors ${\n                  activeTab === 'profile'\n                    ? 'border-blue-600 text-blue-600'\n                    : 'border-transparent text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                المعلومات الشخصية\n              </button>\n              <button\n                onClick={() => setActiveTab('security')}\n                className={`pb-2 px-1 border-b-2 transition-colors ${\n                  activeTab === 'security'\n                    ? 'border-blue-600 text-blue-600'\n                    : 'border-transparent text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                الأمان\n              </button>\n            </div>\n          </div>\n\n          {/* Tab Content */}\n          <div className=\"bg-white rounded-lg shadow-sm p-6\">\n            {activeTab === 'profile' && (\n              <div className=\"space-y-6\">\n                <div className=\"flex items-center justify-between\">\n                  <h3 className=\"text-lg font-semibold text-gray-900\">المعلومات الشخصية</h3>\n                  <button\n                    onClick={() => setIsEditing(!isEditing)}\n                    className=\"text-blue-600 hover:text-blue-700 transition-colors\"\n                  >\n                    {isEditing ? 'إلغاء' : 'تعديل'}\n                  </button>\n                </div>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      الاسم الكامل\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={studentData.name}\n                      onChange={(e) => setStudentData(prev => ({ ...prev, name: e.target.value }))}\n                      disabled={!isEditing}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      البريد الإلكتروني\n                    </label>\n                    <input\n                      type=\"email\"\n                      value={studentData.email}\n                      onChange={(e) => setStudentData(prev => ({ ...prev, email: e.target.value }))}\n                      disabled={!isEditing}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      رقم الهاتف\n                    </label>\n                    <input\n                      type=\"tel\"\n                      value={studentData.phone}\n                      onChange={(e) => setStudentData(prev => ({ ...prev, phone: e.target.value }))}\n                      disabled={!isEditing}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      المدينة\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={studentData.city}\n                      onChange={(e) => setStudentData(prev => ({ ...prev, city: e.target.value }))}\n                      disabled={!isEditing}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50\"\n                    />\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    نبذة شخصية\n                  </label>\n                  <textarea\n                    value={studentData.bio}\n                    onChange={(e) => setStudentData(prev => ({ ...prev, bio: e.target.value }))}\n                    disabled={!isEditing}\n                    rows={3}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50\"\n                  />\n                </div>\n\n                {isEditing && (\n                  <div className=\"flex justify-end space-x-3 space-x-reverse\">\n                    <button\n                      onClick={() => setIsEditing(false)}\n                      className=\"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors\"\n                    >\n                      إلغاء\n                    </button>\n                    <button\n                      onClick={handleSaveProfile}\n                      className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n                    >\n                      حفظ التغييرات\n                    </button>\n                  </div>\n                )}\n              </div>\n            )}\n\n            {activeTab === 'security' && (\n              <div className=\"space-y-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900\">تغيير كلمة المرور</h3>\n\n                <div className=\"space-y-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      كلمة المرور الحالية\n                    </label>\n                    <input\n                      type=\"password\"\n                      value={passwordData.currentPassword}\n                      onChange={(e) => setPasswordData(prev => ({ ...prev, currentPassword: e.target.value }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      كلمة المرور الجديدة\n                    </label>\n                    <input\n                      type=\"password\"\n                      value={passwordData.newPassword}\n                      onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      تأكيد كلمة المرور الجديدة\n                    </label>\n                    <input\n                      type=\"password\"\n                      value={passwordData.confirmPassword}\n                      onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n\n                  <button\n                    onClick={handleChangePassword}\n                    className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n                  >\n                    تغيير كلمة المرور\n                  </button>\n                </div>\n              </div>\n            )}\n          </div>\n        </motion.div>\n      </div>\n    </div>\n  );\n};\n\nexport default StudentProfile;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,QAAQ,EAGRC,UAAU,EACVC,eAAe,EACfC,eAAe,EACfC,UAAU,QACL,6BAA6B;;AAEpC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAQA,MAAMC,cAA6C,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAC1E,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACA,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC;IAC7CmB,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,mBAAmB;IAC1BC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;IAChCC,MAAM,EAAE,IAAI;IACZC,GAAG,EAAE,wCAAwC;IAC7CC,KAAK,EAAE,eAAe;IACtBC,IAAI,EAAE;EACR,CAAC,CAAC;EAEF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC;IAC/C8B,eAAe,EAAE,EAAE;IACnBC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE;EACnB,CAAC,CAAC;;EAEF;EACA,MAAMC,KAAK,GAAG;IACZC,eAAe,EAAE,CAAC;IAClBC,gBAAgB,EAAE,CAAC;IACnBC,YAAY,EAAE,CAAC;IACfC,cAAc,EAAE,EAAE;IAAE;IACpBC,YAAY,EAAE;EAChB,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B;IACAC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAExB,WAAW,CAAC;IACzCD,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAM0B,oBAAoB,GAAGA,CAAA,KAAM;IACjC;IACAF,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAC9BZ,eAAe,CAAC;MACdC,eAAe,EAAE,EAAE;MACnBC,WAAW,EAAE,EAAE;MACfC,eAAe,EAAE;IACnB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMW,kBAAkB,GAAIC,KAA0C,IAAK;IACzE;IACAJ,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEG,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EACnD,CAAC;EAED,oBACEtC,OAAA;IAAKuC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBxC,OAAA;MAAKuC,SAAS,EAAC,6CAA6C;MAAAC,QAAA,GACzDrC,MAAM,iBACLH,OAAA;QACEyC,OAAO,EAAEtC,MAAO;QAChBoC,SAAS,EAAC,yDAAyD;QAAAC,QAAA,eAEnExC,OAAA;UAAKuC,SAAS,EAAC,SAAS;UAACG,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAJ,QAAA,eAC5ExC,OAAA;YAAM6C,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,WAAW,EAAE,CAAE;YAACC,CAAC,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACT,eACDpD,OAAA;QAAAwC,QAAA,gBACExC,OAAA;UAAIuC,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAY;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClEpD,OAAA;UAAGuC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAgC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpD,OAAA;MAAKuC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDxC,OAAA,CAACP,MAAM,CAAC4D,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BjB,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAEzBxC,OAAA;UAAKuC,SAAS,EAAC,+CAA+C;UAAAC,QAAA,gBAC5DxC,OAAA;YAAKuC,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzCxC,OAAA;cAAKuC,SAAS,EAAC,6EAA6E;cAAAC,QAAA,EACzF/B,WAAW,CAACO,MAAM,gBACjBhB,OAAA;gBACE0D,GAAG,EAAEjD,WAAW,CAACO,MAAO;gBACxB2C,GAAG,EAAC,SAAS;gBACbpB,SAAS,EAAC;cAAqC;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,gBAEFpD,OAAA,CAACN,QAAQ;gBAAC6C,SAAS,EAAC;cAAyB;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAChD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNpD,OAAA;cAAOuC,SAAS,EAAC,sHAAsH;cAAAC,QAAA,gBACrIxC,OAAA,CAACL,UAAU;gBAAC4C,SAAS,EAAC;cAAS;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClCpD,OAAA;gBACE4D,IAAI,EAAC,MAAM;gBACXC,MAAM,EAAC,SAAS;gBAChBC,QAAQ,EAAE3B,kBAAmB;gBAC7BI,SAAS,EAAC;cAAQ;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENpD,OAAA;YAAIuC,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAE/B,WAAW,CAACE;UAAI;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5EpD,OAAA;YAAGuC,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAE/B,WAAW,CAACG;UAAK;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzDpD,OAAA;YAAGuC,SAAS,EAAC,4BAA4B;YAAAC,QAAA,GAAC,2DAC5B,eAAAxC,OAAA;cAAMuC,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAE/B,WAAW,CAACI;YAAU;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpG,CAAC,eACJpD,OAAA;YAAGuC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,wCAC3B,EAAC/B,WAAW,CAACK,QAAQ,CAACiD,kBAAkB,CAAC,OAAO,CAAC;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGNpD,OAAA;UAAKuC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDxC,OAAA;YAAIuC,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAS;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvEpD,OAAA;YAAKuC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBxC,OAAA;cAAKuC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDxC,OAAA;gBAAKuC,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBAC1DxC,OAAA,CAACH,eAAe;kBAAC0C,SAAS,EAAC;gBAAuB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACrDpD,OAAA;kBAAMuC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,eACNpD,OAAA;gBAAMuC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAEf,KAAK,CAACC;cAAe;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CAAC,eACNpD,OAAA;cAAKuC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDxC,OAAA;gBAAKuC,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBAC1DxC,OAAA,CAACJ,eAAe;kBAAC2C,SAAS,EAAC;gBAAwB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACtDpD,OAAA;kBAAMuC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACNpD,OAAA;gBAAMuC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAEf,KAAK,CAACE;cAAgB;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC,eACNpD,OAAA;cAAKuC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDxC,OAAA;gBAAKuC,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBAC1DxC,OAAA,CAACF,UAAU;kBAACyC,SAAS,EAAC;gBAAyB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClDpD,OAAA;kBAAMuC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACNpD,OAAA;gBAAMuC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAEf,KAAK,CAACG;cAAY;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eACNpD,OAAA;cAAKuC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDxC,OAAA;gBAAMuC,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAc;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7DpD,OAAA;gBAAMuC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,GAAEf,KAAK,CAACI,cAAc,EAAC,GAAC;cAAA;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CAAC,eACNpD,OAAA;cAAKuC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDxC,OAAA;gBAAMuC,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAa;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5DpD,OAAA;gBAAMuC,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,GAAEf,KAAK,CAACK,YAAY,EAAC,GAAC;cAAA;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbpD,OAAA,CAACP,MAAM,CAAC4D,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BQ,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3B1B,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAGzBxC,OAAA;UAAKuC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,eACrDxC,OAAA;YAAKuC,SAAS,EAAC,yDAAyD;YAAAC,QAAA,gBACtExC,OAAA;cACEyC,OAAO,EAAEA,CAAA,KAAMnC,YAAY,CAAC,SAAS,CAAE;cACvCiC,SAAS,EAAE,0CACTlC,SAAS,KAAK,SAAS,GACnB,+BAA+B,GAC/B,sDAAsD,EACzD;cAAAmC,QAAA,EACJ;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpD,OAAA;cACEyC,OAAO,EAAEA,CAAA,KAAMnC,YAAY,CAAC,UAAU,CAAE;cACxCiC,SAAS,EAAE,0CACTlC,SAAS,KAAK,UAAU,GACpB,+BAA+B,GAC/B,sDAAsD,EACzD;cAAAmC,QAAA,EACJ;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNpD,OAAA;UAAKuC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,GAC/CnC,SAAS,KAAK,SAAS,iBACtBL,OAAA;YAAKuC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBxC,OAAA;cAAKuC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDxC,OAAA;gBAAIuC,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAAiB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1EpD,OAAA;gBACEyC,OAAO,EAAEA,CAAA,KAAMjC,YAAY,CAAC,CAACD,SAAS,CAAE;gBACxCgC,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,EAE9DjC,SAAS,GAAG,OAAO,GAAG;cAAO;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENpD,OAAA;cAAKuC,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDxC,OAAA;gBAAAwC,QAAA,gBACExC,OAAA;kBAAOuC,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRpD,OAAA;kBACE4D,IAAI,EAAC,MAAM;kBACXM,KAAK,EAAEzD,WAAW,CAACE,IAAK;kBACxBmD,QAAQ,EAAGK,CAAC,IAAKzD,cAAc,CAAC0D,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEzD,IAAI,EAAEwD,CAAC,CAAC9B,MAAM,CAAC6B;kBAAM,CAAC,CAAC,CAAE;kBAC7EG,QAAQ,EAAE,CAAC9D,SAAU;kBACrBgC,SAAS,EAAC;gBAA4H;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENpD,OAAA;gBAAAwC,QAAA,gBACExC,OAAA;kBAAOuC,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRpD,OAAA;kBACE4D,IAAI,EAAC,OAAO;kBACZM,KAAK,EAAEzD,WAAW,CAACG,KAAM;kBACzBkD,QAAQ,EAAGK,CAAC,IAAKzD,cAAc,CAAC0D,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAExD,KAAK,EAAEuD,CAAC,CAAC9B,MAAM,CAAC6B;kBAAM,CAAC,CAAC,CAAE;kBAC9EG,QAAQ,EAAE,CAAC9D,SAAU;kBACrBgC,SAAS,EAAC;gBAA4H;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENpD,OAAA;gBAAAwC,QAAA,gBACExC,OAAA;kBAAOuC,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRpD,OAAA;kBACE4D,IAAI,EAAC,KAAK;kBACVM,KAAK,EAAEzD,WAAW,CAACS,KAAM;kBACzB4C,QAAQ,EAAGK,CAAC,IAAKzD,cAAc,CAAC0D,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAElD,KAAK,EAAEiD,CAAC,CAAC9B,MAAM,CAAC6B;kBAAM,CAAC,CAAC,CAAE;kBAC9EG,QAAQ,EAAE,CAAC9D,SAAU;kBACrBgC,SAAS,EAAC;gBAA4H;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENpD,OAAA;gBAAAwC,QAAA,gBACExC,OAAA;kBAAOuC,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRpD,OAAA;kBACE4D,IAAI,EAAC,MAAM;kBACXM,KAAK,EAAEzD,WAAW,CAACU,IAAK;kBACxB2C,QAAQ,EAAGK,CAAC,IAAKzD,cAAc,CAAC0D,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEjD,IAAI,EAAEgD,CAAC,CAAC9B,MAAM,CAAC6B;kBAAM,CAAC,CAAC,CAAE;kBAC7EG,QAAQ,EAAE,CAAC9D,SAAU;kBACrBgC,SAAS,EAAC;gBAA4H;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENpD,OAAA;cAAAwC,QAAA,gBACExC,OAAA;gBAAOuC,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRpD,OAAA;gBACEkE,KAAK,EAAEzD,WAAW,CAACQ,GAAI;gBACvB6C,QAAQ,EAAGK,CAAC,IAAKzD,cAAc,CAAC0D,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEnD,GAAG,EAAEkD,CAAC,CAAC9B,MAAM,CAAC6B;gBAAM,CAAC,CAAC,CAAE;gBAC5EG,QAAQ,EAAE,CAAC9D,SAAU;gBACrB+D,IAAI,EAAE,CAAE;gBACR/B,SAAS,EAAC;cAA4H;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAEL7C,SAAS,iBACRP,OAAA;cAAKuC,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBACzDxC,OAAA;gBACEyC,OAAO,EAAEA,CAAA,KAAMjC,YAAY,CAAC,KAAK,CAAE;gBACnC+B,SAAS,EAAC,oFAAoF;gBAAAC,QAAA,EAC/F;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTpD,OAAA;gBACEyC,OAAO,EAAEV,iBAAkB;gBAC3BQ,SAAS,EAAC,iFAAiF;gBAAAC,QAAA,EAC5F;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAEA/C,SAAS,KAAK,UAAU,iBACvBL,OAAA;YAAKuC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBxC,OAAA;cAAIuC,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAiB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAE1EpD,OAAA;cAAKuC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBxC,OAAA;gBAAAwC,QAAA,gBACExC,OAAA;kBAAOuC,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRpD,OAAA;kBACE4D,IAAI,EAAC,UAAU;kBACfM,KAAK,EAAE9C,YAAY,CAACE,eAAgB;kBACpCwC,QAAQ,EAAGK,CAAC,IAAK9C,eAAe,CAAC+C,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE9C,eAAe,EAAE6C,CAAC,CAAC9B,MAAM,CAAC6B;kBAAM,CAAC,CAAC,CAAE;kBACzF3B,SAAS,EAAC;gBAAwG;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENpD,OAAA;gBAAAwC,QAAA,gBACExC,OAAA;kBAAOuC,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRpD,OAAA;kBACE4D,IAAI,EAAC,UAAU;kBACfM,KAAK,EAAE9C,YAAY,CAACG,WAAY;kBAChCuC,QAAQ,EAAGK,CAAC,IAAK9C,eAAe,CAAC+C,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE7C,WAAW,EAAE4C,CAAC,CAAC9B,MAAM,CAAC6B;kBAAM,CAAC,CAAC,CAAE;kBACrF3B,SAAS,EAAC;gBAAwG;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENpD,OAAA;gBAAAwC,QAAA,gBACExC,OAAA;kBAAOuC,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRpD,OAAA;kBACE4D,IAAI,EAAC,UAAU;kBACfM,KAAK,EAAE9C,YAAY,CAACI,eAAgB;kBACpCsC,QAAQ,EAAGK,CAAC,IAAK9C,eAAe,CAAC+C,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE5C,eAAe,EAAE2C,CAAC,CAAC9B,MAAM,CAAC6B;kBAAM,CAAC,CAAC,CAAE;kBACzF3B,SAAS,EAAC;gBAAwG;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENpD,OAAA;gBACEyC,OAAO,EAAEP,oBAAqB;gBAC9BK,SAAS,EAAC,iFAAiF;gBAAAC,QAAA,EAC5F;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChD,EAAA,CAlVIH,cAA6C;AAAAsE,EAAA,GAA7CtE,cAA6C;AAoVnD,eAAeA,cAAc;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}