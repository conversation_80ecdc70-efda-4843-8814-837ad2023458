{"ast": null, "code": "import { invariant } from '../../utils/errors.mjs';\nimport { cubicBezier } from '../cubic-bezier.mjs';\nimport { noop } from '../../utils/noop.mjs';\nimport { easeIn, easeInOut, easeOut } from '../ease.mjs';\nimport { circIn, circInOut, circOut } from '../circ.mjs';\nimport { backIn, backInOut, backOut } from '../back.mjs';\nimport { anticipate } from '../anticipate.mjs';\nconst easingLookup = {\n  linear: noop,\n  easeIn,\n  easeInOut,\n  easeOut,\n  circIn,\n  circInOut,\n  circOut,\n  backIn,\n  backInOut,\n  backOut,\n  anticipate\n};\nconst easingDefinitionToFunction = definition => {\n  if (Array.isArray(definition)) {\n    // If cubic bezier definition, create bezier curve\n    invariant(definition.length === 4, `Cubic bezier arrays must contain four numerical values.`);\n    const [x1, y1, x2, y2] = definition;\n    return cubicBezier(x1, y1, x2, y2);\n  } else if (typeof definition === \"string\") {\n    // Else lookup from table\n    invariant(easingLookup[definition] !== undefined, `Invalid easing type '${definition}'`);\n    return easingLookup[definition];\n  }\n  return definition;\n};\nexport { easingDefinitionToFunction };", "map": {"version": 3, "names": ["invariant", "cubicBezier", "noop", "easeIn", "easeInOut", "easeOut", "circIn", "circInOut", "circOut", "backIn", "backInOut", "backOut", "anticipate", "easingLookup", "linear", "easingDefinitionToFunction", "definition", "Array", "isArray", "length", "x1", "y1", "x2", "y2", "undefined"], "sources": ["C:/Users/<USER>/Desktop/مشروع/node_modules/framer-motion/dist/es/easing/utils/map.mjs"], "sourcesContent": ["import { invariant } from '../../utils/errors.mjs';\nimport { cubicBezier } from '../cubic-bezier.mjs';\nimport { noop } from '../../utils/noop.mjs';\nimport { easeIn, easeInOut, easeOut } from '../ease.mjs';\nimport { circIn, circInOut, circOut } from '../circ.mjs';\nimport { backIn, backInOut, backOut } from '../back.mjs';\nimport { anticipate } from '../anticipate.mjs';\n\nconst easingLookup = {\n    linear: noop,\n    easeIn,\n    easeInOut,\n    easeOut,\n    circIn,\n    circInOut,\n    circOut,\n    backIn,\n    backInOut,\n    backOut,\n    anticipate,\n};\nconst easingDefinitionToFunction = (definition) => {\n    if (Array.isArray(definition)) {\n        // If cubic bezier definition, create bezier curve\n        invariant(definition.length === 4, `Cubic bezier arrays must contain four numerical values.`);\n        const [x1, y1, x2, y2] = definition;\n        return cubicBezier(x1, y1, x2, y2);\n    }\n    else if (typeof definition === \"string\") {\n        // Else lookup from table\n        invariant(easingLookup[definition] !== undefined, `Invalid easing type '${definition}'`);\n        return easingLookup[definition];\n    }\n    return definition;\n};\n\nexport { easingDefinitionToFunction };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,wBAAwB;AAClD,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,IAAI,QAAQ,sBAAsB;AAC3C,SAASC,MAAM,EAAEC,SAAS,EAAEC,OAAO,QAAQ,aAAa;AACxD,SAASC,MAAM,EAAEC,SAAS,EAAEC,OAAO,QAAQ,aAAa;AACxD,SAASC,MAAM,EAAEC,SAAS,EAAEC,OAAO,QAAQ,aAAa;AACxD,SAASC,UAAU,QAAQ,mBAAmB;AAE9C,MAAMC,YAAY,GAAG;EACjBC,MAAM,EAAEZ,IAAI;EACZC,MAAM;EACNC,SAAS;EACTC,OAAO;EACPC,MAAM;EACNC,SAAS;EACTC,OAAO;EACPC,MAAM;EACNC,SAAS;EACTC,OAAO;EACPC;AACJ,CAAC;AACD,MAAMG,0BAA0B,GAAIC,UAAU,IAAK;EAC/C,IAAIC,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,EAAE;IAC3B;IACAhB,SAAS,CAACgB,UAAU,CAACG,MAAM,KAAK,CAAC,EAAE,yDAAyD,CAAC;IAC7F,MAAM,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,GAAGP,UAAU;IACnC,OAAOf,WAAW,CAACmB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;EACtC,CAAC,MACI,IAAI,OAAOP,UAAU,KAAK,QAAQ,EAAE;IACrC;IACAhB,SAAS,CAACa,YAAY,CAACG,UAAU,CAAC,KAAKQ,SAAS,EAAE,wBAAwBR,UAAU,GAAG,CAAC;IACxF,OAAOH,YAAY,CAACG,UAAU,CAAC;EACnC;EACA,OAAOA,UAAU;AACrB,CAAC;AAED,SAASD,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}