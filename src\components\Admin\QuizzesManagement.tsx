import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  ClipboardDocumentListIcon,
  QuestionMarkCircleIcon
} from '@heroicons/react/24/outline';

// Types
import { Quiz } from '../../types';

interface QuizzesManagementProps {
  onBack?: () => void;
}

const QuizzesManagement: React.FC<QuizzesManagementProps> = ({ onBack }) => {
  const [quizzes, setQuizzes] = useState<Quiz[]>([]);
  const [searchTerm, setSearchTerm] = useState('');

  // Mock data for demonstration
  const mockQuizzes: Quiz[] = [
    {
      id: '1',
      courseId: '1',
      title: 'اختبار أساسيات البرمجة',
      description: 'اختبار شامل لأساسيات البرمجة',
      questions: [
        {
          id: '1',
          question: 'ما هو المتغير؟',
          type: 'multiple-choice',
          options: ['مكان لتخزين البيانات', 'نوع من الدوال', 'أمر برمجي'],
          correctAnswer: 0,
          points: 10
        }
      ],
      passingScore: 70,
      timeLimit: 30,
      attempts: 3,
      isActive: true,
      createdAt: new Date()
    },
    {
      id: '2',
      courseId: '2',
      title: 'اختبار تطوير المواقع',
      description: 'اختبار في HTML و CSS',
      questions: [],
      passingScore: 80,
      timeLimit: 45,
      attempts: 2,
      isActive: true,
      createdAt: new Date()
    }
  ];

  React.useEffect(() => {
    setQuizzes(mockQuizzes);
  }, []);

  const filteredQuizzes = quizzes.filter(quiz =>
    quiz.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    quiz.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAddQuiz = () => {
    console.log('Add quiz');
  };

  const handleEditQuiz = (quizId: string) => {
    console.log('Edit quiz:', quizId);
  };

  const handleDeleteQuiz = (quizId: string) => {
    console.log('Delete quiz:', quizId);
  };

  const handleViewQuiz = (quizId: string) => {
    console.log('View quiz:', quizId);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 space-x-reverse">
          <button
            onClick={onBack}
            className="p-2 text-gray-600 hover:text-gray-900 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">إدارة الاختبارات</h1>
            <p className="text-gray-600">إنشاء وإدارة اختبارات الكورسات</p>
          </div>
        </div>
        <button
          onClick={handleAddQuiz}
          className="flex items-center space-x-2 space-x-reverse bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          <PlusIcon className="w-5 h-5" />
          <span>إضافة اختبار جديد</span>
        </button>
      </div>

      {/* Search */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            البحث في الاختبارات
          </label>
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="ابحث عن اختبار..."
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      {/* Quizzes Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredQuizzes.map((quiz, index) => (
          <motion.div
            key={quiz.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow"
          >
            <div className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <ClipboardDocumentListIcon className="w-6 h-6 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{quiz.title}</h3>
                    <p className="text-sm text-gray-600">{quiz.description}</p>
                  </div>
                </div>
                <span className={`px-2 py-1 text-xs rounded-full ${
                  quiz.isActive 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {quiz.isActive ? 'نشط' : 'غير نشط'}
                </span>
              </div>

              <div className="space-y-2 text-sm text-gray-600 mb-4">
                <div className="flex items-center justify-between">
                  <span>عدد الأسئلة:</span>
                  <span className="font-medium">{quiz.questions.length}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>درجة النجاح:</span>
                  <span className="font-medium">{quiz.passingScore}%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>الوقت المحدد:</span>
                  <span className="font-medium">{quiz.timeLimit} دقيقة</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>عدد المحاولات:</span>
                  <span className="font-medium">{quiz.attempts}</span>
                </div>
              </div>

              <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <button
                    onClick={() => handleViewQuiz(quiz.id)}
                    className="p-2 text-gray-600 hover:text-blue-600 transition-colors"
                    title="عرض الاختبار"
                  >
                    <EyeIcon className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleEditQuiz(quiz.id)}
                    className="p-2 text-gray-600 hover:text-green-600 transition-colors"
                    title="تعديل الاختبار"
                  >
                    <PencilIcon className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleDeleteQuiz(quiz.id)}
                    className="p-2 text-gray-600 hover:text-red-600 transition-colors"
                    title="حذف الاختبار"
                  >
                    <TrashIcon className="w-4 h-4" />
                  </button>
                </div>
                <span className="text-xs text-gray-500">
                  {new Date(quiz.createdAt).toLocaleDateString('ar-SA')}
                </span>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {filteredQuizzes.length === 0 && (
        <div className="text-center py-12">
          <QuestionMarkCircleIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد اختبارات</h3>
          <p className="text-gray-600">لم يتم العثور على أي اختبارات تطابق البحث</p>
        </div>
      )}
    </div>
  );
};

export default QuizzesManagement;
