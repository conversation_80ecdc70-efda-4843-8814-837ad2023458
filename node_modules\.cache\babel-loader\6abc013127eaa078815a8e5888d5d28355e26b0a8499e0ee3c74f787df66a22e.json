{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0634\\u0631\\u0648\\u0639\\\\src\\\\components\\\\Admin\\\\QuickActions.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { useNavigate } from 'react-router-dom';\nimport { PlusIcon, FolderPlusIcon, UserPlusIcon, ClipboardDocumentListIcon, DocumentTextIcon, ChartBarIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuickActions = () => {\n  _s();\n  const navigate = useNavigate();\n  const actions = [{\n    title: 'إضافة كورس جديد',\n    description: 'إنشاء كورس تعليمي جديد',\n    icon: PlusIcon,\n    color: 'blue',\n    onClick: () => navigate('/admin/courses?action=create')\n  }, {\n    title: 'إضافة قسم',\n    description: 'إنشاء قسم جديد للكورسات',\n    icon: FolderPlusIcon,\n    color: 'green',\n    onClick: () => navigate('/admin/categories?action=create')\n  }, {\n    title: 'إضافة طالب',\n    description: 'إنشاء حساب طالب جديد',\n    icon: UserPlusIcon,\n    color: 'purple',\n    onClick: () => navigate('/admin/students?action=create')\n  }, {\n    title: 'إنشاء اختبار',\n    description: 'إضافة اختبار جديد',\n    icon: ClipboardDocumentListIcon,\n    color: 'orange',\n    onClick: () => navigate('/admin/quizzes?action=create')\n  }, {\n    title: 'إصدار شهادة',\n    description: 'إنشاء شهادة جديدة',\n    icon: DocumentTextIcon,\n    color: 'red',\n    onClick: () => navigate('/admin/certificates?action=create')\n  }, {\n    title: 'عرض التحليلات',\n    description: 'مراجعة إحصائيات المنصة',\n    icon: ChartBarIcon,\n    color: 'indigo',\n    onClick: () => navigate('/admin/analytics')\n  }];\n  const colorClasses = {\n    blue: 'from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700',\n    green: 'from-green-500 to-green-600 hover:from-green-600 hover:to-green-700',\n    purple: 'from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700',\n    orange: 'from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700',\n    red: 'from-red-500 to-red-600 hover:from-red-600 hover:to-red-700',\n    indigo: 'from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-xl p-6 shadow-sm\",\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      className: \"text-lg font-bold text-gray-900 mb-4\",\n      children: \"\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A \\u0633\\u0631\\u064A\\u0639\\u0629\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n      children: actions.map((action, index) => /*#__PURE__*/_jsxDEV(motion.button, {\n        initial: {\n          opacity: 0,\n          scale: 0.9\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        transition: {\n          delay: index * 0.1\n        },\n        whileHover: {\n          scale: 1.02\n        },\n        whileTap: {\n          scale: 0.98\n        },\n        onClick: action.onClick,\n        className: `\n              p-4 rounded-lg text-white text-right transition-all duration-200\n              bg-gradient-to-br ${colorClasses[action.color]}\n              hover:shadow-lg transform hover:-translate-y-1\n            `,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-semibold text-sm mb-1\",\n              children: action.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs opacity-90\",\n              children: action.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(action.icon, {\n            className: \"w-6 h-6 opacity-80\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 13\n        }, this)\n      }, action.title, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6 pt-4 border-t border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between text-sm\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-600\",\n          children: \"\\u0622\\u062E\\u0631 \\u062A\\u062D\\u062F\\u064A\\u062B\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-900 font-medium\",\n          children: new Date().toLocaleDateString('ar-SA')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this);\n};\n_s(QuickActions, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = QuickActions;\nexport default QuickActions;\nvar _c;\n$RefreshReg$(_c, \"QuickActions\");", "map": {"version": 3, "names": ["React", "motion", "useNavigate", "PlusIcon", "FolderPlusIcon", "UserPlusIcon", "ClipboardDocumentListIcon", "DocumentTextIcon", "ChartBarIcon", "jsxDEV", "_jsxDEV", "QuickActions", "_s", "navigate", "actions", "title", "description", "icon", "color", "onClick", "colorClasses", "blue", "green", "purple", "orange", "red", "indigo", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "action", "index", "button", "initial", "opacity", "scale", "animate", "transition", "delay", "whileHover", "whileTap", "Date", "toLocaleDateString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Admin/QuickActions.tsx"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  PlusIcon,\n  FolderPlusIcon,\n  UserPlusIcon,\n  ClipboardDocumentListIcon,\n  DocumentTextIcon,\n  ChartBarIcon\n} from '@heroicons/react/24/outline';\n\nconst QuickActions: React.FC = () => {\n  const navigate = useNavigate();\n\n  const actions = [\n    {\n      title: 'إضافة كورس جديد',\n      description: 'إنشاء كورس تعليمي جديد',\n      icon: PlusIcon,\n      color: 'blue',\n      onClick: () => navigate('/admin/courses?action=create')\n    },\n    {\n      title: 'إضافة قسم',\n      description: 'إنشاء قسم جديد للكورسات',\n      icon: FolderPlusIcon,\n      color: 'green',\n      onClick: () => navigate('/admin/categories?action=create')\n    },\n    {\n      title: 'إضافة طالب',\n      description: 'إنشاء حساب طالب جديد',\n      icon: UserPlusIcon,\n      color: 'purple',\n      onClick: () => navigate('/admin/students?action=create')\n    },\n    {\n      title: 'إنشاء اختبار',\n      description: 'إضافة اختبار جديد',\n      icon: ClipboardDocumentListIcon,\n      color: 'orange',\n      onClick: () => navigate('/admin/quizzes?action=create')\n    },\n    {\n      title: 'إصدار شهادة',\n      description: 'إنشاء شهادة جديدة',\n      icon: DocumentTextIcon,\n      color: 'red',\n      onClick: () => navigate('/admin/certificates?action=create')\n    },\n    {\n      title: 'عرض التحليلات',\n      description: 'مراجعة إحصائيات المنصة',\n      icon: ChartBarIcon,\n      color: 'indigo',\n      onClick: () => navigate('/admin/analytics')\n    }\n  ];\n\n  const colorClasses = {\n    blue: 'from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700',\n    green: 'from-green-500 to-green-600 hover:from-green-600 hover:to-green-700',\n    purple: 'from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700',\n    orange: 'from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700',\n    red: 'from-red-500 to-red-600 hover:from-red-600 hover:to-red-700',\n    indigo: 'from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700'\n  };\n\n  return (\n    <div className=\"bg-white rounded-xl p-6 shadow-sm\">\n      <h3 className=\"text-lg font-bold text-gray-900 mb-4\">إجراءات سريعة</h3>\n      \n      <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\n        {actions.map((action, index) => (\n          <motion.button\n            key={action.title}\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ delay: index * 0.1 }}\n            whileHover={{ scale: 1.02 }}\n            whileTap={{ scale: 0.98 }}\n            onClick={action.onClick}\n            className={`\n              p-4 rounded-lg text-white text-right transition-all duration-200\n              bg-gradient-to-br ${colorClasses[action.color as keyof typeof colorClasses]}\n              hover:shadow-lg transform hover:-translate-y-1\n            `}\n          >\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex-1\">\n                <h4 className=\"font-semibold text-sm mb-1\">{action.title}</h4>\n                <p className=\"text-xs opacity-90\">{action.description}</p>\n              </div>\n              <action.icon className=\"w-6 h-6 opacity-80\" />\n            </div>\n          </motion.button>\n        ))}\n      </div>\n      \n      <div className=\"mt-6 pt-4 border-t border-gray-200\">\n        <div className=\"flex items-center justify-between text-sm\">\n          <span className=\"text-gray-600\">آخر تحديث</span>\n          <span className=\"text-gray-900 font-medium\">\n            {new Date().toLocaleDateString('ar-SA')}\n          </span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuickActions;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,yBAAyB,EACzBC,gBAAgB,EAChBC,YAAY,QACP,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAE9B,MAAMY,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,wBAAwB;IACrCC,IAAI,EAAEd,QAAQ;IACde,KAAK,EAAE,MAAM;IACbC,OAAO,EAAEA,CAAA,KAAMN,QAAQ,CAAC,8BAA8B;EACxD,CAAC,EACD;IACEE,KAAK,EAAE,WAAW;IAClBC,WAAW,EAAE,yBAAyB;IACtCC,IAAI,EAAEb,cAAc;IACpBc,KAAK,EAAE,OAAO;IACdC,OAAO,EAAEA,CAAA,KAAMN,QAAQ,CAAC,iCAAiC;EAC3D,CAAC,EACD;IACEE,KAAK,EAAE,YAAY;IACnBC,WAAW,EAAE,sBAAsB;IACnCC,IAAI,EAAEZ,YAAY;IAClBa,KAAK,EAAE,QAAQ;IACfC,OAAO,EAAEA,CAAA,KAAMN,QAAQ,CAAC,+BAA+B;EACzD,CAAC,EACD;IACEE,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE,mBAAmB;IAChCC,IAAI,EAAEX,yBAAyB;IAC/BY,KAAK,EAAE,QAAQ;IACfC,OAAO,EAAEA,CAAA,KAAMN,QAAQ,CAAC,8BAA8B;EACxD,CAAC,EACD;IACEE,KAAK,EAAE,aAAa;IACpBC,WAAW,EAAE,mBAAmB;IAChCC,IAAI,EAAEV,gBAAgB;IACtBW,KAAK,EAAE,KAAK;IACZC,OAAO,EAAEA,CAAA,KAAMN,QAAQ,CAAC,mCAAmC;EAC7D,CAAC,EACD;IACEE,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE,wBAAwB;IACrCC,IAAI,EAAET,YAAY;IAClBU,KAAK,EAAE,QAAQ;IACfC,OAAO,EAAEA,CAAA,KAAMN,QAAQ,CAAC,kBAAkB;EAC5C,CAAC,CACF;EAED,MAAMO,YAAY,GAAG;IACnBC,IAAI,EAAE,iEAAiE;IACvEC,KAAK,EAAE,qEAAqE;IAC5EC,MAAM,EAAE,yEAAyE;IACjFC,MAAM,EAAE,yEAAyE;IACjFC,GAAG,EAAE,6DAA6D;IAClEC,MAAM,EAAE;EACV,CAAC;EAED,oBACEhB,OAAA;IAAKiB,SAAS,EAAC,mCAAmC;IAAAC,QAAA,gBAChDlB,OAAA;MAAIiB,SAAS,EAAC,sCAAsC;MAAAC,QAAA,EAAC;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEvEtB,OAAA;MAAKiB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,EACnDd,OAAO,CAACmB,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACzBzB,OAAA,CAACT,MAAM,CAACmC,MAAM;QAEZC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAI,CAAE;QACpCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,KAAK,EAAEP,KAAK,GAAG;QAAI,CAAE;QACnCQ,UAAU,EAAE;UAAEJ,KAAK,EAAE;QAAK,CAAE;QAC5BK,QAAQ,EAAE;UAAEL,KAAK,EAAE;QAAK,CAAE;QAC1BpB,OAAO,EAAEe,MAAM,CAACf,OAAQ;QACxBQ,SAAS,EAAE;AACvB;AACA,kCAAkCP,YAAY,CAACc,MAAM,CAAChB,KAAK,CAA8B;AACzF;AACA,aAAc;QAAAU,QAAA,eAEFlB,OAAA;UAAKiB,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDlB,OAAA;YAAKiB,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrBlB,OAAA;cAAIiB,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAEM,MAAM,CAACnB;YAAK;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9DtB,OAAA;cAAGiB,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAEM,MAAM,CAAClB;YAAW;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eACNtB,OAAA,CAACwB,MAAM,CAACjB,IAAI;YAACU,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C;MAAC,GAnBDE,MAAM,CAACnB,KAAK;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoBJ,CAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENtB,OAAA;MAAKiB,SAAS,EAAC,oCAAoC;MAAAC,QAAA,eACjDlB,OAAA;QAAKiB,SAAS,EAAC,2CAA2C;QAAAC,QAAA,gBACxDlB,OAAA;UAAMiB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAChDtB,OAAA;UAAMiB,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EACxC,IAAIiB,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,OAAO;QAAC;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpB,EAAA,CAlGID,YAAsB;EAAA,QACTT,WAAW;AAAA;AAAA6C,EAAA,GADxBpC,YAAsB;AAoG5B,eAAeA,YAAY;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}