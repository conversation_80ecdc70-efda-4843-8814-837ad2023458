{"ast": null, "code": "import { createMotionComponent } from '../../motion/index.mjs';\nimport { createMotionProxy } from './motion-proxy.mjs';\nimport { createDomMotionConfig } from './utils/create-config.mjs';\nimport { gestureAnimations } from '../../motion/features/gestures.mjs';\nimport { animations } from '../../motion/features/animations.mjs';\nimport { drag } from '../../motion/features/drag.mjs';\nimport { createDomVisualElement } from './create-visual-element.mjs';\nimport { layout } from '../../motion/features/layout.mjs';\nconst preloadedFeatures = {\n  ...animations,\n  ...gestureAnimations,\n  ...drag,\n  ...layout\n};\n/**\n * HTML & SVG components, optimised for use with gestures and animation. These can be used as\n * drop-in replacements for any HTML & SVG component, all CSS & SVG properties are supported.\n *\n * @public\n */\nconst motion = /*@__PURE__*/createMotionProxy((Component, config) => createDomMotionConfig(Component, config, preloadedFeatures, createDomVisualElement));\n/**\n * Create a DOM `motion` component with the provided string. This is primarily intended\n * as a full alternative to `motion` for consumers who have to support environments that don't\n * support `Proxy`.\n *\n * ```javascript\n * import { createDomMotionComponent } from \"framer-motion\"\n *\n * const motion = {\n *   div: createDomMotionComponent('div')\n * }\n * ```\n *\n * @public\n */\nfunction createDomMotionComponent(key) {\n  return createMotionComponent(createDomMotionConfig(key, {\n    forwardMotionProps: false\n  }, preloadedFeatures, createDomVisualElement));\n}\nexport { createDomMotionComponent, motion };", "map": {"version": 3, "names": ["createMotionComponent", "createMotionProxy", "createDomMotionConfig", "gestureAnimations", "animations", "drag", "createDomVisualElement", "layout", "preloadedFeatures", "motion", "Component", "config", "createDomMotionComponent", "key", "forwardMotionProps"], "sources": ["C:/Users/<USER>/Desktop/مشروع/node_modules/framer-motion/dist/es/render/dom/motion.mjs"], "sourcesContent": ["import { createMotionComponent } from '../../motion/index.mjs';\nimport { createMotionProxy } from './motion-proxy.mjs';\nimport { createDomMotionConfig } from './utils/create-config.mjs';\nimport { gestureAnimations } from '../../motion/features/gestures.mjs';\nimport { animations } from '../../motion/features/animations.mjs';\nimport { drag } from '../../motion/features/drag.mjs';\nimport { createDomVisualElement } from './create-visual-element.mjs';\nimport { layout } from '../../motion/features/layout.mjs';\n\nconst preloadedFeatures = {\n    ...animations,\n    ...gestureAnimations,\n    ...drag,\n    ...layout,\n};\n/**\n * HTML & SVG components, optimised for use with gestures and animation. These can be used as\n * drop-in replacements for any HTML & SVG component, all CSS & SVG properties are supported.\n *\n * @public\n */\nconst motion = /*@__PURE__*/ createMotionProxy((Component, config) => createDomMotionConfig(Component, config, preloadedFeatures, createDomVisualElement));\n/**\n * Create a DOM `motion` component with the provided string. This is primarily intended\n * as a full alternative to `motion` for consumers who have to support environments that don't\n * support `Proxy`.\n *\n * ```javascript\n * import { createDomMotionComponent } from \"framer-motion\"\n *\n * const motion = {\n *   div: createDomMotionComponent('div')\n * }\n * ```\n *\n * @public\n */\nfunction createDomMotionComponent(key) {\n    return createMotionComponent(createDomMotionConfig(key, { forwardMotionProps: false }, preloadedFeatures, createDomVisualElement));\n}\n\nexport { createDomMotionComponent, motion };\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,wBAAwB;AAC9D,SAASC,iBAAiB,QAAQ,oBAAoB;AACtD,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,iBAAiB,QAAQ,oCAAoC;AACtE,SAASC,UAAU,QAAQ,sCAAsC;AACjE,SAASC,IAAI,QAAQ,gCAAgC;AACrD,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,SAASC,MAAM,QAAQ,kCAAkC;AAEzD,MAAMC,iBAAiB,GAAG;EACtB,GAAGJ,UAAU;EACb,GAAGD,iBAAiB;EACpB,GAAGE,IAAI;EACP,GAAGE;AACP,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,MAAM,GAAG,aAAcR,iBAAiB,CAAC,CAACS,SAAS,EAAEC,MAAM,KAAKT,qBAAqB,CAACQ,SAAS,EAAEC,MAAM,EAAEH,iBAAiB,EAAEF,sBAAsB,CAAC,CAAC;AAC1J;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,wBAAwBA,CAACC,GAAG,EAAE;EACnC,OAAOb,qBAAqB,CAACE,qBAAqB,CAACW,GAAG,EAAE;IAAEC,kBAAkB,EAAE;EAAM,CAAC,EAAEN,iBAAiB,EAAEF,sBAAsB,CAAC,CAAC;AACtI;AAEA,SAASM,wBAAwB,EAAEH,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}