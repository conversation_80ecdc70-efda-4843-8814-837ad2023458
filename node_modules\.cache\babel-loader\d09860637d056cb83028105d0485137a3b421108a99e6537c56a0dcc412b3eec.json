{"ast": null, "code": "import { isNumericalString } from '../../utils/is-numerical-string.mjs';\nimport { isZeroValueString } from '../../utils/is-zero-value-string.mjs';\nimport { resolveFinalValueInKeyframes } from '../../utils/resolve-value.mjs';\nimport { motionValue } from '../../value/index.mjs';\nimport { complex } from '../../value/types/complex/index.mjs';\nimport { getAnimatableNone } from '../dom/value-types/animatable-none.mjs';\nimport { findValueType } from '../dom/value-types/find.mjs';\nimport { resolveVariant } from './resolve-dynamic-variants.mjs';\n\n/**\n * Set VisualElement's MotionValue, creating a new MotionValue for it if\n * it doesn't exist.\n */\nfunction setMotionValue(visualElement, key, value) {\n  if (visualElement.hasValue(key)) {\n    visualElement.getValue(key).set(value);\n  } else {\n    visualElement.addValue(key, motionValue(value));\n  }\n}\nfunction setTarget(visualElement, definition) {\n  const resolved = resolveVariant(visualElement, definition);\n  let {\n    transitionEnd = {},\n    transition = {},\n    ...target\n  } = resolved ? visualElement.makeTargetAnimatable(resolved, false) : {};\n  target = {\n    ...target,\n    ...transitionEnd\n  };\n  for (const key in target) {\n    const value = resolveFinalValueInKeyframes(target[key]);\n    setMotionValue(visualElement, key, value);\n  }\n}\nfunction setVariants(visualElement, variantLabels) {\n  const reversedLabels = [...variantLabels].reverse();\n  reversedLabels.forEach(key => {\n    const variant = visualElement.getVariant(key);\n    variant && setTarget(visualElement, variant);\n    if (visualElement.variantChildren) {\n      visualElement.variantChildren.forEach(child => {\n        setVariants(child, variantLabels);\n      });\n    }\n  });\n}\nfunction setValues(visualElement, definition) {\n  if (Array.isArray(definition)) {\n    return setVariants(visualElement, definition);\n  } else if (typeof definition === \"string\") {\n    return setVariants(visualElement, [definition]);\n  } else {\n    setTarget(visualElement, definition);\n  }\n}\nfunction checkTargetForNewValues(visualElement, target, origin) {\n  var _a, _b;\n  const newValueKeys = Object.keys(target).filter(key => !visualElement.hasValue(key));\n  const numNewValues = newValueKeys.length;\n  if (!numNewValues) return;\n  for (let i = 0; i < numNewValues; i++) {\n    const key = newValueKeys[i];\n    const targetValue = target[key];\n    let value = null;\n    /**\n     * If the target is a series of keyframes, we can use the first value\n     * in the array. If this first value is null, we'll still need to read from the DOM.\n     */\n    if (Array.isArray(targetValue)) {\n      value = targetValue[0];\n    }\n    /**\n     * If the target isn't keyframes, or the first keyframe was null, we need to\n     * first check if an origin value was explicitly defined in the transition as \"from\",\n     * if not read the value from the DOM. As an absolute fallback, take the defined target value.\n     */\n    if (value === null) {\n      value = (_b = (_a = origin[key]) !== null && _a !== void 0 ? _a : visualElement.readValue(key)) !== null && _b !== void 0 ? _b : target[key];\n    }\n    /**\n     * If value is still undefined or null, ignore it. Preferably this would throw,\n     * but this was causing issues in Framer.\n     */\n    if (value === undefined || value === null) continue;\n    if (typeof value === \"string\" && (isNumericalString(value) || isZeroValueString(value))) {\n      // If this is a number read as a string, ie \"0\" or \"200\", convert it to a number\n      value = parseFloat(value);\n    } else if (!findValueType(value) && complex.test(targetValue)) {\n      value = getAnimatableNone(key, targetValue);\n    }\n    visualElement.addValue(key, motionValue(value, {\n      owner: visualElement\n    }));\n    if (origin[key] === undefined) {\n      origin[key] = value;\n    }\n    if (value !== null) visualElement.setBaseTarget(key, value);\n  }\n}\nfunction getOriginFromTransition(key, transition) {\n  if (!transition) return;\n  const valueTransition = transition[key] || transition[\"default\"] || transition;\n  return valueTransition.from;\n}\nfunction getOrigin(target, transition, visualElement) {\n  const origin = {};\n  for (const key in target) {\n    const transitionOrigin = getOriginFromTransition(key, transition);\n    if (transitionOrigin !== undefined) {\n      origin[key] = transitionOrigin;\n    } else {\n      const value = visualElement.getValue(key);\n      if (value) {\n        origin[key] = value.get();\n      }\n    }\n  }\n  return origin;\n}\nexport { checkTargetForNewValues, getOrigin, getOriginFromTransition, setTarget, setValues };", "map": {"version": 3, "names": ["isNumericalString", "isZeroValueString", "resolveFinalValueInKeyframes", "motionValue", "complex", "getAnimatableNone", "findValueType", "resolveV<PERSON>t", "setMotionValue", "visualElement", "key", "value", "hasValue", "getValue", "set", "addValue", "<PERSON><PERSON><PERSON><PERSON>", "definition", "resolved", "transitionEnd", "transition", "target", "makeTargetAnimatable", "setVariants", "variantLabels", "<PERSON><PERSON><PERSON><PERSON>", "reverse", "for<PERSON>ach", "variant", "getVariant", "variant<PERSON><PERSON><PERSON>n", "child", "set<PERSON><PERSON><PERSON>", "Array", "isArray", "checkTargetForNewValues", "origin", "_a", "_b", "newValueKeys", "Object", "keys", "filter", "numNewV<PERSON>ues", "length", "i", "targetValue", "readValue", "undefined", "parseFloat", "test", "owner", "set<PERSON><PERSON><PERSON><PERSON>get", "getOriginFromTransition", "valueTransition", "from", "<PERSON><PERSON><PERSON><PERSON>", "transitionOrigin", "get"], "sources": ["C:/Users/<USER>/Desktop/مشروع/node_modules/framer-motion/dist/es/render/utils/setters.mjs"], "sourcesContent": ["import { isNumericalString } from '../../utils/is-numerical-string.mjs';\nimport { isZeroValueString } from '../../utils/is-zero-value-string.mjs';\nimport { resolveFinalValueInKeyframes } from '../../utils/resolve-value.mjs';\nimport { motionValue } from '../../value/index.mjs';\nimport { complex } from '../../value/types/complex/index.mjs';\nimport { getAnimatableNone } from '../dom/value-types/animatable-none.mjs';\nimport { findValueType } from '../dom/value-types/find.mjs';\nimport { resolveVariant } from './resolve-dynamic-variants.mjs';\n\n/**\n * Set VisualElement's MotionValue, creating a new MotionValue for it if\n * it doesn't exist.\n */\nfunction setMotionValue(visualElement, key, value) {\n    if (visualElement.hasValue(key)) {\n        visualElement.getValue(key).set(value);\n    }\n    else {\n        visualElement.addValue(key, motionValue(value));\n    }\n}\nfunction setTarget(visualElement, definition) {\n    const resolved = resolveVariant(visualElement, definition);\n    let { transitionEnd = {}, transition = {}, ...target } = resolved ? visualElement.makeTargetAnimatable(resolved, false) : {};\n    target = { ...target, ...transitionEnd };\n    for (const key in target) {\n        const value = resolveFinalValueInKeyframes(target[key]);\n        setMotionValue(visualElement, key, value);\n    }\n}\nfunction setVariants(visualElement, variantLabels) {\n    const reversedLabels = [...variantLabels].reverse();\n    reversedLabels.forEach((key) => {\n        const variant = visualElement.getVariant(key);\n        variant && setTarget(visualElement, variant);\n        if (visualElement.variantChildren) {\n            visualElement.variantChildren.forEach((child) => {\n                setVariants(child, variantLabels);\n            });\n        }\n    });\n}\nfunction setValues(visualElement, definition) {\n    if (Array.isArray(definition)) {\n        return setVariants(visualElement, definition);\n    }\n    else if (typeof definition === \"string\") {\n        return setVariants(visualElement, [definition]);\n    }\n    else {\n        setTarget(visualElement, definition);\n    }\n}\nfunction checkTargetForNewValues(visualElement, target, origin) {\n    var _a, _b;\n    const newValueKeys = Object.keys(target).filter((key) => !visualElement.hasValue(key));\n    const numNewValues = newValueKeys.length;\n    if (!numNewValues)\n        return;\n    for (let i = 0; i < numNewValues; i++) {\n        const key = newValueKeys[i];\n        const targetValue = target[key];\n        let value = null;\n        /**\n         * If the target is a series of keyframes, we can use the first value\n         * in the array. If this first value is null, we'll still need to read from the DOM.\n         */\n        if (Array.isArray(targetValue)) {\n            value = targetValue[0];\n        }\n        /**\n         * If the target isn't keyframes, or the first keyframe was null, we need to\n         * first check if an origin value was explicitly defined in the transition as \"from\",\n         * if not read the value from the DOM. As an absolute fallback, take the defined target value.\n         */\n        if (value === null) {\n            value = (_b = (_a = origin[key]) !== null && _a !== void 0 ? _a : visualElement.readValue(key)) !== null && _b !== void 0 ? _b : target[key];\n        }\n        /**\n         * If value is still undefined or null, ignore it. Preferably this would throw,\n         * but this was causing issues in Framer.\n         */\n        if (value === undefined || value === null)\n            continue;\n        if (typeof value === \"string\" &&\n            (isNumericalString(value) || isZeroValueString(value))) {\n            // If this is a number read as a string, ie \"0\" or \"200\", convert it to a number\n            value = parseFloat(value);\n        }\n        else if (!findValueType(value) && complex.test(targetValue)) {\n            value = getAnimatableNone(key, targetValue);\n        }\n        visualElement.addValue(key, motionValue(value, { owner: visualElement }));\n        if (origin[key] === undefined) {\n            origin[key] = value;\n        }\n        if (value !== null)\n            visualElement.setBaseTarget(key, value);\n    }\n}\nfunction getOriginFromTransition(key, transition) {\n    if (!transition)\n        return;\n    const valueTransition = transition[key] || transition[\"default\"] || transition;\n    return valueTransition.from;\n}\nfunction getOrigin(target, transition, visualElement) {\n    const origin = {};\n    for (const key in target) {\n        const transitionOrigin = getOriginFromTransition(key, transition);\n        if (transitionOrigin !== undefined) {\n            origin[key] = transitionOrigin;\n        }\n        else {\n            const value = visualElement.getValue(key);\n            if (value) {\n                origin[key] = value.get();\n            }\n        }\n    }\n    return origin;\n}\n\nexport { checkTargetForNewValues, getOrigin, getOriginFromTransition, setTarget, setValues };\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,qCAAqC;AACvE,SAASC,iBAAiB,QAAQ,sCAAsC;AACxE,SAASC,4BAA4B,QAAQ,+BAA+B;AAC5E,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,OAAO,QAAQ,qCAAqC;AAC7D,SAASC,iBAAiB,QAAQ,wCAAwC;AAC1E,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,cAAc,QAAQ,gCAAgC;;AAE/D;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,aAAa,EAAEC,GAAG,EAAEC,KAAK,EAAE;EAC/C,IAAIF,aAAa,CAACG,QAAQ,CAACF,GAAG,CAAC,EAAE;IAC7BD,aAAa,CAACI,QAAQ,CAACH,GAAG,CAAC,CAACI,GAAG,CAACH,KAAK,CAAC;EAC1C,CAAC,MACI;IACDF,aAAa,CAACM,QAAQ,CAACL,GAAG,EAAEP,WAAW,CAACQ,KAAK,CAAC,CAAC;EACnD;AACJ;AACA,SAASK,SAASA,CAACP,aAAa,EAAEQ,UAAU,EAAE;EAC1C,MAAMC,QAAQ,GAAGX,cAAc,CAACE,aAAa,EAAEQ,UAAU,CAAC;EAC1D,IAAI;IAAEE,aAAa,GAAG,CAAC,CAAC;IAAEC,UAAU,GAAG,CAAC,CAAC;IAAE,GAAGC;EAAO,CAAC,GAAGH,QAAQ,GAAGT,aAAa,CAACa,oBAAoB,CAACJ,QAAQ,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;EAC5HG,MAAM,GAAG;IAAE,GAAGA,MAAM;IAAE,GAAGF;EAAc,CAAC;EACxC,KAAK,MAAMT,GAAG,IAAIW,MAAM,EAAE;IACtB,MAAMV,KAAK,GAAGT,4BAA4B,CAACmB,MAAM,CAACX,GAAG,CAAC,CAAC;IACvDF,cAAc,CAACC,aAAa,EAAEC,GAAG,EAAEC,KAAK,CAAC;EAC7C;AACJ;AACA,SAASY,WAAWA,CAACd,aAAa,EAAEe,aAAa,EAAE;EAC/C,MAAMC,cAAc,GAAG,CAAC,GAAGD,aAAa,CAAC,CAACE,OAAO,CAAC,CAAC;EACnDD,cAAc,CAACE,OAAO,CAAEjB,GAAG,IAAK;IAC5B,MAAMkB,OAAO,GAAGnB,aAAa,CAACoB,UAAU,CAACnB,GAAG,CAAC;IAC7CkB,OAAO,IAAIZ,SAAS,CAACP,aAAa,EAAEmB,OAAO,CAAC;IAC5C,IAAInB,aAAa,CAACqB,eAAe,EAAE;MAC/BrB,aAAa,CAACqB,eAAe,CAACH,OAAO,CAAEI,KAAK,IAAK;QAC7CR,WAAW,CAACQ,KAAK,EAAEP,aAAa,CAAC;MACrC,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;AACN;AACA,SAASQ,SAASA,CAACvB,aAAa,EAAEQ,UAAU,EAAE;EAC1C,IAAIgB,KAAK,CAACC,OAAO,CAACjB,UAAU,CAAC,EAAE;IAC3B,OAAOM,WAAW,CAACd,aAAa,EAAEQ,UAAU,CAAC;EACjD,CAAC,MACI,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;IACrC,OAAOM,WAAW,CAACd,aAAa,EAAE,CAACQ,UAAU,CAAC,CAAC;EACnD,CAAC,MACI;IACDD,SAAS,CAACP,aAAa,EAAEQ,UAAU,CAAC;EACxC;AACJ;AACA,SAASkB,uBAAuBA,CAAC1B,aAAa,EAAEY,MAAM,EAAEe,MAAM,EAAE;EAC5D,IAAIC,EAAE,EAAEC,EAAE;EACV,MAAMC,YAAY,GAAGC,MAAM,CAACC,IAAI,CAACpB,MAAM,CAAC,CAACqB,MAAM,CAAEhC,GAAG,IAAK,CAACD,aAAa,CAACG,QAAQ,CAACF,GAAG,CAAC,CAAC;EACtF,MAAMiC,YAAY,GAAGJ,YAAY,CAACK,MAAM;EACxC,IAAI,CAACD,YAAY,EACb;EACJ,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,YAAY,EAAEE,CAAC,EAAE,EAAE;IACnC,MAAMnC,GAAG,GAAG6B,YAAY,CAACM,CAAC,CAAC;IAC3B,MAAMC,WAAW,GAAGzB,MAAM,CAACX,GAAG,CAAC;IAC/B,IAAIC,KAAK,GAAG,IAAI;IAChB;AACR;AACA;AACA;IACQ,IAAIsB,KAAK,CAACC,OAAO,CAACY,WAAW,CAAC,EAAE;MAC5BnC,KAAK,GAAGmC,WAAW,CAAC,CAAC,CAAC;IAC1B;IACA;AACR;AACA;AACA;AACA;IACQ,IAAInC,KAAK,KAAK,IAAI,EAAE;MAChBA,KAAK,GAAG,CAAC2B,EAAE,GAAG,CAACD,EAAE,GAAGD,MAAM,CAAC1B,GAAG,CAAC,MAAM,IAAI,IAAI2B,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG5B,aAAa,CAACsC,SAAS,CAACrC,GAAG,CAAC,MAAM,IAAI,IAAI4B,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGjB,MAAM,CAACX,GAAG,CAAC;IAChJ;IACA;AACR;AACA;AACA;IACQ,IAAIC,KAAK,KAAKqC,SAAS,IAAIrC,KAAK,KAAK,IAAI,EACrC;IACJ,IAAI,OAAOA,KAAK,KAAK,QAAQ,KACxBX,iBAAiB,CAACW,KAAK,CAAC,IAAIV,iBAAiB,CAACU,KAAK,CAAC,CAAC,EAAE;MACxD;MACAA,KAAK,GAAGsC,UAAU,CAACtC,KAAK,CAAC;IAC7B,CAAC,MACI,IAAI,CAACL,aAAa,CAACK,KAAK,CAAC,IAAIP,OAAO,CAAC8C,IAAI,CAACJ,WAAW,CAAC,EAAE;MACzDnC,KAAK,GAAGN,iBAAiB,CAACK,GAAG,EAAEoC,WAAW,CAAC;IAC/C;IACArC,aAAa,CAACM,QAAQ,CAACL,GAAG,EAAEP,WAAW,CAACQ,KAAK,EAAE;MAAEwC,KAAK,EAAE1C;IAAc,CAAC,CAAC,CAAC;IACzE,IAAI2B,MAAM,CAAC1B,GAAG,CAAC,KAAKsC,SAAS,EAAE;MAC3BZ,MAAM,CAAC1B,GAAG,CAAC,GAAGC,KAAK;IACvB;IACA,IAAIA,KAAK,KAAK,IAAI,EACdF,aAAa,CAAC2C,aAAa,CAAC1C,GAAG,EAAEC,KAAK,CAAC;EAC/C;AACJ;AACA,SAAS0C,uBAAuBA,CAAC3C,GAAG,EAAEU,UAAU,EAAE;EAC9C,IAAI,CAACA,UAAU,EACX;EACJ,MAAMkC,eAAe,GAAGlC,UAAU,CAACV,GAAG,CAAC,IAAIU,UAAU,CAAC,SAAS,CAAC,IAAIA,UAAU;EAC9E,OAAOkC,eAAe,CAACC,IAAI;AAC/B;AACA,SAASC,SAASA,CAACnC,MAAM,EAAED,UAAU,EAAEX,aAAa,EAAE;EAClD,MAAM2B,MAAM,GAAG,CAAC,CAAC;EACjB,KAAK,MAAM1B,GAAG,IAAIW,MAAM,EAAE;IACtB,MAAMoC,gBAAgB,GAAGJ,uBAAuB,CAAC3C,GAAG,EAAEU,UAAU,CAAC;IACjE,IAAIqC,gBAAgB,KAAKT,SAAS,EAAE;MAChCZ,MAAM,CAAC1B,GAAG,CAAC,GAAG+C,gBAAgB;IAClC,CAAC,MACI;MACD,MAAM9C,KAAK,GAAGF,aAAa,CAACI,QAAQ,CAACH,GAAG,CAAC;MACzC,IAAIC,KAAK,EAAE;QACPyB,MAAM,CAAC1B,GAAG,CAAC,GAAGC,KAAK,CAAC+C,GAAG,CAAC,CAAC;MAC7B;IACJ;EACJ;EACA,OAAOtB,MAAM;AACjB;AAEA,SAASD,uBAAuB,EAAEqB,SAAS,EAAEH,uBAAuB,EAAErC,SAAS,EAAEgB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}