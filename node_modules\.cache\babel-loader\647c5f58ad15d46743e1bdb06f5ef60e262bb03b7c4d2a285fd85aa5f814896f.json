{"ast": null, "code": "import React,{useState}from'react';import{motion}from'framer-motion';import{PlusIcon,PencilIcon,TrashIcon,EyeIcon,UserIcon,AcademicCapIcon,CheckBadgeIcon}from'@heroicons/react/24/outline';// Types\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const StudentsManagement=_ref=>{let{onBack}=_ref;const[students,setStudents]=useState([]);const[searchTerm,setSearchTerm]=useState('');const[statusFilter,setStatusFilter]=useState('all');// Mock data for demonstration\nconst mockStudents=[{id:'1',email:'<EMAIL>',role:'student',name:'أحمد محمد',accessCode:'STU001',enrolledCourses:['1','2'],completedCourses:['1'],certificates:['cert1'],createdAt:new Date('2024-01-15')},{id:'2',email:'<EMAIL>',role:'student',name:'فاطمة علي',accessCode:'STU002',enrolledCourses:['2'],completedCourses:[],certificates:[],createdAt:new Date('2024-02-10')},{id:'3',email:'<EMAIL>',role:'student',name:'محمد حسن',accessCode:'STU003',enrolledCourses:['1','2','3'],completedCourses:['1','2'],certificates:['cert1','cert2'],createdAt:new Date('2024-01-20')}];React.useEffect(()=>{setStudents(mockStudents);},[]);const filteredStudents=students.filter(student=>{var _student$name;const matchesSearch=((_student$name=student.name)===null||_student$name===void 0?void 0:_student$name.toLowerCase().includes(searchTerm.toLowerCase()))||student.email.toLowerCase().includes(searchTerm.toLowerCase())||student.accessCode.toLowerCase().includes(searchTerm.toLowerCase());let matchesStatus=true;if(statusFilter==='active'){matchesStatus=student.enrolledCourses.length>0;}else if(statusFilter==='completed'){matchesStatus=student.completedCourses.length>0;}return matchesSearch&&matchesStatus;});const handleAddStudent=()=>{// TODO: Implement add student functionality\nconsole.log('Add student');};const handleEditStudent=studentId=>{// TODO: Implement edit student functionality\nconsole.log('Edit student:',studentId);};const handleDeleteStudent=studentId=>{// TODO: Implement delete student functionality\nconsole.log('Delete student:',studentId);};const handleViewStudent=studentId=>{// TODO: Implement view student functionality\nconsole.log('View student:',studentId);};const getStudentStatus=student=>{if(student.completedCourses.length>0){return{status:'مكتمل',color:'green'};}else if(student.enrolledCourses.length>0){return{status:'نشط',color:'blue'};}else{return{status:'غير نشط',color:'gray'};}};return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4 space-x-reverse\",children:[onBack&&/*#__PURE__*/_jsx(\"button\",{onClick:onBack,className:\"p-2 text-gray-600 hover:text-gray-900 transition-colors\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-6 h-6\",fill:\"none\",stroke:\"currentColor\",viewBox:\"0 0 24 24\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M15 19l-7-7 7-7\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:\"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0637\\u0644\\u0627\\u0628\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0648\\u062A\\u062A\\u0628\\u0639 \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0637\\u0644\\u0627\\u0628 \\u0627\\u0644\\u0645\\u0633\\u062C\\u0644\\u064A\\u0646\"})]})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleAddStudent,className:\"flex items-center space-x-2 space-x-reverse bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",children:[/*#__PURE__*/_jsx(PlusIcon,{className:\"w-5 h-5\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0637\\u0627\\u0644\\u0628 \\u062C\\u062F\\u064A\\u062F\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow-sm p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0627\\u0644\\u0628\\u062D\\u062B \\u0641\\u064A \\u0627\\u0644\\u0637\\u0644\\u0627\\u0628\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value),placeholder:\"\\u0627\\u0628\\u062D\\u062B \\u0628\\u0627\\u0644\\u0627\\u0633\\u0645\\u060C \\u0627\\u0644\\u0625\\u064A\\u0645\\u064A\\u0644\\u060C \\u0623\\u0648 \\u0631\\u0645\\u0632 \\u0627\\u0644\\u0648\\u0635\\u0648\\u0644...\",className:\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u062D\\u0627\\u0644\\u0629 \\u0627\\u0644\\u0637\\u0627\\u0644\\u0628\"}),/*#__PURE__*/_jsxs(\"select\",{value:statusFilter,onChange:e=>setStatusFilter(e.target.value),className:\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"all\",children:\"\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0637\\u0644\\u0627\\u0628\"}),/*#__PURE__*/_jsx(\"option\",{value:\"active\",children:\"\\u0627\\u0644\\u0637\\u0644\\u0627\\u0628 \\u0627\\u0644\\u0646\\u0634\\u0637\\u0648\\u0646\"}),/*#__PURE__*/_jsx(\"option\",{value:\"completed\",children:\"\\u0627\\u0644\\u0637\\u0644\\u0627\\u0628 \\u0627\\u0644\\u0645\\u0643\\u062A\\u0645\\u0644\\u0648\\u0646\"})]})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow-sm overflow-hidden\",children:/*#__PURE__*/_jsx(\"div\",{className:\"overflow-x-auto\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"min-w-full divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-gray-50\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u0627\\u0644\\u0637\\u0627\\u0644\\u0628\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u0631\\u0645\\u0632 \\u0627\\u0644\\u0648\\u0635\\u0648\\u0644\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A \\u0627\\u0644\\u0645\\u0633\\u062C\\u0644\\u0629\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A \\u0627\\u0644\\u0645\\u0643\\u062A\\u0645\\u0644\\u0629\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u062A\\u0627\\u0631\\u064A\\u062E \\u0627\\u0644\\u062A\\u0633\\u062C\\u064A\\u0644\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{className:\"bg-white divide-y divide-gray-200\",children:filteredStudents.map((student,index)=>{const studentStatus=getStudentStatus(student);return/*#__PURE__*/_jsxs(motion.tr,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:index*0.05},className:\"hover:bg-gray-50\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 space-x-reverse\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(UserIcon,{className:\"w-6 h-6 text-blue-600\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-gray-900\",children:student.name||'غير محدد'}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-500\",children:student.email})]})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-mono text-gray-900 bg-gray-100 px-2 py-1 rounded\",children:student.accessCode})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1 space-x-reverse\",children:[/*#__PURE__*/_jsx(AcademicCapIcon,{className:\"w-4 h-4 text-blue-600\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-900\",children:student.enrolledCourses.length})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1 space-x-reverse\",children:[/*#__PURE__*/_jsx(CheckBadgeIcon,{className:\"w-4 h-4 text-green-600\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-900\",children:student.completedCourses.length})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-900\",children:student.certificates.length})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"span\",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${studentStatus.color==='green'?'bg-green-100 text-green-800':studentStatus.color==='blue'?'bg-blue-100 text-blue-800':'bg-gray-100 text-gray-800'}`,children:studentStatus.status})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",children:new Date(student.createdAt).toLocaleDateString('ar-SA')}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm font-medium\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 space-x-reverse\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleViewStudent(student.id),className:\"text-blue-600 hover:text-blue-900\",title:\"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u0637\\u0627\\u0644\\u0628\",children:/*#__PURE__*/_jsx(EyeIcon,{className:\"w-4 h-4\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleEditStudent(student.id),className:\"text-green-600 hover:text-green-900\",title:\"\\u062A\\u0639\\u062F\\u064A\\u0644 \\u0627\\u0644\\u0637\\u0627\\u0644\\u0628\",children:/*#__PURE__*/_jsx(PencilIcon,{className:\"w-4 h-4\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleDeleteStudent(student.id),className:\"text-red-600 hover:text-red-900\",title:\"\\u062D\\u0630\\u0641 \\u0627\\u0644\\u0637\\u0627\\u0644\\u0628\",children:/*#__PURE__*/_jsx(TrashIcon,{className:\"w-4 h-4\"})})]})})]},student.id);})})]})})}),filteredStudents.length===0&&/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(UserIcon,{className:\"w-12 h-12 text-gray-400 mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-2\",children:\"\\u0644\\u0627 \\u064A\\u0648\\u062C\\u062F \\u0637\\u0644\\u0627\\u0628\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"\\u0644\\u0645 \\u064A\\u062A\\u0645 \\u0627\\u0644\\u0639\\u062B\\u0648\\u0631 \\u0639\\u0644\\u0649 \\u0623\\u064A \\u0637\\u0644\\u0627\\u0628 \\u064A\\u0637\\u0627\\u0628\\u0642\\u0648\\u0646 \\u0627\\u0644\\u0628\\u062D\\u062B\"})]})]});};export default StudentsManagement;", "map": {"version": 3, "names": ["React", "useState", "motion", "PlusIcon", "PencilIcon", "TrashIcon", "EyeIcon", "UserIcon", "AcademicCapIcon", "CheckBadgeIcon", "jsx", "_jsx", "jsxs", "_jsxs", "StudentsManagement", "_ref", "onBack", "students", "setStudents", "searchTerm", "setSearchTerm", "statusFilter", "setStatus<PERSON>ilter", "mockStudents", "id", "email", "role", "name", "accessCode", "enrolledCourses", "completedCourses", "certificates", "createdAt", "Date", "useEffect", "filteredStudents", "filter", "student", "_student$name", "matchesSearch", "toLowerCase", "includes", "matchesStatus", "length", "handleAddStudent", "console", "log", "handleEditStudent", "studentId", "handleDeleteStudent", "handleViewStudent", "getStudentStatus", "status", "color", "className", "children", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "type", "value", "onChange", "e", "target", "placeholder", "map", "index", "studentStatus", "tr", "initial", "opacity", "y", "animate", "transition", "delay", "toLocaleDateString", "title"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Admin/StudentsManagement.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  PlusIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n  UserIcon,\n  AcademicCapIcon,\n  CheckBadgeIcon\n} from '@heroicons/react/24/outline';\n\n// Types\nimport { Student } from '../../types';\n\ninterface StudentsManagementProps {\n  onBack?: () => void;\n}\n\nconst StudentsManagement: React.FC<StudentsManagementProps> = ({ onBack }) => {\n  const [students, setStudents] = useState<Student[]>([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState('all');\n\n  // Mock data for demonstration\n  const mockStudents: Student[] = [\n    {\n      id: '1',\n      email: '<EMAIL>',\n      role: 'student',\n      name: 'أحمد محمد',\n      accessCode: 'STU001',\n      enrolledCourses: ['1', '2'],\n      completedCourses: ['1'],\n      certificates: ['cert1'],\n      createdAt: new Date('2024-01-15')\n    },\n    {\n      id: '2',\n      email: '<EMAIL>',\n      role: 'student',\n      name: 'فاطمة علي',\n      accessCode: 'STU002',\n      enrolledCourses: ['2'],\n      completedCourses: [],\n      certificates: [],\n      createdAt: new Date('2024-02-10')\n    },\n    {\n      id: '3',\n      email: '<EMAIL>',\n      role: 'student',\n      name: 'محمد حسن',\n      accessCode: 'STU003',\n      enrolledCourses: ['1', '2', '3'],\n      completedCourses: ['1', '2'],\n      certificates: ['cert1', 'cert2'],\n      createdAt: new Date('2024-01-20')\n    }\n  ];\n\n  React.useEffect(() => {\n    setStudents(mockStudents);\n  }, []);\n\n  const filteredStudents = students.filter(student => {\n    const matchesSearch = student.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         student.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         student.accessCode.toLowerCase().includes(searchTerm.toLowerCase());\n    \n    let matchesStatus = true;\n    if (statusFilter === 'active') {\n      matchesStatus = student.enrolledCourses.length > 0;\n    } else if (statusFilter === 'completed') {\n      matchesStatus = student.completedCourses.length > 0;\n    }\n    \n    return matchesSearch && matchesStatus;\n  });\n\n  const handleAddStudent = () => {\n    // TODO: Implement add student functionality\n    console.log('Add student');\n  };\n\n  const handleEditStudent = (studentId: string) => {\n    // TODO: Implement edit student functionality\n    console.log('Edit student:', studentId);\n  };\n\n  const handleDeleteStudent = (studentId: string) => {\n    // TODO: Implement delete student functionality\n    console.log('Delete student:', studentId);\n  };\n\n  const handleViewStudent = (studentId: string) => {\n    // TODO: Implement view student functionality\n    console.log('View student:', studentId);\n  };\n\n  const getStudentStatus = (student: Student) => {\n    if (student.completedCourses.length > 0) {\n      return { status: 'مكتمل', color: 'green' };\n    } else if (student.enrolledCourses.length > 0) {\n      return { status: 'نشط', color: 'blue' };\n    } else {\n      return { status: 'غير نشط', color: 'gray' };\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4 space-x-reverse\">\n          {onBack && (\n            <button\n              onClick={onBack}\n              className=\"p-2 text-gray-600 hover:text-gray-900 transition-colors\"\n            >\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n              </svg>\n            </button>\n          )}\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">إدارة الطلاب</h1>\n            <p className=\"text-gray-600\">إدارة وتتبع جميع الطلاب المسجلين</p>\n          </div>\n        </div>\n        <button\n          onClick={handleAddStudent}\n          className=\"flex items-center space-x-2 space-x-reverse bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n        >\n          <PlusIcon className=\"w-5 h-5\" />\n          <span>إضافة طالب جديد</span>\n        </button>\n      </div>\n\n      {/* Filters */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              البحث في الطلاب\n            </label>\n            <input\n              type=\"text\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              placeholder=\"ابحث بالاسم، الإيميل، أو رمز الوصول...\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            />\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              حالة الطالب\n            </label>\n            <select\n              value={statusFilter}\n              onChange={(e) => setStatusFilter(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              <option value=\"all\">جميع الطلاب</option>\n              <option value=\"active\">الطلاب النشطون</option>\n              <option value=\"completed\">الطلاب المكتملون</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* Students Table */}\n      <div className=\"bg-white rounded-lg shadow-sm overflow-hidden\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  الطالب\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  رمز الوصول\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  الكورسات المسجلة\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  الكورسات المكتملة\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  الشهادات\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  الحالة\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  تاريخ التسجيل\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  الإجراءات\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {filteredStudents.map((student, index) => {\n                const studentStatus = getStudentStatus(student);\n                return (\n                  <motion.tr\n                    key={student.id}\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: index * 0.05 }}\n                    className=\"hover:bg-gray-50\"\n                  >\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"flex items-center space-x-3 space-x-reverse\">\n                        <div className=\"flex-shrink-0\">\n                          <div className=\"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\">\n                            <UserIcon className=\"w-6 h-6 text-blue-600\" />\n                          </div>\n                        </div>\n                        <div>\n                          <div className=\"text-sm font-medium text-gray-900\">\n                            {student.name || 'غير محدد'}\n                          </div>\n                          <div className=\"text-sm text-gray-500\">{student.email}</div>\n                        </div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className=\"text-sm font-mono text-gray-900 bg-gray-100 px-2 py-1 rounded\">\n                        {student.accessCode}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"flex items-center space-x-1 space-x-reverse\">\n                        <AcademicCapIcon className=\"w-4 h-4 text-blue-600\" />\n                        <span className=\"text-sm text-gray-900\">{student.enrolledCourses.length}</span>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"flex items-center space-x-1 space-x-reverse\">\n                        <CheckBadgeIcon className=\"w-4 h-4 text-green-600\" />\n                        <span className=\"text-sm text-gray-900\">{student.completedCourses.length}</span>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className=\"text-sm text-gray-900\">{student.certificates.length}</span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                        studentStatus.color === 'green' \n                          ? 'bg-green-100 text-green-800'\n                          : studentStatus.color === 'blue'\n                          ? 'bg-blue-100 text-blue-800'\n                          : 'bg-gray-100 text-gray-800'\n                      }`}>\n                        {studentStatus.status}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                      {new Date(student.createdAt).toLocaleDateString('ar-SA')}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                      <div className=\"flex items-center space-x-2 space-x-reverse\">\n                        <button\n                          onClick={() => handleViewStudent(student.id)}\n                          className=\"text-blue-600 hover:text-blue-900\"\n                          title=\"عرض الطالب\"\n                        >\n                          <EyeIcon className=\"w-4 h-4\" />\n                        </button>\n                        <button\n                          onClick={() => handleEditStudent(student.id)}\n                          className=\"text-green-600 hover:text-green-900\"\n                          title=\"تعديل الطالب\"\n                        >\n                          <PencilIcon className=\"w-4 h-4\" />\n                        </button>\n                        <button\n                          onClick={() => handleDeleteStudent(student.id)}\n                          className=\"text-red-600 hover:text-red-900\"\n                          title=\"حذف الطالب\"\n                        >\n                          <TrashIcon className=\"w-4 h-4\" />\n                        </button>\n                      </div>\n                    </td>\n                  </motion.tr>\n                );\n              })}\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      {filteredStudents.length === 0 && (\n        <div className=\"text-center py-12\">\n          <UserIcon className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">لا يوجد طلاب</h3>\n          <p className=\"text-gray-600\">لم يتم العثور على أي طلاب يطابقون البحث</p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default StudentsManagement;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,MAAM,KAAQ,eAAe,CACtC,OACEC,QAAQ,CACRC,UAAU,CACVC,SAAS,CACTC,OAAO,CACPC,QAAQ,CACRC,eAAe,CACfC,cAAc,KACT,6BAA6B,CAEpC;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAOA,KAAM,CAAAC,kBAAqD,CAAGC,IAAA,EAAgB,IAAf,CAAEC,MAAO,CAAC,CAAAD,IAAA,CACvE,KAAM,CAACE,QAAQ,CAAEC,WAAW,CAAC,CAAGjB,QAAQ,CAAY,EAAE,CAAC,CACvD,KAAM,CAACkB,UAAU,CAAEC,aAAa,CAAC,CAAGnB,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACoB,YAAY,CAAEC,eAAe,CAAC,CAAGrB,QAAQ,CAAC,KAAK,CAAC,CAEvD;AACA,KAAM,CAAAsB,YAAuB,CAAG,CAC9B,CACEC,EAAE,CAAE,GAAG,CACPC,KAAK,CAAE,sBAAsB,CAC7BC,IAAI,CAAE,SAAS,CACfC,IAAI,CAAE,WAAW,CACjBC,UAAU,CAAE,QAAQ,CACpBC,eAAe,CAAE,CAAC,GAAG,CAAE,GAAG,CAAC,CAC3BC,gBAAgB,CAAE,CAAC,GAAG,CAAC,CACvBC,YAAY,CAAE,CAAC,OAAO,CAAC,CACvBC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAClC,CAAC,CACD,CACET,EAAE,CAAE,GAAG,CACPC,KAAK,CAAE,sBAAsB,CAC7BC,IAAI,CAAE,SAAS,CACfC,IAAI,CAAE,WAAW,CACjBC,UAAU,CAAE,QAAQ,CACpBC,eAAe,CAAE,CAAC,GAAG,CAAC,CACtBC,gBAAgB,CAAE,EAAE,CACpBC,YAAY,CAAE,EAAE,CAChBC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAClC,CAAC,CACD,CACET,EAAE,CAAE,GAAG,CACPC,KAAK,CAAE,sBAAsB,CAC7BC,IAAI,CAAE,SAAS,CACfC,IAAI,CAAE,UAAU,CAChBC,UAAU,CAAE,QAAQ,CACpBC,eAAe,CAAE,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAChCC,gBAAgB,CAAE,CAAC,GAAG,CAAE,GAAG,CAAC,CAC5BC,YAAY,CAAE,CAAC,OAAO,CAAE,OAAO,CAAC,CAChCC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAClC,CAAC,CACF,CAEDjC,KAAK,CAACkC,SAAS,CAAC,IAAM,CACpBhB,WAAW,CAACK,YAAY,CAAC,CAC3B,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAY,gBAAgB,CAAGlB,QAAQ,CAACmB,MAAM,CAACC,OAAO,EAAI,KAAAC,aAAA,CAClD,KAAM,CAAAC,aAAa,CAAG,EAAAD,aAAA,CAAAD,OAAO,CAACV,IAAI,UAAAW,aAAA,iBAAZA,aAAA,CAAcE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACtB,UAAU,CAACqB,WAAW,CAAC,CAAC,CAAC,GAC/DH,OAAO,CAACZ,KAAK,CAACe,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACtB,UAAU,CAACqB,WAAW,CAAC,CAAC,CAAC,EAC9DH,OAAO,CAACT,UAAU,CAACY,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACtB,UAAU,CAACqB,WAAW,CAAC,CAAC,CAAC,CAExF,GAAI,CAAAE,aAAa,CAAG,IAAI,CACxB,GAAIrB,YAAY,GAAK,QAAQ,CAAE,CAC7BqB,aAAa,CAAGL,OAAO,CAACR,eAAe,CAACc,MAAM,CAAG,CAAC,CACpD,CAAC,IAAM,IAAItB,YAAY,GAAK,WAAW,CAAE,CACvCqB,aAAa,CAAGL,OAAO,CAACP,gBAAgB,CAACa,MAAM,CAAG,CAAC,CACrD,CAEA,MAAO,CAAAJ,aAAa,EAAIG,aAAa,CACvC,CAAC,CAAC,CAEF,KAAM,CAAAE,gBAAgB,CAAGA,CAAA,GAAM,CAC7B;AACAC,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC,CAC5B,CAAC,CAED,KAAM,CAAAC,iBAAiB,CAAIC,SAAiB,EAAK,CAC/C;AACAH,OAAO,CAACC,GAAG,CAAC,eAAe,CAAEE,SAAS,CAAC,CACzC,CAAC,CAED,KAAM,CAAAC,mBAAmB,CAAID,SAAiB,EAAK,CACjD;AACAH,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAEE,SAAS,CAAC,CAC3C,CAAC,CAED,KAAM,CAAAE,iBAAiB,CAAIF,SAAiB,EAAK,CAC/C;AACAH,OAAO,CAACC,GAAG,CAAC,eAAe,CAAEE,SAAS,CAAC,CACzC,CAAC,CAED,KAAM,CAAAG,gBAAgB,CAAId,OAAgB,EAAK,CAC7C,GAAIA,OAAO,CAACP,gBAAgB,CAACa,MAAM,CAAG,CAAC,CAAE,CACvC,MAAO,CAAES,MAAM,CAAE,OAAO,CAAEC,KAAK,CAAE,OAAQ,CAAC,CAC5C,CAAC,IAAM,IAAIhB,OAAO,CAACR,eAAe,CAACc,MAAM,CAAG,CAAC,CAAE,CAC7C,MAAO,CAAES,MAAM,CAAE,KAAK,CAAEC,KAAK,CAAE,MAAO,CAAC,CACzC,CAAC,IAAM,CACL,MAAO,CAAED,MAAM,CAAE,SAAS,CAAEC,KAAK,CAAE,MAAO,CAAC,CAC7C,CACF,CAAC,CAED,mBACExC,KAAA,QAAKyC,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExB1C,KAAA,QAAKyC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD1C,KAAA,QAAKyC,SAAS,CAAC,6CAA6C,CAAAC,QAAA,EACzDvC,MAAM,eACLL,IAAA,WACE6C,OAAO,CAAExC,MAAO,CAChBsC,SAAS,CAAC,yDAAyD,CAAAC,QAAA,cAEnE5C,IAAA,QAAK2C,SAAS,CAAC,SAAS,CAACG,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAJ,QAAA,cAC5E5C,IAAA,SAAMiD,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,WAAW,CAAE,CAAE,CAACC,CAAC,CAAC,iBAAiB,CAAE,CAAC,CACtF,CAAC,CACA,CACT,cACDlD,KAAA,QAAA0C,QAAA,eACE5C,IAAA,OAAI2C,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,qEAAY,CAAI,CAAC,cAClE5C,IAAA,MAAG2C,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,8KAAgC,CAAG,CAAC,EAC9D,CAAC,EACH,CAAC,cACN1C,KAAA,WACE2C,OAAO,CAAEZ,gBAAiB,CAC1BU,SAAS,CAAC,6HAA6H,CAAAC,QAAA,eAEvI5C,IAAA,CAACR,QAAQ,EAACmD,SAAS,CAAC,SAAS,CAAE,CAAC,cAChC3C,IAAA,SAAA4C,QAAA,CAAM,kFAAe,CAAM,CAAC,EACtB,CAAC,EACN,CAAC,cAGN5C,IAAA,QAAK2C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,cAChD1C,KAAA,QAAKyC,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpD1C,KAAA,QAAA0C,QAAA,eACE5C,IAAA,UAAO2C,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,kFAEhE,CAAO,CAAC,cACR5C,IAAA,UACEqD,IAAI,CAAC,MAAM,CACXC,KAAK,CAAE9C,UAAW,CAClB+C,QAAQ,CAAGC,CAAC,EAAK/C,aAAa,CAAC+C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC/CI,WAAW,CAAC,8LAAwC,CACpDf,SAAS,CAAC,wGAAwG,CACnH,CAAC,EACC,CAAC,cACNzC,KAAA,QAAA0C,QAAA,eACE5C,IAAA,UAAO2C,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,+DAEhE,CAAO,CAAC,cACR1C,KAAA,WACEoD,KAAK,CAAE5C,YAAa,CACpB6C,QAAQ,CAAGC,CAAC,EAAK7C,eAAe,CAAC6C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACjDX,SAAS,CAAC,wGAAwG,CAAAC,QAAA,eAElH5C,IAAA,WAAQsD,KAAK,CAAC,KAAK,CAAAV,QAAA,CAAC,+DAAW,CAAQ,CAAC,cACxC5C,IAAA,WAAQsD,KAAK,CAAC,QAAQ,CAAAV,QAAA,CAAC,iFAAc,CAAQ,CAAC,cAC9C5C,IAAA,WAAQsD,KAAK,CAAC,WAAW,CAAAV,QAAA,CAAC,6FAAgB,CAAQ,CAAC,EAC7C,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,cAGN5C,IAAA,QAAK2C,SAAS,CAAC,+CAA+C,CAAAC,QAAA,cAC5D5C,IAAA,QAAK2C,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B1C,KAAA,UAAOyC,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eACpD5C,IAAA,UAAO2C,SAAS,CAAC,YAAY,CAAAC,QAAA,cAC3B1C,KAAA,OAAA0C,QAAA,eACE5C,IAAA,OAAI2C,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,sCAEhG,CAAI,CAAC,cACL5C,IAAA,OAAI2C,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,yDAEhG,CAAI,CAAC,cACL5C,IAAA,OAAI2C,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,6FAEhG,CAAI,CAAC,cACL5C,IAAA,OAAI2C,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,mGAEhG,CAAI,CAAC,cACL5C,IAAA,OAAI2C,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,kDAEhG,CAAI,CAAC,cACL5C,IAAA,OAAI2C,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,sCAEhG,CAAI,CAAC,cACL5C,IAAA,OAAI2C,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,2EAEhG,CAAI,CAAC,cACL5C,IAAA,OAAI2C,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,wDAEhG,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cACR5C,IAAA,UAAO2C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CACjDpB,gBAAgB,CAACmC,GAAG,CAAC,CAACjC,OAAO,CAAEkC,KAAK,GAAK,CACxC,KAAM,CAAAC,aAAa,CAAGrB,gBAAgB,CAACd,OAAO,CAAC,CAC/C,mBACExB,KAAA,CAACX,MAAM,CAACuE,EAAE,EAERC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,KAAK,CAAER,KAAK,CAAG,IAAK,CAAE,CACpCjB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAE5B5C,IAAA,OAAI2C,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC1C,KAAA,QAAKyC,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1D5C,IAAA,QAAK2C,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B5C,IAAA,QAAK2C,SAAS,CAAC,qEAAqE,CAAAC,QAAA,cAClF5C,IAAA,CAACJ,QAAQ,EAAC+C,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAC3C,CAAC,CACH,CAAC,cACNzC,KAAA,QAAA0C,QAAA,eACE5C,IAAA,QAAK2C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC/ClB,OAAO,CAACV,IAAI,EAAI,UAAU,CACxB,CAAC,cACNhB,IAAA,QAAK2C,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAElB,OAAO,CAACZ,KAAK,CAAM,CAAC,EACzD,CAAC,EACH,CAAC,CACJ,CAAC,cACLd,IAAA,OAAI2C,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC5C,IAAA,SAAM2C,SAAS,CAAC,+DAA+D,CAAAC,QAAA,CAC5ElB,OAAO,CAACT,UAAU,CACf,CAAC,CACL,CAAC,cACLjB,IAAA,OAAI2C,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC1C,KAAA,QAAKyC,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1D5C,IAAA,CAACH,eAAe,EAAC8C,SAAS,CAAC,uBAAuB,CAAE,CAAC,cACrD3C,IAAA,SAAM2C,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAElB,OAAO,CAACR,eAAe,CAACc,MAAM,CAAO,CAAC,EAC5E,CAAC,CACJ,CAAC,cACLhC,IAAA,OAAI2C,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC1C,KAAA,QAAKyC,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1D5C,IAAA,CAACF,cAAc,EAAC6C,SAAS,CAAC,wBAAwB,CAAE,CAAC,cACrD3C,IAAA,SAAM2C,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAElB,OAAO,CAACP,gBAAgB,CAACa,MAAM,CAAO,CAAC,EAC7E,CAAC,CACJ,CAAC,cACLhC,IAAA,OAAI2C,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC5C,IAAA,SAAM2C,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAElB,OAAO,CAACN,YAAY,CAACY,MAAM,CAAO,CAAC,CAC1E,CAAC,cACLhC,IAAA,OAAI2C,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC5C,IAAA,SAAM2C,SAAS,CAAE,4DACfkB,aAAa,CAACnB,KAAK,GAAK,OAAO,CAC3B,6BAA6B,CAC7BmB,aAAa,CAACnB,KAAK,GAAK,MAAM,CAC9B,2BAA2B,CAC3B,2BAA2B,EAC9B,CAAAE,QAAA,CACAiB,aAAa,CAACpB,MAAM,CACjB,CAAC,CACL,CAAC,cACLzC,IAAA,OAAI2C,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC9D,GAAI,CAAAtB,IAAI,CAACI,OAAO,CAACL,SAAS,CAAC,CAACgD,kBAAkB,CAAC,OAAO,CAAC,CACtD,CAAC,cACLrE,IAAA,OAAI2C,SAAS,CAAC,iDAAiD,CAAAC,QAAA,cAC7D1C,KAAA,QAAKyC,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1D5C,IAAA,WACE6C,OAAO,CAAEA,CAAA,GAAMN,iBAAiB,CAACb,OAAO,CAACb,EAAE,CAAE,CAC7C8B,SAAS,CAAC,mCAAmC,CAC7C2B,KAAK,CAAC,yDAAY,CAAA1B,QAAA,cAElB5C,IAAA,CAACL,OAAO,EAACgD,SAAS,CAAC,SAAS,CAAE,CAAC,CACzB,CAAC,cACT3C,IAAA,WACE6C,OAAO,CAAEA,CAAA,GAAMT,iBAAiB,CAACV,OAAO,CAACb,EAAE,CAAE,CAC7C8B,SAAS,CAAC,qCAAqC,CAC/C2B,KAAK,CAAC,qEAAc,CAAA1B,QAAA,cAEpB5C,IAAA,CAACP,UAAU,EAACkD,SAAS,CAAC,SAAS,CAAE,CAAC,CAC5B,CAAC,cACT3C,IAAA,WACE6C,OAAO,CAAEA,CAAA,GAAMP,mBAAmB,CAACZ,OAAO,CAACb,EAAE,CAAE,CAC/C8B,SAAS,CAAC,iCAAiC,CAC3C2B,KAAK,CAAC,yDAAY,CAAA1B,QAAA,cAElB5C,IAAA,CAACN,SAAS,EAACiD,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CAAC,EACN,CAAC,CACJ,CAAC,GA/EAjB,OAAO,CAACb,EAgFJ,CAAC,CAEhB,CAAC,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,CACH,CAAC,CAELW,gBAAgB,CAACQ,MAAM,GAAK,CAAC,eAC5B9B,KAAA,QAAKyC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC5C,IAAA,CAACJ,QAAQ,EAAC+C,SAAS,CAAC,sCAAsC,CAAE,CAAC,cAC7D3C,IAAA,OAAI2C,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,gEAAY,CAAI,CAAC,cACxE5C,IAAA,MAAG2C,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,yMAAuC,CAAG,CAAC,EACrE,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAzC,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}