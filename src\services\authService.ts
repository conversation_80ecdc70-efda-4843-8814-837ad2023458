import { 
  signInWithEmailAndPassword, 
  signOut, 
  onAuthStateChanged,
  User as FirebaseUser
} from 'firebase/auth';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { auth, db } from '../config/firebase';
import { User, Student, Admin } from '../types';

class AuthService {
  // Admin login
  async loginAdmin(email: string, password: string): Promise<Admin> {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;
      
      // Get admin data from Firestore
      const adminDoc = await getDoc(doc(db, 'admins', user.uid));
      if (!adminDoc.exists()) {
        throw new Error('المستخدم غير مخول كمدير');
      }
      
      const adminData = adminDoc.data();
      return {
        id: user.uid,
        email: user.email!,
        role: 'admin',
        name: adminData.name,
        avatar: adminData.avatar,
        permissions: adminData.permissions || [],
        createdAt: adminData.createdAt?.toDate() || new Date()
      };
    } catch (error: any) {
      throw new Error(this.getErrorMessage(error.code));
    }
  }

  // Student login with access code
  async loginStudent(accessCode: string): Promise<Student> {
    try {
      // Find student by access code in Firestore
      const studentsRef = doc(db, 'students', accessCode);
      const studentDoc = await getDoc(studentsRef);
      
      if (!studentDoc.exists()) {
        throw new Error('كود الدخول غير صحيح');
      }
      
      const studentData = studentDoc.data();
      if (!studentData.isActive) {
        throw new Error('الحساب غير مفعل');
      }
      
      return {
        id: studentData.id,
        email: studentData.email || '',
        role: 'student',
        name: studentData.name,
        avatar: studentData.avatar,
        accessCode: accessCode,
        enrolledCourses: studentData.enrolledCourses || [],
        completedCourses: studentData.completedCourses || [],
        certificates: studentData.certificates || [],
        createdAt: studentData.createdAt?.toDate() || new Date()
      };
    } catch (error: any) {
      throw new Error(error.message || 'حدث خطأ في تسجيل الدخول');
    }
  }

  // Generate access code for student
  generateAccessCode(): string {
    return Math.floor(1000000 + Math.random() * 9000000).toString();
  }

  // Create student account
  async createStudent(studentData: {
    name: string;
    email?: string;
    enrolledCourses?: string[];
  }): Promise<string> {
    try {
      const accessCode = this.generateAccessCode();
      
      // Check if access code already exists
      const existingStudent = await getDoc(doc(db, 'students', accessCode));
      if (existingStudent.exists()) {
        // Generate new code if exists
        return this.createStudent(studentData);
      }
      
      const student = {
        id: accessCode,
        name: studentData.name,
        email: studentData.email || '',
        accessCode: accessCode,
        enrolledCourses: studentData.enrolledCourses || [],
        completedCourses: [],
        certificates: [],
        isActive: true,
        createdAt: new Date()
      };
      
      await setDoc(doc(db, 'students', accessCode), student);
      return accessCode;
    } catch (error: any) {
      throw new Error('فشل في إنشاء حساب الطالب');
    }
  }

  // Logout
  async logout(): Promise<void> {
    try {
      await signOut(auth);
    } catch (error: any) {
      throw new Error('فشل في تسجيل الخروج');
    }
  }

  // Get current user
  getCurrentUser(): Promise<FirebaseUser | null> {
    return new Promise((resolve) => {
      const unsubscribe = onAuthStateChanged(auth, (user) => {
        unsubscribe();
        resolve(user);
      });
    });
  }

  // Auth state listener
  onAuthStateChange(callback: (user: FirebaseUser | null) => void) {
    return onAuthStateChanged(auth, callback);
  }

  private getErrorMessage(errorCode: string): string {
    switch (errorCode) {
      case 'auth/user-not-found':
        return 'المستخدم غير موجود';
      case 'auth/wrong-password':
        return 'كلمة المرور غير صحيحة';
      case 'auth/invalid-email':
        return 'البريد الإلكتروني غير صحيح';
      case 'auth/user-disabled':
        return 'الحساب معطل';
      case 'auth/too-many-requests':
        return 'محاولات كثيرة، حاول مرة أخرى لاحقاً';
      default:
        return 'حدث خطأ في تسجيل الدخول';
    }
  }
}

export const authService = new AuthService();
