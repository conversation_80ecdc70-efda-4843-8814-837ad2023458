import { db } from '../config/firebase';
import { collection, getDocs, doc, getDoc, addDoc, updateDoc, deleteDoc } from 'firebase/firestore';
import { Course, Student, Quiz, Certificate } from '../types';
import { mockCourses, mockVideos, mockQuizzes, mockCertificates } from '../data/mockCourses';
import { mockStudents } from '../data/mockStudents';

class DataService {
  // Courses
  async getCourses(): Promise<Course[]> {
    try {
      // Return mock data for now
      return mockCourses;
    } catch (error) {
      console.error('Error fetching courses:', error);
      return mockCourses; // Fallback to mock data
    }
  }

  async getCourse(id: string): Promise<Course | null> {
    try {
      const course = mockCourses.find(c => c.id === id);
      return course || null;
    } catch (error) {
      console.error('Error fetching course:', error);
      return null;
    }
  }

  async addCourse(course: Omit<Course, 'id'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, 'courses'), course);
      return docRef.id;
    } catch (error) {
      console.error('Error adding course:', error);
      throw error;
    }
  }

  async updateCourse(id: string, course: Partial<Course>): Promise<void> {
    try {
      await updateDoc(doc(db, 'courses', id), course);
    } catch (error) {
      console.error('Error updating course:', error);
      throw error;
    }
  }

  async deleteCourse(id: string): Promise<void> {
    try {
      await deleteDoc(doc(db, 'courses', id));
    } catch (error) {
      console.error('Error deleting course:', error);
      throw error;
    }
  }

  // Students
  async getStudents(): Promise<Student[]> {
    try {
      return mockStudents;
    } catch (error) {
      console.error('Error fetching students:', error);
      return mockStudents;
    }
  }

  async getStudent(id: string): Promise<Student | null> {
    try {
      const student = mockStudents.find(s => s.id === id);
      return student || null;
    } catch (error) {
      console.error('Error fetching student:', error);
      return null;
    }
  }

  // Quizzes
  async getQuizzes(): Promise<Quiz[]> {
    try {
      return mockQuizzes;
    } catch (error) {
      console.error('Error fetching quizzes:', error);
      return mockQuizzes;
    }
  }

  async getQuiz(id: string): Promise<Quiz | null> {
    try {
      const quiz = mockQuizzes.find(q => q.id === id);
      return quiz || null;
    } catch (error) {
      console.error('Error fetching quiz:', error);
      return null;
    }
  }

  // Certificates
  async getCertificates(): Promise<Certificate[]> {
    try {
      return mockCertificates;
    } catch (error) {
      console.error('Error fetching certificates:', error);
      return mockCertificates;
    }
  }

  async getStudentCertificates(studentId: string): Promise<Certificate[]> {
    try {
      return mockCertificates.filter(cert => cert.studentId === studentId);
    } catch (error) {
      console.error('Error fetching student certificates:', error);
      return [];
    }
  }

  // Analytics
  async getAnalytics() {
    return {
      totalStudents: mockStudents.length,
      totalCourses: mockCourses.length,
      totalQuizzes: mockQuizzes.length,
      totalCertificates: mockCertificates.length,
      revenue: mockCourses.reduce((sum, course) => sum + course.price, 0),
      enrollments: mockStudents.reduce((sum, student) => sum + student.enrolledCourses.length, 0)
    };
  }
}

export const dataService = new DataService();
